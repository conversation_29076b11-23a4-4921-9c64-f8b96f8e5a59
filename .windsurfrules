Keep going until the job is completely solved before ending your turn

If you’re unsure about the code or files, open them - dont hallucinate.

Plan thoroughly before every tool call and reflect on the outcome after.

# Flutter App Expert – Unified Implementation & Code Quality Rules

// Flexibility Notice

Note: This is a recommended project structure and set of best practices. Be flexible and adapt to the existing project structure. Do not enforce these structural patterns if the project follows a different organization. Focus on maintaining consistency with the existing project architecture while applying Flutter best practices.

---

## 1. Core Principles

- **ONE CLASS ONE FILE**: Each Dart class must be in its own file.
- **English Only**: All code, comments, and documentation must be in English.
- **No magic numbers**: Use named constants.
- **No abbreviations** (except for standard/accepted ones).
- **No unnecessary code or comments**: Code should be self-explanatory; comment only for rationale or complex logic.
- **Null Safety**: Use proper null safety practices throughout the codebase.

---

## 2. Project Architecture & Structure

- **Follow Clean Architecture**: UI (presentation) → Cubit/Bloc → Domain (entities, repositories) ← Data (implementations).
- **Use Cubit for State Management**: Prefer Cubit for state management unless Bloc is required for complex event-driven flows.
- **Layered Structure**: Maintain the actual project structure as described in [architecure.mdc](mdc:architecure.mdc). Adapt to the project's existing organization.
- **Widget Structure**: Use core widgets, feature widgets, and screen-specific components as described in the architecture rule.
- **Dependency Injection**: Use getIt for DI, with proper singleton/factory patterns.
- **Routing**: Use GoRouter for navigation, define routes centrally, and use DetoxBottomNavBar for main navigation.

---

## 3. Coding Guidelines

- **Naming**: PascalCase for classes, camelCase for variables/functions, UPPERCASE for constants, underscores_case for files.
- **Functions**: Short, single-purpose, named with verbs. Use early returns, avoid deep nesting, use higher-order functions.
- **Classes**: Small, single-responsibility, SOLID, prefer composition.
- **Testing**: Arrange-Act-Assert, clear variable names, unit and widget tests for all logic and UI.
- **Encapsulation**: Hide implementation details, expose clear interfaces.
- **DRY**: Extract repeated code, use reusable widgets and utilities.
- **Version Control**: Small, focused commits with clear messages.
- **Error Handling**: Use Either for repository errors, show user-friendly messages, localize all errors.
- **Form Validation**: Use proper form validation techniques.
- **Asset Management**: Use proper asset management and referencing.

---

## 4. Widget Guidelines

- **Small & Focused**: Keep widgets small and focused.
- **Const Constructors**: Use const constructors when possible.
- **Widget Keys**: Implement proper widget keys for lists and dynamic widgets.
- **Layout**: Follow proper layout and composition principles.
- **Lifecycle**: Use proper widget lifecycle methods.
- **Error Boundaries**: Implement error boundaries where appropriate.
- **Performance**: Optimize build methods, avoid unnecessary rebuilds, and use performance best practices.
- **Accessibility**: Follow accessibility guidelines and ensure all interactive elements are accessible.

---

## 5. Performance Guidelines

- **Image Caching**: Use proper image caching techniques.
- **List Optimization**: Use ListView.builder and similar optimizations for large lists.
- **Build Optimization**: Optimize build methods and widget trees.
- **Memory Management**: Implement proper memory management and avoid leaks.
- **Platform Channels**: Use platform channels only when needed.
- **Compilation Optimization**: Follow best practices for build and compilation performance.

---

## 6. Testing Guidelines

- **Unit Tests**: Write unit tests for business logic.
- **Widget Tests**: Implement widget tests for UI components.
- **Integration Tests**: Use integration tests for feature flows.
- **Mocking**: Implement proper mocking strategies for dependencies.
- **Coverage**: Use test coverage tools to ensure code is well-tested.
- **Naming**: Follow clear and descriptive test naming conventions.
- **CI/CD**: Integrate tests into CI/CD pipelines.

---

## 7. Internationalization & Theming

- **Localization**: All user-facing strings must use AppLocalizations and be present in all .arb files (en, de, ru, tr, ar).


---

## 8. Code Quality & Review Discipline

- **Verify before presenting**: Never speculate or assume.
- **File-by-file changes**: Make atomic, reviewable edits.
- **No apologies, no summaries, no unnecessary confirmations**.
- **Preserve existing code**: Don't remove unrelated code.
- **No implementation checks unless requested**.
- **Provide real file links in documentation**.

---

## 9. Feature Completion Checklist

- Use the [feature_completion_checklist.mdc](mdc:feature_completion_checklist.mdc) as a practical appendix for every new feature or enhancement.

---

## 10. Flutter Best Practices Reference

- Adapt to existing project architecture while maintaining clean code principles
- Use Flutter 3.x features and Material 3 design
- Implement clean architecture with Cubit/BLoC pattern
- Follow proper state management principles
- Use proper dependency injection
- Implement proper error handling
- Follow platform-specific design guidelines
- Use proper localization techniques

# Cubit State Management in UI Screens

This document outlines the established patterns for implementing proper state management using Cubits in the UI layer of the DetoxMe app. Following these patterns ensures consistent state handling and avoids issues with local state management.

## Key Principles

1. **Avoid Local State**: Minimize use of `setState` in screens with Cubit patterns
2. **Use BlocProvider**: Provide Cubits through `BlocProvider` for dependency injection 
3. **React to State Changes**: Use `BlocBuilder` or `BlocConsumer` to react to state updates
4. **Separate Logic from UI**: Keep business logic in Cubits, presentation logic in UI
5. **Use BlocListener for Side Effects**: Handle navigation, dialogs, and other side effects in listeners

## Implementation Patterns

### Screen Structure with Cubit

The recommended structure for screens using Cubit state management:

```dart
// Provider pattern
class ActivityScreen extends StatelessWidget {
  const ActivityScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ActivityCubit>(
      create: (context) => getIt<ActivityCubit>()..loadActivities(),
      child: const ActivityView(),
    );
  }
}

// View implementation with BlocConsumer
class ActivityView extends StatelessWidget {
  const ActivityView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ActivityCubit, ActivityState>(
      listener: (context, state) {
        // Handle side effects like navigation, dialogs, etc.
        if (state is ActivityError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      builder: (context, state) {
        if (state is ActivityLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is ActivitiesLoaded) {
          return _buildContent(context, state.activities);
        } else if (state is ActivityError) {
          return _buildErrorState(context, state.message);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildContent(BuildContext context, List<Activity> activities) {
    // UI building logic
  }

  Widget _buildErrorState(BuildContext context, String message) {
    // Error UI building logic
  }
}
```

### Handling Multiple Cubits

For screens that require multiple Cubits, use `MultiBlocProvider` to inject them:

```dart
class DetailScreen extends StatelessWidget {
  final String itemId;
  
  const DetailScreen({Key? key, required this.itemId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ItemCubit>(
          create: (context) => getIt<ItemCubit>()..loadItem(itemId),
        ),
        BlocProvider<CommentsCubit>(
          create: (context) => getIt<CommentsCubit>()..loadComments(itemId),
        ),
        BlocProvider<ActivityTimerCubit>(
          create: (context) => getIt<ActivityTimerCubit>(),
        ),
      ],
      child: const DetailView(),
    );
  }
}
```

### Using BlocListener for Side Effects

When you need to react to state changes without rebuilding the UI, use `BlocListener`:

```dart
BlocListener<ActivityTimerCubit, ActivityTimerState>(
  listener: (context, state) {
    if (state is ActivityTimerCompleted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(l10n.activityComplete),
          content: Text(l10n.activityCompleteMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.ok),
            ),
          ],
        ),
      );
      
      // Call another cubit method
      context.read<ActivityCubit>().completeActivity();
    }
  },
  child: const SizedBox.shrink(), // No UI to build
),
```

### Multiple State Reactions with MultiBlocListener

Group multiple listeners with `MultiBlocListener`:

```dart
MultiBlocListener(
  listeners: [
    BlocListener<ActivityCubit, ActivityState>(
      listener: (context, state) {
        // Activity state reactions
      },
    ),
    BlocListener<ActivityTimerCubit, ActivityTimerState>(
      listener: (context, state) {
        // Timer state reactions
      },
    ),
    BlocListener<RewardCubit, RewardState>(
      listener: (context, state) {
        // Reward state reactions
      },
    ),
  ],
  child: Scaffold(
    // The rest of your UI
  ),
),
```

### Accessing Cubits in Event Handlers

Use `context.read()` to access Cubits in event handlers:

```dart
ElevatedButton(
  onPressed: () {
    // Get a reference to the cubit without subscribing to changes
    final activityCubit = context.read<ActivityCubit>();
    activityCubit.startActivity();
    
    // If another cubit needs to be updated as well
    context.read<ActivityTimerCubit>().startTimer();
  },
  child: Text(l10n.startActivity),
),
```

### Selective Rebuilds with BlocSelector

Use `BlocSelector` to rebuild only when specific parts of the state change:

```dart
BlocSelector<ProfileCubit, ProfileState, bool>(
  selector: (state) => state is ProfileLoaded ? state.isEditing : false,
  builder: (context, isEditing) {
    return isEditing
      ? _buildEditForm(context)
      : _buildProfileView(context);
  },
),
```

## State Classes and Extensions

### Using copyWith for State Updates

Create immutable states with `copyWith` methods for easy updates:

```dart
class ActivityDetailState extends Equatable {
  final Activity? activity;
  final bool isLoading;
  final String? error;
  final bool isActive;
  final int elapsedSeconds;
  final List<Comment> comments;
  
  const ActivityDetailState({
    this.activity,
    this.isLoading = false,
    this.error,
    this.isActive = false,
    this.elapsedSeconds = 0,
    this.comments = const [],
  });
  
  ActivityDetailState copyWith({
    Activity? activity,
    bool? isLoading,
    String? error,
    bool? isActive,
    int? elapsedSeconds,
    List<Comment>? comments,
  }) {
    return ActivityDetailState(
      activity: activity ?? this.activity,
      isLoading: isLoading ?? this.isLoading,
      error: error,  // null is valid to clear errors
      isActive: isActive ?? this.isActive,
      elapsedSeconds: elapsedSeconds ?? this.elapsedSeconds,
      comments: comments ?? this.comments,
    );
  }
  
  @override
  List<Object?> get props => [
    activity, isLoading, error, isActive, elapsedSeconds, comments
  ];
}
```

### State Extension Methods

Add extension methods to extract common state properties:

```dart
extension ActivityDetailStateExtensions on ActivityDetailState {
  bool get isCompleted => 
    activity != null && activity!.completionDate != null;
    
  bool get canStart => 
    activity != null && !isLoading && !isActive && !isCompleted;
    
  bool get canPause => isActive;
  
  bool get canResume => !isActive && elapsedSeconds > 0 && !isCompleted;
  
  bool get hasComments => comments.isNotEmpty;
  
  String get formattedElapsedTime {
    final minutes = (elapsedSeconds / 60).floor();
    final seconds = elapsedSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
```

## Converting from setState to Cubit Pattern

When refactoring a screen from local state to Cubit pattern:

1. **Identify State Variables**: Identify all variables managed with `setState`
2. **Create Cubit State Class**: Define a state class with all required properties
3. **Implement Cubit Methods**: Create methods in Cubit to update state
4. **Replace setState Calls**: Replace all `setState` calls with Cubit method calls
5. **Use BlocBuilder/Consumer**: Replace direct state usage with BlocBuilder/Consumer

### Before (with setState):

```dart
class ActivityDetailScreen extends StatefulWidget {
  final Activity activity;
  
  const ActivityDetailScreen({Key? key, required this.activity}) 
      : super(key: key);
  
  @override
  State<ActivityDetailScreen> createState() => _ActivityDetailScreenState();
}

class _ActivityDetailScreenState extends State<ActivityDetailScreen> {
  bool _isActive = false;
  int _elapsedSeconds = 0;
  Timer? _timer;
  
  void _startActivity() {
    setState(() {
      _isActive = true;
    });
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _elapsedSeconds++;
      });
    });
  }
  
  void _pauseActivity() {
    _timer?.cancel();
    setState(() {
      _isActive = false;
    });
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // UI implementation using _isActive and _elapsedSeconds
  }
}
```

### After (with Cubit):

```dart
// ActivityTimerCubit
class ActivityTimerCubit extends Cubit<ActivityTimerState> {
  Timer? _timer;
  
  ActivityTimerCubit() : super(const ActivityTimerState());
  
  void startTimer() {
    emit(state.copyWith(isActive: true));
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      emit(state.copyWith(
        elapsedSeconds: state.elapsedSeconds + 1
      ));
    });
  }
  
  void pauseTimer() {
    _timer?.cancel();
    emit(state.copyWith(isActive: false));
  }
  
  void resetTimer() {
    _timer?.cancel();
    emit(state.copyWith(
      isActive: false,
      elapsedSeconds: 0,
    ));
  }
  
  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}

// UI Implementation
class ActivityDetailScreen extends StatelessWidget {
  final Activity activity;
  
  const ActivityDetailScreen({Key? key, required this.activity}) 
      : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ActivityTimerCubit>(),
      child: ActivityDetailView(activity: activity),
    );
  }
}

class ActivityDetailView extends StatelessWidget {
  final Activity activity;
  
  const ActivityDetailView({Key? key, required this.activity}) 
      : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActivityTimerCubit, ActivityTimerState>(
      builder: (context, state) {
        // UI implementation using state.isActive and state.elapsedSeconds
        return Scaffold(
          // ...
          floatingActionButton: state.isActive
              ? FloatingActionButton(
                  onPressed: () => context.read<ActivityTimerCubit>().pauseTimer(),
                  child: const Icon(Icons.pause),
                )
              : FloatingActionButton(
                  onPressed: () => context.read<ActivityTimerCubit>().startTimer(),
                  child: const Icon(Icons.play_arrow),
                ),
        );
      },
    );
  }
}
```

## Common Cubit Usage Patterns

### Loading Data

```dart
class ItemCubit extends Cubit<ItemState> {
  final ItemRepository _repository;
  
  ItemCubit(this._repository) : super(const ItemState.initial());
  
  Future<void> loadItem(String id) async {
    emit(const ItemState.loading());
    
    try {
      final item = await _repository.getItem(id);
      emit(ItemState.loaded(item));
    } catch (e) {
      emit(ItemState.error(e.toString()));
    }
  }
}
```

### Handling Form Input

```dart
class FormCubit extends Cubit<FormState> {
  FormCubit() : super(const FormState());
  
  void nameChanged(String value) {
    emit(state.copyWith(
      name: value,
      nameError: value.isEmpty ? 'Name cannot be empty' : null,
    ));
  }
  
  void emailChanged(String value) {
    final emailValid = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value);
    emit(state.copyWith(
      email: value,
      emailError: !emailValid ? 'Invalid email address' : null,
    ));
  }
  
  bool get isValid => state.nameError == null && 
                     state.emailError == null &&
                     state.name.isNotEmpty &&
                     state.email.isNotEmpty;
  
  Future<void> submit() async {
    if (!isValid) return;
    
    emit(state.copyWith(isSubmitting: true));
    
    try {
      // Repository call
      emit(state.copyWith(isSuccess: true, isSubmitting: false));
    } catch (e) {
      emit(state.copyWith(
        isSubmitting: false,
        error: e.toString(),
      ));
    }
  }
}
```

### Managing Tab State

```dart
class TabCubit extends Cubit<int> {
  TabCubit() : super(0);
  
  void changeTab(int index) => emit(index);
}

// In UI
BlocProvider(
  create: (context) => TabCubit(),
  child: BlocBuilder<TabCubit, int>(
    builder: (context, activeTabIndex) {
      return DefaultTabController(
        length: 3,
        initialIndex: activeTabIndex,
        child: Scaffold(
          appBar: AppBar(
            bottom: TabBar(
              onTap: (index) => context.read<TabCubit>().changeTab(index),
              tabs: [
                Tab(text: 'Tab 1'),
                Tab(text: 'Tab 2'),
                Tab(text: 'Tab 3'),
              ],
            ),
          ),
          body: TabBarView(
            physics: const NeverScrollableScrollPhysics(), // Prevent swiping to change tabs
            children: [
              // Tab contents
            ],
          ),
        ),
      );
    },
  ),
),
```

## Best Practices

1. **Use Dependency Injection**: Inject dependencies through constructors to allow for easy testing
2. **Keep States Immutable**: Always create new state instances; never mutate existing state
3. **Handle Error States**: Include error handling in all asynchronous operations
4. **Make Small, Focused Cubits**: Create purpose-specific Cubits rather than large, general ones
5. **Consistency in State Design**: Follow a consistent pattern for state class design
6. **Use Equatable for States**: Extend Equatable to prevent unnecessary rebuilds

---
description: 
globs: 
alwaysApply: true
---

# Project Architecture and Widget Structure

## Clean Architecture Overview

The application follows a clean architecture approach with clear separation of concerns across multiple layers. The structure ensures maintainability, testability, and scalability.

## Layer Structure
/lib
├── core # Utilities, Constants, Extensions, Widgets
├── data # Repositories implementations, Data Sources (Local & Remote)
├── domain # Entities, Repository interfaces, UseCases
└── presentation
    ├── bloc # Cubits/Blocs for state management
    └── ui # Screens, Widgets, Themes

### Core Layer
The [core](mdc:lib/core) directory contains app-wide utilities and base components:
- [constants](mdc:lib/core/constants) - App-wide constant values and enums
- [extensions](mdc:lib/core/extensions) - Extension methods on existing types
- [widgets](mdc:lib/core/widgets) - Reusable widgets used across the app
- [themes](mdc:lib/core/themes) - App theming and styling
- [router](mdc:lib/core/router) - App navigation configuration using GoRouter
- [network](mdc:lib/core/network) - Network info and connectivity handling
- [services](mdc:lib/core/services) - App-wide services (notifications, analytics)

### Domain Layer
The [domain](mdc:lib/domain) directory contains business logic independent of any framework:
- [entities](mdc:lib/domain/entities) - Plain Dart objects representing core business models
  - Business models include `Activity`, `Mood`, `UserProfile`, `Coupon`, etc.
- [repositories](mdc:lib/domain/repositories) - Repository interfaces
- [failure](mdc:lib/domain/failure) - Domain-specific failure classes

### Data Layer
The [data](mdc:lib/data) directory implements data access and persistence:
- [repositories](mdc:lib/data/repositories) - Concrete implementations of repository interfaces
- [local](mdc:lib/data/local) - Local data sources (Hive)
  - Includes sync queue for offline-first capabilities
- [remote](mdc:lib/data/remote) - Remote data sources (Supabase)
  - API clients and data models that map to/from JSON

### Presentation Layer
The [presentation](mdc:lib/presentation) directory handles UI and state management:
- [bloc](mdc:lib/presentation/bloc) - State management using Cubit/Bloc pattern
  - Organized by feature (dashboard, activity, user_profile, etc.)
- [ui](mdc:lib/presentation/ui) - UI components organized by feature
  - [screens](mdc:lib/presentation/ui/screens) - Main screen widgets
  - [widgets](mdc:lib/presentation/ui/widgets) - Common widgets used across features

## Widget Reuse Guidelines

### Component Hierarchy

1. **Core Widgets**: Located in [core/widgets](mdc:lib/core/widgets)
   - Base components used throughout the app
   - Example: [BaseScaffold](mdc:lib/core/widgets/base_scaffold.dart) for consistent page structure
   - Example: [DetoxBottomNavBar](mdc:lib/core/widgets/detox_bottom_nav_bar.dart) for navigation

2. **Feature-Specific Widgets**: Located in feature subdirectories
   - Widgets specific to a feature but potentially reusable
   - Path: `lib/presentation/ui/screens/[feature]/widgets/`
   - Examples: [MoodOMeter](mdc:lib/presentation/ui/screens/dashboard/widgets/mood_o_meter.dart), [AnimatedSpeedometerChart](mdc:lib/presentation/ui/screens/dashboard/widgets/animated_speedometer_chart.dart)

3. **Screen-Specific Components**: Located directly in screen files
   - Components only used in a single screen
   - Usually defined as private methods within the screen widget (e.g., `_buildProfileHeader`, `_buildStatCards`)

4. **Component Views**: For complex screens with multiple sections
   - Path: `lib/presentation/ui/screens/[feature]/components/`
   - Example: [DashboardContentView](mdc:lib/presentation/ui/screens/dashboard/components/dashboard_content_view.dart)

### Widget Organization

For each feature (e.g., dashboard, profile, detox_points):
```
presentation/ui/screens/[feature]/
├── [feature]_screen.dart # Main screen widget
├── components/ # (Optional) Larger composed components
│   └── [component]_view.dart
└── widgets/ # Reusable widgets specific to the feature
    └── [widget_name].dart
```

### Screen Structure Pattern
Most screen widgets follow this structure:
1. Main screen class (StatelessWidget) that sets up BlocProvider(s)
2. View class that handles the presentation logic and BlocConsumer/BlocBuilder
3. Private methods to build different sections of the screen

Example:
```dart
class ProfileScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Setup providers
    return MultiBlocProvider(
      providers: [...],
      child: const ProfileView(),
    );
  }
}

class ProfileView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BaseScaffold.withTitle(
      title: 'Profile',
      body: BlocConsumer<ProfileCubit, ProfileState>(
        // Handle state transitions
        builder: (context, state) {
          if (state is ProfileLoaded) {
            return _buildProfileContent(context, state.profile);
          }
          // Other states...
        },
      ),
    );
  }

  // Private methods for UI sections
  Widget _buildProfileContent(BuildContext context, UserProfile profile) {...}
  Widget _buildProfileHeader(BuildContext context, UserProfile profile) {...}
  Widget _buildStatCards(BuildContext context, UserProfile profile) {...}
}
```

### State Management

- Each feature has corresponding state management in [presentation/bloc](mdc:lib/presentation/bloc)
- Follow the pattern:
  ```
  presentation/bloc/[feature]/
  ├── [feature]_cubit.dart
  └── [feature]_state.dart
  ```
- Cubits are used for simpler features, Blocs for complex event-driven features
- States are typically modeled using sealed classes or a single class with status enum

## Design Patterns and UI Components

### Card Components
- Feature Cards: Gradient backgrounds, decorative elements, rounded corners
- Stat Cards: Consistent size (160px), icon + bold value + subtitle
- Activity Cards: Used in horizontal scrollable lists

### Custom UI Components
- MoodOMeter: Interactive semi-circular mood selector with emoji indicators
- AnimatedSpeedometerChart: Displays progress metrics in semi-circular design
- Section Headers: Icon + title + optional action
- Empty States: Icon, title, message, and action button

### Animation Patterns
- Staggered Loading: FadeInUp with increasing delays for list items
- Pulse Animations: For interactive elements
- Transitions: Consistent duration (300-800ms) with easeInOut curves

## Dependency Flow

The dependency flow follows the principles of clean architecture:
- Presentation layer depends on Domain layer
- Data layer implements Domain interfaces
- Domain layer has no dependencies on outer layers

This ensures that business logic remains pure and testable, independent of UI or data source implementation details.

## Development Flow for New Features

1. **Analyze & Define Use Case**
   - Determine the feature requirements
   - Create any necessary use cases in the domain layer

2. **Data Layer Implementation**
   - Define repository interfaces in domain layer
   - Implement repositories in data layer
   - Create data models and sources as needed

3. **State Management**
   - Create or update Cubits/Blocs in presentation layer
   - Define states and events/methods

4. **UI Implementation**
   - Create screens and widgets following the structure guidelines
   - Apply consistent styling using the theme system
   - Connect UI to state management

5. **Testing**
   - Write unit tests for business logic
   - Add widget tests for complex UI components

## Styling Guidelines

- Use the theme system consistently
- Apply theme colors through `theme.colorScheme`
- Use `withValues(alpha: x)` instead of `withValues(alpha:)` for transparency
- Follow consistent spacing and sizing conventions
- Refer to [design-guidelines.mdc](.cursor/rules/design-guidelines.mdc) and [ui-components.mdc](.cursor/rules/ui-components.mdc) for detailed styling patterns

## Navigation

- Use [GoRouter](mdc:lib/core/router/app_router.dart) for app navigation
- Define routes in a central location
- Use the [DetoxBottomNavBar](mdc:lib/core/widgets/detox_bottom_nav_bar.dart) for main navigation
- Navigation actions use `context.go()` or `context.push()` methods

## Error Handling

- Use the Either type from dartz for error handling in repositories
- Define domain-specific failures in [domain/failure](mdc:lib/domain/failure)
- Handle errors appropriately in the presentation layer
- Show user-friendly error messages with SnackBar or Dialog

## Offline-First Approach

- Local data sources serve as single source of truth
- Remote operations are queued when offline and synced when online
- Network state is monitored via the NetworkInfo service
- Repository implementations handle connection state and data sync

## Internationalization

- Localization files located in [localization/arb](mdc:lib/localization/arb)
- Use AppLocalizations for accessing localized strings
- Access via: `final l10n = AppLocalizations.of(context)!;`