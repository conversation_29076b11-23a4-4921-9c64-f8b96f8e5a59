import 'package:equatable/equatable.dart';
import 'package:projectpilot/domain/entities/platform_connector.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';

/// State for the settings cubit
class SettingsState extends Equatable {
  /// User profile data
  final UserProfile? userProfile;

  /// Platform connectors for integrations
  final List<PlatformConnector> platformConnectors;

  /// Token balance for AI services
  final int tokenBalance;

  /// Minutes balance for recording services
  final int minutesBalance;

  /// Indicates if data is being loaded
  final bool isLoading;

  /// Error message if any
  final String? error;

  /// Indicates if connecting to a platform is in progress
  final bool isConnectingPlatform;

  /// ID of the platform currently being connected to
  final String? connectingPlatformId;

  /// Whether automatic assignment is enabled
  final bool isAutoAssignEnabled;

  /// Creates a new settings state
  const SettingsState({
    this.userProfile,
    this.platformConnectors = const [],
    this.tokenBalance = 0,
    this.minutesBalance = 0,
    this.isLoading = false,
    this.error,
    this.isConnectingPlatform = false,
    this.connectingPlatformId,
    this.isAutoAssignEnabled = true,
  });

  /// Factory method to create the initial state
  factory SettingsState.initial() => const SettingsState();

  /// Copies the current state with modified values
  SettingsState copyWith({
    UserProfile? userProfile,
    List<PlatformConnector>? platformConnectors,
    int? tokenBalance,
    int? minutesBalance,
    bool? isLoading,
    String? error,
    bool? isConnectingPlatform,
    String? connectingPlatformId,
    bool? isAutoAssignEnabled,
  }) {
    return SettingsState(
      userProfile: userProfile ?? this.userProfile,
      platformConnectors: platformConnectors ?? this.platformConnectors,
      tokenBalance: tokenBalance ?? this.tokenBalance,
      minutesBalance: minutesBalance ?? this.minutesBalance,
      isLoading: isLoading ?? this.isLoading,
      error: error, // Pass error directly to allow setting to null
      isConnectingPlatform: isConnectingPlatform ?? this.isConnectingPlatform,
      connectingPlatformId: connectingPlatformId ?? this.connectingPlatformId,
      isAutoAssignEnabled: isAutoAssignEnabled ?? this.isAutoAssignEnabled,
    );
  }

  @override
  List<Object?> get props => [
    userProfile,
    platformConnectors,
    tokenBalance,
    minutesBalance,
    isLoading,
    error,
    isConnectingPlatform,
    connectingPlatformId,
    isAutoAssignEnabled,
  ];
}
