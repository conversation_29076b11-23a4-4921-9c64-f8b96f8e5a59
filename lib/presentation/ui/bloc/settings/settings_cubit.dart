import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_web_auth/flutter_web_auth.dart';
import 'package:logger/logger.dart';
import 'package:projectpilot/core/di/service_locator.dart';
import 'package:projectpilot/domain/entities/api_key.dart';
import 'package:projectpilot/domain/entities/platform_connector.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';
import 'package:projectpilot/domain/repositories/user_repository.dart';
import 'package:projectpilot/presentation/ui/bloc/api_key/api_key_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/language/language_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_state.dart';
import 'package:projectpilot/presentation/ui/bloc/theme/theme_cubit.dart';
import 'package:projectpilot/presentation/ui/screens/connections/oauth_handler.dart';
import 'package:projectpilot/presentation/ui/screens/info/about_screen.dart';
import 'package:projectpilot/presentation/ui/screens/info/contact_support_screen.dart';
import 'package:projectpilot/presentation/ui/screens/info/faq_screen.dart';
import 'package:projectpilot/presentation/ui/screens/info/privacy_policy_screen.dart';
import 'package:projectpilot/presentation/ui/screens/info/terms_of_service_screen.dart';
import 'dart:io';
import 'package:projectpilot/core/services/voice/audio_file_manager.dart';

/// Cubit for managing all settings in the application
class SettingsCubit extends Cubit<SettingsState> {
  final UserRepository _userRepository;
  final LanguageCubit _languageCubit;
  final ThemeCubit _themeCubit;
  final ApiKeyCubit _apiKeyCubit;
  final OAuthHandler _oAuthHandler;

  /// Constructor for the SettingsCubit
  SettingsCubit({
    required UserRepository userRepository,
    required LanguageCubit languageCubit,
    required ThemeCubit themeCubit,
    required ApiKeyCubit apiKeyCubit,
    required OAuthHandler oAuthHandler,
  }) : _userRepository = userRepository,
       _languageCubit = languageCubit,
       _themeCubit = themeCubit,
       _apiKeyCubit = apiKeyCubit,
       _oAuthHandler = oAuthHandler,
       super(SettingsState.initial());

  /// Load all settings data
  Future<void> loadSettings() async {
    emit(state.copyWith(isLoading: true));

    // Load user profile
    final userResult = await _userRepository.getCurrentUserProfile();

    // Load token and minutes balances
    final tokensResult = await _userRepository.getTokenBalance();
    final minutesResult = await _userRepository.getMinutesBalance();

    // Combine results
    userResult.fold(
      (failure) =>
          emit(state.copyWith(error: failure.message, isLoading: false)),
      (userProfile) {
        tokensResult.fold(
          (failure) => emit(
            state.copyWith(
              error: failure.message,
              isLoading: false,
              userProfile: userProfile,
            ),
          ),
          (tokens) {
            minutesResult.fold(
              (failure) => emit(
                state.copyWith(
                  error: failure.message,
                  isLoading: false,
                  userProfile: userProfile,
                  tokenBalance: tokens,
                ),
              ),
              (minutes) {
                emit(
                  state.copyWith(
                    isLoading: false,
                    userProfile: userProfile,
                    tokenBalance: tokens,
                    minutesBalance: minutes,
                    platformConnectors:
                        [], // Empty list since platform connections are temporarily disabled
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  /// Change application language
  Future<void> changeLanguage(String languageCode) async {
    await _languageCubit.changeLanguage(languageCode);
  }

  /// Toggle theme between light and dark
  Future<void> toggleTheme() async {
    await _themeCubit.toggleTheme();
  }

  /// Save API key
  Future<void> saveApiKey(String keyType, String value) async {
    final apiKey = ApiKey(
      keyType: keyType,
      keyValue: value,
      description: 'Added via settings',
    );

    await _apiKeyCubit.saveApiKey(apiKey);
  }

  /// Navigate to the packages screen to purchase more tokens
  void navigateToPackages(BuildContext context) {
    // TODO: Implement navigation to packages screen when it's available
    // Navigator.push(
    //   context,
    //   MaterialPageRoute(
    //     builder: (context) => const PackagesScreen(),
    //   ),
    // );
  }

  /// Navigate to privacy policy
  void navigateToPrivacyPolicy(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
    );
  }

  /// Navigate to terms of service
  void navigateToTermsOfService(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const TermsOfServiceScreen()),
    );
  }

  /// Navigate to FAQ
  void navigateToFAQ(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const FAQScreen()));
  }

  /// Navigate to contact support
  void navigateToContactSupport(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ContactSupportScreen()),
    );
  }

  /// Navigate to about page
  void navigateToAbout(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AboutScreen()));
  }

  /// Logout user
  Future<void> logout() async {
    emit(state.copyWith(isLoading: true));

    final result = await _userRepository.logout();

    result.fold(
      (failure) =>
          emit(state.copyWith(error: failure.message, isLoading: false)),
      (_) {
        // Handle successful logout, typically navigation would happen in the UI
        emit(SettingsState.initial());
      },
    );
  }

  /// Clear error message
  void clearError() {
    if (state.error != null) {
      emit(state.copyWith(error: null));
    }
  }

  /// Toggle automatic assignment setting
  void toggleAutoAssign(bool value) {
    emit(state.copyWith(isAutoAssignEnabled: value));
  }

  /// Update user profile
  Future<void> updateUserProfile({String? username}) async {
    emit(state.copyWith(isLoading: true));

    // Get current profile first
    final currentProfileResult = await _userRepository.getCurrentUserProfile();

    currentProfileResult.fold(
      (failure) =>
          emit(state.copyWith(error: failure.message, isLoading: false)),
      (currentProfile) async {
        // Create updated profile with new username if provided
        final updatedProfile = currentProfile.copyWith(
          username: username ?? currentProfile.username,
        );

        // Update the profile
        final result = await _userRepository.updateUserProfile(updatedProfile);

        result.fold(
          (failure) =>
              emit(state.copyWith(error: failure.message, isLoading: false)),
          (profile) {
            emit(state.copyWith(userProfile: profile, isLoading: false));
          },
        );
      },
    );
  }

  /// Upload profile image
  Future<void> uploadProfileImage(File imageFile) async {
    emit(state.copyWith(isLoading: true));

    // First upload the image
    final uploadResult = await _userRepository.uploadProfileImage(imageFile);

    uploadResult.fold(
      (failure) =>
          emit(state.copyWith(error: failure.message, isLoading: false)),
      (imageUrl) async {
        // Get current profile
        final currentProfileResult =
            await _userRepository.getCurrentUserProfile();

        currentProfileResult.fold(
          (failure) =>
              emit(state.copyWith(error: failure.message, isLoading: false)),
          (currentProfile) async {
            // Create updated profile with new image URL
            final updatedProfile = currentProfile.copyWith(
              profileImageUrl: imageUrl,
            );

            // Update the profile
            final result = await _userRepository.updateUserProfile(
              updatedProfile,
            );

            result.fold(
              (failure) => emit(
                state.copyWith(error: failure.message, isLoading: false),
              ),
              (profile) {
                emit(state.copyWith(userProfile: profile, isLoading: false));
              },
            );
          },
        );
      },
    );
  }

  /// Delete all audio files
  Future<void> deleteAllAudioFiles() async {
    try {
      final audioFileManager = sl<AudioFileManager>();
      final success = await audioFileManager.deleteAllAudioFiles();

      if (success) {
        // Show success notification through error popup and clear immediately
        emit(state.copyWith(error: 'All audio files deleted successfully'));
        await Future.delayed(const Duration(seconds: 2));
        emit(state.copyWith(error: null));
      } else {
        emit(state.copyWith(error: 'Failed to delete audio files'));
      }
    } catch (e) {
      emit(state.copyWith(error: 'Error deleting audio files: $e'));
    }
  }
}
