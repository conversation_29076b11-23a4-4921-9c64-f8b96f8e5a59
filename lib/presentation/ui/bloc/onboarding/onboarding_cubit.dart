import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:logger/logger.dart';
import 'package:projectpilot/domain/repositories/user_repository.dart';
import 'package:projectpilot/presentation/ui/screens/auth/onboarding_screen.dart';
import 'package:projectpilot/presentation/ui/screens/auth/platform_connection_screen.dart';

part 'onboarding_state.dart';

/// Cubit for handling onboarding data
class OnboardingCubit extends Cubit<OnboardingState> {
  final UserRepository _userRepository;
  final Logger _logger = Logger();
  
  /// Creates an onboarding cubit
  OnboardingCubit({required UserRepository userRepository})
    : _userRepository = userRepository,
      super(const OnboardingState());

  /// Update username
  void updateUsername(String username) {
    if (isClosed) return; // Prevent emitting states after close
    emit(state.copyWith(username: username));
    _validateForm();
  }

  /// Update avatar image
  void updateAvatar(File? avatarImage) {
    if (isClosed) return; // Prevent emitting states after close
    emit(state.copyWith(avatarImage: avatarImage));
    _validateForm();
  }

  /// Crop image with proper aspect ratio
  Future<File?> cropImage(File imageFile) async {
    final CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: imageFile.path,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Crop Profile Image',
          toolbarColor: const Color(0xFF6366F1),
          toolbarWidgetColor: const Color(0xFFFFFFFF),
          activeControlsWidgetColor: const Color(0xFF6366F1),
          initAspectRatio: CropAspectRatioPreset.square,
          lockAspectRatio: true,
        ),
        IOSUiSettings(
          title: 'Crop Profile Image',
          aspectRatioLockEnabled: true,
          minimumAspectRatio: 1.0,
        ),
      ],
    );

    if (croppedFile != null) {
      return File(croppedFile.path);
    }
    return null;
  }

  /// Update birth year
  void updateBirthYear(int birthYear) {
    if (isClosed) return; // Prevent emitting states after close
    emit(state.copyWith(birthYear: birthYear));
    _validateForm();
  }

  /// Update user role
  void updateUserRole(UserRole? role) {
    if (isClosed) return; // Prevent emitting states after close
    emit(state.copyWith(selectedRole: role));
    _validateForm();
  }

  /// Toggle user goal
  void toggleUserGoal(UserGoal goal) {
    if (isClosed) return; // Prevent emitting states after close
    final updatedGoals = Set<UserGoal>.from(state.selectedGoals);
    if (updatedGoals.contains(goal)) {
      updatedGoals.remove(goal);
    } else {
      updatedGoals.add(goal);
    }

    emit(state.copyWith(selectedGoals: updatedGoals));
    _validateForm();
  }

  /// Toggle platform selection
  void togglePlatform(PlatformType platform) {
    if (isClosed) return; // Prevent emitting states after close
    final updatedPlatforms = Set<PlatformType>.from(state.selectedPlatforms);
    if (updatedPlatforms.contains(platform)) {
      updatedPlatforms.remove(platform);
    } else {
      updatedPlatforms.add(platform);
    }

    emit(state.copyWith(selectedPlatforms: updatedPlatforms));
  }

  /// Initialize user data from OAuth provider
  Future<void> initializeFromOAuthUser() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null || isClosed) return;

      emit(state.copyWith(isLoading: true));

      // Extract data from OAuth providers
      String? displayName;
      String? photoUrl;

      // Extract from user metadata (Google/Apple)
      final userMetadata = user.userMetadata;
      if (userMetadata != null) {
        displayName =
            userMetadata['full_name'] as String? ??
            userMetadata['name'] as String? ??
            userMetadata['display_name'] as String?;
        photoUrl =
            userMetadata['picture'] as String? ??
            userMetadata['avatar_url'] as String?;
      }

      // Extract from app metadata if available
      final appMetadata = user.appMetadata;
      displayName ??=
          appMetadata['full_name'] as String? ?? appMetadata['name'] as String?;
      photoUrl ??=
          appMetadata['picture'] as String? ??
          appMetadata['avatar_url'] as String?;

      // Update state with extracted name
      if (displayName != null && displayName.isNotEmpty && !isClosed) {
        emit(state.copyWith(username: displayName));
      }

      // Download and set profile image if available
      if (photoUrl != null && photoUrl.isNotEmpty) {
        try {
          final imageFile = await _downloadProfileImage(photoUrl);
          if (imageFile != null && !isClosed) {
            emit(state.copyWith(avatarImage: imageFile));
          }
        } catch (e) {
          // Log error but continue - profile image is not critical
          _logger.w('Failed to download profile image: $e');
        }
      }

      if (!isClosed) {
        emit(state.copyWith(isLoading: false));
      }
    } catch (e) {
      // Log error but don't fail onboarding
      if (!isClosed) {
        emit(state.copyWith(isLoading: false));
      }
    }
  }

  /// Download profile image from URL
  Future<File?> _downloadProfileImage(String imageUrl) async {
    try {
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        final tempDir = await getTemporaryDirectory();
        final fileName =
            'oauth_profile_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final file = File(path.join(tempDir.path, fileName));
        await file.writeAsBytes(response.bodyBytes);
        return file;
      }
    } catch (e) {
      _logger.e('Error downloading profile image: $e');
    }
    return null;
  }

  /// Upload profile image and save onboarding data
  Future<void> saveOnboardingData() async {
    if (isClosed) return; // Prevent emitting states after close
    emit(state.copyWith(isLoading: true));

    try {
      String? profileImageUrl;

      // Upload avatar image if provided (optional - don't fail onboarding if this fails)
      if (state.avatarImage != null) {
        try {
          final uploadResult = await _userRepository.uploadProfileImage(
            state.avatarImage!,
          );
          uploadResult.fold(
            (failure) {
              // Log the error but don't fail onboarding
              _logger.w('Failed to upload profile image: ${failure.message}');
              // Continue without profile image
            },
            (imageUrl) {
              profileImageUrl = imageUrl;
            },
          );
        } catch (e) {
          // Log the error but don't fail onboarding
          _logger.w('Profile image upload failed: $e');
          // Continue without profile image
        }
      }

      // Get current user profile
      final userProfileResult = await _userRepository.getCurrentUserProfile();

      await userProfileResult.fold(
        (failure) async {
          // Handle failure (log error, etc.)
          throw Exception('Failed to get user profile: ${failure.message}');
        },
        (userProfile) async {
          // Convert enum values to strings for storage
          final roleString = state.selectedRole?.name;
          final goalStrings =
              state.selectedGoals.map((goal) => goal.name).toList();

          // Update user profile with onboarding data
          final updatedProfile = userProfile.copyWith(
            username: state.username,
            profileImageUrl: profileImageUrl ?? userProfile.profileImageUrl,
            birthYear: state.birthYear,
            role: roleString,
            goals: goalStrings,
            completedOnboarding: true,
          );

          // Save to database
          final updateResult = await _userRepository.updateUserProfile(
            updatedProfile,
          );

          // Handle update result
          updateResult.fold(
            (failure) {
              throw Exception(
                'Failed to update user profile: ${failure.message}',
              );
            },
            (_) {
              // Success - don't need to do anything here
            },
          );
        },
      );

      if (!isClosed) {
        emit(state.copyWith(isLoading: false, isCompleted: true));
      }
    } catch (e) {
      if (!isClosed) {
        emit(state.copyWith(isLoading: false, errorMessage: e.toString()));
      }
      rethrow; // Re-throw to handle in UI
    }
  }

  /// Private method to validate form
  void _validateForm() {
    if (isClosed) return; // Prevent emitting states after close
    final isValid =
        state.username.isNotEmpty &&
        state.username.length >= 3 &&
        state.selectedRole != null &&
        state.selectedGoals.isNotEmpty;

    emit(state.copyWith(isFormValid: isValid));
  }
}
