part of 'onboarding_cubit.dart';

/// State for onboarding flow
class OnboardingState extends Equatable {
  /// Username of the user
  final String username;

  /// Avatar image file
  final File? avatarImage;

  /// Birth year
  final int birthYear;

  /// Selected user role
  final UserRole? selectedRole;

  /// Selected user goals
  final Set<UserGoal> selectedGoals;

  /// Selected platforms
  final Set<PlatformType> selectedPlatforms;

  /// Whether the form is valid
  final bool isFormValid;

  /// Whether data is loading
  final bool isLoading;

  /// Whether onboarding is completed
  final bool isCompleted;

  /// Error message if any
  final String? errorMessage;

  /// Creates an onboarding state
  const OnboardingState({
    this.username = '',
    this.avatarImage,
    this.birthYear = 1998, // Default to current year - 25
    this.selectedRole,
    this.selectedGoals = const {},
    this.selectedPlatforms = const {},
    this.isFormValid = false,
    this.isLoading = false,
    this.isCompleted = false,
    this.errorMessage,
  });

  /// Creates a copy of this state with the given fields replaced
  OnboardingState copyWith({
    String? username,
    File? avatarImage,
    int? birthYear,
    UserRole? selectedRole,
    Set<UserGoal>? selectedGoals,
    Set<PlatformType>? selectedPlatforms,
    bool? isFormValid,
    bool? isLoading,
    bool? isCompleted,
    String? errorMessage,
  }) {
    return OnboardingState(
      username: username ?? this.username,
      avatarImage: avatarImage ?? this.avatarImage,
      birthYear: birthYear ?? this.birthYear,
      selectedRole: selectedRole ?? this.selectedRole,
      selectedGoals: selectedGoals ?? this.selectedGoals,
      selectedPlatforms: selectedPlatforms ?? this.selectedPlatforms,
      isFormValid: isFormValid ?? this.isFormValid,
      isLoading: isLoading ?? this.isLoading,
      isCompleted: isCompleted ?? this.isCompleted,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    username,
    avatarImage,
    birthYear,
    selectedRole,
    selectedGoals,
    selectedPlatforms,
    isFormValid,
    isLoading,
    isCompleted,
    errorMessage,
  ];
}
