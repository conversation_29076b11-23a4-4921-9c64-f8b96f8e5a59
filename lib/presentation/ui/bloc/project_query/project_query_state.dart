part of 'project_query_cubit.dart';

/// Represents a query and its response in the conversation history
class QueryHistoryItem extends Equatable {
  final String query;
  final String response;
  final DateTime timestamp;

  const QueryHistoryItem({
    required this.query,
    required this.response,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [query, response, timestamp];
}

/// Base state for project query operations
abstract class ProjectQueryState extends Equatable {
  const ProjectQueryState();

  @override
  List<Object?> get props => [];
}

/// Initial state - no queries made yet
class ProjectQueryInitial extends ProjectQueryState {
  const ProjectQueryInitial();

  @override
  List<Object?> get props => [];
}

/// Processing state - query is being processed
class ProjectQueryProcessing extends ProjectQueryState {
  const ProjectQueryProcessing();

  @override
  List<Object?> get props => [];
}

/// Query result state - query processed successfully
class ProjectQueryResult extends ProjectQueryState {
  final String currentResponse;
  final List<QueryHistoryItem> queryHistory;

  const ProjectQueryResult(this.currentResponse, this.queryHistory);

  @override
  List<Object?> get props => [currentResponse, queryHistory];
}

/// Error state - query processing failed
class ProjectQueryError extends ProjectQueryState {
  final String message;

  const ProjectQueryError(this.message);

  @override
  List<Object?> get props => [message];
}
