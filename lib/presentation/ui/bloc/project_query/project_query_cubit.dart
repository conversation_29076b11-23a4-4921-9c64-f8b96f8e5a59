import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import '../../../../domain/entities/project_data.dart';
import '../../../../domain/repositories/analysis_repository.dart';

part 'project_query_state.dart';

/// Cubit for managing project queries and conversations
class ProjectQueryCubit extends Cubit<ProjectQueryState> {
  final AnalysisRepository _analysisRepository;
  final Logger _logger = Logger();

  ProjectQueryCubit({required AnalysisRepository analysisRepository})
    : _analysisRepository = analysisRepository,
      super(const ProjectQueryInitial());

  /// Process a natural language query about the project
  Future<void> processQuery(String query, ProjectData projectContext) async {
    if (query.trim().isEmpty) {
      _logger.w('Empty query provided');
      return;
    }

    emit(const ProjectQueryProcessing());

    try {
      final result = await _analysisRepository.processNaturalLanguageQuery(
        query,
        projectContext,
      );

      result.fold(
        (failure) {
          _logger.e('Failed to process query: ${failure.message}');
          emit(ProjectQueryError(failure.message));
        },
        (response) {
          // Add the query and response to the conversation history
          final newQuery = QueryHistoryItem(
            query: query,
            response: response,
            timestamp: DateTime.now(),
          );

          List<QueryHistoryItem> updatedHistory = [];
          if (state is ProjectQueryResult) {
            updatedHistory = List.from(
              (state as ProjectQueryResult).queryHistory,
            );
          }
          updatedHistory.add(newQuery);

          emit(ProjectQueryResult(response, updatedHistory));
        },
      );
    } catch (e) {
      _logger.e('Error processing query: $e');
      emit(ProjectQueryError('Failed to process query: $e'));
    }
  }

  /// Clear query history
  void clearHistory() {
    emit(const ProjectQueryInitial());
  }

  /// Get a quick insight about the project
  Future<void> getQuickInsight(
    ProjectData projectContext,
    String insightType,
  ) async {
    String query;

    switch (insightType) {
      case 'blockers':
        query = 'What are the main blockers in this project?';
        break;
      case 'progress':
        query = 'What is the current progress status of this project?';
        break;
      case 'timeline':
        query = 'Is this project on track with its timeline?';
        break;
      case 'budget':
        query = 'How is the budget utilization for this project?';
        break;
      case 'risks':
        query = 'What are the potential risks for this project?';
        break;
      case 'next_actions':
        query = 'What are the recommended next actions for this project?';
        break;
      default:
        query = 'Give me a general overview of this project status';
    }

    await processQuery(query, projectContext);
  }

  /// Regenerate the last response
  Future<void> regenerateLastResponse(ProjectData projectContext) async {
    if (state is ProjectQueryResult) {
      final lastQuery = (state as ProjectQueryResult).queryHistory.last;
      await processQuery(lastQuery.query, projectContext);
    }
  }
}
