part of 'recording_cubit.dart';

/// Enum of recording statuses
enum RecordingStatus {
  initial,
  initializing,
  readyToRecord,
  recording,
  paused,
  stopped,
  playing,
  transcribing,
  transcribed,
  idle,
  permissionDenied,
  error
}

/// State for recording screen
class RecordingState extends Equatable {
  final RecordingStatus status;
  final String? filePath;
  final Duration duration;
  final String? error;
  final String? transcription;
  final String? optimizedContent;
  final String? category;
  final String? detectedPlatform;
  final double? platformConfidence;
  final Duration playbackProgress;
  final bool isBatchModeEnabled;
  final TranscriptionResult? transcriptionResult;
  final String? detectedPlatformId;
  final String? detectedProjectName;
  final double intentConfidence;
  
  const RecordingState({
    this.status = RecordingStatus.initial,
    this.filePath,
    this.duration = Duration.zero,
    this.error,
    this.transcription,
    this.optimizedContent,
    this.category,
    this.detectedPlatform,
    this.platformConfidence,
    this.playbackProgress = Duration.zero,
    this.isBatchModeEnabled = false,
    this.transcriptionResult,
    this.detectedPlatformId,
    this.detectedProjectName,
    this.intentConfidence = 0.0,
  });

  RecordingState copyWith({
    RecordingStatus? status,
    String? filePath,
    Duration? duration,
    String? error,
    String? transcription,
    String? optimizedContent,
    String? category,
    String? detectedPlatform,
    double? platformConfidence,
    Duration? playbackProgress,
    bool? isBatchModeEnabled,
    TranscriptionResult? transcriptionResult,
    String? detectedPlatformId,
    String? detectedProjectName,
    double? intentConfidence,
  }) {
    return RecordingState(
      status: status ?? this.status,
      filePath: filePath ?? this.filePath,
      duration: duration ?? this.duration,
      error: error ?? this.error,
      transcription: transcription ?? this.transcription,
      optimizedContent: optimizedContent ?? this.optimizedContent,
      category: category ?? this.category,
      detectedPlatform: detectedPlatform ?? this.detectedPlatform,
      platformConfidence: platformConfidence ?? this.platformConfidence,
      playbackProgress: playbackProgress ?? this.playbackProgress,
      isBatchModeEnabled: isBatchModeEnabled ?? this.isBatchModeEnabled,
      transcriptionResult: transcriptionResult ?? this.transcriptionResult,
      detectedPlatformId: detectedPlatformId ?? this.detectedPlatformId,
      detectedProjectName: detectedProjectName ?? this.detectedProjectName,
      intentConfidence: intentConfidence ?? this.intentConfidence,
    );
  }

  @override
  List<Object?> get props => [
    status,
    filePath,
    duration,
    error,
    transcription,
    optimizedContent,
    category,
    detectedPlatform,
    platformConfidence,
    playbackProgress,
    isBatchModeEnabled,
    transcriptionResult,
    detectedPlatformId,
    detectedProjectName,
    intentConfidence,
  ];
}
