import 'dart:async';
import 'dart:io';
import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';
import 'package:projectpilot/core/constants/app_constants.dart';
import 'package:projectpilot/core/services/cache/transcription_cache_service.dart';
import 'package:projectpilot/core/services/intent/intent_parser_service.dart';
import 'package:projectpilot/core/services/voice/audio_level_service.dart';
import 'package:projectpilot/core/services/voice/audio_service.dart';
import 'package:projectpilot/core/services/voice/speech_recognition_service_factory.dart';
import 'package:projectpilot/core/utils/error_handler.dart';
import 'package:projectpilot/data/repositories/storage_repository.dart';
import 'package:projectpilot/domain/entities/transcription_result.dart';
import 'package:projectpilot/presentation/ui/bloc/language/language_cubit.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:projectpilot/core/services/token/token_service.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:projectpilot/core/constants/api_constants.dart';

part 'recording_state.dart';

// Global navigator key for accessing context from outside of UI widgets
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class RecordingCubit extends Cubit<RecordingState> {
  final AudioService _audioService;
  final StorageRepository _storageRepository;
  final Logger _logger = Logger();
  final IntentParserService _intentParserService = IntentParserService();
  final LanguageCubit? _languageCubit;
  final _uuid = const Uuid();
  final TokenService _tokenService = TokenService();

  StreamSubscription? _audioServiceStatusSubscription;
  StreamSubscription? _recordingDurationSubscription;
  StreamSubscription? _playbackProgressSubscription;

  RecordingCubit({
    required AudioService audioService,
    required StorageRepository storageRepository,
    LanguageCubit? languageCubit,
  }) : _audioService = audioService,
       _storageRepository = storageRepository,
       _languageCubit = languageCubit,
       super(const RecordingState()) {
    // Initialize recorder asynchronously but don't block constructor
    // Status will be updated via state events
    initRecorder();
  }

  Future<void> initRecorder() async {
    if (state.status == RecordingStatus.initializing) {
      // Already initializing, don't start again
      return;
    }

    emit(state.copyWith(status: RecordingStatus.initializing));

    try {
      _logger.i('Initializing AudioService...');
      // Make sure initialization is complete before proceeding
      await _audioService.init();
      _listenToAudioServiceStreams();
      emit(state.copyWith(status: RecordingStatus.readyToRecord));
      _logger.i('AudioService initialized, ready to record.');
    } catch (e) {
      _logger.e('Error initializing AudioService: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to initialize recorder: ${e.toString()}',
        ),
      );
    }
  }

  void _listenToAudioServiceStreams() {
    _audioServiceStatusSubscription?.cancel();
    _audioServiceStatusSubscription = _audioService.recordingStatusStream.listen((
      statusUpdate,
    ) {
      if (statusUpdate.isRecording) {
        // Status is mainly handled by start/stopRecording
      } else if (statusUpdate.isFinished) {
        // Handled by stopRecording
      } else if (statusUpdate.isPlaying) {
        // Status is mainly handled by play/stopPlayback
      } else if (statusUpdate.isPlaybackFinished) {
        emit(
          state.copyWith(
            status: RecordingStatus.stopped,
            playbackProgress: Duration.zero,
          ),
        );
      }
      _logger.d(
        'AudioService Status Update: isRecording: ${statusUpdate.isRecording}, isPlaying: ${statusUpdate.isPlaying}, duration: ${statusUpdate.duration}, progress: ${statusUpdate.playbackPosition}',
      );
    });

    _recordingDurationSubscription?.cancel();
    _recordingDurationSubscription = _audioService.recordingDurationStream
        .listen((duration) {
          if (state.status == RecordingStatus.recording) {
            emit(state.copyWith(duration: duration));
          }
        });

    _playbackProgressSubscription?.cancel();
    _playbackProgressSubscription = _audioService.playbackProgressStream.listen(
      (progress) {
        if (state.status == RecordingStatus.playing) {
          emit(state.copyWith(playbackProgress: progress));
        }
      },
    );
  }

  Future<void> startRecording() async {
    // Ensure recorder is initialized before recording
    if (state.status == RecordingStatus.initializing) {
      _logger.w('Recorder is still initializing, please wait...');
      return;
    }

    // If we're paused, resume recording instead
    if (state.status == RecordingStatus.paused) {
      return resumeRecording();
    }

    if (state.status != RecordingStatus.readyToRecord &&
        state.status != RecordingStatus.stopped &&
        state.status != RecordingStatus.error) {
      _logger.w('Cannot start recording in status: ${state.status}');
      return;
    }

    try {
      // Make sure recorder is initialized
      if (state.status == RecordingStatus.error) {
        await initRecorder();
        // If initialization failed, don't proceed
        if (state.status == RecordingStatus.error) {
          return;
        }
      }

      await _audioService.startRecording();
      emit(
        state.copyWith(
          status: RecordingStatus.recording,
          filePath: null,
          duration: Duration.zero,
          error: null,
        ),
      );
      _logger.i('Recording started via Cubit.');
    } catch (e) {
      _logger.e('Error starting recording: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to start recording: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> pauseRecording() async {
    if (state.status != RecordingStatus.recording) {
      _logger.w(
        'Cannot pause recording if not recording. Current status: ${state.status}',
      );
      return;
    }

    try {
      await _audioService.pauseRecording();
      emit(state.copyWith(status: RecordingStatus.paused));
      _logger.i('Recording paused.');
    } catch (e) {
      _logger.e('Error pausing recording: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to pause recording: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> resumeRecording() async {
    if (state.status != RecordingStatus.paused) {
      _logger.w(
        'Cannot resume recording if not paused. Current status: ${state.status}',
      );
      return;
    }

    try {
      await _audioService.resumeRecording();
      emit(state.copyWith(status: RecordingStatus.recording));
      _logger.i('Recording resumed.');
    } catch (e) {
      _logger.e('Error resuming recording: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to resume recording: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> stopRecording() async {
    if (state.status != RecordingStatus.recording &&
        state.status != RecordingStatus.paused) {
      _logger.w(
        'Cannot stop recording if not recording or paused. Current status: ${state.status}',
      );
      return;
    }

    try {
      final filePath = await _audioService.stopRecording();
      
      emit(state.copyWith(status: RecordingStatus.stopped, filePath: filePath));
      
      _logger.i('Recording stopped. File saved to: $filePath');

      // Auto-transcribe the recording
      if (filePath != null) {
        await _transcribeRecording(filePath);
      }
    } catch (e) {
      _logger.e('Error stopping recording: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to stop recording: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _transcribeRecording(String filePath) async {
    try {
      emit(state.copyWith(status: RecordingStatus.transcribing));
      
      // For now, create a simple transcription result as a placeholder
      // TODO: Implement actual transcription service integration
      final transcriptionResult = TranscriptionResult(
        originalTranscription: 'Voice recording transcription placeholder',
        optimizedContent: 'Voice recording transcription placeholder',
        category: ContentCategory.note,
        confidence: 0.95,
      );
      
      emit(
        state.copyWith(
          status: RecordingStatus.readyToRecord,
          transcriptionResult: transcriptionResult,
        ),
      );
      
      _logger.i('Transcription placeholder created for file: $filePath');
    } catch (e) {
      _logger.e('Error creating transcription placeholder: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to process recording: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> playRecording() async {
    if (state.filePath == null) {
      _logger.w('No recording to play');
      return;
    }

    if (state.status == RecordingStatus.playing) {
      _logger.w('Already playing');
      return;
    }

    try {
      await _audioService.playRecording(state.filePath!);
      emit(
        state.copyWith(
          status: RecordingStatus.playing,
          playbackProgress: Duration.zero,
        ),
      );
      _logger.i('Playback started.');
    } catch (e) {
      _logger.e('Error playing recording: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to play recording: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> stopPlayback() async {
    if (state.status != RecordingStatus.playing) {
      _logger.w('Not currently playing');
      return;
    }

    try {
      await _audioService.stopPlayback();
      emit(
        state.copyWith(
          status: RecordingStatus.stopped,
          playbackProgress: Duration.zero,
        ),
      );
      _logger.i('Playback stopped.');
    } catch (e) {
      _logger.e('Error stopping playback: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to stop playback: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> deleteRecording() async {
    final filePath = state.filePath;
    if (filePath == null) {
      _logger.w('No recording to delete');
      return;
    }

    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        _logger.i('Recording deleted: $filePath');
      }

      emit(
        state.copyWith(
          status: RecordingStatus.readyToRecord,
          filePath: null,
          duration: Duration.zero,
          playbackProgress: Duration.zero,
          transcriptionResult: null,
        ),
      );
    } catch (e) {
      _logger.e('Error deleting recording: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to delete recording: ${e.toString()}',
        ),
      );
    }
  }

  void clearError() {
    if (state.status == RecordingStatus.error) {
      emit(state.copyWith(status: RecordingStatus.readyToRecord, error: null));
    }
  }

  /// Reset the recording state to initial state
  Future<void> resetRecording() async {
    try {
      // Stop any ongoing playback
      if (state.status == RecordingStatus.playing) {
        await stopPlayback();
      }

      // Delete the current recording file if it exists
      await deleteRecording();

      // Reset to ready state
      emit(
        state.copyWith(
          status: RecordingStatus.readyToRecord,
          filePath: null,
          duration: Duration.zero,
          playbackProgress: Duration.zero,
          transcriptionResult: null,
          error: null,
        ),
      );
      _logger.i('Recording state reset to initial');
    } catch (e) {
      _logger.e('Error resetting recording: $e');
      emit(
        state.copyWith(
          status: RecordingStatus.error,
          error: 'Failed to reset recording: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<void> close() {
    _audioServiceStatusSubscription?.cancel();
    _recordingDurationSubscription?.cancel();
    _playbackProgressSubscription?.cancel();
    return super.close();
  }
}
