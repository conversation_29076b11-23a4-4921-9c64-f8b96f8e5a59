import 'package:equatable/equatable.dart';
import 'package:projectpilot/domain/entities/api_key.dart';

/// Represents the state of API keys
class ApiKeyState extends Equatable {
  /// The OpenAI API key
  final String openaiApiKey;

  /// The Whisper API key
  final String whisperApiKey;

  /// Loading status
  final bool isLoading;

  /// Error message, if any
  final String? error;

  /// Creates a new [ApiKeyState] instance
  const ApiKeyState({
    this.openaiApiKey = '',
    this.whisperApiKey = '',
    this.isLoading = false,
    this.error,
  });

  /// Initial state
  factory ApiKeyState.initial() => const ApiKeyState();

  /// Creates a copy of this state with the given fields replaced
  ApiKeyState copyWith({
    String? openaiApiKey,
    String? whisperApiKey,
    bool? isLoading,
    String? error,
  }) {
    return ApiKeyState(
      openaiApiKey: openaiApiKey ?? this.openaiApiKey,
      whisperApiKey: whisperApiKey ?? this.whisperApiKey,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
  
  @override
  List<Object?> get props => [openaiApiKey, whisperApiKey, isLoading, error];
}

/// Initial state when API keys have not been loaded
class ApiKeyInitial extends ApiKeyState {
  const ApiKeyInitial();
}

/// State when API keys are loading
class ApiKeyLoading extends ApiKeyState {
  const ApiKeyLoading();
}

/// State when API keys have been loaded successfully
class ApiKeyLoaded extends ApiKeyState {
  final Map<String, ApiKey?> apiKeys;

  const ApiKeyLoaded(this.apiKeys);

  @override
  List<Object?> get props => [apiKeys];
}

/// State when saving an API key
class ApiKeySaving extends ApiKeyState {
  final String keyType;

  const ApiKeySaving(this.keyType);

  @override
  List<Object?> get props => [keyType];
}

/// State when an API key has been saved successfully
class ApiKeySaved extends ApiKeyState {
  final ApiKey apiKey;

  const ApiKeySaved(this.apiKey);

  @override
  List<Object?> get props => [apiKey];
}

/// State when deleting an API key
class ApiKeyDeleting extends ApiKeyState {
  final String keyType;

  const ApiKeyDeleting(this.keyType);

  @override
  List<Object?> get props => [keyType];
}

/// State when an API key has been deleted successfully
class ApiKeyDeleted extends ApiKeyState {
  final String keyType;

  const ApiKeyDeleted(this.keyType);

  @override
  List<Object?> get props => [keyType];
}

/// State when there was an error with API key operations
class ApiKeyError extends ApiKeyState {
  final String message;

  const ApiKeyError(this.message);

  @override
  List<Object?> get props => [message];
}
