import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/domain/entities/api_key.dart';
import 'package:projectpilot/domain/repositories/api_key_repository.dart';
import 'package:projectpilot/presentation/ui/bloc/api_key/api_key_state.dart';

/// Cubit for managing API keys in the application
class ApiKeyCubit extends Cubit<ApiKeyState> {
  final ApiKeyRepository _apiKeyRepository;
  final List<String> _keyTypes = ['OpenAI', 'Stripe'];

  /// Constructor for ApiKeyCubit
  ApiKeyCubit(this._apiKeyRepository) : super(const ApiKeyInitial());

  /// Load all API keys
  Future<void> loadApiKeys() async {
    emit(const ApiKeyLoading());

    try {
      final Map<String, ApiKey?> apiKeys = {};

      // Load each key type
      for (final keyType in _keyTypes) {
        final result = await _apiKeyRepository.getApiKey(keyType);

        result.fold(
          (failure) {
            // If we fail to load one key, continue with others
            apiKeys[keyType] = null;
          },
          (apiKey) {
            apiKeys[keyType] = apiKey;
          },
        );
      }

      emit(ApiKeyLoaded(apiKeys));
    } catch (e) {
      emit(ApiKeyError('Failed to load API keys: ${e.toString()}'));
    }
  }

  /// Save an API key
  Future<void> saveApiKey(ApiKey apiKey) async {
    emit(ApiKeySaving(apiKey.keyType));

    final result = await _apiKeyRepository.saveApiKey(apiKey);

    result.fold((failure) => emit(ApiKeyError(failure.message)), (success) {
      if (success) {
        emit(ApiKeySaved(apiKey));
        loadApiKeys(); // Reload all keys
      } else {
        emit(ApiKeyError('Failed to save API key'));
      }
    });
  }

  /// Delete an API key
  Future<void> deleteApiKey(String keyType) async {
    emit(ApiKeyDeleting(keyType));

    final result = await _apiKeyRepository.deleteApiKey(keyType);

    result.fold((failure) => emit(ApiKeyError(failure.message)), (success) {
      if (success) {
        emit(ApiKeyDeleted(keyType));
        loadApiKeys(); // Reload all keys
      } else {
        emit(ApiKeyError('Failed to delete API key'));
      }
    });
  }

  /// Check if an API key exists
  Future<bool> hasApiKey(String keyType) async {
    final result = await _apiKeyRepository.hasApiKey(keyType);

    return result.fold((failure) => false, (hasKey) => hasKey);
  }
}
