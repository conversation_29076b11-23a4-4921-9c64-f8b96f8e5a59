import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:projectpilot/presentation/ui/bloc/language/language_state.dart';

/// Cubit for managing application language
class LanguageCubit extends Cubit<LanguageState> {
  /// Key for storing language preference
  static const String _languagePreferenceKey = 'language_code';
  
  /// Default language code
  static const String _defaultLanguageCode = 'en';
  
  /// Available language codes
  static const List<String> supportedLanguageCodes = ['en', 'de', 'ru', 'tr', 'ar'];

  /// Constructor for LanguageCubit
  LanguageCubit() : super(const LanguageState(languageCode: _defaultLanguageCode)) {
    _loadSavedLanguage();
  }

  /// Load the saved language preference
  Future<void> _loadSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLanguage = prefs.getString(_languagePreferenceKey);
    
    if (savedLanguage != null && supportedLanguageCodes.contains(savedLanguage)) {
      emit(LanguageState(languageCode: savedLanguage));
    }
  }

  /// Change the application language
  Future<void> changeLanguage(String languageCode) async {
    if (!supportedLanguageCodes.contains(languageCode)) {
      return;
    }
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languagePreferenceKey, languageCode);
    
    emit(LanguageState(languageCode: languageCode));
  }
}
