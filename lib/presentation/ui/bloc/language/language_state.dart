import 'package:equatable/equatable.dart';

/// Represents the state of the language selection
class LanguageState extends Equatable {
  /// The current locale code (e.g., 'en', 'de', 'ru', etc.)
  final String languageCode;

  /// Creates a new [LanguageState] instance
  const LanguageState({required this.languageCode});

  /// Creates a copy of this state with the given fields replaced
  LanguageState copyWith({String? languageCode}) {
    return LanguageState(
      languageCode: languageCode ?? this.languageCode,
    );
  }

  @override
  List<Object> get props => [languageCode];
}
