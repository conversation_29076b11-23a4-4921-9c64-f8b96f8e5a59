import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:projectpilot/presentation/ui/bloc/theme/theme_state.dart';

/// Cubit for managing application theme
class ThemeCubit extends Cubit<ThemeState> {
  /// Key for storing theme preference
  static const String _themePreferenceKey = 'theme_mode';

  /// Default theme mode
  static const ThemeMode _defaultThemeMode = ThemeMode.system;

  /// Constructor for ThemeCubit
  ThemeCubit() : super(const ThemeState(themeMode: _defaultThemeMode)) {
    _loadSavedTheme();
  }

  /// Load the saved theme preference
  Future<void> _loadSavedTheme() async {
    final prefs = await SharedPreferences.getInstance();
    final savedThemeIndex = prefs.getInt(_themePreferenceKey);

    if (savedThemeIndex != null) {
      final themeMode = ThemeMode.values[savedThemeIndex];
      if (!isClosed) {
        emit(ThemeState(themeMode: themeMode));
      }
    }
  }

  /// Change the application theme
  Future<void> changeTheme(ThemeMode themeMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_themePreferenceKey, themeMode.index);

    if (!isClosed) {
      emit(ThemeState(themeMode: themeMode));
    }
  }

  /// Toggle between light and dark themes
  Future<void> toggleTheme() async {
    final newThemeMode =
        state.themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;

    await changeTheme(newThemeMode);
  }
}
