import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Represents the state of the theme settings
class ThemeState extends Equatable {
  /// Current theme mode
  final ThemeMode themeMode;

  /// Creates a new [ThemeState] instance
  const ThemeState({required this.themeMode});

  /// Creates a copy of this state with the given fields replaced
  ThemeState copyWith({ThemeMode? themeMode}) {
    return ThemeState(themeMode: themeMode ?? this.themeMode);
  }

  @override
  List<Object> get props => [themeMode];
}
