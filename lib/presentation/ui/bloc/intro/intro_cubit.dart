import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'intro_state.dart';

/// Cubit for managing intro screen state
class IntroCubit extends Cubit<IntroState> {
  /// Preference key to store intro completion status
  static const String _prefsKeyIntroCompleted = 'intro_completed';

  /// Preference key to track reinstall detection
  static const String _prefsKeyInstallCheck = 'install_check';

  /// Shared preferences instance for persistent storage
  final SharedPreferences _preferences;

  /// Creates an intro cubit with the given preferences
  IntroCubit({required SharedPreferences preferences})
    : _preferences = preferences,
      super(const IntroState()) {
    // Check for reinstall when cubit is created
    _checkFirstRunAfterReinstall();
  }

  /// Check if this is the first run after reinstall and reset intro if needed
  Future<void> _checkFirstRunAfterReinstall() async {
    try {
      // The marker will be missing if app was reinstalled or first install
      final hasMarker = _preferences.getBool(_prefsKeyInstallCheck) ?? false;
      final introCompleted =
          _preferences.getBool(_prefsKeyIntroCompleted) ?? false;

      debugPrint(
        'IntroCubit: _checkFirstRunAfterReinstall - hasMarker: $hasMarker, introCompleted: $introCompleted',
      );

      if (!hasMarker) {
        // This is a first install or reinstall
        debugPrint('IntroCubit: First install or reinstall detected');
        // Set marker for future checks FIRST
        await _preferences.setBool(_prefsKeyInstallCheck, true);
        debugPrint('IntroCubit: Install check marker set');

        // Only reset intro if it hasn't been completed yet
        if (!introCompleted) {
          debugPrint('IntroCubit: Intro not completed, resetting intro');
          await resetIntroCompleted();
        } else {
          debugPrint(
            'IntroCubit: Intro already completed, keeping completed state',
          );
        }
      } else {
        debugPrint('IntroCubit: Install check marker exists, no action needed');
      }
    } catch (e) {
      debugPrint('IntroCubit: Error checking install status: $e');
      // Play it safe - but don't reset if intro was already completed
      final introCompleted =
          _preferences.getBool(_prefsKeyIntroCompleted) ?? false;
      if (!introCompleted) {
        debugPrint(
          'IntroCubit: Error occurred and intro not completed, resetting',
        );
        await resetIntroCompleted();
      }
    }
  }

  /// Navigate to the next page
  void nextPage() {
    debugPrint(
      'IntroCubit: nextPage called. Current page: ${state.currentPage}, isLastPage: ${state.isLastPage}',
    );

    if (state.isLastPage) {
      debugPrint('IntroCubit: On last page, completing intro');
      _completeIntro();
    } else {
      final nextPage = state.currentPage + 1;
      debugPrint('IntroCubit: Moving to page $nextPage');
      emit(state.copyWith(currentPage: nextPage));
    }
  }

  /// Navigate to the previous page
  void previousPage() {
    if (state.currentPage > 0) {
      emit(state.copyWith(currentPage: state.currentPage - 1));
    }
  }

  /// Navigate to a specific page by index
  void goToPage(int page) {
    debugPrint('IntroCubit: goToPage called with page: $page');
    if (page >= 0 && page < state.totalPages) {
      emit(state.copyWith(currentPage: page));
    }
  }

  /// Skip the intro and mark as completed
  void skipIntro() {
    debugPrint('IntroCubit: skipIntro called');
    _completeIntro();
  }

  /// Mark the intro as completed and save to preferences
  void _completeIntro() {
    debugPrint('IntroCubit: _completeIntro called');
    emit(state.copyWith(isCompleted: true, isCompletedThisSession: true));
    _preferences.setBool(_prefsKeyIntroCompleted, true);
  }

  /// Check if intro has been completed previously
  Future<bool> checkIntroCompleted() async {
    final completed = _preferences.getBool(_prefsKeyIntroCompleted) ?? false;
    final hasMarker = _preferences.getBool(_prefsKeyInstallCheck) ?? false;
    debugPrint('IntroCubit: checkIntroCompleted returning: $completed');
    debugPrint('IntroCubit: install_check marker exists: $hasMarker');

    // Don't automatically set isCompleted to true here - let the user go through intro
    // Only mark as completed when they actually complete it in this session
    return completed;
  }

  /// Force show intro (useful for testing or if user wants to see it again)
  void forceShowIntro() {
    debugPrint('IntroCubit: forceShowIntro called - resetting to first page');
    emit(const IntroState(currentPage: 0, isCompleted: false));
  }

  /// Reset intro completed status (for testing or user preference reset)
  Future<void> resetIntroCompleted() async {
    debugPrint('IntroCubit: resetIntroCompleted called');
    await _preferences.setBool(_prefsKeyIntroCompleted, false);
    emit(const IntroState());
  }
}
