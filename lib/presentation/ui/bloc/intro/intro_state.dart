/// Represents the state of the intro screen
class IntroState {
  /// Current page index
  final int currentPage;

  /// Total number of intro pages
  final int totalPages;

  /// Whether the intro is completed
  final bool isCompleted;

  /// Whether the intro was completed in this session (not previously completed)
  final bool isCompletedThisSession;

  /// Creates an intro state with the given parameters
  const IntroState({
    this.currentPage = 0,
    this.totalPages = 3,
    this.isCompleted = false,
    this.isCompletedThisSession = false,
  });

  /// Creates a copy of this state with the given fields replaced
  IntroState copyWith({
    int? currentPage,
    int? totalPages,
    bool? isCompleted,
    bool? isCompletedThisSession,
  }) {
    return IntroState(
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      isCompleted: isCompleted ?? this.isCompleted,
      isCompletedThisSession:
          isCompletedThisSession ?? this.isCompletedThisSession,
    );
  }

  /// Whether the current page is the last page
  bool get isLastPage => currentPage == totalPages - 1;
}
