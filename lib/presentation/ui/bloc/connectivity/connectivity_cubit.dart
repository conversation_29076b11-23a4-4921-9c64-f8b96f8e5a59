import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/core/services/network/connectivity_service.dart';
import 'package:projectpilot/presentation/ui/bloc/connectivity/connectivity_state.dart';

/// Cubit to manage connectivity state
class ConnectivityCubit extends Cubit<ConnectivityState> {
  /// Connectivity service
  final ConnectivityService _connectivityService;
  
  /// Subscription to connectivity status changes
  late StreamSubscription<ConnectivityStatus> _connectivitySubscription;
  
  /// Constructor
  ConnectivityCubit({
    required ConnectivityService connectivityService,
  }) : 
    _connectivityService = connectivityService,
    super(
      ConnectivityState(
        status: connectivityService.currentStatus == ConnectivityStatus.online
            ? ConnectivityStatus.online
            : ConnectivityStatus.offline,
      ),
    ) {
    // Initialize
    _init();
  }
  
  /// Initialize the cubit
  void _init() {
    // Listen for connectivity changes
    _connectivitySubscription = _connectivityService.statusStream.listen(
      (ConnectivityStatus status) {
        emit(ConnectivityState(status: status));
      },
    );
  }
  
  /// Check connectivity manually
  Future<void> checkConnectivity() async {
    final status = await _connectivityService.checkConnectivity();
    emit(ConnectivityState(status: status));
  }
  
  @override
  Future<void> close() {
    _connectivitySubscription.cancel();
    return super.close();
  }
}
