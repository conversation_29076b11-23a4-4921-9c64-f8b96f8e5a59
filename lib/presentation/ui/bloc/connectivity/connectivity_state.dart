import 'package:equatable/equatable.dart';
import 'package:projectpilot/core/services/network/connectivity_service.dart';

/// State for connectivity cubit
class ConnectivityState extends Equatable {
  /// Current connectivity status
  final ConnectivityStatus status;
  
  /// Whether the device is online
  bool get isOnline => status == ConnectivityStatus.online;
  
  /// Whether the device is offline
  bool get isOffline => status == ConnectivityStatus.offline;
  
  /// Constructor
  const ConnectivityState({
    required this.status,
  });
  
  @override
  List<Object?> get props => [status];
}
