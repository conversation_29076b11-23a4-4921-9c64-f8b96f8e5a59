import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'package:projectpilot/core/services/auth/social_auth_service.dart';
import 'package:projectpilot/domain/repositories/user_repository.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';
import 'auth_state.dart' as app_auth;

/// Cubit for handling authentication operations
class AuthCubit extends Cubit<app_auth.AuthState> {
  /// Supabase client for authentication
  final SupabaseClient _supabaseClient;
  final UserRepository _userRepository;
  final SocialAuthService _socialAuthService;
  final Logger _logger = Logger();

  /// Creates an authentication cubit
  AuthCubit({
    SupabaseClient? supabaseClient,
    required UserRepository userRepository,
    SocialAuthService? socialAuthService,
  }) : _supabaseClient = supabaseClient ?? Supabase.instance.client,
       _userRepository = userRepository,
       _socialAuthService =
           socialAuthService ??
           SocialAuthService(supabaseClient: supabaseClient),
       super(app_auth.AuthState.initial()) {
    // Check if user is already authenticated
    _checkCurrentSession();

    // Listen to authentication state changes
    _listenToAuthChanges();
  }

  /// Checks if there's an active session
  Future<void> _checkCurrentSession() async {
    try {
      final session = _supabaseClient.auth.currentSession;
      if (session != null) {
        emit(
          app_auth.AuthState.authenticated(_supabaseClient.auth.currentUser!),
        );
      }
    } catch (e) {
      _logger.e('Error checking current session: $e');
      // If there's an error, we remain in the initial state
    }
  }

  /// Listen to authentication state changes from Supabase
  void _listenToAuthChanges() {
    _supabaseClient.auth.onAuthStateChange.listen((data) {
      final session = data.session;
      final user = data.session?.user;

      _logger.i('Auth state change detected: ${data.event}');

      if (session != null && user != null) {
        // User is authenticated
        _logger.i('Auth state changed: User authenticated - ${user.email}');
        _ensureUserProfile(user).then((_) {
          emit(app_auth.AuthState.authenticated(user));
        });
      } else if (data.event == AuthChangeEvent.signedOut) {
        // User is not authenticated
        _logger.i('Auth state changed: User signed out');
        emit(app_auth.AuthState.initial());
      }
      // Note: We don't change state for other events like TOKEN_REFRESHED
    });
  }

  /// Sign in with email and password
  Future<void> signInWithEmail(String email, String password) async {
    emit(app_auth.AuthState.loading());
    try {
      final response = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // After successful sign-in, fetch user profile from the user table
        await _ensureUserProfile(response.user!);
        emit(
          app_auth.AuthState.authenticated(response.user!, provider: 'email'),
        );
      } else {
        emit(app_auth.AuthState.error('Email sign in failed'));
      }
    } catch (e) {
      _logger.e('Error signing in with email: $e');
      emit(app_auth.AuthState.error(e.toString()));
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmail(String email, String password) async {
    emit(app_auth.AuthState.loading());
    try {
      final response = await _supabaseClient.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Create a user profile in the user table
        await _createUserProfile(response.user!);
        emit(
          app_auth.AuthState.authenticated(response.user!, provider: 'email'),
        );
      } else {
        emit(app_auth.AuthState.error('Email sign up failed'));
      }
    } catch (e) {
      _logger.e('Error signing up with email: $e');
      emit(app_auth.AuthState.error(e.toString()));
    }
  }

  /// Sign in with OAuth provider
  Future<void> signInWithOAuth(String provider) async {
    emit(app_auth.AuthState.loading());
    try {
      // Use SocialAuthService for Google and Apple, fallback to Supabase OAuth for others
      if (provider.toLowerCase() == 'google') {
        await _signInWithGoogle();
      } else if (provider.toLowerCase() == 'apple') {
        await _signInWithApple();
      } else {
        // Use provider parameter to determine which OAuth provider to use
        final providerType = _mapProviderToEnum(provider);

        if (providerType != null) {
          // Start the OAuth flow
          await _supabaseClient.auth.signInWithOAuth(
            providerType,
            redirectTo: 'com.innovatio.projectpilot://login-callback',
          );

          // Note: The OAuth flow is handled by the native platform
          // The session will be updated automatically via auth state changes
          // We don't emit state here as it will be handled by auth state listeners
        } else {
          emit(app_auth.AuthState.error('Unsupported provider: $provider'));
        }
      }
    } catch (e) {
      _logger.e('Error signing in with OAuth: $e');
      emit(app_auth.AuthState.error(e.toString()));
    }
  }

  /// Sign in with Google using SocialAuthService
  Future<void> _signInWithGoogle() async {
    try {
      // Use the social auth service which handles native Google Sign-In
      final user = await _socialAuthService.signInWithGoogle();
      
      if (user != null) {
        // User signed in successfully
        await _ensureUserProfile(user);
        emit(app_auth.AuthState.authenticated(user, provider: 'google'));
        _logger.i('Successfully signed in with Google');
      } else {
        // User canceled the sign-in or it failed
        _logger.i('Google Sign-In was canceled or failed');
        emit(app_auth.AuthState.initial());
      }
      
    } catch (e) {
      _logger.e('Error signing in with Google: $e');
      
      // Provide user-friendly error messages
      String errorMessage = e.toString();
      if (errorMessage.contains('Google Sign-In Konfigurationsfehler') ||
          errorMessage.contains('Client ID Konflikt')) {
        // Pass through detailed configuration error messages
        emit(app_auth.AuthState.error(errorMessage));
      } else if (errorMessage.contains('Google Sign-In Plattformfehler') ||
          errorMessage.contains('URL kann nicht geöffnet werden')) {
        // Pass through platform error messages
        emit(app_auth.AuthState.error(errorMessage));
      } else if (errorMessage.contains('wurde abgebrochen') ||
          errorMessage.contains('canceled')) {
        // Handle cancellation gracefully
        _logger.i('Google Sign-In was canceled by user');
        emit(app_auth.AuthState.initial());
        return;
      } else if (errorMessage.contains('timed out')) {
        errorMessage = 'Google-Anmeldung hat zu lange gedauert.\n\nBitte versuche es erneut oder überprüfe deine Internetverbindung.';
      } else if (errorMessage.contains('Network error') ||
          errorMessage.contains('network')) {
        errorMessage = 'Netzwerkfehler bei der Google-Anmeldung.\n\nBitte überprüfe deine Internetverbindung und versuche es erneut.';
      } else if (errorMessage.contains('platform error')) {
        errorMessage = 'Google-Anmeldung ist momentan nicht verfügbar.\n\nBitte versuche es später erneut oder verwende eine andere Anmeldemethode.';
      } else {
        // Generic error message for other cases
        errorMessage =
            'Google-Anmeldung fehlgeschlagen.\n\nBitte versuche es erneut oder verwende eine andere Anmeldemethode.';
      }
      
      emit(app_auth.AuthState.error(errorMessage));
    }
  }

  /// Sign in with Apple using SocialAuthService
  Future<void> _signInWithApple() async {
    try {
      final user = await _socialAuthService.signInWithApple();
      if (user != null) {
        await _ensureUserProfile(user);
        emit(app_auth.AuthState.authenticated(user, provider: 'apple'));
      } else {
        // The auth flow will be handled by the platform callback
        // Don't update state yet since we'll catch the auth state change
      }
    } catch (e) {
      _logger.e('Error signing in with Apple: $e');
      
      // Handle user cancellation gracefully
      String errorMessage = e.toString();
      if (errorMessage.contains('AuthorizationErrorCode.canceled') ||
          errorMessage.contains('error 1001') ||
          errorMessage.contains('canceled') ||
          errorMessage.contains('cancelled') ||
          errorMessage.contains('SignInWithAppleAuthorizationException')) {
        _logger.i('Apple Sign-In was canceled by user');
        emit(app_auth.AuthState.initial());
        return;
      }

      // Provide user-friendly error messages for other errors
      if (errorMessage.contains('not available')) {
        errorMessage =
            'Apple Sign-In ist auf diesem Gerät nicht verfügbar.\n\nBitte verwende eine andere Anmeldemethode.';
      } else if (errorMessage.contains('network')) {
        errorMessage =
            'Netzwerkfehler bei der Apple-Anmeldung.\n\nBitte überprüfe deine Internetverbindung und versuche es erneut.';
      }

      emit(app_auth.AuthState.error(errorMessage));
    }
  }

  /// Sign in with phone number
  Future<void> signInWithPhone(String phone, String password) async {
    emit(app_auth.AuthState.loading());
    try {
      final response = await _supabaseClient.auth.signInWithPassword(
        phone: phone,
        password: password,
      );

      if (response.user != null) {
        // After successful sign-in, fetch user profile from the user table
        await _ensureUserProfile(response.user!);
        emit(
          app_auth.AuthState.authenticated(response.user!, provider: 'phone'),
        );
      } else {
        emit(app_auth.AuthState.error('Phone sign in failed'));
      }
    } catch (e) {
      _logger.e('Error signing in with phone: $e');
      emit(app_auth.AuthState.error(e.toString()));
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _supabaseClient.auth.signOut();
      emit(app_auth.AuthState.initial());
    } catch (e) {
      _logger.e('Error signing out: $e');
      emit(app_auth.AuthState.error(e.toString()));
    }
  }

  /// Verify the authenticated user's database access
  /// Returns true if access is verified, false otherwise
  Future<bool> verifyDatabaseAccess() async {
    try {
      if (_supabaseClient.auth.currentSession == null) {
        _logger.w('Cannot verify database access: No active session');
        return false;
      }

      // Try to read from a protected table
      try {
        final data = await _supabaseClient
            .from('profiles')
            .select('id')
            .limit(1);

        // If we get here, access is verified (no errors thrown)
        _logger.i('Successfully queried database: ${data.length} results');
      } catch (queryError) {
        _logger.e('Database access verification failed: $queryError');
        return false;
      }

      _logger.i('Database access verified successfully');
      return true;
    } catch (e) {
      _logger.e('Error verifying database access: $e');
      return false;
    }
  }

  /// Create a user profile in the user table
  Future<void> _createUserProfile(User user) async {
    try {
      // Check if this user already exists in the database
      final existsResult = await _userRepository.userExists(user.id);
      final userExists = existsResult.fold((l) => false, (r) => r);
      
      final userProfile = UserProfile(
        id: user.id, 
        email: user.email,
        // If user already exists, they've completed onboarding before
        completedOnboarding: userExists,
      );

      await _userRepository.updateUserProfile(userProfile);
    } catch (e) {
      _logger.e('Error creating user profile: $e');
      // We don't update the auth state here as the user is already authenticated
      // This allows users to continue even if profile creation fails
    }
  }

  /// Ensure user profile exists in the user table
  Future<void> _ensureUserProfile(User user) async {
    try {
      // Try to get the user profile
      final result = await _userRepository.getCurrentUserProfile();

      // If the profile doesn't exist, create it
      result.fold(
        (failure) async {
          // User profile not found, create one
          await _createUserProfile(user);
        },
        (profile) {
          // User profile exists, no action needed
          _logger.i('User profile already exists');
        },
      );
    } catch (e) {
      _logger.e('Error ensuring user profile: $e');
    }
  }

  /// Map string provider name to OAuthProvider enum
  OAuthProvider? _mapProviderToEnum(String provider) {
    switch (provider.toLowerCase()) {
      case 'google':
        return OAuthProvider.google;
      case 'apple':
        return OAuthProvider.apple;
      case 'azure':
        return OAuthProvider.azure;
      case 'linkedin':
        // Check if Supabase supports LinkedIn through OIDC
        return OAuthProvider.linkedinOidc;
      case 'notion':
        return OAuthProvider.notion;

      default:
        return null;
    }
  }
}
