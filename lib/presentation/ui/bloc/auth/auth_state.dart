import 'package:equatable/equatable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Possible authentication states
enum AuthStatus {
  /// Initial state - not authenticated
  initial,

  /// Authentication in progress
  loading,

  /// Successfully authenticated
  authenticated,

  /// Authentication failed
  error,
}

/// Authentication state class
class AuthState extends Equatable {
  /// Current authentication status
  final AuthStatus status;

  /// User data if authenticated
  final User? user;

  /// Error message if authentication failed
  final String? errorMessage;

  /// Provider that was used for authentication
  final String? provider;

  /// Creates an authentication state
  const AuthState({
    this.status = AuthStatus.initial,
    this.user,
    this.errorMessage,
    this.provider,
  });

  /// Create a copy of this state with the given fields replaced
  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? errorMessage,
    String? provider,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
      provider: provider ?? this.provider,
    );
  }

  /// Initial authentication state
  factory AuthState.initial() => const AuthState();

  /// Loading authentication state
  factory AuthState.loading() => const AuthState(status: AuthStatus.loading);

  /// Authenticated state with user
  factory AuthState.authenticated(User user, {String? provider}) => AuthState(
    status: AuthStatus.authenticated,
    user: user,
    provider: provider,
  );

  /// Error authentication state with error message
  factory AuthState.error(String message) =>
      AuthState(status: AuthStatus.error, errorMessage: message);

  /// Whether the user is authenticated
  bool get isAuthenticated =>
      status == AuthStatus.authenticated && user != null;

  /// Whether authentication is in progress
  bool get isLoading => status == AuthStatus.loading;

  /// Whether authentication has failed
  bool get hasError => status == AuthStatus.error && errorMessage != null;

  @override
  List<Object?> get props => [status, user, errorMessage, provider];
}
