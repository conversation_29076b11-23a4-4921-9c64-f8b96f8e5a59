part of 'platform_connections_cubit.dart';

/// Base state for the platform connections cubit.
abstract class PlatformConnectionsState extends Equatable {
  const PlatformConnectionsState();

  @override
  List<Object?> get props => [];
}

/// Initial state before any actions.
class PlatformConnectionsInitial extends PlatformConnectionsState {}

/// Loading state when performing operations.
class PlatformConnectionsLoading extends PlatformConnectionsState {}

/// State when platform connectors are loaded.
class PlatformConnectionsLoaded extends PlatformConnectionsState {
  final List<PlatformConnector> connectors;
  final PlatformConnector? verifiedConnector;

  const PlatformConnectionsLoaded({
    this.connectors = const [],
    this.verifiedConnector,
  });

  @override
  List<Object?> get props => [connectors, verifiedConnector];
}

/// State when connecting to a platform.
class PlatformConnectionsConnecting extends PlatformConnectionsState {
  final String platformId;

  const PlatformConnectionsConnecting({required this.platformId});

  @override
  List<Object> get props => [platformId];
}

/// State when a platform connection operation fails.
class PlatformConnectionsError extends PlatformConnectionsState {
  final String message;
  final String? platformId;

  const PlatformConnectionsError({required this.message, this.platformId});

  @override
  List<Object?> get props => [message, platformId];
}

/// State when a platform connection operation succeeds.
class PlatformConnectionSuccess extends PlatformConnectionsState {
  final String message;
  final PlatformConnector? connector;

  const PlatformConnectionSuccess({required this.message, this.connector});

  @override
  List<Object?> get props => [message, connector];
}

/// State when a platform is connected.
class PlatformConnectionsConnected extends PlatformConnectionsState {
  final String platformId;
  final String message;

  const PlatformConnectionsConnected({
    required this.platformId,
    required this.message,
  });

  @override
  List<Object> get props => [platformId, message];
}
