import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logger/logger.dart';
import '../../../../data/local/platform_connector_local_source.dart';
import '../../../../domain/entities/platform_connector.dart';

part 'platform_connections_state.dart';

/// Cubit for managing platform connections
class PlatformConnectionsCubit extends Cubit<PlatformConnectionsState> {
  final PlatformConnectorLocalSource _localSource;
  final Logger _logger = Logger();

  PlatformConnectionsCubit({required PlatformConnectorLocalSource localSource})
    : _localSource = localSource,
      super(PlatformConnectionsInitial());

  /// Load all platform connections
  Future<void> loadConnections() async {
    try {
      emit(PlatformConnectionsLoading());

      final connectors = await _localSource.getPlatformConnectors();

      emit(PlatformConnectionsLoaded(connectors: connectors));
    } catch (e) {
      _logger.e('Error loading platform connections: $e');
      emit(
        PlatformConnectionsError(
          message: 'Failed to load platform connections: $e',
        ),
      );
    }
  }

  /// Add a new platform connection
  Future<void> addConnection(PlatformConnector connector) async {
    try {
      await _localSource.savePlatformConnector(connector);
      emit(
        PlatformConnectionSuccess(
          message: 'Platform connection added successfully',
          connector: connector,
        ),
      );
      await loadConnections(); // Reload to get updated list
    } catch (e) {
      _logger.e('Error adding platform connection: $e');
      emit(
        PlatformConnectionsError(
          message: 'Failed to add platform connection: $e',
        ),
      );
    }
  }

  /// Remove a platform connection
  Future<void> removeConnection(String platformId) async {
    try {
      await _localSource.deletePlatformConnector(platformId);
      emit(
        PlatformConnectionSuccess(
          message: 'Platform connection removed successfully',
        ),
      );
      await loadConnections(); // Reload to get updated list
    } catch (e) {
      _logger.e('Error removing platform connection: $e');
      emit(
        PlatformConnectionsError(
          message: 'Failed to remove platform connection: $e',
        ),
      );
    }
  }

  /// Update a platform connection
  Future<void> updateConnection(PlatformConnector connector) async {
    try {
      await _localSource.savePlatformConnector(connector);
      emit(
        PlatformConnectionSuccess(
          message: 'Platform connection updated successfully',
          connector: connector,
        ),
      );
      await loadConnections(); // Reload to get updated list
    } catch (e) {
      _logger.e('Error updating platform connection: $e');
      emit(
        PlatformConnectionsError(
          message: 'Failed to update platform connection: $e',
        ),
      );
    }
  }

  /// Clear any error state
  void clearError() {
    if (state is PlatformConnectionsError) {
      emit(PlatformConnectionsInitial());
    }
  }

  /// Get connector by platform ID
  PlatformConnector? getConnectorByPlatformId(String platformId) {
    if (state is PlatformConnectionsLoaded) {
      final connectors = (state as PlatformConnectionsLoaded).connectors;
      try {
        return connectors.firstWhere(
          (connector) =>
              connector.platformId == platformId && connector.isActive,
        );
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Connect to a platform using OAuth
  Future<void> connectPlatform(String platformId, dynamic context) async {
    try {
      emit(PlatformConnectionsLoading());

      // Create a temporary connector for the connection process
      final connector = PlatformConnector(
        id: platformId,
        platformId: platformId,
        name: _getPlatformName(platformId),
        apiKey: '', // Will be set after OAuth
        isActive: true,
      );

      await addConnection(connector);

      emit(
        PlatformConnectionSuccess(
          message: 'Successfully connected to ${_getPlatformName(platformId)}',
          connector: connector,
        ),
      );
    } catch (e) {
      _logger.e('Failed to connect to platform $platformId: $e');
      emit(
        PlatformConnectionsError(
          message: 'Failed to connect to ${_getPlatformName(platformId)}: $e',
        ),
      );
    }
  }

  /// Disconnect from a platform
  Future<void> disconnectPlatform(PlatformConnector connector) async {
    try {
      await removeConnection(connector.platformId);

      emit(
        PlatformConnectionSuccess(
          message: 'Successfully disconnected from ${connector.name}',
        ),
      );
    } catch (e) {
      _logger.e(
        'Failed to disconnect from platform ${connector.platformId}: $e',
      );
      emit(
        PlatformConnectionsError(
          message: 'Failed to disconnect from ${connector.name}: $e',
        ),
      );
    }
  }

  /// Toggle content type for a connector
  Future<void> toggleContentType(
    PlatformConnector connector,
    String type,
  ) async {
    try {
      final updatedContentTypes = Map<String, bool>.from(
        connector.contentTypes,
      );
      updatedContentTypes[type] = !(updatedContentTypes[type] ?? false);

      final updatedConnector = connector.copyWith(
        contentTypes: updatedContentTypes,
      );

      await updateConnection(updatedConnector);
    } catch (e) {
      _logger.e(
        'Failed to toggle content type $type for ${connector.name}: $e',
      );
      emit(
        PlatformConnectionsError(
          message: 'Failed to update content type settings',
        ),
      );
    }
  }

  /// Get platform display name
  String _getPlatformName(String platformId) {
    switch (platformId) {
      case 'clickup':
        return 'ClickUp';
      case 'notion':
        return 'Notion';
      
      case 'jira':
        return 'Jira';
      case 'monday':
        return 'Monday.com';
      case 'asana':
        return 'Asana';
      case 'trello':
        return 'Trello';
      default:
        return platformId.toUpperCase();
    }
  }
}
