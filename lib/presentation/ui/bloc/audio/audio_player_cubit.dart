import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_session/audio_session.dart';
import 'dart:async';
import 'dart:math';
import 'dart:io';
import 'package:flutter/foundation.dart';

part 'audio_player_state.dart';

/// Cubit for managing audio playback
class AudioPlayerCubit extends Cubit<AudioPlayerState> {
  // Make this non-final so we can replace it during recovery
  AudioPlayer _player = AudioPlayer();

  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<ProcessingState>? _processingStateSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;

  Timer? _audioLevelTimer;
  final List<double> _audioLevels = [];
  final Random _random = Random();

  AudioPlayerCubit() : super(const AudioPlayerState()) {
    _init();
  }

  Future<void> _init() async {
    // Configure audio session
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.speech());

      // Set up all streams
      _setupStreams();
    } catch (e) {
      emit(
        state.copyWith(
          status: AudioPlayerStatus.error,
          errorMessage: 'Failed to initialize audio player: $e',
        ),
      );
    }
  }

  /// Starts a timer to simulate audio level changes for visualization
  void _startAudioLevelSimulation() {
    _audioLevelTimer?.cancel();
    _audioLevelTimer = Timer.periodic(const Duration(milliseconds: 100), (_) {
      // Generate random-ish but smooth audio levels for visualization
      // This simulates actual audio level monitoring
      final newLevel = _getNextAudioLevel();
      emit(state.copyWith(audioLevel: newLevel));
    });
  }

  /// Calculates the next audio level value
  /// This creates a smooth, natural looking waveform animation
  double _getNextAudioLevel() {
    if (_audioLevels.isEmpty) {
      _audioLevels.add(0.5);
    }

    final lastLevel = _audioLevels.last;
    // Add some randomness but keep it smooth by blending with previous value
    final randomComponent = _random.nextDouble() * 0.4;
    final newLevel = (lastLevel * 0.6) + randomComponent;

    // Keep the values in bounds and add to history
    final boundedLevel = newLevel.clamp(0.1, 1.0);
    _audioLevels.add(boundedLevel);

    // Limit history length
    if (_audioLevels.length > 10) {
      _audioLevels.removeAt(0);
    }

    return boundedLevel;
  }

  /// Stops the audio level simulation timer
  void _stopAudioLevelSimulation() {
    _audioLevelTimer?.cancel();
    _audioLevels.clear();
    emit(state.copyWith(audioLevel: 0.0));
  }

  /// Load audio from a file path
  Future<void> loadAudio(String filePath) async {
    try {
      emit(state.copyWith(status: AudioPlayerStatus.loading));

      // Check if file exists and has content
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Audio file does not exist');
      }

      if (await file.length() == 0) {
        throw Exception('Audio file is empty');
      }

      // Try to load the audio file with timeout
      await _player
          .setFilePath(filePath)
          .timeout(
            const Duration(seconds: 5),
            onTimeout: () => throw TimeoutException('Loading audio timed out'),
          );

      // If duration is zero, the file might be corrupt
      final duration = _player.duration;
      if (duration == null || duration.inMilliseconds == 0) {
        throw Exception('Invalid audio file or zero duration');
      }

      emit(state.copyWith(status: AudioPlayerStatus.ready, filePath: filePath));
    } catch (e) {
      debugPrint('Error loading audio: $e');

      // If this is not a recovery attempt, try to recover the file
      if (!state.recoveryAttempted && filePath.isNotEmpty) {
        await _attemptRecovery(filePath, e.toString());
      } else {
        // Handle the error and set appropriate state
        emit(
          state.copyWith(
            status: AudioPlayerStatus.error,
            errorMessage: 'Failed to load audio: $e',
          ),
        );

        // Try to release resources when an error occurs
        try {
          await _player.stop();
        } catch (_) {
          // Ignore errors when stopping
        }
      }
    }
  }

  /// Attempts to recover from corrupted audio file
  Future<void> _attemptRecovery(String filePath, String errorMessage) async {
    try {
      debugPrint('Attempting to recover corrupted audio file: $filePath');

      // Mark that we attempted recovery
      emit(state.copyWith(recoveryAttempted: true));

      // Reset the player
      await _player.stop();

      // Try to create a clean player
      final recoveryPlayer = AudioPlayer();

      // Try to set a different processing mode
      await recoveryPlayer.setAudioSource(
        AudioSource.file(filePath),
        preload: false,
      );

      // If we got here without an error, replace our player
      await _player.dispose();
      _player = recoveryPlayer;

      // Set up the streams again
      _setupStreams();

      // Success! Continue with the recovered player
      emit(
        state.copyWith(
          status: AudioPlayerStatus.ready,
          filePath: filePath,
          errorMessage: null,
        ),
      );

      debugPrint('Successfully recovered audio file!');
    } catch (recoveryError) {
      debugPrint('Recovery failed: $recoveryError');

      // Handle the error and set appropriate state
      emit(
        state.copyWith(
          status: AudioPlayerStatus.error,
          errorMessage:
              'Audio file may be corrupted. Original error: $errorMessage',
        ),
      );

      // Try to release resources
      try {
        await _player.stop();
      } catch (_) {
        // Ignore errors when stopping
      }
    }
  }

  /// Set up all the player streams
  void _setupStreams() {
    // Cancel any existing subscriptions
    _playerStateSubscription?.cancel();
    _processingStateSubscription?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();

    // Listen to player state changes
    _playerStateSubscription = _player.playerStateStream.listen((playerState) {
      emit(
        state.copyWith(
          playing: playerState.playing,
          processingState: playerState.processingState,
        ),
      );

      // Start or stop audio level simulation based on playing state
      if (playerState.playing) {
        _startAudioLevelSimulation();
      } else {
        _stopAudioLevelSimulation();
      }
    });

    // Listen to processing state changes to handle completion
    _processingStateSubscription = _player.processingStateStream.listen((
      processingState,
    ) {
      if (processingState == ProcessingState.completed) {
        _player.seek(Duration.zero);
        _player.pause();
        _stopAudioLevelSimulation();
        emit(state.copyWith(playing: false, position: Duration.zero));
      }
    });

    // Listen to position changes
    _positionSubscription = _player.positionStream.listen((position) {
      emit(state.copyWith(position: position));
    });

    // Listen to duration changes
    _durationSubscription = _player.durationStream.listen((duration) {
      emit(state.copyWith(duration: duration ?? Duration.zero));
    });
  }

  /// Play or pause the audio
  void playPause() {
    if (state.playing) {
      _player.pause();
    } else {
      // If we've reached the end, start from beginning
      if (state.progress >= 0.99) {
        _player.seek(Duration.zero);
      }
      _player.play();
    }
  }

  /// Seek to a specific position
  void seek(double position) {
    final duration = state.duration;
    if (duration.inMilliseconds > 0) {
      final newPosition = duration * position;
      _player.seek(newPosition);
    }
  }

  /// Seek forward by 10 seconds
  void seekForward() {
    final position = state.position;
    final seekPosition = position + const Duration(seconds: 10);
    _player.seek(seekPosition);
  }

  /// Seek backward by 10 seconds
  void seekBackward() {
    final position = state.position;
    final seekPosition = position - const Duration(seconds: 10);
    _player.seek(seekPosition.isNegative ? Duration.zero : seekPosition);
  }

  /// Change playback speed
  void setSpeed(double speed) {
    _player.setSpeed(speed);
    emit(state.copyWith(speed: speed));
  }

  /// Cycle through available speeds
  void cycleSpeed() {
    const speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
    final currentIndex = speeds.indexOf(state.speed);
    final nextIndex = (currentIndex + 1) % speeds.length;
    setSpeed(speeds[nextIndex]);
  }

  /// Reset the player state
  Future<void> reset() async {
    try {
      await _player.stop();
      await _player.seek(Duration.zero);
      _stopAudioLevelSimulation();
      emit(const AudioPlayerState());
    } catch (e) {
      // Ignore errors during reset
    }
  }

  @override
  Future<void> close() {
    _playerStateSubscription?.cancel();
    _processingStateSubscription?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _audioLevelTimer?.cancel();
    _player.dispose();
    return super.close();
  }
}
