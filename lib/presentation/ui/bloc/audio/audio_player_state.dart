part of 'audio_player_cubit.dart';

/// Possible statuses for the audio player
enum AudioPlayerStatus {
  /// Initial state
  initial,

  /// Audio is loading
  loading,

  /// Audio is ready to play
  ready,

  /// An error occurred
  error,
}

/// State for audio player
class AudioPlayerState extends Equatable {
  /// Whether audio is currently playing
  final bool playing;

  /// Current position in the audio
  final Duration position;

  /// Total duration of the audio
  final Duration duration;

  /// Current processing state
  final ProcessingState processingState;

  /// Current audio level (0.0 to 1.0) for visualization
  final double audioLevel;

  /// Current playback speed
  final double speed;

  /// Current player status
  final AudioPlayerStatus status;

  /// Path to the loaded audio file
  final String? filePath;

  /// Error message (if status is error)
  final String? errorMessage;

  /// Whether a recovery attempt was made
  final bool recoveryAttempted;

  /// Creates an audio player state
  const AudioPlayerState({
    this.playing = false,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.processingState = ProcessingState.idle,
    this.audioLevel = 0.0,
    this.speed = 1.0,
    this.status = AudioPlayerStatus.initial,
    this.filePath,
    this.errorMessage,
    this.recoveryAttempted = false,
  });

  /// Creates a copy of this state with the given fields replaced
  AudioPlayerState copyWith({
    bool? playing,
    Duration? position,
    Duration? duration,
    ProcessingState? processingState,
    double? audioLevel,
    double? speed,
    AudioPlayerStatus? status,
    String? filePath,
    String? errorMessage,
    bool? recoveryAttempted,
  }) {
    return AudioPlayerState(
      playing: playing ?? this.playing,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      processingState: processingState ?? this.processingState,
      audioLevel: audioLevel ?? this.audioLevel,
      speed: speed ?? this.speed,
      status: status ?? this.status,
      filePath: filePath ?? this.filePath,
      errorMessage: errorMessage ?? this.errorMessage,
      recoveryAttempted: recoveryAttempted ?? this.recoveryAttempted,
    );
  }

  /// Calculate progress as a value from 0.0 to 1.0
  double get progress {
    if (duration.inMilliseconds == 0) return 0.0;
    return position.inMilliseconds / duration.inMilliseconds;
  }

  /// Format duration as mm:ss
  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return "$minutes:$seconds";
  }

  @override
  List<Object?> get props => [
    playing,
    position,
    duration,
    processingState,
    audioLevel,
    speed,
    status,
    filePath,
    errorMessage,
    recoveryAttempted,
  ];
}
