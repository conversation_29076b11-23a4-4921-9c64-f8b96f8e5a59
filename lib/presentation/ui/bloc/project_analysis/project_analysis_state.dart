part of 'project_analysis_cubit.dart';

/// Base state for project analysis operations
abstract class ProjectAnalysisState extends Equatable {
  const ProjectAnalysisState();

  @override
  List<Object?> get props => [];
}

/// Initial state - no analysis performed yet
class ProjectAnalysisInitial extends ProjectAnalysisState {
  const ProjectAnalysisInitial();

  @override
  List<Object?> get props => [];
}

/// Loading state - analysis in progress
class ProjectAnalysisLoading extends ProjectAnalysisState {
  const ProjectAnalysisLoading();

  @override
  List<Object?> get props => [];
}

/// Analysis loaded successfully
class ProjectAnalysisLoaded extends ProjectAnalysisState {
  final ProjectAnalysis analysis;
  final ProjectData projectData;

  const ProjectAnalysisLoaded(this.analysis, this.projectData);

  @override
  List<Object?> get props => [analysis, projectData];
}

/// Query processing state
class ProjectAnalysisQueryProcessing extends ProjectAnalysisState {
  const ProjectAnalysisQueryProcessing();

  @override
  List<Object?> get props => [];
}

/// Query result state
class ProjectAnalysisQueryResult extends ProjectAnalysisState {
  final String response;
  final ProjectAnalysis analysis;

  const ProjectAnalysisQueryResult(this.response, this.analysis);

  @override
  List<Object?> get props => [response, analysis];
}

/// Executive summary generated
class ProjectAnalysisSummaryGenerated extends ProjectAnalysisState {
  final String summary;
  final ProjectAnalysis analysis;

  const ProjectAnalysisSummaryGenerated(this.summary, this.analysis);

  @override
  List<Object?> get props => [summary, analysis];
}

/// Error state
class ProjectAnalysisError extends ProjectAnalysisState {
  final String message;

  const ProjectAnalysisError(this.message);

  @override
  List<Object?> get props => [message];
}
