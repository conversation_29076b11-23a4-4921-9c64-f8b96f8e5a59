import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import '../../../../domain/entities/project_data.dart';
import '../../../../domain/entities/project_analysis.dart';
import '../../../../domain/repositories/analysis_repository.dart';
import '../../../../domain/repositories/project_repository.dart';

part 'project_analysis_state.dart';

/// Cubit for managing project analysis state and operations
class ProjectAnalysisCubit extends Cubit<ProjectAnalysisState> {
  final AnalysisRepository _analysisRepository;
  final ProjectRepository _projectRepository;
  final Logger _logger = Logger();

  ProjectAnalysisCubit({
    required AnalysisRepository analysisRepository,
    required ProjectRepository projectRepository,
  }) : _analysisRepository = analysisRepository,
       _projectRepository = projectRepository,
       super(const ProjectAnalysisInitial());

  /// Analyze a project by ID
  Future<void> analyzeProject(String projectId) async {
    emit(const ProjectAnalysisLoading());

    try {
      // Fetch fresh project data from platform
      final projectDataResult = await _projectRepository
          .fetchProjectDataFromPlatform(projectId);

      await projectDataResult.fold(
        (failure) async {
          _logger.e('Failed to fetch project data: ${failure.message}');
          emit(ProjectAnalysisError(failure.message));
        },
        (projectData) async {
          // Analyze with AI
          final analysisResult = await _analysisRepository.analyzeProject(
            projectData,
          );

          analysisResult.fold(
            (failure) {
              _logger.e('Failed to analyze project: ${failure.message}');
              emit(ProjectAnalysisError(failure.message));
            },
            (analysis) {
              emit(ProjectAnalysisLoaded(analysis, projectData));
            },
          );
        },
      );
    } catch (e) {
      _logger.e('Error analyzing project: $e');
      emit(ProjectAnalysisError('Failed to analyze project: $e'));
    }
  }

  /// Process natural language query about the project
  Future<void> processNaturalLanguageQuery(String query) async {
    if (state is! ProjectAnalysisLoaded) {
      _logger.w('Cannot process query - no project data loaded');
      return;
    }

    emit(const ProjectAnalysisQueryProcessing());

    try {
      final currentState = state as ProjectAnalysisLoaded;
      final result = await _analysisRepository.processNaturalLanguageQuery(
        query,
        currentState.projectData,
      );

      result.fold(
        (failure) {
          _logger.e('Failed to process query: ${failure.message}');
          emit(ProjectAnalysisError(failure.message));
        },
        (response) {
          emit(ProjectAnalysisQueryResult(response, currentState.analysis));
        },
      );
    } catch (e) {
      _logger.e('Error processing query: $e');
      emit(ProjectAnalysisError('Failed to process query: $e'));
    }
  }

  /// Refresh analysis for current project
  Future<void> refreshAnalysis() async {
    if (state is ProjectAnalysisLoaded) {
      final currentState = state as ProjectAnalysisLoaded;
      await analyzeProject(currentState.projectData.project.id);
    }
  }

  /// Get cached analysis for a project
  Future<void> getCachedAnalysis(String projectId) async {
    emit(const ProjectAnalysisLoading());

    try {
      final result = await _analysisRepository.getCachedAnalysis(projectId);

      result.fold(
        (failure) {
          _logger.e('Failed to get cached analysis: ${failure.message}');
          emit(ProjectAnalysisError(failure.message));
        },
        (analysis) {
          if (analysis != null) {
            // Try to get cached project data as well
            _getCachedProjectData(projectId, analysis);
          } else {
            // No cached analysis, start fresh analysis
            analyzeProject(projectId);
          }
        },
      );
    } catch (e) {
      _logger.e('Error getting cached analysis: $e');
      emit(ProjectAnalysisError('Failed to get cached analysis: $e'));
    }
  }

  /// Get cached project data and emit with analysis
  Future<void> _getCachedProjectData(
    String projectId,
    ProjectAnalysis analysis,
  ) async {
    final result = await _projectRepository.getCachedProjectData(projectId);

    result.fold(
      (failure) {
        _logger.w('No cached project data, fetching fresh data');
        analyzeProject(projectId);
      },
      (projectData) {
        if (projectData != null) {
          emit(ProjectAnalysisLoaded(analysis, projectData));
        } else {
          analyzeProject(projectId);
        }
      },
    );
  }

  /// Generate executive summary
  Future<void> generateExecutiveSummary() async {
    if (state is! ProjectAnalysisLoaded) {
      _logger.w('Cannot generate summary - no project data loaded');
      return;
    }

    try {
      final currentState = state as ProjectAnalysisLoaded;
      final result = await _analysisRepository.generateExecutiveSummary(
        currentState.projectData,
      );

      result.fold(
        (failure) {
          _logger.e('Failed to generate summary: ${failure.message}');
          emit(ProjectAnalysisError(failure.message));
        },
        (summary) {
          emit(ProjectAnalysisSummaryGenerated(summary, currentState.analysis));
        },
      );
    } catch (e) {
      _logger.e('Error generating summary: $e');
      emit(ProjectAnalysisError('Failed to generate summary: $e'));
    }
  }

  /// Clear current analysis
  void clearAnalysis() {
    emit(const ProjectAnalysisInitial());
  }
}
