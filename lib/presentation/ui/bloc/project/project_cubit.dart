import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import '../../../../domain/entities/project.dart';
import '../../../../domain/repositories/project_repository.dart';

part 'project_state.dart';

/// Cubit for managing project operations
class ProjectCubit extends Cubit<ProjectState> {
  final ProjectRepository _projectRepository;
  final Logger _logger = Logger();

  ProjectCubit({required ProjectRepository projectRepository})
    : _projectRepository = projectRepository,
      super(const ProjectInitial());

  /// Load all projects for the current user
  Future<void> loadProjects() async {
    emit(const ProjectLoading());

    try {
      final result = await _projectRepository.getProjects();

      result.fold(
        (failure) {
          _logger.e('Failed to load projects: ${failure.message}');
          emit(ProjectError(failure.message));
        },
        (projects) {
          emit(ProjectsLoaded(projects));
        },
      );
    } catch (e) {
      _logger.e('Error loading projects: $e');
      emit(ProjectError('Failed to load projects: $e'));
    }
  }

  /// Load a specific project by ID
  Future<void> loadProject(String projectId) async {
    emit(const ProjectLoading());

    try {
      final result = await _projectRepository.getProject(projectId);

      result.fold(
        (failure) {
          _logger.e('Failed to load project: ${failure.message}');
          emit(ProjectError(failure.message));
        },
        (project) {
          emit(ProjectLoaded(project));
        },
      );
    } catch (e) {
      _logger.e('Error loading project: $e');
      emit(ProjectError('Failed to load project: $e'));
    }
  }

  /// Load projects by platform
  Future<void> loadProjectsByPlatform(String platformId) async {
    emit(const ProjectLoading());

    try {
      final result = await _projectRepository.getProjectsByPlatform(platformId);

      result.fold(
        (failure) {
          _logger.e('Failed to load projects by platform: ${failure.message}');
          emit(ProjectError(failure.message));
        },
        (projects) {
          emit(ProjectsLoaded(projects));
        },
      );
    } catch (e) {
      _logger.e('Error loading projects by platform: $e');
      emit(ProjectError('Failed to load projects by platform: $e'));
    }
  }

  /// Search projects by query
  Future<void> searchProjects(String query) async {
    if (query.trim().isEmpty) {
      loadProjects();
      return;
    }

    emit(const ProjectLoading());

    try {
      final result = await _projectRepository.searchProjects(query);

      result.fold(
        (failure) {
          _logger.e('Failed to search projects: ${failure.message}');
          emit(ProjectError(failure.message));
        },
        (projects) {
          emit(ProjectsLoaded(projects));
        },
      );
    } catch (e) {
      _logger.e('Error searching projects: $e');
      emit(ProjectError('Failed to search projects: $e'));
    }
  }

  /// Create a new project
  Future<void> createProject(Project project) async {
    emit(const ProjectLoading());

    try {
      final result = await _projectRepository.createProject(project);

      result.fold(
        (failure) {
          _logger.e('Failed to create project: ${failure.message}');
          emit(ProjectError(failure.message));
        },
        (createdProject) {
          emit(ProjectCreated(createdProject));
          // Reload projects to get updated list
          loadProjects();
        },
      );
    } catch (e) {
      _logger.e('Error creating project: $e');
      emit(ProjectError('Failed to create project: $e'));
    }
  }

  /// Update an existing project
  Future<void> updateProject(Project project) async {
    emit(const ProjectLoading());

    try {
      final result = await _projectRepository.updateProject(project);

      result.fold(
        (failure) {
          _logger.e('Failed to update project: ${failure.message}');
          emit(ProjectError(failure.message));
        },
        (updatedProject) {
          emit(ProjectUpdated(updatedProject));
          // Reload projects to get updated list
          loadProjects();
        },
      );
    } catch (e) {
      _logger.e('Error updating project: $e');
      emit(ProjectError('Failed to update project: $e'));
    }
  }

  /// Delete a project
  Future<void> deleteProject(String projectId) async {
    emit(const ProjectLoading());

    try {
      final result = await _projectRepository.deleteProject(projectId);

      result.fold(
        (failure) {
          _logger.e('Failed to delete project: ${failure.message}');
          emit(ProjectError(failure.message));
        },
        (_) {
          emit(const ProjectDeleted());
          // Reload projects to get updated list
          loadProjects();
        },
      );
    } catch (e) {
      _logger.e('Error deleting project: $e');
      emit(ProjectError('Failed to delete project: $e'));
    }
  }

  /// Sync project data with remote platform
  Future<void> syncProject(String projectId) async {
    emit(const ProjectSyncing());

    try {
      final result = await _projectRepository.syncProjectData(projectId);

      result.fold(
        (failure) {
          _logger.e('Failed to sync project: ${failure.message}');
          emit(ProjectError(failure.message));
        },
        (_) {
          emit(const ProjectSynced());
          // Reload project to get updated data
          loadProject(projectId);
        },
      );
    } catch (e) {
      _logger.e('Error syncing project: $e');
      emit(ProjectError('Failed to sync project: $e'));
    }
  }

  /// Get project statistics
  Future<void> getProjectStatistics(String projectId) async {
    try {
      final result = await _projectRepository.getProjectStatistics(projectId);

      result.fold(
        (failure) {
          _logger.e('Failed to get project statistics: ${failure.message}');
          emit(ProjectError(failure.message));
        },
        (statistics) {
          emit(ProjectStatisticsLoaded(statistics));
        },
      );
    } catch (e) {
      _logger.e('Error getting project statistics: $e');
      emit(ProjectError('Failed to get project statistics: $e'));
    }
  }

  /// Clear current state
  void clearState() {
    emit(const ProjectInitial());
  }
}
