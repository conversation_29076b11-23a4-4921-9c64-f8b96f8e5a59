part of 'project_cubit.dart';

/// Base state for project operations
abstract class ProjectState extends Equatable {
  const ProjectState();

  @override
  List<Object?> get props => [];
}

/// Initial state - no projects loaded yet
class ProjectInitial extends ProjectState {
  const ProjectInitial();

  @override
  List<Object?> get props => [];
}

/// Loading state - operations in progress
class ProjectLoading extends ProjectState {
  const ProjectLoading();

  @override
  List<Object?> get props => [];
}

/// Multiple projects loaded successfully
class ProjectsLoaded extends ProjectState {
  final List<Project> projects;

  const ProjectsLoaded(this.projects);

  @override
  List<Object?> get props => [projects];
}

/// Single project loaded successfully
class ProjectLoaded extends ProjectState {
  final Project project;

  const ProjectLoaded(this.project);

  @override
  List<Object?> get props => [project];
}

/// Project created successfully
class ProjectCreated extends ProjectState {
  final Project project;

  const ProjectCreated(this.project);

  @override
  List<Object?> get props => [project];
}

/// Project updated successfully
class ProjectUpdated extends ProjectState {
  final Project project;

  const ProjectUpdated(this.project);

  @override
  List<Object?> get props => [project];
}

/// Project deleted successfully
class ProjectDeleted extends ProjectState {
  const ProjectDeleted();

  @override
  List<Object?> get props => [];
}

/// Project syncing with remote platform
class ProjectSyncing extends ProjectState {
  const ProjectSyncing();

  @override
  List<Object?> get props => [];
}

/// Project synced successfully
class ProjectSynced extends ProjectState {
  const ProjectSynced();

  @override
  List<Object?> get props => [];
}

/// Project statistics loaded
class ProjectStatisticsLoaded extends ProjectState {
  final Map<String, dynamic> statistics;

  const ProjectStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

/// Error state
class ProjectError extends ProjectState {
  final String message;

  const ProjectError(this.message);

  @override
  List<Object?> get props => [message];
}
