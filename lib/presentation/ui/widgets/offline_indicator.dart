import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/presentation/ui/bloc/connectivity/connectivity_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/connectivity/connectivity_state.dart';

/// A widget that displays an offline indicator when the app is offline
class OfflineIndicator extends StatelessWidget {
  /// The child widget to display below the offline indicator
  final Widget child;

  /// Whether to show a banner (true) or a small indicator (false)
  final bool showBanner;

  /// Creates a new offline indicator
  const OfflineIndicator({
    super.key,
    required this.child,
    this.showBanner = true,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConnectivityCubit, ConnectivityState>(
      builder: (context, state) {
        if (state.isOnline) {
          return child;
        }

        return Column(
          children: [
            if (showBanner) _buildOfflineBanner(context) else _buildOfflineIndicator(context),
            Expanded(child: child),
          ],
        );
      },
    );
  }

  Widget _buildOfflineBanner(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        vertical: AppSizes.spacing2x,
        horizontal: AppSizes.spacing3x,
      ),
      color: theme.colorScheme.errorContainer,
      child: Row(
        children: [
          Icon(
            Icons.wifi_off,
            color: theme.colorScheme.onErrorContainer,
            size: 20,
          ),
          const SizedBox(width: AppSizes.spacing2x),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.offlineMode,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: theme.colorScheme.onErrorContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  l10n.offlineModeDescription,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onErrorContainer.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onErrorContainer,
            ),
            onPressed: () {
              context.read<ConnectivityCubit>().checkConnectivity();
            },
            tooltip: l10n.retrySync,
          ),
        ],
      ),
    );
  }

  Widget _buildOfflineIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        vertical: AppSizes.spacing1x,
        horizontal: AppSizes.spacing2x,
      ),
      color: theme.colorScheme.errorContainer,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.wifi_off,
            color: theme.colorScheme.onErrorContainer,
            size: 14,
          ),
          const SizedBox(width: AppSizes.spacing1x),
          Text(
            l10n.offlineMode,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onErrorContainer,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
