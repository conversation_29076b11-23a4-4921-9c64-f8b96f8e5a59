import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/core/constants/api_constants.dart';
import 'package:projectpilot/domain/entities/platform_connector.dart';
import 'package:projectpilot/presentation/ui/bloc/platform_connections/platform_connections_cubit.dart';
import 'package:projectpilot/l10n/app_localizations.dart';

/// Dialog that allows the user to choose which platform to send content to.
/// Shows available connected platforms and handles the selection.
class ChoosePlatformDialog extends StatelessWidget {
  final String? optimizedContent;
  final Function(String platformId, String? projectName) onPlatformSelected;
  final List<String>? suggestedPlatforms;
  final double? detectedConfidence;

  const ChoosePlatformDialog({
    super.key,
    this.optimizedContent,
    required this.onPlatformSelected,
    this.suggestedPlatforms,
    this.detectedConfidence,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      backgroundColor: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.choosePlatformTitle,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              l10n.choosePlatformDescription,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            if (optimizedContent != null) ...[
              const SizedBox(height: 16),
              _ContentPreview(content: optimizedContent!),
            ],
            const SizedBox(height: 24),
            BlocBuilder<PlatformConnectionsCubit, PlatformConnectionsState>(
              builder: (context, state) {
                if (state is PlatformConnectionsLoaded) {
                  final connectors = state.connectors;

                  if (connectors.isEmpty) {
                    return _EmptyState(message: l10n.noPlatformsConnected);
                  }

                  // Sort platforms: suggested ones first, then alphabetically
                  final sortedConnectors = _sortConnectors(connectors);

                  return SizedBox(
                    height: 200,
                    child: _PlatformList(
                      connectors: sortedConnectors,
                      suggestedPlatforms: suggestedPlatforms,
                      onPlatformSelected: onPlatformSelected,
                    ),
                  );
                }

                // Fallback loading state
                return const Center(child: CircularProgressIndicator());
              },
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(l10n.cancel),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<PlatformConnector> _sortConnectors(List<PlatformConnector> connectors) {
    if (suggestedPlatforms == null || suggestedPlatforms!.isEmpty) {
      // Just sort alphabetically by platform name
      return List.from(connectors)..sort(
        (a, b) => _getPlatformName(
          a.platformId,
        ).compareTo(_getPlatformName(b.platformId)),
      );
    }

    // Clone the list to avoid modifying the original
    final result = List<PlatformConnector>.from(connectors);

    // Custom sorting: suggested platforms first, then alphabetically
    result.sort((a, b) {
      final aIsSuggested = suggestedPlatforms!.contains(a.platformId);
      final bIsSuggested = suggestedPlatforms!.contains(b.platformId);

      if (aIsSuggested && !bIsSuggested) {
        return -1;
      } else if (!aIsSuggested && bIsSuggested) {
        return 1;
      } else {
        // Both suggested or both not suggested, sort alphabetically
        return _getPlatformName(
          a.platformId,
        ).compareTo(_getPlatformName(b.platformId));
      }
    });

    return result;
  }

  String _getPlatformName(String platformId) {
    switch (platformId) {
      case ApiConstants.clickUp:
        return 'ClickUp';

      case ApiConstants.notion:
        return 'Notion';
      case ApiConstants.asana:
        return 'Asana';
      case ApiConstants.monday:
        return 'Monday.com';
      case ApiConstants.jira:
        return 'Jira';
      case ApiConstants.trello:
        return 'Trello';
      default:
        return platformId;
    }
  }
}

/// Widget to display a preview of the content being dispatched
class _ContentPreview extends StatelessWidget {
  final String content;

  const _ContentPreview({required this.content});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).contentPreview,
            style: theme.textTheme.labelMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            content,
            style: theme.textTheme.bodyMedium,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

/// List of platform options
class _PlatformList extends StatelessWidget {
  final List<PlatformConnector> connectors;
  final List<String>? suggestedPlatforms;
  final Function(String platformId, String? projectName) onPlatformSelected;

  const _PlatformList({
    required this.connectors,
    required this.onPlatformSelected,
    this.suggestedPlatforms,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: connectors.length,
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        final connector = connectors[index];
        final isSuggested =
            suggestedPlatforms?.contains(connector.platformId) ?? false;

        return _PlatformListItem(
          connector: connector,
          isSuggested: isSuggested,
          onPlatformSelected: onPlatformSelected,
        );
      },
    );
  }
}

/// Individual platform list item with icon and metadata
class _PlatformListItem extends StatelessWidget {
  final PlatformConnector connector;
  final bool isSuggested;
  final Function(String platformId, String? projectName) onPlatformSelected;

  const _PlatformListItem({
    required this.connector,
    required this.isSuggested,
    required this.onPlatformSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Determine platform icon
    IconData platformIcon = Icons.check_box_outline_blank;
    Color iconColor = theme.colorScheme.primary;

    switch (connector.platformId) {
      case 'clickup':
        platformIcon = Icons.check_circle_outline;
        iconColor = Colors.green;
        break;
      case 'notion':
        platformIcon = Icons.edit_note;
        iconColor = Colors.black87;
        break;
      case 'jira':
        platformIcon = Icons.arrow_upward;
        iconColor = Colors.blue;
        break;
      case 'trello':
        platformIcon = Icons.dashboard;
        iconColor = Colors.blue;
        break;
      case 'asana':
        platformIcon = Icons.category;
        iconColor = Colors.red.shade400;
        break;

      case 'monday':
        platformIcon = Icons.view_week;
        iconColor = Colors.orange;
        break;
    }

    // Platform name with capitalized first letter
    String platformName = connector.platformId;
    if (platformName.isNotEmpty) {
      platformName = platformName[0].toUpperCase() + platformName.substring(1);
    }

    return ListTile(
      leading: Icon(platformIcon, color: iconColor, size: 28),
      title: Text(
        platformName,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: isSuggested ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      subtitle:
          connector.label.isNotEmpty
              ? Text(connector.label, style: theme.textTheme.bodySmall)
              : null,
      trailing:
          isSuggested
              ? Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  AppLocalizations.of(context).suggested,
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
              )
              : null,
      onTap:
          () => onPlatformSelected(
            connector.platformId,
            connector.defaultProject,
          ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      tileColor:
          isSuggested
              ? theme.colorScheme.surfaceContainerHighest.withOpacity(0.3)
              : null,
    );
  }
}

/// Empty state when no platforms are connected
class _EmptyState extends StatelessWidget {
  final String message;

  const _EmptyState({required this.message});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.devices_other,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.4),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to the connections screen
              // This would typically use a route name from your route configuration
              Navigator.of(context).pushNamed('/connections');
            },
            child: Text(l10n.connectPlatform),
          ),
        ],
      ),
    );
  }
}

/// Shows a dialog to choose a platform from connected accounts
Future<String?> showPlatformSelectionDialog(
  BuildContext context, {
  String? optimizedContent,
  List<String>? suggestedPlatforms,
  double? detectedConfidence,
}) async {
  String? selectedPlatformId;
  String? projectName;

  await showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      return ChoosePlatformDialog(
        optimizedContent: optimizedContent,
        suggestedPlatforms: suggestedPlatforms,
        detectedConfidence: detectedConfidence,
        onPlatformSelected: (platformId, project) {
          selectedPlatformId = platformId;
          projectName = project;
          Navigator.of(context).pop();
        },
      );
    },
  );

  // Load platforms first if needed
  if (selectedPlatformId != null) {
    final platformCubit = context.read<PlatformConnectionsCubit>();
    final connector = platformCubit.getConnectorByPlatformId(
      selectedPlatformId!,
    );

    // Return the selected platform ID
    return selectedPlatformId;
  }

  return null;
}
