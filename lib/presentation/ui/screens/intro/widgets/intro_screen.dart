import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../l10n/app_localizations.dart';
import '../../../../../core/widgets/dot_indicator.dart';
import '../../../../../core/di/service_locator.dart';
import '../../../../../core/navigation/routes/routes.dart';
import '../../../bloc/intro/intro_cubit.dart';
import '../../../bloc/intro/intro_state.dart';
import 'voice_animation.dart';
import 'app_connections_animation.dart';
import 'organize_thoughts_animation.dart';

/// A screen that introduces the app's main features to new users
class IntroScreen extends StatelessWidget {
  /// Creates an intro screen
  const IntroScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Use the IntroCubit instance from the service locator
    return BlocProvider.value(
      value: sl<IntroCubit>(),
      child: const IntroScreenView(),
    );
  }
}

/// The main view of the intro screen
class IntroScreenView extends StatefulWidget {
  /// Creates an intro screen view
  const IntroScreenView({super.key});

  @override
  State<IntroScreenView> createState() => _IntroScreenViewState();
}

class _IntroScreenViewState extends State<IntroScreenView> {
  final PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  colorScheme.surface,
                  colorScheme.surface,
                  colorScheme.primary.withValues(alpha: 0.05),
                ],
              ),
            ),
          ),

          // Decorative shapes
          Positioned(
            top: 0,
            right: 0,
            child: _buildDecorativeShape(
              width: 200,
              height: 200,
              color: const Color(0xFF6C63FF).withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(100),
              ),
            ),
          ),

          Positioned(
            bottom: 0,
            left: 0,
            child: _buildDecorativeShape(
              width: 150,
              height: 150,
              color: const Color(0xFF2ACAEA).withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(100),
              ),
            ),
          ),

          SafeArea(
            child: BlocConsumer<IntroCubit, IntroState>(
              listener: (context, state) {
                print(
                  'DEBUG - IntroState changed: currentPage=${state.currentPage}, totalPages=${state.totalPages}, isLastPage=${state.isLastPage}, isCompleted=${state.isCompleted}',
                );
                
                // Only navigate when intro is actively being completed by user interaction
                // Don't navigate if intro was already completed before this screen was shown
                if (state.isCompleted && state.isCompletedThisSession) {
                  print(
                    'DEBUG - Intro completed this session, navigating to auth',
                  );
                  // Navigate to the auth screen when intro is completed
                  Navigator.of(context).pushReplacementNamed(AppRoutes.auth);
                } else if (state.isCompleted && !state.isCompletedThisSession) {
                  print(
                    'DEBUG - Intro was already completed, but this screen is being shown - this should not happen',
                  );
                  // This indicates a routing issue - the app should not show intro if it's already completed
                  // We should not navigate here to avoid routing loops
                }

                // Update page controller when page changes from cubit
                if (_pageController.hasClients &&
                    _pageController.page?.round() != state.currentPage) {
                  _pageController.animateToPage(
                    state.currentPage,
                    duration: const Duration(milliseconds: 350),
                    curve: Curves.easeInOut,
                  );
                }
              },
              builder: (context, state) {
                return Column(
                  children: [
                    // Skip button at top
                    Align(
                      alignment: Alignment.topRight,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: TextButton(
                          style: TextButton.styleFrom(
                            foregroundColor: colorScheme.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                              side: BorderSide(
                                color: colorScheme.primary.withValues(
                                  alpha: 0.3,
                                ),
                                width: 1,
                              ),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                          onPressed: () {
                            print('DEBUG - Skip button pressed');
                            context.read<IntroCubit>().skipIntro();
                          },
                          child: Text(l10n.skip),
                        ),
                      ),
                    ),

                    // Title and subtitle for first screen only
                    if (state.currentPage == 0) ...[
                      const SizedBox(height: 8),
                      ShaderMask(
                        shaderCallback:
                            (bounds) => LinearGradient(
                              colors: [
                                const Color(0xFF6C63FF),
                                const Color(0xFF2ACAEA),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ).createShader(bounds),
                        child: Text(
                          l10n.introMainTitle,
                          style: theme.textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                            letterSpacing: 1.2,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        l10n.introMainSubtitle,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.2,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                    ],

                    // Main content
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        onPageChanged: (index) {
                          print(
                            'DEBUG - PageView onPageChanged called with index: $index',
                          );
                          context.read<IntroCubit>().goToPage(index);
                        },
                        children: [
                          // Screen 1: AI-Powered Project Intelligence
                          _buildIntroPage(
                            title: l10n.introScreen1Title,
                            animation: const VoiceAnimation(),
                            description: l10n.introScreen1Description,
                          ),

                          // Screen 2: Multi-Platform Overview
                          _buildIntroPage(
                            title: l10n.introScreen2Title,
                            animation: const AppConnectionsAnimation(),
                            description: l10n.introScreen2Description,
                          ),

                          // Screen 3: Smart Problem Detection
                          _buildIntroPage(
                            title: l10n.introScreen3Title,
                            animation: const OrganizeThoughtsAnimation(),
                            description: l10n.introScreen3Description,
                          ),
                        ],
                      ),
                    ),

                    // Dot indicator
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 24.0),
                      child: DotIndicator(
                        count: state.totalPages,
                        current: state.currentPage,
                        dotColor: colorScheme.primary.withValues(alpha: 0.3),
                        activeDotColor: colorScheme.primary,
                      ),
                    ),

                    // Navigation buttons
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 24.0,
                        right: 24.0,
                        bottom: 32.0,
                      ),
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16.0),
                            ),
                            elevation: 4,
                            shadowColor: colorScheme.primary.withValues(
                              alpha: 0.3,
                            ),
                          ),
                          onPressed: () {
                            print(
                              'DEBUG - Next/Get Started button pressed. Current page: ${state.currentPage}, isLastPage: ${state.isLastPage}',
                            );
                            context.read<IntroCubit>().nextPage();
                          },
                          child: Text(
                            state.isLastPage ? l10n.getStarted : l10n.next,
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: colorScheme.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a decorative shape for the background
  Widget _buildDecorativeShape({
    required double width,
    required double height,
    required Color color,
    required BorderRadius borderRadius,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(color: color, borderRadius: borderRadius,
      ),
    );
  }

  /// Builds an intro page with a title and animation
  Widget _buildIntroPage({
    required String title,
    required Widget animation,
    String? description,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animation area
            animation,

            const SizedBox(height: 8),
            // Title with gradient
            ShaderMask(
              shaderCallback:
                  (bounds) => LinearGradient(
                    colors: [
                      const Color(0xFF6C63FF), // Deep purple
                      const Color(0xFF2ACAEA), // Electric blue
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
              child: Text(
                title,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white, // This will be masked by the gradient
                ),
                textAlign: TextAlign.center,
              ),
            ),

            if (description != null) ...[
              const SizedBox(height: 8),
              Text(
                description,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.2,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
