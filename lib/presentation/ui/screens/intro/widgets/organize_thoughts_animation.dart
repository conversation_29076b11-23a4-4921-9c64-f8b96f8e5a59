import 'dart:math';
import 'package:flutter/material.dart';

/// A widget that displays an animation of smart problem detection and resolution
class OrganizeThoughtsAnimation extends StatefulWidget {
  /// Creates a smart problem detection animation
  const OrganizeThoughtsAnimation({super.key});

  @override
  State<OrganizeThoughtsAnimation> createState() =>
      _OrganizeThoughtsAnimationState();
}

class _OrganizeThoughtsAnimationState extends State<OrganizeThoughtsAnimation>
    with TickerProviderStateMixin {
  // Animation controllers
  late AnimationController _mainController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  // Track which boxes have been reached by the dot
  final List<bool> _boxesReached = List.filled(5, false);
  bool _animationCompleted = false;

  // Problem boxes data
  final List<ProblemBox> _problemBoxes = [
    ProblemBox(
      title: 'Budget\nOverrun',
      position: const Offset(0.15, 0.25),
      delay: 0.0,
      pathOrder: 0,
    ),
    ProblemBox(
      title: 'Missed\nDeadline',
      position: const Offset(0.75, 0.15),
      delay: 0.2,
      pathOrder: 1,
    ),
    ProblemBox(
      title: 'Resource\nConflict',
      position: const Offset(0.6, 0.5),
      delay: 0.4,
      pathOrder: 2,
    ),
    ProblemBox(
      title: 'Quality\nIssues',
      position: const Offset(0.25, 0.75),
      delay: 0.6,
      pathOrder: 3,
    ),
    ProblemBox(
      title: 'Team\nBottleneck',
      position: const Offset(0.8, 0.8),
      delay: 0.8,
      pathOrder: 4,
    ),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
      parent: _mainController,
      curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.elasticInOut),
    );

    // Listen to animation progress to update box states
    _progressAnimation.addListener(_updateBoxStates);

    // Start animation once and stop when complete
    _startAnimation();
  }

  void _startAnimation() async {
    await _mainController.forward();
    if (mounted) {
      setState(() {
        _animationCompleted = true;
      });
    }
  }

  void _updateBoxStates() {
    if (!mounted) return;
    
    final progress = _progressAnimation.value;
    bool stateChanged = false;

    // Check each box to see if the dot has reached it
    for (int i = 0; i < _problemBoxes.length; i++) {
      final box = _problemBoxes[i];
      // Each box gets reached at 20% intervals (0.2, 0.4, 0.6, 0.8, 1.0)
      final boxThreshold = (i + 1) * 0.2;

      if (progress >= boxThreshold && !_boxesReached[i]) {
        _boxesReached[i] = true;
        stateChanged = true;
      }
    }

    if (stateChanged) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _progressAnimation.removeListener(_updateBoxStates);
    _mainController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final animationSize = Size(size.width * 0.8, size.height * 0.4);

    return SizedBox(
      height: size.height * 0.48,
      child: Center(
        child: SizedBox(
          width: animationSize.width,
          height: animationSize.height,
          child: AnimatedBuilder(
            animation: _mainController,
            builder: (context, child) {
              return CustomPaint(
                painter: ProjectFlowPainter(
                  progress: _progressAnimation.value,
                  problemBoxes: _problemBoxes,
                ),
                child: Stack(
                  children:
                      _problemBoxes.asMap().entries.map((entry) {
                        final index = entry.key;
                        final box = entry.value;
                        return _buildProblemBox(box, animationSize, index);
                      }).toList(),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildProblemBox(ProblemBox box, Size containerSize, int index) {
    final isReached = _boxesReached[index];
    final showBox = _progressAnimation.value > box.delay;
    
    // Start red, turn green when reached by the dot
    final boxColor = isReached ? Colors.green.shade400 : Colors.red.shade400;
    final shadowColor = isReached ? Colors.green : Colors.red;
    
    // Pulse effect for green (solved) boxes
    final scale = showBox ? (isReached ? _pulseAnimation.value : 1.0) : 0.0;

    return Positioned(
      left: box.position.dx * containerSize.width - 40,
      top: box.position.dy * containerSize.height - 25,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        child: Transform.scale(
          scale: scale,
          child: Container(
            width: 80,
            height: 50,
            decoration: BoxDecoration(
              color: boxColor,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: shadowColor.withOpacity(isReached ? 0.4 : 0.3),
                  blurRadius: isReached ? 12 : 8,
                  spreadRadius: isReached ? 3 : 1,
                ),
              ],
            ),
            child: Center(
              child: Text(
                box.title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  height: 1.1,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Data class for problem boxes
class ProblemBox {
  final String title;
  final Offset position;
  final double delay;
  final int pathOrder;

  ProblemBox({
    required this.title,
    required this.position,
    required this.delay,
    required this.pathOrder,
  });
}

/// Custom painter for the red thread connecting problem boxes
class ProjectFlowPainter extends CustomPainter {
  final double progress;
  final List<ProblemBox> problemBoxes;

  ProjectFlowPainter({
    required this.progress,
    required this.problemBoxes,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.0
          ..strokeCap = StrokeCap.round;

    // Sort boxes by path order for proper connection
    final sortedBoxes = List<ProblemBox>.from(problemBoxes)
      ..sort((a, b) => a.pathOrder.compareTo(b.pathOrder));

    // Create path connecting all problem boxes in order
    final path = Path();
    
    for (int i = 0; i < sortedBoxes.length; i++) {
      final box = sortedBoxes[i];
      
      final position = Offset(
        box.position.dx * size.width,
        box.position.dy * size.height,
      );
      
      if (i == 0) {
        path.moveTo(position.dx, position.dy);
      } else {
        // Create smooth curved connection to previous point
        final prevBox = sortedBoxes[i - 1];
        final prevPosition = Offset(
          prevBox.position.dx * size.width,
          prevBox.position.dy * size.height,
        );
        
        final controlPoint1 = Offset(
          prevPosition.dx + (position.dx - prevPosition.dx) * 0.3,
          prevPosition.dy + (position.dy - prevPosition.dy) * 0.1,
        );
        final controlPoint2 = Offset(
          prevPosition.dx + (position.dx - prevPosition.dx) * 0.7,
          position.dy - (position.dy - prevPosition.dy) * 0.1,
        );
        
        path.cubicTo(
          controlPoint1.dx,
          controlPoint1.dy,
          controlPoint2.dx,
          controlPoint2.dy,
          position.dx,
          position.dy,
        );
      }
    }

    // Draw the red thread path
    paint.color = Colors.red.shade600.withOpacity(0.8);
    canvas.drawPath(path, paint);

    // Draw progress indicator (orange dot) along the path
    final pathMetrics = path.computeMetrics();
    for (final metric in pathMetrics) {
      final progressDistance = metric.length * progress;
      final tangent = metric.getTangentForOffset(progressDistance);

      if (tangent != null) {
        // Draw a glowing orange dot at the current progress position
        final glowPaint =
            Paint()
              ..color = Colors.orange.withOpacity(0.6)
              ..style = PaintingStyle.fill;
          
        canvas.drawCircle(tangent.position, 10, glowPaint);
        
        final dotPaint =
            Paint()
              ..color = Colors.orange
              ..style = PaintingStyle.fill;
          
        canvas.drawCircle(tangent.position, 6, dotPaint);
      }
    }
  }

  @override
  bool shouldRepaint(ProjectFlowPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
