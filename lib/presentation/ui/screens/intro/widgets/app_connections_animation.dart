import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A widget that displays an animation of app connections
class AppConnectionsAnimation extends StatefulWidget {
  /// Creates an app connections animation
  const AppConnectionsAnimation({super.key});

  @override
  State<AppConnectionsAnimation> createState() =>
      _AppConnectionsAnimationState();
}

class _AppConnectionsAnimationState extends State<AppConnectionsAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotateAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  // List of app platforms
  final List<PlatformInfo> _platforms = [
    PlatformInfo(
      name: 'ClickUp',
      color: const Color(0xFF7B68EE),
      svgPath: 'assets/svgs/clickup.svg',
    ),

    PlatformInfo(
      name: 'Notion',
      color: Colors.black87,
      svgPath: 'assets/svgs/notion.svg',
    ),
    PlatformInfo(
      name: 'Asana',
      color: const Color(0xFFF06A6A),
      svgPath: 'assets/svgs/asana.svg',
    ),
    PlatformInfo(
      name: 'Monday',
      color: const Color(0xFF00A9FF),
      svgPath: 'assets/svgs/monday.svg',
    ),
    PlatformInfo(
      name: 'Trello',
      color: const Color(0xFF0079BF),
      svgPath: 'assets/svgs/trello.svg',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8), // Slower rotation
    )..repeat(reverse: false);

    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 2 * pi,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _scaleAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 0.8, curve: Curves.easeInOut),
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 0.8, curve: Curves.easeInOut),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return SizedBox(
      height: size.height * 0.48,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Central VoicePilot logo
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: SizedBox(
              width: 200,
              height: 200,
              child: SvgPicture.asset(
                'assets/svgs/voicepilot.svg',
                fit: BoxFit.contain,
              ),
            ),
          ),

          // App platforms orbiting around
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Stack(
                alignment: Alignment.center,
                children: List.generate(_platforms.length, (index) {
                  final angle =
                      _rotateAnimation.value +
                      (index * (2 * pi / _platforms.length));
                  final radius = size.width * 0.35;
          
                  // Calculate position based on angle and radius
                  final x = cos(angle) * radius;
                  final y = sin(angle) * radius;
          
                  // Determine if this platform should be highlighted
                  final highlight =
                      (_controller.value * _platforms.length) %
                      _platforms.length;
                  final isHighlighted = index == highlight.floor();
          
                  return Positioned(
                    left: size.width / 2 + x - 50,
                    top: size.height * 0.35 / 2 + y + 30,
                    child: Transform.scale(
                      scale:
                          isHighlighted
                              ? _scaleAnimation.value
                              : 1.0 - ((_scaleAnimation.value - 0.7) * 0.3),
                      child: Opacity(
                        opacity:
                            isHighlighted
                                ? _opacityAnimation.value
                                : 1.0 - ((_opacityAnimation.value - 0.3) * 0.3),
                        child: PlatformLogo(
                          platform: _platforms[index],
                          size: 60,
                        ),
                      ),
                    ),
                  );
                }),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// Information about a platform
class PlatformInfo {
  /// Name of the platform
  final String name;

  /// Brand color of the platform
  final Color color;
  
  /// Path to the SVG logo
  final String svgPath;

  /// Creates a platform info object with the given parameters
  PlatformInfo({
    required this.name,
    required this.color,
    required this.svgPath,
  });
}

/// A widget that displays a platform logo
class PlatformLogo extends StatelessWidget {
  /// Platform information
  final PlatformInfo platform;

  /// Size of the logo
  final double size;

  /// Creates a platform logo widget with the given platform info
  const PlatformLogo({super.key, required this.platform, required this.size});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: platform.color.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: platform.color.withValues(alpha: 0.5),
          width: 2,
        ),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: SvgPicture.asset(
            platform.svgPath,
            fit: BoxFit.contain,
            colorFilter: ColorFilter.mode(platform.color, BlendMode.srcIn),
          ),
        ),
      ),
    );
  }
}
