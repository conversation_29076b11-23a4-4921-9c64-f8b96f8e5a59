import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A widget that displays an animated project intelligence visualization
class VoiceAnimation extends StatefulWidget {
  /// Creates a project intelligence animation widget
  const VoiceAnimation({super.key});

  @override
  State<VoiceAnimation> createState() => _VoiceAnimationState();
}

class _VoiceAnimationState extends State<VoiceAnimation>
    with TickerProviderStateMixin {
  late AnimationController _logoPulseController;
  late AnimationController _chartsController;
  late Animation<double> _logoPulseAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    // Logo pulse animation controller
    _logoPulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    _logoPulseAnimation = Tween<double>(begin: 1.0, end: 1.12).animate(
      CurvedAnimation(
        parent: _logoPulseController,
        curve: Curves.easeInOutCubic,
      ),
    );

    // Charts animation controller
    _chartsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    _progressAnimation = CurvedAnimation(
      parent: _chartsController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _logoPulseController.dispose();
    _chartsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return SizedBox(
      height: size.height * 0.38,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // AI Analytics visualization above the logo
          Positioned(
            top: 10,
            child: SizedBox(
              width: size.width * 0.9,
              height: size.height * 0.15,
              child: AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return CustomPaint(
                    size: Size(size.width * 0.9, size.height * 0.15),
                    painter: ProjectIntelligencePainter(
                      progress: _progressAnimation.value,
                    ),
                  );
                },
              ),
            ),
          ),

          // ProjectPilot logo with pulsing animation
          Positioned(
            top: 140,
            child: AnimatedBuilder(
              animation: _logoPulseController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _logoPulseAnimation.value,
                  child: Container(
                    width: 180,
                    height: 180,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFF6C63FF).withValues(alpha: 0.1),
                          const Color(0xFF2ACAEA).withValues(alpha: 0.1),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      border: Border.all(
                        color: const Color(0xFF6C63FF).withValues(alpha: 0.2),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        'assets/svgs/voicepilot.svg',
                        width: 140,
                        height: 140,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // AI indicators around the logo
          ..._buildAIIndicators(),
        ],
      ),
    );
  }

  List<Widget> _buildAIIndicators() {
    final indicators = <Widget>[];
    const indicatorCount = 6;

    for (int i = 0; i < indicatorCount; i++) {
      final angle = (i * 2 * pi / indicatorCount);
      final radius = 120.0;

      indicators.add(
        AnimatedBuilder(
          animation: _chartsController,
          builder: (context, child) {
            final animatedAngle =
                angle + (_chartsController.value * 2 * pi * 0.1);
            final x = cos(animatedAngle) * radius;
            final y = sin(animatedAngle) * radius;

            final opacity =
                (sin(_chartsController.value * 4 * pi + angle) + 1) / 4 + 0.3;

            return Positioned(
              left: 200 + x - 8,
              top: 230 + y - 8,
              child: Opacity(
                opacity: opacity,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        i % 3 == 0
                            ? const Color(0xFF6C63FF)
                            : i % 3 == 1
                            ? const Color(0xFF2ACAEA)
                            : const Color(0xFF1FE2C1),
                  ),
                ),
              ),
            );
          },
        ),
      );
    }

    return indicators;
  }
}

/// Custom painter for project intelligence visualization
class ProjectIntelligencePainter extends CustomPainter {
  final double progress;

  ProjectIntelligencePainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw progress charts
    _drawProgressChart(
      canvas,
      Rect.fromLTWH(20, 10, width * 0.25, height * 0.6),
    );
    _drawRiskIndicator(
      canvas,
      Rect.fromLTWH(width * 0.35, 10, width * 0.25, height * 0.6),
    );
    _drawTaskDistribution(
      canvas,
      Rect.fromLTWH(width * 0.7, 10, width * 0.25, height * 0.6),
    );

    // Draw connecting lines (network effect)
    _drawConnectionLines(canvas, size);
  }

  void _drawProgressChart(Canvas canvas, Rect rect) {
    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..color = const Color(0xFF6C63FF).withValues(alpha: 0.3);

    final strokePaint =
        Paint()
          ..style = PaintingStyle.stroke
          ..color = const Color(0xFF6C63FF)
          ..strokeWidth = 2;

    // Animated progress bar
    final progressHeight = rect.height * (0.3 + 0.7 * sin(progress * 2 * pi));
    final progressRect = Rect.fromLTWH(
      rect.left,
      rect.bottom - progressHeight,
      rect.width,
      progressHeight,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(progressRect, const Radius.circular(4)),
      paint,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      strokePaint,
    );
  }

  void _drawRiskIndicator(Canvas canvas, Rect rect) {
    final center = rect.center;
    final radius = min(rect.width, rect.height) / 2 * 0.8;

    final riskLevel = 0.3 + 0.4 * sin(progress * 3 * pi);
    final sweepAngle = 2 * pi * riskLevel;

    final paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 6
          ..strokeCap = StrokeCap.round
          ..color =
              riskLevel < 0.5
                  ? const Color(0xFF1FE2C1)
                  : riskLevel < 0.7
                  ? const Color(0xFF2ACAEA)
                  : const Color(0xFF6C63FF);

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2,
      sweepAngle,
      false,
      paint,
    );

    // Background circle
    final backgroundPaint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 6
          ..color = Colors.grey.withValues(alpha: 0.2);

    canvas.drawCircle(center, radius, backgroundPaint);
  }

  void _drawTaskDistribution(Canvas canvas, Rect rect) {
    final barPaint = Paint()..style = PaintingStyle.fill;

    final bars = [
      0.8 + 0.2 * sin(progress * 2 * pi),
      0.6 + 0.3 * sin(progress * 2 * pi + pi / 3),
      0.4 + 0.4 * sin(progress * 2 * pi + 2 * pi / 3),
    ];

    final colors = [
      const Color(0xFF6C63FF),
      const Color(0xFF2ACAEA),
      const Color(0xFF1FE2C1),
    ];

    final barWidth = rect.width / 4;

    for (int i = 0; i < bars.length; i++) {
      barPaint.color = colors[i].withValues(alpha: 0.7);
      final barHeight = rect.height * bars[i];
      final barRect = Rect.fromLTWH(
        rect.left + i * barWidth * 1.2,
        rect.bottom - barHeight,
        barWidth,
        barHeight,
      );
      canvas.drawRRect(
        RRect.fromRectAndRadius(barRect, const Radius.circular(2)),
        barPaint,
      );
    }
  }

  void _drawConnectionLines(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1
          ..color = const Color(0xFF6C63FF).withValues(alpha: 0.3);

    final animatedOffset = progress * 20;

    // Horizontal connecting lines
    for (int i = 0; i < 3; i++) {
      final y = size.height * 0.2 + i * 15 + sin(progress * 2 * pi + i) * 5;
      final path = Path();
      path.moveTo(0, y);
      path.lineTo(size.width, y);
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(ProjectIntelligencePainter oldDelegate) =>
      progress != oldDelegate.progress;
}


