import 'package:flutter/material.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/domain/entities/transcription_result.dart';
import 'package:projectpilot/l10n/app_localizations.dart';

class AnimatedResultCard extends StatefulWidget {
  final TranscriptionResult result;
  final VoidCallback? onShare;
  final VoidCallback? onEdit;

  const AnimatedResultCard({
    super.key,
    required this.result,
    this.onShare,
    this.onEdit,
  });

  @override
  State<AnimatedResultCard> createState() => _AnimatedResultCardState();
}

class _AnimatedResultCardState extends State<AnimatedResultCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutBack,
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeIn,
      ),
    );

    // Start the animation
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Card(
              elevation: 4,
              margin: const EdgeInsets.all(AppSizes.spacing2x),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.spacing2x),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildCategoryBadge(context),
                        Row(
                          children: [
                            if (widget.onEdit != null)
                              IconButton(
                                icon: const Icon(Icons.edit),
                                onPressed: widget.onEdit,
                                tooltip: l10n.edit,
                              ),
                            if (widget.onShare != null)
                              IconButton(
                                icon: const Icon(Icons.share),
                                onPressed: widget.onShare,
                                tooltip: l10n.share,
                              ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.spacing2x),
                    Text(
                      widget.result.optimizedContent,
                      style: theme.textTheme.bodyLarge,
                    ),
                    const SizedBox(height: AppSizes.spacing2x),
                    const Divider(),
                    ExpansionTile(
                      title: Text(
                        l10n.originalTranscription,
                        style: theme.textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(AppSizes.spacing2x),
                          child: Text(
                            widget.result.originalTranscription,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontStyle: FontStyle.italic,
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategoryBadge(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    
    Color bgColor;
    IconData icon;
    String label;

    switch (widget.result.category) {
      case ContentCategory.task:
        bgColor = theme.colorScheme.primary;
        icon = Icons.check_circle;
        label = l10n.task;
        break;
      case ContentCategory.idea:
        bgColor = Colors.amber;
        icon = Icons.lightbulb;
        label = l10n.idea;
        break;
      case ContentCategory.note:
        bgColor = Colors.green;
        icon = Icons.note;
        label = l10n.note;
        break;
      default:
        bgColor = Colors.grey;
        icon = Icons.help;
        label = l10n.uncategorized;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.spacing2x,
        vertical: AppSizes.spacing1x,
      ),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 16),
          const SizedBox(width: AppSizes.spacing1x),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
