import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:ambient_gradient_border/ambient_gradient_border.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/core/services/voice/audio_level_service.dart';
import 'package:projectpilot/core/utils/haptic_feedback_service.dart';
import 'package:projectpilot/core/utils/performance_utils.dart';
import 'package:projectpilot/presentation/ui/bloc/recording/recording_cubit.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/modern_sound_wave.dart';

/// A standardized recording interface that can be used across all screens
class StandardRecordingInterface extends StatefulWidget {
  /// Creates a new recording interface
  const StandardRecordingInterface({
    super.key,
    this.onRecordingComplete,
    this.showControls = true,
    this.showWaveform = true,
    this.showDuration = true,
    this.waveformHeight = 150,
    this.buttonSize = 80,
    this.useCustomIcon = false,
  });

  /// Callback when recording is complete
  final VoidCallback? onRecordingComplete;

  /// Whether to show recording controls (pause, stop, reset)
  final bool showControls;

  /// Whether to show the waveform animation
  final bool showWaveform;

  /// Whether to show the recording duration
  final bool showDuration;

  /// Height of the waveform animation
  final double waveformHeight;

  /// Size of the record button
  final double buttonSize;
  
  /// Whether to use voicepilot.png as the custom icon
  final bool useCustomIcon;

  @override
  State<StandardRecordingInterface> createState() =>
      _StandardRecordingInterfaceState();
}

class _StandardRecordingInterfaceState extends State<StandardRecordingInterface>
    with TickerProviderStateMixin {
  late AnimationController _recordPulseController;

  // Audio level service for real-time visualization
  final AudioLevelService _audioLevelService = AudioLevelService();

  // Haptic feedback service
  final HapticFeedbackService _hapticFeedbackService = HapticFeedbackService();

  // Stream subscription for audio levels
  StreamSubscription<double>? _audioLevelSubscription;

  // Performance monitoring
  bool _isMonitoringPerformance = false;

  @override
  void initState() {
    super.initState();

    // Initialize the pulse animation controller
    _recordPulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);

    // Initialize audio level service
    _initializeAudioLevelService();

    // Start performance monitoring in debug mode
    if (PerformanceUtils.enableLogging) {
      _isMonitoringPerformance = true;
      PerformanceUtils.startFrameMonitoring();
    }
  }

  // Initialize audio level service
  Future<void> _initializeAudioLevelService() async {
    await _audioLevelService.init();
  }

  @override
  void dispose() {
    _recordPulseController.dispose();
    _audioLevelSubscription?.cancel();

    // Stop performance monitoring
    if (_isMonitoringPerformance) {
      PerformanceUtils.stopFrameMonitoring();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RecordingCubit, RecordingState>(
      builder: (context, state) {
        final isRecording =
            state.status == RecordingStatus.recording ||
            state.status == RecordingStatus.paused;
        final isPaused = state.status == RecordingStatus.paused;
        final theme = Theme.of(context);
        final colorScheme = theme.colorScheme;

        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (widget.showWaveform) ...[
              // Sound wave animation
              SizedBox(
                height: widget.waveformHeight,
                child: _buildWaveformWithAudioLevels(isRecording && !isPaused),
              ),
              const SizedBox(height: AppSizes.spacing2x),
            ],

            if (widget.showDuration && isRecording) ...[
              // Status indicator (Recording/Paused)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.spacing3x,
                  vertical: AppSizes.spacing1x,
                ),
                decoration: BoxDecoration(
                  color:
                      isPaused
                          ? Colors.blue.withValues(alpha: 0.1)
                          : colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isPaused ? Icons.pause_circle : Icons.mic,
                      size: 18,
                      color: isPaused ? Colors.blue : colorScheme.primary,
                    ),
                    const SizedBox(width: AppSizes.spacing1x),
                    Text(
                      isPaused ? 'Paused' : 'Recording',
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: isPaused ? Colors.blue : colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppSizes.spacing1x),
              // Recording duration display
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.spacing3x,
                  vertical: AppSizes.spacing1x,
                ),
                decoration: BoxDecoration(
                  color:
                      isPaused
                          ? Colors.blue.withValues(alpha: 0.1)
                          : colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _formatDuration(state.duration),
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: isPaused ? Colors.blue : colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(height: AppSizes.spacing2x),
            ],

            if (widget.showControls && isRecording)
              _buildRecordingControls(context, state, colorScheme)
            else
              _buildRecordButton(context, state, colorScheme),
              
            // Recording guide information (only show when recording)
            if (isRecording && widget.showControls) ...[
              const SizedBox(height: AppSizes.spacing4x),
              _buildRecordingGuide(context),
            ],
          ],
        );
      },
    );
  }

  Widget _buildRecordingGuide(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSizes.spacing4x),
      padding: const EdgeInsets.all(AppSizes.spacing3x),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
        border: Border.all(
          color: theme.colorScheme.outlineVariant.withValues(alpha: 0.5),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: AppSizes.spacing2x),
              Expanded(
                child: Text(
                  'How it works',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.spacing2x),
          Row(
            children: [
              Icon(Icons.mic, color: theme.colorScheme.outline, size: 16),
              const SizedBox(width: AppSizes.spacing2x),
              Expanded(
                child: Text(
                  'Record your voice note',
                  style: theme.textTheme.bodySmall,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.spacing1x),
          Row(
            children: [
              Icon(
                Icons.auto_awesome,
                color: theme.colorScheme.outline,
                size: 16,
              ),
              const SizedBox(width: AppSizes.spacing2x),
              Expanded(
                child: Text(
                  'VoicePilot magically transcribes and enhances it',
                  style: theme.textTheme.bodySmall,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.spacing1x),
          Row(
            children: [
              Icon(Icons.send, color: theme.colorScheme.outline, size: 16),
              const SizedBox(width: AppSizes.spacing2x),
              Expanded(
                child: Text(
                  'Send it to your favorite platform',
                  style: theme.textTheme.bodySmall,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecordingControls(
    BuildContext context,
    RecordingState state,
    ColorScheme colorScheme,
  ) {
    final bool isPaused = state.status == RecordingStatus.paused;
    final recordingCubit = context.read<RecordingCubit>();

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Pause/Resume button (left)
        _buildControlButton(
          context,
          isPaused ? Icons.play_arrow : Icons.pause,
          colorScheme,
          onTap: () {
            if (isPaused) {
              recordingCubit.resumeRecording();
              _hapticFeedbackService.lightImpact();
            } else {
              recordingCubit.pauseRecording();
              _hapticFeedbackService.mediumImpact();
            }
          },
          buttonColor: isPaused ? Colors.blue : null,
          iconColor: isPaused ? Colors.white : null,
          isDisabled: false,
        ),

        const SizedBox(width: AppSizes.spacing4x),

        // Main record/stop button
        GestureDetector(
          onTap: () {
            _handleRecordButtonTap(context, state);
          },
          child: AmbientGradientBorder(
            width: widget.buttonSize + 20,
            height: widget.buttonSize + 20,
            strokeWidth: 5.0,
            radius: (widget.buttonSize + 20) / 2,
            gradientColors: const [
              Color(0xFF6C63FF), // Deep purple
              Color(0xFF2ACAEA), // Electric blue
              Color(0xFF1FE2C1), // Neon turquoise
              Color(0xFF6C63FF), // Deep purple (loop)
            ],
            glowSpread: 12.0,
            glowWidthMultiplier: 3.5,
            showSharpBorder: true,
            child: Container(
              width: widget.buttonSize,
              height: widget.buttonSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors:
                      isPaused
                          ? [Colors.blue.shade400, Colors.blue.shade600]
                          : [Colors.red.shade400, Colors.red.shade600],
                ),
                boxShadow: [
                  BoxShadow(
                    color:
                        isPaused
                            ? Colors.blue.withValues(alpha: 0.3)
                            : Colors.red.withValues(alpha: 0.3),
                    spreadRadius: 3,
                    blurRadius: 16,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                isPaused ? Icons.play_arrow : Icons.stop,
                color: Colors.white,
                size: 40,
                shadows: const [
                  Shadow(
                    color: Colors.black26,
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),
        ),

        const SizedBox(width: AppSizes.spacing4x),

        // Reset button (right)
        _buildControlButton(
          context,
          Icons.refresh,
          colorScheme,
          onTap: () {
            // Show confirmation dialog
            showDialog(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: const Text('Reset Recording?'),
                    content: const Text(
                      'Are you sure you want to reset this recording? This action cannot be undone.',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          recordingCubit.resetRecording();
                          _hapticFeedbackService.heavyImpact();
                        },
                        child: Text(
                          'Reset',
                          style: TextStyle(color: colorScheme.error),
                        ),
                      ),
                    ],
                  ),
            );
          },
          buttonColor: colorScheme.errorContainer,
          iconColor: colorScheme.error,
          isDisabled: false,
        ),
      ],
    );
  }

  Widget _buildControlButton(
    BuildContext context,
    IconData icon,
    ColorScheme colorScheme, {
    required VoidCallback onTap,
    Color? buttonColor,
    Color? iconColor,
    bool isDisabled = false,
  }) {
    return GestureDetector(
      onTap: isDisabled ? null : onTap,
      child: Opacity(
        opacity: isDisabled ? 0.5 : 1.0,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: buttonColor ?? colorScheme.surfaceContainerHighest,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: (buttonColor ?? colorScheme.primary).withValues(
                alpha: 0.3,
              ),
              width: 2,
            ),
          ),
          child: Icon(icon, color: iconColor ?? colorScheme.primary, size: 30),
        ),
      ),
    );
  }

  Widget _buildRecordButton(
    BuildContext context,
    RecordingState state,
    ColorScheme colorScheme,
  ) {
    final bool isRecording = state.status == RecordingStatus.recording;
    final bool isPaused = state.status == RecordingStatus.paused;
    
    Widget icon;
    
    if (widget.useCustomIcon && !isRecording && !isPaused) {
      // Use the voicepilot.png image as icon
      icon = Image.asset(
        'assets/images/projectpilot.png',
        width: 40,
        height: 40,
      );
    } else if (isRecording) {
      icon = const Icon(
          Icons.stop,
          color: Colors.white,
          size: 40,
          shadows: [
            Shadow(
              color: Colors.black26,
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        );
    } else {
      icon = const Icon(
          Icons.mic,
          color: Colors.white,
          size: 40,
          shadows: [
            Shadow(
              color: Colors.black26,
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        );
    }

    return GestureDetector(
      onTap: () => _handleRecordButtonTap(context, state),
      child: AmbientGradientBorder(
        width: widget.buttonSize,
        height: widget.buttonSize,
        strokeWidth: 5.0,
        radius: widget.buttonSize / 2,
        gradientColors: const [
          Color(0xFF6C63FF), // Deep purple
          Color(0xFF2ACAEA), // Electric blue
          Color(0xFF1FE2C1), // Neon turquoise
          Color(0xFF6C63FF), // Deep purple (loop)
        ],
        glowSpread: 16.0,
        glowWidthMultiplier: 3.5,
        showSharpBorder: true,
        child: isPaused
            ? const Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 40,
              shadows: [
                Shadow(
                  color: Colors.black26,
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            )
            : icon,
      ),
    );
  }

  void _handleRecordButtonTap(BuildContext context, RecordingState state) {
    final recordingCubit = context.read<RecordingCubit>();

    if (state.status == RecordingStatus.recording) {
      // Stop recording
      recordingCubit.stopRecording();

      // Provide haptic feedback
      _hapticFeedbackService.recordingStop();

      // After stopping, the recording is processed and saved
      // The app should navigate to the collection details screen
      // instead of returning to the empty state
      if (widget.onRecordingComplete != null) {
        widget.onRecordingComplete!();
      }
    } else if (state.status == RecordingStatus.paused) {
      // Resume recording
      recordingCubit.resumeRecording();

      // Provide haptic feedback
      _hapticFeedbackService.lightImpact();
    } else if (state.status == RecordingStatus.readyToRecord) {
      // Start recording
      recordingCubit.startRecording();

      // Provide haptic feedback
      _hapticFeedbackService.recordingStart();
    } else if (state.status == RecordingStatus.error) {
      // Try to reinitialize the recorder
      recordingCubit.initRecorder();

      // Provide haptic feedback for error
      _hapticFeedbackService.error();
    }
  }

  // Format duration as MM:SS
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  // Build waveform with audio level visualization
  Widget _buildWaveformWithAudioLevels(bool isRecording) {
    // Start or stop audio level processing based on recording state
    if (isRecording) {
      // Start audio level processing if not already subscribed
      if (_audioLevelSubscription == null) {
        _audioLevelService.startProcessing();
        _audioLevelSubscription = _audioLevelService.audioLevelStream.listen((
          level,
        ) {
          // The stream updates will automatically refresh the ModernSoundWave
          // No need to call setState() as the stream is passed directly to the widget
        });

        // Provide haptic feedback when recording starts
        _hapticFeedbackService.recordingStart();
      }
    } else {
      // Stop audio level processing if subscribed
      if (_audioLevelSubscription != null) {
        _audioLevelService.stopProcessing();
        _audioLevelSubscription?.cancel();
        _audioLevelSubscription = null;
      }
    }

    // Return the ModernSoundWave with audio level stream
    return RepaintBoundary(
      child: ModernSoundWave(
        isActive: isRecording,
        amplitude: isRecording ? 0.8 : 0.2,
        audioLevelStream:
            isRecording ? _audioLevelService.audioLevelStream : null,
        height: widget.waveformHeight,
        colors: const [
          Color(0xFF6C63FF), // Deep purple
          Color(0xFF2ACAEA), // Electric blue
          Color(0xFF1FE2C1), // Neon turquoise
        ],
      ),
    );
  }
}
