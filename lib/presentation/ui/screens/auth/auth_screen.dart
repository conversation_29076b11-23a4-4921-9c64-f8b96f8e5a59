import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/di/service_locator.dart';
import 'package:projectpilot/core/widgets/animation_controller_mixin.dart';
import 'package:projectpilot/core/widgets/loading_display.dart';
import '../../bloc/auth/auth_cubit.dart';
import '../../bloc/auth/auth_state.dart' as app_auth;
import 'package:projectpilot/presentation/ui/screens/auth/onboarding_screen.dart';
import 'package:projectpilot/presentation/ui/screens/auth/widgets/animated_login_button.dart';

/// A unified authentication screen that combines login and signup
class AuthScreen extends StatelessWidget {
  /// Creates a unified auth screen
  const AuthScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Disable back navigation
      child: BlocProvider.value(
        value: sl<AuthCubit>(),
        child: const AuthView(),
      ),
    );
  }
}

/// Main view for the unified auth screen
class AuthView extends StatefulWidget {
  /// Creates a unified auth view
  const AuthView({super.key});

  @override
  State<AuthView> createState() => _AuthViewState();
}

class _FloatingElement {
  final double x;
  final double y;
  final double size;
  final Color color;
  final IconData? icon;
  final AnimationController controller;
  final Animation<double> animation;
  final double angle;
  final double speed;

  _FloatingElement({
    required this.x,
    required this.y,
    required this.size,
    required this.color,
    this.icon,
    required this.controller,
    required this.angle,
    required this.speed,
  }) : animation = Tween<double>(
         begin: 0.0,
         end: 1.0,
       ).animate(CurvedAnimation(parent: controller, curve: Curves.easeInOut));
}

class _AuthViewState extends State<AuthView>
    with TickerProviderStateMixin, StandardAnimationControllerMixin {
  bool _showEmailLogin = false;
  bool _isSignup = false;

  // Form keys for validation
  final _emailFormKey = GlobalKey<FormState>();

  // Controllers for form fields
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _nameController = TextEditingController();

  // Animation controllers for email form
  late AnimationController _emailFormAnimationController;
  late Animation<double> _emailFormAnimation;

  // Background animation controllers and elements
  late AnimationController _backgroundAnimationController;
  final List<_FloatingElement> _floatingElements = [];

  // Logo animation
  late AnimationController _logoAnimationController;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoGlowAnimation;

  // Visual elements for background
  final List<Color> _elementColors = [
    const Color(0xFF6C63FF), // Deep purple
    const Color(0xFF2ACAEA), // Electric blue
    const Color(0xFF1FE2C1), // Neon turquoise
  ];

  final List<IconData> _elementIcons = [
    Icons.mic,
    Icons.music_note,
    Icons.chat_bubble_outline,
    Icons.headphones,
    Icons.lightbulb_outline,
    Icons.edit_note,
    Icons.task_alt,
  ];

  final Random _random = Random();

  @override
  void initState() {
    super.initState();

    // Initialize standard animations
    initializeAnimations(
      duration: const Duration(milliseconds: 800),
      autoStart: true,
    );

    // Initialize email form animation
    _emailFormAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _emailFormAnimation = CurvedAnimation(
      parent: _emailFormAnimationController,
      curve: Curves.easeInOut,
    );

    // Initialize background animation
    _backgroundAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat(reverse: false);

    // Initialize logo animation
    _logoAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat(reverse: true);

    _logoScaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(
        parent: _logoAnimationController,
        curve: Curves.easeInOutSine,
      ),
    );

    _logoGlowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoAnimationController,
        curve: Curves.easeInOutSine,
      ),
    );

    // Create floating elements
    _createFloatingElements();
  }

  void _createFloatingElements() {
    // Create a variety of floating elements
    for (int i = 0; i < 12; i++) {
      final x = _random.nextDouble() * 2 - 1.0; // -1.0 to 1.0
      final y = _random.nextDouble() * 2 - 1.0; // -1.0 to 1.0
      final size = _random.nextDouble() * 20 + 10; // 10 to 30
      final color = _elementColors[_random.nextInt(_elementColors.length)]
          .withOpacity(_random.nextDouble() * 0.3 + 0.1); // opacity 0.1 to 0.4

      final hasIcon =
          _random.nextBool() && _random.nextBool(); // 25% chance for icon
      final IconData? icon =
          hasIcon ? _elementIcons[_random.nextInt(_elementIcons.length)] : null;

      final controller = AnimationController(
        vsync: this,
        duration: Duration(
          seconds: _random.nextInt(10) + 15,
        ), // 15 to 25 seconds
      );

      final angle = _random.nextDouble() * pi * 2; // 0 to 2π
      final speed = _random.nextDouble() * 0.4 + 0.3; // 0.3 to 0.7

      _floatingElements.add(
        _FloatingElement(
          x: x,
          y: y,
          size: size,
          color: color,
          icon: icon,
          controller: controller,
          angle: angle,
          speed: speed,
        ),
      );

      // Start with random offset
      controller.forward(from: _random.nextDouble());
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _nameController.dispose();
    _emailFormAnimationController.dispose();
    _backgroundAnimationController.dispose();
    _logoAnimationController.dispose();

    // Dispose all element controllers
    for (final element in _floatingElements) {
      element.controller.dispose();
    }
    
    super.dispose();
  }

  void _toggleEmailLogin() {
    setState(() {
      _showEmailLogin = !_showEmailLogin;
      if (_showEmailLogin) {
        _emailFormAnimationController.forward();
      } else {
        _emailFormAnimationController.reverse();
      }
    });
  }

  void _toggleSignupMode() {
    setState(() {
      _isSignup = !_isSignup;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    final authCubit = context.read<AuthCubit>();
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: AnimatedBuilder(
        animation: _backgroundAnimationController,
        builder: (context, _) {
          return BlocConsumer<AuthCubit, app_auth.AuthState>(
            listener: (context, state) {
              if (state.isAuthenticated) {
                // Navigate to main app when authenticated
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const OnboardingScreen(),
                  ),
                );
              } else if (state.hasError) {
                // Show error snackbar
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.errorMessage!),
                    backgroundColor: theme.colorScheme.error,
                  ),
                );
              }
            },
            builder: (context, state) {
              return Container(
                width: size.width,
                height: size.height,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.colorScheme.surface,
                      theme.colorScheme.surface.withOpacity(0.95),
                      theme.colorScheme.primary.withOpacity(0.05),
                      theme.colorScheme.surface.withOpacity(0.97),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Animated background elements
                    ..._buildBackgroundElements(size),

                    // Content
                    SafeArea(
                      child: Center(
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24.0,
                            ),
                            child: FadeTransition(
                              opacity: fadeAnimation,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  // Logo with glow effect
                                  AnimatedBuilder(
                                    animation: _logoAnimationController,
                                    builder: (context, child) {
                                      return Container(
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color: theme.colorScheme.primary
                                                  .withOpacity(
                                                    0.1 +
                                                        _logoGlowAnimation
                                                                .value *
                                                            0.2,
                                                  ),
                                              blurRadius:
                                                  30 +
                                                  _logoGlowAnimation.value * 20,
                                              spreadRadius:
                                                  5 +
                                                  _logoGlowAnimation.value * 10,
                                            ),
                                          ],
                                        ),
                                        child: Transform.scale(
                                          scale: _logoScaleAnimation.value,
                                          child: Image.asset(
                                            'assets/images/projectpilot.png',
                                            width: 150,
                                            height: 120,
                                          ),
                                        ),
                                      );
                                    },
                                  ),

                                  // App title
                                  ShaderMask(
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                        colors: [
                                          theme.colorScheme.primary,
                                          theme.colorScheme.tertiary,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ).createShader(bounds);
                                    },
                                    child: Text(
                                      'VoicePilot',
                                      style: theme.textTheme.headlineMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color:
                                                Colors
                                                    .white, // This color is overridden by the shader
                                            letterSpacing: 1.2,
                                          ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  
                                  // Subtitle
                                  Text(
                                    l10n.loginSubtitle,
                                    style: theme.textTheme.titleMedium
                                        ?.copyWith(
                                          color: theme.colorScheme.onSurface
                                              .withOpacity(0.7),
                                        ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 40),

                                  // Loading indicator
                                  if (state.isLoading)
                                    LoadingDisplay(
                                      message:
                                          l10n.authenticating ??
                                          'Authenticating...',
                                    ),

                                  // Social login options
                                  if (!state.isLoading) ...[
                                    // Google login
                                    AnimatedLoginButton(
                                      icon: Icons.g_mobiledata,
                                      text: l10n.continueWithGoogle,
                                      color: Colors.white,
                                      onPressed:
                                          () => authCubit.signInWithOAuth(
                                            'google',
                                          ),
                                    ),
                                    const SizedBox(height: 16),

                                    // Apple login
                                    AnimatedLoginButton(
                                      icon: Icons.apple,
                                      text: l10n.continueWithApple,
                                      color: Colors.black,
                                      textColor: Colors.white,
                                      onPressed:
                                          () => authCubit.signInWithOAuth(
                                            'apple',
                                          ),
                                    ),
                                    const SizedBox(height: 16),



                                    // LinkedIn login
                                    AnimatedLoginButton(
                                      icon: Icons.language,
                                      text: l10n.continueWithLinkedIn,
                                      color: const Color(0xFF0077B5),
                                      textColor: Colors.white,
                                      onPressed:
                                          () => authCubit.signInWithOAuth(
                                            'linkedin',
                                          ),
                                    ),
                                    const SizedBox(height: 24),

                                    // Divider with arrow
                                    GestureDetector(
                                      onTap: _toggleEmailLogin,
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Divider(
                                              color: theme.dividerColor,
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                            ),
                                            child: Row(
                                              children: [
                                                Text(
                                                  l10n.or,
                                                  style: theme
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.copyWith(
                                                        color: theme
                                                            .colorScheme
                                                            .onSurface
                                                            .withOpacity(0.6),
                                                      ),
                                                ),
                                                const SizedBox(width: 4),
                                                AnimatedRotation(
                                                  turns:
                                                      _showEmailLogin ? 0.5 : 0,
                                                  duration: const Duration(
                                                    milliseconds: 300,
                                                  ),
                                                  child: Icon(
                                                    Icons.arrow_drop_down,
                                                    color:
                                                        theme
                                                            .colorScheme
                                                            .primary,
                                                    size: 24,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            child: Divider(
                                              color: theme.dividerColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 16),

                                    // Email login/signup form
                                    SizeTransition(
                                      sizeFactor: _emailFormAnimation,
                                      child: Card(
                                        elevation: 10,
                                        shadowColor: theme.colorScheme.shadow
                                            .withOpacity(0.3),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            16,
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.all(16.0),
                                          child: Form(
                                            key: _emailFormKey,
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.stretch,
                                              children: [
                                                // Toggle between login and signup
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Text(
                                                      _isSignup
                                                          ? l10n.createAccount ??
                                                              'Create Account'
                                                          : l10n.signIn,
                                                      style: theme
                                                          .textTheme
                                                          .titleMedium
                                                          ?.copyWith(
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                    ),
                                                    TextButton(
                                                      onPressed:
                                                          _toggleSignupMode,
                                                      child: Text(
                                                        _isSignup
                                                            ? l10n.signIn
                                                            : l10n.createAccount ??
                                                                'Create account',
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 8),

                                                // Name field (signup only)
                                                if (_isSignup)
                                                  TextFormField(
                                                    controller: _nameController,
                                                    decoration: InputDecoration(
                                                      labelText: l10n.username,
                                                      hintText:
                                                          l10n.nameFieldHint,
                                                      prefixIcon: const Icon(
                                                        Icons.person_outline,
                                                      ),
                                                      border: OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              12,
                                                            ),
                                                      ),
                                                    ),
                                                    validator: (value) {
                                                      if (value == null ||
                                                          value.isEmpty) {
                                                        return l10n
                                                            .usernameRequired;
                                                      }
                                                      return null;
                                                    },
                                                  ),
                                                if (_isSignup)
                                                  const SizedBox(height: 16),

                                                // Email field
                                                TextFormField(
                                                  controller: _emailController,
                                                  decoration: InputDecoration(
                                                    labelText: l10n.email,
                                                    hintText:
                                                        l10n.emailFieldHint,
                                                    prefixIcon: const Icon(
                                                      Icons.email_outlined,
                                                    ),
                                                    border: OutlineInputBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            12,
                                                          ),
                                                    ),
                                                  ),
                                                  keyboardType:
                                                      TextInputType
                                                          .emailAddress,
                                                  validator: (value) {
                                                    if (value == null ||
                                                        value.isEmpty) {
                                                      return l10n.emailRequired;
                                                    }
                                                    return null;
                                                  },
                                                ),
                                                const SizedBox(height: 16),
                                                
                                                // Password field
                                                TextFormField(
                                                  controller:
                                                      _passwordController,
                                                  decoration: InputDecoration(
                                                    labelText: l10n.password,
                                                    hintText:
                                                        l10n.passwordFieldHint,
                                                    prefixIcon: const Icon(
                                                      Icons.lock_outline,
                                                    ),
                                                    border: OutlineInputBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            12,
                                                          ),
                                                    ),
                                                  ),
                                                  obscureText: true,
                                                  validator: (value) {
                                                    if (value == null ||
                                                        value.isEmpty) {
                                                      return l10n
                                                          .passwordRequired;
                                                    }
                                                    if (_isSignup &&
                                                        value.length < 8) {
                                                      return l10n
                                                          .passwordMinLength;
                                                    }
                                                    return null;
                                                  },
                                                ),
                                                const SizedBox(height: 16),
                                                
                                                // Confirm password field (signup only)
                                                if (_isSignup)
                                                  TextFormField(
                                                    controller:
                                                        _confirmPasswordController,
                                                    decoration: InputDecoration(
                                                      labelText:
                                                          l10n.confirmPassword,
                                                      hintText:
                                                          l10n.confirmPasswordHint,
                                                      prefixIcon: const Icon(
                                                        Icons.lock_outline,
                                                      ),
                                                      border: OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              12,
                                                            ),
                                                      ),
                                                    ),
                                                    obscureText: true,
                                                    validator: (value) {
                                                      if (value == null ||
                                                          value.isEmpty) {
                                                        return l10n
                                                            .confirmPasswordRequired;
                                                      }
                                                      if (value !=
                                                          _passwordController
                                                              .text) {
                                                        return l10n
                                                            .passwordsDoNotMatch;
                                                      }
                                                      return null;
                                                    },
                                                  ),
                                                if (_isSignup)
                                                  const SizedBox(height: 16),

                                                // Submit button
                                                SizedBox(
                                                  height: 50,
                                                  child: ElevatedButton(
                                                    onPressed: () {
                                                      if (_emailFormKey
                                                          .currentState!
                                                          .validate()) {
                                                        if (_isSignup) {
                                                          authCubit
                                                              .signUpWithEmail(
                                                                _emailController
                                                                    .text,
                                                                _passwordController
                                                                    .text,
                                                              );
                                                        } else {
                                                          authCubit
                                                              .signInWithEmail(
                                                                _emailController
                                                                    .text,
                                                                _passwordController
                                                                    .text,
                                                              );
                                                        }
                                                      }
                                                    },
                                                    style: ElevatedButton.styleFrom(
                                                      backgroundColor:
                                                          theme
                                                              .colorScheme
                                                              .primary,
                                                      foregroundColor:
                                                          theme
                                                              .colorScheme
                                                              .onPrimary,
                                                      shape: RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              12,
                                                            ),
                                                      ),
                                                      elevation: 2,
                                                    ),
                                                    child: Text(
                                                      _isSignup
                                                          ? l10n.createAccount
                                                          : l10n.login,
                                                      style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                  ),
                                                ),

                                                if (!_isSignup) ...[
                                                  const SizedBox(height: 12),
                                                  Align(
                                                    alignment: Alignment.center,
                                                    child: TextButton(
                                                      onPressed: () {
                                                        // Handle forgot password
                                                      },
                                                      child: Text(
                                                        l10n.forgotPassword,
                                                        style: TextStyle(
                                                          color:
                                                              theme
                                                                  .colorScheme
                                                                  .primary,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  List<Widget> _buildBackgroundElements(Size size) {
    final elements = <Widget>[];

    for (int i = 0; i < _floatingElements.length; i++) {
      final element = _floatingElements[i];
      final value = element.controller.value;

      // Calculate position with oscillating motion
      final time =
          _backgroundAnimationController.value * 2 * pi * element.speed +
          element.angle;
      final dx = sin(time) * 20;
      final dy = cos(time) * 20;

      // Position element based on relative coordinates
      final xPos = size.width * (element.x * 0.8 + 0.5) + dx;
      final yPos = size.height * (element.y * 0.8 + 0.5) + dy;

      // Add either shape or icon
      if (element.icon != null) {
        elements.add(
          Positioned(
            left: xPos - element.size / 2,
            top: yPos - element.size / 2,
            child: Icon(
              element.icon,
              size: element.size * 2,
              color: element.color,
            ),
          ),
        );
      } else {
        // Determine shape type
        final shapeIndex = (i % 3);
        Widget shape;

        switch (shapeIndex) {
          case 0: // Circle
            shape = Container(
              width: element.size,
              height: element.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: element.color,
              ),
            );
            break;
          case 1: // Square
            shape = Transform.rotate(
              angle: time / 2,
              child: Container(
                width: element.size,
                height: element.size,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(element.size / 8),
                  color: element.color,
                ),
              ),
            );
            break;
          case 2: // Triangle
          default:
            shape = CustomPaint(
              size: Size(element.size, element.size),
              painter: _TrianglePainter(element.color, time),
            );
            break;
        }

        elements.add(
          Positioned(
            left: xPos - element.size / 2,
            top: yPos - element.size / 2,
            child: shape,
          ),
        );
      }
    }

    return elements;
  }
}

class _TrianglePainter extends CustomPainter {
  final Color color;
  final double angle;

  _TrianglePainter(this.color, this.angle);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final path = Path();
    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // Rotate triangle
    canvas.save();
    canvas.translate(centerX, centerY);
    canvas.rotate(angle);
    canvas.translate(-centerX, -centerY);

    // Draw triangle
    path.moveTo(centerX, 0);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
    canvas.restore();
  }
  
  @override
  bool shouldRepaint(_TrianglePainter oldDelegate) =>
      color != oldDelegate.color || angle != oldDelegate.angle;
}
