import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/di/service_locator.dart';
import 'package:projectpilot/presentation/ui/bloc/auth/auth_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/auth/auth_state.dart' as app_auth;
import 'package:projectpilot/presentation/ui/bloc/onboarding/onboarding_cubit.dart';
import 'package:projectpilot/presentation/ui/screens/auth/platform_connection_screen.dart';

/// User role options for onboarding
enum UserRole { developer, projectManager, productOwner, qaTester, ctoCeo }

/// User goals for ProjectPilot usage
enum UserGoal {
  organizeTasksEfficiently,
  trackProjectProgress,
  receiveRegularUpdates,
  makeSmarterDecisions,
  summarizeMeetings,
  manageDeadlinesAndBudgets,
}

/// Onboarding screen after sign-up
class OnboardingScreen extends StatelessWidget {
  /// Creates an onboarding screen
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: sl<AuthCubit>()),
        BlocProvider.value(value: sl<OnboardingCubit>()),
      ],
      child: const OnboardingView(),
    );
  }
}

/// Main view for the onboarding screen
class OnboardingView extends StatefulWidget {
  /// Creates an onboarding view
  const OnboardingView({super.key});

  @override
  State<OnboardingView> createState() => _OnboardingViewState();
}

class _OnboardingViewState extends State<OnboardingView>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  bool _isFormValid = false;
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isNavigating = false; // Prevent multiple navigations

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _animationController.forward();
    
    // Initialize user data from OAuth provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OnboardingCubit>().initializeFromOAuthUser();
    });
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _pickImage(ImageSource source) async {
    final picker = ImagePicker();
    final pickedImage = await picker.pickImage(
      source: source,
      maxWidth: 1024,
      maxHeight: 1024,
      imageQuality: 90,
    );

    if (pickedImage != null && mounted) {
      // Crop the image before setting it
      final croppedImage = await context.read<OnboardingCubit>().cropImage(
        File(pickedImage.path),
      );
      if (croppedImage != null && mounted && !_isNavigating) {
        context.read<OnboardingCubit>().updateAvatar(croppedImage);
      }
    }
  }

  void _showImageSourceDialog() {
    final l10n = AppLocalizations.of(context);
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: Text(l10n.camera),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: Text(l10n.gallery),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showBirthYearPicker() {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    // Calculate year range
    final currentYear = DateTime.now().year;
    final minYear = currentYear - 100;
    final maxYear = currentYear - 13; // Minimum age 13

    // Get current selected birth year from cubit
    final currentBirthYear = context.read<OnboardingCubit>().state.birthYear;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 300,
          color: theme.colorScheme.surface,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  border: Border(
                    bottom: BorderSide(
                      color: theme.colorScheme.outline.withValues(alpha: 0.2),
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      l10n.selectBirthYear,
                      style: theme.textTheme.titleSmall,
                    ),
                    IconButton(
                      icon: const Icon(Icons.check),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoPicker(
                  scrollController: FixedExtentScrollController(
                    initialItem: maxYear - currentBirthYear,
                  ),
                  itemExtent: 40,
                  backgroundColor: theme.colorScheme.surface,
                  onSelectedItemChanged: (int index) {
                    if (mounted && !_isNavigating) {
                      final selectedYear = maxYear - index;
                      context.read<OnboardingCubit>().updateBirthYear(
                        selectedYear,
                      );
                    }
                  },
                  children: List<Widget>.generate(maxYear - minYear + 1, (
                    int index,
                  ) {
                    final year = maxYear - index;
                    return Center(
                      child: Text(
                        year.toString(),
                        style: theme.textTheme.bodyLarge,
                      ),
                    );
                  }),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _validateForm() {
    if (!mounted) return;

    // Get current state from cubit to validate
    final currentState = context.read<OnboardingCubit>().state;
    
    setState(() {
      _isFormValid = _formKey.currentState?.validate() ?? false;
      
      // Validate based on current page using cubit state
      switch (_currentPage) {
        case 0: // Profile page
          _isFormValid = _isFormValid && _usernameController.text.isNotEmpty;
          break;
        case 1: // User role page
          _isFormValid = currentState.selectedRole != null;
          break;
        case 2: // User goals page
          _isFormValid = currentState.selectedGoals.isNotEmpty;
          break;
      }
    });
  }

  void _nextPage() {
    if (_currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
    } else {
      _navigateToNextScreen();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
    }
  }

  void _navigateToNextScreen() {
    if (_isNavigating) return; // Prevent multiple navigations
    
    // Update username from controller
    final username = _usernameController.text.trim();
    if (username.isNotEmpty && mounted) {
      context.read<OnboardingCubit>().updateUsername(username);
    }

    setState(() {
      _isNavigating = true;
    });

    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const PlatformConnectionScreen()),
    );
  }

  String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.developer:
        return 'Developer';
      case UserRole.projectManager:
        return 'Project Manager';
      case UserRole.productOwner:
        return 'Product Owner';
      case UserRole.qaTester:
        return 'QA Tester';
      case UserRole.ctoCeo:
        return 'CTO / CEO';
    }
  }

  String _getRoleDescription(UserRole role) {
    switch (role) {
      case UserRole.developer:
        return 'Ich programmiere und entwickle Software';
      case UserRole.projectManager:
        return 'Ich plane und organisiere Projekte';
      case UserRole.productOwner:
        return 'Ich definiere Anforderungen und Ziele';
      case UserRole.qaTester:
        return 'Ich teste und verbessere Qualität';
      case UserRole.ctoCeo:
        return 'Ich treffe strategische Entscheidungen';
    }
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.developer:
        return Icons.code;
      case UserRole.projectManager:
        return Icons.assignment;
      case UserRole.productOwner:
        return Icons.lightbulb;
      case UserRole.qaTester:
        return Icons.bug_report;
      case UserRole.ctoCeo:
        return Icons.business;
    }
  }

  String _getGoalDisplayName(UserGoal goal) {
    switch (goal) {
      case UserGoal.organizeTasksEfficiently:
        return 'Ich will meine Aufgaben organisiert sehen';
      case UserGoal.trackProjectProgress:
        return 'Ich will wissen, warum mein Projekt nicht vorankommt';
      case UserGoal.receiveRegularUpdates:
        return 'Ich will regelmäßig über Fortschritt informiert werden';
      case UserGoal.makeSmarterDecisions:
        return 'Ich will smartere Entscheidungen treffen';
      case UserGoal.summarizeMeetings:
        return 'Ich will Meetings zusammenfassen lassen';
      case UserGoal.manageDeadlinesAndBudgets:
        return 'Ich will Deadlines, Blocker & Budgets im Blick behalten';
    }
  }

  IconData _getGoalIcon(UserGoal goal) {
    switch (goal) {
      case UserGoal.organizeTasksEfficiently:
        return Icons.task_alt;
      case UserGoal.trackProjectProgress:
        return Icons.trending_up;
      case UserGoal.receiveRegularUpdates:
        return Icons.notifications_active;
      case UserGoal.makeSmarterDecisions:
        return Icons.psychology;
      case UserGoal.summarizeMeetings:
        return Icons.record_voice_over;
      case UserGoal.manageDeadlinesAndBudgets:
        return Icons.schedule;
    }
  }

  Color _getGoalColor(UserGoal goal) {
    switch (goal) {
      case UserGoal.organizeTasksEfficiently:
        return Colors.blue;
      case UserGoal.trackProjectProgress:
        return Colors.green;
      case UserGoal.receiveRegularUpdates:
        return Colors.orange;
      case UserGoal.makeSmarterDecisions:
        return Colors.purple;
      case UserGoal.summarizeMeetings:
        return Colors.teal;
      case UserGoal.manageDeadlinesAndBudgets:
        return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return PopScope(
      canPop: false, // Prevent going back during onboarding
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (!didPop) {
          // If user tries to go back and we're not on first page, go to previous page
          if (_currentPage > 0) {
            _previousPage();
          }
          // If we're on first page, do nothing (prevent exit)
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            l10n.onboarding,
            style: TextStyle(color: theme.colorScheme.onSurface),
          ),
          leading:
              _currentPage > 0
                  ? IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: _previousPage,
                  )
                  : null,
          automaticallyImplyLeading: false, // Disable default back button
        ),
        body: BlocConsumer<OnboardingCubit, OnboardingState>(
          listener: (context, state) {
            // Update username controller if username changes externally
            if (state.username.isNotEmpty &&
                _usernameController.text != state.username) {
              _usernameController.text = state.username;
            }

            // Validate form when state changes (role selection, goal selection)
            _validateForm();

            // Navigate when onboarding is completed (only if not already navigating)
            if (state.isCompleted && !_isNavigating && mounted) {
              setState(() {
                _isNavigating = true;
              });
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PlatformConnectionScreen(),
                ),
              );
            }
          },
          builder: (context, state) {
            return SafeArea(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    // Progress indicator
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Row(
                        children: List.generate(3, (index) {
                          return Expanded(
                            child: Container(
                              height: 4,
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              decoration: BoxDecoration(
                                color:
                                    index <= _currentPage
                                        ? theme.colorScheme.primary
                                        : theme
                                            .colorScheme
                                            .surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Main content
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        physics: const NeverScrollableScrollPhysics(),
                        onPageChanged: (page) {
                          setState(() {
                            _currentPage = page;
                          });
                          _validateForm();
                        },
                        children: [
                          _buildProfilePage(context, state, theme, l10n),
                          _buildRolePage(context, state, theme, l10n),
                          _buildGoalsPage(context, state, theme, l10n),
                        ],
                      ),
                    ),

                    // Next button
                    Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: _buildNextButton(theme, l10n, state),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfilePage(
    BuildContext context,
    OnboardingState state,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        onChanged: _validateForm,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Page title with animation
            SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, -0.1),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
                ),
              ),
              child: Text(
                'Willkommen bei ProjectPilot',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 8),

            SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, -0.1),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.1, 0.7, curve: Curves.easeOut),
                ),
              ),
              child: Text(
                'Erzähl uns ein wenig über dich',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 32),

            // Avatar upload with animation
            SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 0.2),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.3, 0.8, curve: Curves.easeOut),
                ),
              ),
              child: GestureDetector(
                onTap: _showImageSourceDialog,
                child: Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: theme.colorScheme.primary.withOpacity(0.2),
                            blurRadius: 15,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: CircleAvatar(
                        radius: 60,
                        backgroundColor:
                            theme.colorScheme.surfaceContainerHighest,
                        backgroundImage:
                            state.avatarImage != null
                                ? FileImage(state.avatarImage!)
                                : null,
                        child:
                            state.avatarImage == null
                                ? Icon(
                                  Icons.person,
                                  size: 60,
                                  color: theme.colorScheme.onSurfaceVariant,
                                )
                                : null,
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: theme.colorScheme.shadow.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.camera_alt,
                          color: theme.colorScheme.onPrimary,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),
            
            // Username field with animation
            SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 0.3),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.4, 0.9, curve: Curves.easeOut),
                ),
              ),
              child: TextFormField(
                controller: _usernameController,
                decoration: InputDecoration(
                  labelText: l10n.username,
                  hintText: 'Gib deinen gewünschten Namen ein',
                  prefixIcon: const Icon(Icons.person_outline),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surface,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.usernameRequired;
                  }
                  if (value.length < 3) {
                    return l10n.usernameMinLength;
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 24),

            // Birth Year with animation
            SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 0.4),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
                ),
              ),
              child: InkWell(
                onTap: _showBirthYearPicker,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 18,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: theme.colorScheme.outline.withOpacity(0.5),
                    ),
                    borderRadius: BorderRadius.circular(12),
                    color: theme.colorScheme.surface,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              l10n.birthYear,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            Text(
                              state.birthYear.toString(),
                              style: theme.textTheme.bodyLarge,
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_drop_down,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRolePage(
    BuildContext context,
    OnboardingState state,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Page title
          Text(
            'Welche Rolle beschreibt dich am besten?',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Wähle eine Rolle aus, die am besten zu dir passt',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),

          // Role options with staggered animations
          ...UserRole.values.asMap().entries.map((entry) {
            final index = entry.key;
            final role = entry.value;

            final roleText = _getRoleDisplayName(role);
            final roleDescription = _getRoleDescription(role);
            final roleIcon = _getRoleIcon(role);
            
            final isSelected = state.selectedRole == role;

            return TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: 1.0),
              duration: Duration(milliseconds: 300 + (index * 100)),
              curve: Curves.easeOut,
              builder: (context, value, child) {
                return Transform.translate(
                  offset: Offset(0, 20 * (1 - value)),
                  child: Opacity(
                    opacity: value,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: InkWell(
                        onTap: () {
                          if (mounted && !_isNavigating) {
                            context.read<OnboardingCubit>().updateUserRole(
                              role,
                            );
                          }
                        },
                        borderRadius: BorderRadius.circular(16),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color:
                                  isSelected
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.outline.withOpacity(
                                        0.3,
                                      ),
                              width: isSelected ? 2 : 1,
                            ),
                            color:
                                isSelected
                                    ? theme.colorScheme.primary.withOpacity(0.1)
                                    : theme.colorScheme.surface,
                            boxShadow:
                                isSelected
                                    ? [
                                      BoxShadow(
                                        color: theme.colorScheme.primary
                                            .withOpacity(0.2),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ]
                                    : null,
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color:
                                      isSelected
                                          ? theme.colorScheme.primary
                                              .withOpacity(0.2)
                                          : theme
                                              .colorScheme
                                              .surfaceContainerHighest,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  roleIcon,
                                  color:
                                      isSelected
                                          ? theme.colorScheme.primary
                                          : theme.colorScheme.onSurfaceVariant,
                                  size: 32,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      roleText,
                                      style: theme.textTheme.titleMedium
                                          ?.copyWith(
                                            fontWeight:
                                                isSelected
                                                    ? FontWeight.bold
                                                    : FontWeight.w600,
                                            color:
                                                isSelected
                                                    ? theme.colorScheme.primary
                                                    : theme
                                                        .colorScheme
                                                        .onSurface,
                                          ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      roleDescription,
                                      style: theme.textTheme.bodyMedium
                                          ?.copyWith(
                                            color: theme.colorScheme.onSurface
                                                .withOpacity(0.7),
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  Icons.check_circle,
                                  color: theme.colorScheme.primary,
                                  size: 32,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          }),
        ],
      ),
    );
  }

  Widget _buildGoalsPage(
    BuildContext context,
    OnboardingState state,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Page title
          Text(
            'Was möchtest du mit ProjectPilot erreichen?',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Wähle alle Ziele aus, die zu dir passen',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.3),
              ),
            ),
            child: Text(
              'Mehrfachauswahl möglich',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
          const SizedBox(height: 32),

          // Goal options with staggered animations
          ...UserGoal.values.asMap().entries.map((entry) {
            final index = entry.key;
            final goal = entry.value;

            final goalText = _getGoalDisplayName(goal);
            final goalIcon = _getGoalIcon(goal);
            final goalColor = _getGoalColor(goal);
            
            final isSelected = state.selectedGoals.contains(goal);

            return TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: 1.0),
              duration: Duration(milliseconds: 300 + (index * 100)),
              curve: Curves.easeOut,
              builder: (context, value, child) {
                return Transform.translate(
                  offset: Offset(0, 20 * (1 - value)),
                  child: Opacity(
                    opacity: value,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: InkWell(
                        onTap: () {
                          if (mounted && !_isNavigating) {
                            context.read<OnboardingCubit>().toggleUserGoal(
                              goal,
                            );
                          }
                        },
                        borderRadius: BorderRadius.circular(16),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color:
                                  isSelected
                                      ? goalColor
                                      : theme.colorScheme.outline.withOpacity(
                                        0.3,
                                      ),
                              width: isSelected ? 2 : 1,
                            ),
                            color:
                                isSelected
                                    ? goalColor.withOpacity(0.1)
                                    : theme.colorScheme.surface,
                            boxShadow:
                                isSelected
                                    ? [
                                      BoxShadow(
                                        color: goalColor.withOpacity(0.2),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ]
                                    : null,
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color:
                                      isSelected
                                          ? goalColor.withOpacity(0.2)
                                          : theme
                                              .colorScheme
                                              .surfaceContainerHighest,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  goalIcon,
                                  color:
                                      isSelected
                                          ? goalColor
                                          : theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Text(
                                  goalText,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight:
                                        isSelected
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                    color:
                                        isSelected
                                            ? goalColor
                                            : theme.colorScheme.onSurface,
                                  ),
                                ),
                              ),
                              if (isSelected)
                                AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  child: Icon(
                                    Icons.check_circle,
                                    color: goalColor,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          }),
        ],
      ),
    );
  }

  Widget _buildNextButton(
    ThemeData theme,
    AppLocalizations l10n,
    OnboardingState state,
  ) {
    final isLastPage = _currentPage == 2;
    final buttonLabel = isLastPage ? 'Onboarding abschließen' : 'Weiter';

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient:
            _isFormValid
                ? LinearGradient(
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.tertiary,
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                )
                : null,
        color:
            _isFormValid
                ? null
                : theme.colorScheme.primary.withOpacity(0.6),
        boxShadow:
            _isFormValid
                ? [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ]
                : null,
      ),
      child: ElevatedButton(
        onPressed: _isFormValid ? _nextPage : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          disabledBackgroundColor: Colors.transparent,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child:
            state.isLoading
                ? CircularProgressIndicator(color: theme.colorScheme.onPrimary)
                : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      buttonLabel,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (!isLastPage) ...[
                      const SizedBox(width: 8),
                      Icon(
                        Icons.arrow_forward,
                        color: theme.colorScheme.onPrimary,
                        size: 18,
                      ),
                    ],
                  ],
                ),
      ),
    );
  }
}
