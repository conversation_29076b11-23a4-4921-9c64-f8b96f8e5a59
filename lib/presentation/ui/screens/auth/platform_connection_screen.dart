import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/di/service_locator.dart';
import 'package:projectpilot/core/utils/navigation_helper.dart';
import 'package:projectpilot/presentation/ui/bloc/auth/auth_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/onboarding/onboarding_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/platform_connections/platform_connections_cubit.dart';

/// Platform options for connection
enum PlatformType { clickup, notion, monday, other }

/// Platform connection screen to connect external services
class PlatformConnectionScreen extends StatelessWidget {
  /// Creates a platform connection screen
  const PlatformConnectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: sl<AuthCubit>()),
        BlocProvider.value(value: sl<OnboardingCubit>()),
        BlocProvider.value(value: sl<PlatformConnectionsCubit>()),
      ],
      child: const PlatformConnectionView(),
    );
  }
}

/// Main view for the platform connection screen
class PlatformConnectionView extends StatefulWidget {
  /// Creates a platform connection view
  const PlatformConnectionView({super.key});

  @override
  State<PlatformConnectionView> createState() => _PlatformConnectionViewState();
}

class _PlatformConnectionViewState extends State<PlatformConnectionView>
    with SingleTickerProviderStateMixin {
  final Set<PlatformType> _selectedPlatforms = {};
  final bool _isSkippable = true;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _togglePlatform(PlatformType platform) {
    setState(() {
      if (_selectedPlatforms.contains(platform)) {
        _selectedPlatforms.remove(platform);
      } else {
        _selectedPlatforms.add(platform);
      }
    });
  }

  void _finishOnboarding() async {
    // Save selected platforms
    final onboardingCubit = context.read<OnboardingCubit>();
    final authCubit = context.read<AuthCubit>();

    // Save selected platforms in onboarding cubit
    for (var platform in _selectedPlatforms) {
      onboardingCubit.togglePlatform(platform);
    }

    // Get the current user
    final userState = authCubit.state;
    if (userState.isAuthenticated && userState.user != null) {
      try {
        // Show loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                ),
                SizedBox(width: 16),
                Text('Completing setup...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
        
        // Mark onboarding as completed
        await onboardingCubit.saveOnboardingData();

        // Navigate to home screen
        NavigationHelper.navigateToHome(context);
      } catch (e) {
        // Show error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing setup: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } else {
      // If not authenticated, show error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('You must be logged in to complete setup.'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  // Helper to convert PlatformType enum to string
  String platformTypeToString(PlatformType platform) {
    switch (platform) {
      case PlatformType.clickup:
        return 'clickup';
      case PlatformType.notion:
        return 'notion';

      case PlatformType.monday:
        return 'monday';
      case PlatformType.other:
        return 'other';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return PopScope(
      canPop: false, // Prevent going back during platform connection
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (!didPop) {
          // Don't allow going back - must complete or skip
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            l10n.connectPlatforms ?? 'Connect Platforms',
            style: TextStyle(color: theme.colorScheme.onSurface),
          ),
          automaticallyImplyLeading: false, // Disable default back button
          actions:
              _isSkippable
                  ? [
                    TextButton(
                      onPressed: _finishOnboarding,
                      child: Text(
                        l10n.skip ?? 'Skip',
                        style: TextStyle(color: theme.colorScheme.primary),
                      ),
                    ),
                  ]
                  : null,
        ),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.platformsDescription ??
                      'Connect to your favorite platforms',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
                const SizedBox(height: 24),

                Expanded(
                  child: ListView(
                    children: [
                      _buildPlatformCard(
                        theme,
                        PlatformType.clickup,
                        'ClickUp',
                        'Integrate tasks and projects',
                        Icons.task_alt,
                        Colors.deepPurple,
                      ),
                      _buildPlatformCard(
                        theme,
                        PlatformType.notion,
                        'Notion',
                        'Connect notes and databases',
                        Icons.text_snippet_outlined,
                        Colors.black,
                      ),

                      _buildPlatformCard(
                        theme,
                        PlatformType.monday,
                        'Monday',
                        'Manage projects and workflows',
                        Icons.view_week_outlined,
                        Colors.blue,
                      ),
                      _buildPlatformCard(
                        theme,
                        PlatformType.other,
                        l10n.other ?? 'Other',
                        l10n.connectMore ?? 'Connect more platforms later',
                        Icons.add_circle_outline,
                        Colors.grey,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Finish Button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _finishOnboarding,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      l10n.finish ?? 'Finish',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      ),
    );
  }

  Widget _buildPlatformCard(
    ThemeData theme,
    PlatformType platform,
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    final isSelected = _selectedPlatforms.contains(platform);

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        onTap: () => _togglePlatform(platform),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.outline.withValues(alpha: 0.3),
              width: isSelected ? 2 : 1,
            ),
            color:
                isSelected
                    ? theme.colorScheme.primary.withValues(alpha: 0.1)
                    : theme.colorScheme.surface,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Checkbox(
                value: isSelected,
                onChanged: (value) => _togglePlatform(platform),
                activeColor: theme.colorScheme.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
