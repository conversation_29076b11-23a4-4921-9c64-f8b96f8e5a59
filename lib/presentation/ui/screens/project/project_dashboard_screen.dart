import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/widgets/base_scaffold.dart';
import '../../bloc/project/project_cubit.dart';
import '../../bloc/project_analysis/project_analysis_cubit.dart';
import '../../bloc/project_query/project_query_cubit.dart';
import 'components/project_dashboard_content_view.dart';

/// Main project dashboard screen showing projects overview and management
class ProjectDashboardScreen extends StatelessWidget {
  const ProjectDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ProjectCubit>(
          create: (context) => sl<ProjectCubit>()..loadProjects(),
        ),
        BlocProvider<ProjectAnalysisCubit>(
          create: (context) => sl<ProjectAnalysisCubit>(),
        ),
        BlocProvider<ProjectQueryCubit>(
          create: (context) => sl<ProjectQueryCubit>(),
        ),
      ],
      child: const ProjectDashboardView(),
    );
  }
}

/// Project dashboard view implementation
class ProjectDashboardView extends StatelessWidget {
  const ProjectDashboardView({super.key});

  @override
  Widget build(BuildContext context) {
    return BaseScaffold.withTitle(
      title: 'ProjectPilot',
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => _showProjectSearch(context),
        ),
        IconButton(
          icon: const Icon(Icons.add),
          onPressed: () => _showAddProjectDialog(context),
        ),
      ],
      body: const ProjectDashboardContentView(),
    );
  }

  void _showProjectSearch(BuildContext context) {
    // TODO: Implement project search
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Project search coming soon!')),
    );
  }

  void _showAddProjectDialog(BuildContext context) {
    // TODO: Implement add project dialog
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Add project coming soon!')));
  }
}
