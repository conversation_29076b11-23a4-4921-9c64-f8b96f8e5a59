import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/widgets/base_scaffold.dart';
import '../../../../l10n/app_localizations.dart';
import '../../bloc/project/project_cubit.dart';
import '../../bloc/project_query/project_query_cubit.dart';

/// Screen for natural language project queries
class ProjectQueryScreen extends StatefulWidget {
  const ProjectQueryScreen({super.key});

  @override
  State<ProjectQueryScreen> createState() => _ProjectQueryScreenState();
}

class _ProjectQueryScreenState extends State<ProjectQueryScreen> {
  final TextEditingController _queryController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _queryController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ProjectCubit>(
          create: (context) => sl<ProjectCubit>()..loadProjects(),
        ),
        BlocProvider<ProjectQueryCubit>(
          create: (context) => sl<ProjectQueryCubit>(),
        ),
      ],
      child: ProjectQueryView(
        queryController: _queryController,
        scrollController: _scrollController,
      ),
    );
  }
}

/// Project query view implementation
class ProjectQueryView extends StatelessWidget {
  final TextEditingController queryController;
  final ScrollController scrollController;

  const ProjectQueryView({
    super.key,
    required this.queryController,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return BaseScaffold.withTitle(
      title: l10n.aiProjectAssistant,
      body: Column(
        children: [
          // Query history
          Expanded(
            child: BlocBuilder<ProjectQueryCubit, ProjectQueryState>(
              builder: (context, state) {
                if (state is ProjectQueryProcessing) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is ProjectQueryError) {
                  return _buildErrorState(context, state.message, l10n);
                }

                if (state is ProjectQueryResult) {
                  return _buildQueryHistory(context, state, theme);
                }

                return _buildWelcomeState(context);
              },
            ),
          ),

          // Query input
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: _buildQueryInput(context),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(
    BuildContext context,
    String message,
    AppLocalizations l10n,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(l10n.error, style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.read<ProjectQueryCubit>().clearHistory(),
            child: Text(l10n.tryAgain),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeState(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.psychology,
              size: 48,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'AI Project Assistant',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Ask me anything about your projects! I can help with:',
            style: theme.textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          _buildSuggestionChips(context),
        ],
      ),
    );
  }

  Widget _buildSuggestionChips(BuildContext context) {
    final suggestions = [
      'What projects are behind schedule?',
      'Show me project risks',
      'Which tasks are blocked?',
      'Project progress summary',
      'Team workload analysis',
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      alignment: WrapAlignment.center,
      children:
          suggestions.map((suggestion) {
            return ActionChip(
              label: Text(suggestion),
              onPressed: () {
                queryController.text = suggestion;
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('Query: $suggestion')));
              },
            );
          }).toList(),
    );
  }

  Widget _buildQueryHistory(
    BuildContext context,
    ProjectQueryResult state,
    ThemeData theme,
  ) {
    if (state.queryHistory.isEmpty) {
      return _buildWelcomeState(context);
    }

    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: state.queryHistory.length,
      itemBuilder: (context, index) {
        final query = state.queryHistory[index];
        return _buildQueryItem(context, query, theme);
      },
    );
  }

  Widget _buildQueryItem(
    BuildContext context,
    QueryHistoryItem query,
    ThemeData theme,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User query
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.person, color: theme.colorScheme.primary, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    query.query,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // AI response
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.psychology,
                  color: theme.colorScheme.secondary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    query.response,
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQueryInput(BuildContext context) {
    return BlocBuilder<ProjectQueryCubit, ProjectQueryState>(
      builder: (context, state) {
        final isLoading = state is ProjectQueryProcessing;

        return Row(
          children: [
            Expanded(
              child: TextField(
                controller: queryController,
                decoration: InputDecoration(
                  hintText: 'Ask about your projects...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: isLoading ? null : (_) => _submitQuery(context),
              ),
            ),
            const SizedBox(width: 8),
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).colorScheme.primary,
              ),
              child: IconButton(
                onPressed: isLoading ? null : () => _submitQuery(context),
                icon:
                    isLoading
                        ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Theme.of(context).colorScheme.onPrimary,
                          ),
                        )
                        : Icon(
                          Icons.send,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _submitQuery(BuildContext context) {
    final query = queryController.text.trim();
    if (query.isEmpty) return;

    // For now, show a placeholder message since we need project context
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Query submitted: $query'),
        action: SnackBarAction(label: 'Dismiss', onPressed: () {}),
      ),
    );

    queryController.clear();
  }
}
