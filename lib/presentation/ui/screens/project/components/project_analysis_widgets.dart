import 'package:flutter/material.dart';
import '../../../../../domain/entities/project_analysis.dart';
import '../../../../../domain/entities/project_task.dart';
import '../../../../../l10n/app_localizations.dart';

/// Widget for displaying project blockers
class ProjectBlockersWidget extends StatelessWidget {
  final List<ProjectBlocker> blockers;

  const ProjectBlockersWidget({super.key, required this.blockers});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.block, color: theme.colorScheme.error),
                const SizedBox(width: 8),
                Text(
                  l10n.blockers,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${blockers.length}',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.error,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...blockers.map(
              (blocker) => _buildBlockerItem(context, theme, blocker, l10n),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBlockerItem(
    BuildContext context,
    ThemeData theme,
    ProjectBlocker blocker,
    AppLocalizations l10n,
  ) {
    final severityColor = _getSeverityColor(theme, blocker.severity);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(left: BorderSide(width: 4, color: severityColor)),
        color: severityColor.withOpacity(0.05),
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  blocker.taskName,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: severityColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  blocker.severity.name.toUpperCase(),
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: severityColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(blocker.description, style: theme.textTheme.bodyMedium),
          if (blocker.suggestedActions.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              l10n.suggestedActions,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            ...blocker.suggestedActions.map(
              (action) => Padding(
                padding: const EdgeInsets.only(left: 8, bottom: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: severityColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(action, style: theme.textTheme.bodySmall),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getSeverityColor(ThemeData theme, BlockerSeverity severity) {
    switch (severity) {
      case BlockerSeverity.critical:
        return Colors.red;
      case BlockerSeverity.major:
        return Colors.orange;
      case BlockerSeverity.moderate:
        return Colors.yellow.shade700;
      case BlockerSeverity.minor:
        return Colors.blue;
    }
  }
}

/// Widget for displaying project recommendations
class ProjectRecommendationsWidget extends StatelessWidget {
  final List<ActionRecommendation> recommendations;

  const ProjectRecommendationsWidget({
    super.key,
    required this.recommendations,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.recommend, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  l10n.recommendations,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${recommendations.length}',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...recommendations.map(
              (recommendation) =>
                  _buildRecommendationItem(context, theme, recommendation),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationItem(
    BuildContext context,
    ThemeData theme,
    ActionRecommendation recommendation,
  ) {
    final priorityColor = _getPriorityColor(theme, recommendation.priority);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(left: BorderSide(width: 4, color: priorityColor)),
        color: priorityColor.withOpacity(0.05),
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  recommendation.title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: priorityColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  recommendation.priority.name.toUpperCase(),
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: priorityColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(recommendation.description, style: theme.textTheme.bodyMedium),
          const SizedBox(height: 8),
          Text(
            'Category: ${recommendation.category}',
            style: theme.textTheme.bodySmall?.copyWith(
              fontStyle: FontStyle.italic,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(ThemeData theme, RecommendationPriority priority) {
    switch (priority) {
      case RecommendationPriority.critical:
        return Colors.red;
      case RecommendationPriority.urgent:
        return Colors.orange;
      case RecommendationPriority.high:
        return Colors.amber;
      case RecommendationPriority.medium:
        return Colors.blue;
      case RecommendationPriority.low:
        return Colors.green;
    }
  }
}

/// Widget for displaying project critical paths
class ProjectCriticalPathsWidget extends StatelessWidget {
  final List<CriticalPath> criticalPaths;

  const ProjectCriticalPathsWidget({super.key, required this.criticalPaths});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timeline, color: theme.colorScheme.secondary),
                const SizedBox(width: 8),
                Text(
                  'Critical Paths',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${criticalPaths.length}',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.secondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...criticalPaths.map(
              (criticalPath) =>
                  _buildCriticalPathItem(context, theme, criticalPath),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCriticalPathItem(
    BuildContext context,
    ThemeData theme,
    CriticalPath criticalPath,
  ) {
    final riskColor = _getRiskColor(theme, criticalPath.riskLevel);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(left: BorderSide(width: 4, color: riskColor)),
        color: riskColor.withOpacity(0.05),
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  criticalPath.description.isNotEmpty
                      ? criticalPath.description
                      : 'Critical Path ${criticalPath.id.substring(0, 8)}',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: riskColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'RISK: ${criticalPath.riskLevel.toUpperCase()}',
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: riskColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Duration: ${criticalPath.totalDuration} days',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Tasks: ${criticalPath.taskIds.length}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          if (criticalPath.description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(criticalPath.description, style: theme.textTheme.bodySmall),
          ],
        ],
      ),
    );
  }

  Color _getRiskColor(ThemeData theme, String riskLevel) {
    switch (riskLevel.toLowerCase()) {
      case 'critical':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'medium':
        return Colors.amber;
      case 'low':
        return Colors.green;
      default:
        return theme.colorScheme.primary;
    }
  }
}
