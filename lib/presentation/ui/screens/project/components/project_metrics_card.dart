import 'package:flutter/material.dart';
import '../../../../../domain/entities/project.dart';
import '../../../../../domain/entities/project_analysis.dart';
import '../../../../../l10n/app_localizations.dart';

/// Widget for displaying project metrics and overview
class ProjectMetricsCard extends StatelessWidget {
  final Project project;
  final ProjectAnalysis? analysis;

  const ProjectMetricsCard({super.key, required this.project, this.analysis});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Project Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        project.name,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (project.description.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          project.description,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      project.status,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _getStatusColor(
                        project.status,
                      ).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    _getStatusText(project.status, l10n),
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: _getStatusColor(project.status),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress Section
            _buildProgressSection(context, theme, l10n),
            const SizedBox(height: 16),

            // Metrics Grid
            _buildMetricsGrid(context, theme, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    final progressValue =
        analysis?.progressPercentage ?? project.progress * 100;
    final progressText = '${progressValue.toStringAsFixed(1)}%';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              l10n.progress,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              progressText,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progressValue / 100,
          backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(
            _getProgressColor(theme, progressValue / 100),
          ),
        ),
      ],
    );
  }

  Widget _buildMetricsGrid(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildMetricItem(
            context,
            theme,
            l10n.createdAt,
            _formatDate(project.createdAt),
            Icons.event,
          ),
        ),
        const SizedBox(width: 16),
        if (project.deadline != null)
          Expanded(
            child: _buildMetricItem(
              context,
              theme,
              l10n.deadline,
              _formatDate(project.deadline!),
              Icons.schedule,
              isOverdue: project.isOverdue,
            ),
          ),
        if (project.deadline == null)
          Expanded(
            child: _buildMetricItem(
              context,
              theme,
              l10n.platform,
              project.platformId,
              Icons.integration_instructions,
            ),
          ),
      ],
    );
  }

  Widget _buildMetricItem(
    BuildContext context,
    ThemeData theme,
    String label,
    String value,
    IconData icon, {
    bool isOverdue = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            isOverdue
                ? theme.colorScheme.error.withValues(alpha: 0.05)
                : theme.colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isOverdue
                  ? theme.colorScheme.error.withValues(alpha: 0.2)
                  : theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color:
                    isOverdue
                        ? theme.colorScheme.error
                        : theme.colorScheme.primary,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color:
                  isOverdue
                      ? theme.colorScheme.error
                      : theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(ProjectStatus status) {
    switch (status) {
      case ProjectStatus.planning:
        return Colors.blue;
      case ProjectStatus.active:
        return Colors.green;
      case ProjectStatus.onHold:
        return Colors.orange;
      case ProjectStatus.completed:
        return Colors.purple;
      case ProjectStatus.cancelled:
        return Colors.red;
    }
  }

  String _getStatusText(ProjectStatus status, AppLocalizations l10n) {
    switch (status) {
      case ProjectStatus.planning:
        return l10n.planning;
      case ProjectStatus.active:
        return l10n.active;
      case ProjectStatus.onHold:
        return l10n.onHold;
      case ProjectStatus.completed:
        return l10n.completed;
      case ProjectStatus.cancelled:
        return l10n.cancelled;
    }
  }

  Color _getProgressColor(ThemeData theme, double progress) {
    if (progress >= 0.8) return Colors.green;
    if (progress >= 0.5) return theme.colorScheme.primary;
    if (progress >= 0.2) return Colors.orange;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
