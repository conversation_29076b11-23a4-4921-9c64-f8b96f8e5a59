import 'package:flutter/material.dart';
import '../../../../../domain/entities/project_task.dart';
import '../../../../../l10n/app_localizations.dart';

/// Widget for displaying project tasks overview and statistics
class ProjectTasksOverview extends StatelessWidget {
  final List<ProjectTask> tasks;

  const ProjectTasksOverview({super.key, required this.tasks});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.task_alt, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  l10n.tasksOverview,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${tasks.length}',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Task Statistics
            _buildTaskStatistics(context, theme, l10n),
            const SizedBox(height: 16),

            // Task List
            if (tasks.isNotEmpty) ...[
              Text(
                l10n.recentTasks,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ...tasks
                  .take(5)
                  .map((task) => _buildTaskItem(context, theme, task)),
              if (tasks.length > 5) ...[
                const SizedBox(height: 8),
                Center(
                  child: TextButton(
                    onPressed: () {
                      // Navigate to full task list
                    },
                    child: Text('View all ${tasks.length} tasks'),
                  ),
                ),
              ],
            ] else ...[
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.task_outlined,
                      size: 48,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      l10n.noTasksFound,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTaskStatistics(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    final completedTasks = tasks.where((t) => t.isCompleted).length;
    final inProgressTasks =
        tasks.where((t) => t.status == ProjectTaskStatus.inProgress).length;
    final blockedTasks = tasks.where((t) => t.isBlocked).length;
    final overdueTasks = tasks.where((t) => t.isOverdue).length;

    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            context,
            theme,
            l10n.completed,
            completedTasks.toString(),
            Colors.green,
            Icons.check_circle,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatItem(
            context,
            theme,
            l10n.inProgress,
            inProgressTasks.toString(),
            Colors.blue,
            Icons.timelapse,
          ),
        ),
        if (blockedTasks > 0) ...[
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatItem(
              context,
              theme,
              l10n.blocked,
              blockedTasks.toString(),
              Colors.red,
              Icons.block,
            ),
          ),
        ],
        if (overdueTasks > 0) ...[
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatItem(
              context,
              theme,
              l10n.overdue,
              overdueTasks.toString(),
              Colors.orange,
              Icons.warning,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    ThemeData theme,
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTaskItem(
    BuildContext context,
    ThemeData theme,
    ProjectTask task,
  ) {
    final statusColor = _getTaskStatusColor(task.status);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(_getTaskStatusIcon(task.status), size: 20, color: statusColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task.name,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (task.assignee?.isNotEmpty == true) ...[
                  const SizedBox(height: 2),
                  Text(
                    'Assigned to ${task.assignee}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getTaskStatusText(task.status),
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (task.deadline != null) ...[
                const SizedBox(height: 4),
                Text(
                  _formatDate(task.deadline!),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color:
                        task.isOverdue
                            ? theme.colorScheme.error
                            : theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Color _getTaskStatusColor(ProjectTaskStatus status) {
    switch (status) {
      case ProjectTaskStatus.toDo:
        return Colors.grey;
      case ProjectTaskStatus.inProgress:
        return Colors.blue;
      case ProjectTaskStatus.review:
        return Colors.orange;
      case ProjectTaskStatus.done:
        return Colors.green;
      case ProjectTaskStatus.blocked:
        return Colors.red;
      case ProjectTaskStatus.cancelled:
        return Colors.grey;
    }
  }

  IconData _getTaskStatusIcon(ProjectTaskStatus status) {
    switch (status) {
      case ProjectTaskStatus.toDo:
        return Icons.radio_button_unchecked;
      case ProjectTaskStatus.inProgress:
        return Icons.timelapse;
      case ProjectTaskStatus.review:
        return Icons.rate_review;
      case ProjectTaskStatus.done:
        return Icons.check_circle;
      case ProjectTaskStatus.blocked:
        return Icons.block;
      case ProjectTaskStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getTaskStatusText(ProjectTaskStatus status) {
    switch (status) {
      case ProjectTaskStatus.toDo:
        return 'To Do';
      case ProjectTaskStatus.inProgress:
        return 'In Progress';
      case ProjectTaskStatus.review:
        return 'Review';
      case ProjectTaskStatus.done:
        return 'Done';
      case ProjectTaskStatus.blocked:
        return 'Blocked';
      case ProjectTaskStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}';
  }
}
