import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../domain/entities/project.dart';
import '../../../../../domain/entities/project_analysis.dart';
import '../../../../../domain/entities/project_data.dart';
import '../../../../../l10n/app_localizations.dart';
import '../../../bloc/project_analysis/project_analysis_cubit.dart';
import 'project_analysis_widgets.dart';
import 'project_metrics_card.dart';
import 'project_tasks_overview.dart';

/// Content view for project detail screen showing comprehensive project analysis
class ProjectDetailContentView extends StatelessWidget {
  final Project project;

  const ProjectDetailContentView({super.key, required this.project});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return BlocBuilder<ProjectAnalysisCubit, ProjectAnalysisState>(
      builder: (context, state) {
        if (state is ProjectAnalysisLoading) {
          return _buildLoadingState(context, l10n);
        }

        if (state is ProjectAnalysisError) {
          return _buildErrorState(context, l10n, state.message);
        }

        if (state is ProjectAnalysisLoaded) {
          return _buildAnalysisContent(
            context,
            l10n,
            theme,
            state.analysis,
            state.projectData,
          );
        }

        return _buildInitialState(context, l10n);
      },
    );
  }

  Widget _buildLoadingState(BuildContext context, AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(l10n.analyzingProject),
        ],
      ),
    );
  }

  Widget _buildErrorState(
    BuildContext context,
    AppLocalizations l10n,
    String message,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            l10n.errorLoadingAnalysis,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<ProjectAnalysisCubit>().analyzeProject(project.id);
            },
            child: Text(l10n.retry),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState(BuildContext context, AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            l10n.projectAnalysis,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            l10n.tapAnalyzeToGetInsights,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<ProjectAnalysisCubit>().analyzeProject(project.id);
            },
            child: Text(l10n.analyzeProject),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisContent(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    ProjectAnalysis analysis,
    ProjectData projectData,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Project Overview Card
          ProjectMetricsCard(project: projectData.project, analysis: analysis),
          const SizedBox(height: 16),

          // AI Analysis Summary
          _buildAnalysisSummaryCard(context, l10n, theme, analysis),
          const SizedBox(height: 16),

          // Risk Assessment
          _buildRiskAssessmentCard(context, l10n, theme, analysis),
          const SizedBox(height: 16),

          // Blockers Section
          if (analysis.blockers.isNotEmpty) ...[
            ProjectBlockersWidget(blockers: analysis.blockers),
            const SizedBox(height: 16),
          ],

          // Recommendations Section
          if (analysis.recommendations.isNotEmpty) ...[
            ProjectRecommendationsWidget(
              recommendations: analysis.recommendations,
            ),
            const SizedBox(height: 16),
          ],

          // Tasks Overview
          ProjectTasksOverview(tasks: projectData.tasks),
          const SizedBox(height: 16),

          // Team Performance
          if (projectData.teamMembers.isNotEmpty) ...[
            _buildTeamPerformanceCard(
              context,
              l10n,
              theme,
              projectData.teamMembers,
            ),
            const SizedBox(height: 16),
          ],

          // Budget Overview
          if (projectData.budget != null) ...[
            _buildBudgetOverviewCard(context, l10n, theme, projectData.budget!),
            const SizedBox(height: 16),
          ],

          // Key Insights
          if (analysis.keyInsights.isNotEmpty) ...[
            _buildKeyInsightsCard(context, l10n, theme, analysis.keyInsights),
            const SizedBox(height: 16),
          ],

          // Critical Paths
          if (analysis.criticalPaths.isNotEmpty) ...[
            ProjectCriticalPathsWidget(criticalPaths: analysis.criticalPaths),
            const SizedBox(height: 16),
          ],

          // Analysis Metadata
          _buildAnalysisMetadataCard(context, l10n, theme, analysis),
        ],
      ),
    );
  }

  Widget _buildAnalysisSummaryCard(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    ProjectAnalysis analysis,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.summarize, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  l10n.aiAnalysisSummary,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              analysis.summary.isNotEmpty
                  ? analysis.summary
                  : 'AI analysis will appear here once complete.',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  '${analysis.progressPercentage.toStringAsFixed(1)}% Progress',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  'Updated ${_formatRelativeTime(analysis.analyzedAt)}',
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRiskAssessmentCard(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    ProjectAnalysis analysis,
  ) {
    final riskLevel = _getRiskLevel(analysis.riskScore, l10n);
    final riskColor = _getRiskColor(theme, analysis.riskScore);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning_amber, color: riskColor),
                const SizedBox(width: 8),
                Text(
                  l10n.riskAssessment,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: riskColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    riskLevel,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: riskColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: analysis.riskScore,
              backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(riskColor),
            ),
            const SizedBox(height: 8),
            Text(
              'Risk Score: ${(analysis.riskScore * 100).toInt()}/100',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTeamPerformanceCard(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    List<dynamic> teamMembers,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.group, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  l10n.teamMembers,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...teamMembers
                .take(5)
                .map(
                  (member) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 16,
                          child: Text(
                            (member.name as String)
                                .substring(0, 1)
                                .toUpperCase(),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                member.name as String,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                member.role as String,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color:
                                (member.isActive as bool)
                                    ? Colors.green.withValues(alpha: 0.1)
                                    : Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            (member.isActive as bool)
                                ? l10n.active
                                : l10n.inactive,
                            style: theme.textTheme.labelSmall?.copyWith(
                              color:
                                  (member.isActive as bool)
                                      ? Colors.green
                                      : Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            if (teamMembers.length > 5) ...[
              const SizedBox(height: 8),
              Text(
                'And ${teamMembers.length - 5} more team members...',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetOverviewCard(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    dynamic budget,
  ) {
    final isOverBudget = budget.isOverBudget as bool;
    final utilization = (budget.spent as double) / (budget.total as double);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  l10n.budgetOverview,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (isOverBudget)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      l10n.overBudget,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.error,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(l10n.totalBudget, style: theme.textTheme.bodySmall),
                      Text(
                        '\$${(budget.total as double).toStringAsFixed(0)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(l10n.spent, style: theme.textTheme.bodySmall),
                      Text(
                        '\$${(budget.spent as double).toStringAsFixed(0)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isOverBudget ? theme.colorScheme.error : null,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(l10n.remaining, style: theme.textTheme.bodySmall),
                      Text(
                        '\$${(budget.remaining as double).toStringAsFixed(0)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: utilization.clamp(0.0, 1.0),
              backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(
                isOverBudget
                    ? theme.colorScheme.error
                    : theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${(utilization * 100).toStringAsFixed(1)}% utilized',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyInsightsCard(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    List<String> insights,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb_outline, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  l10n.keyInsights,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...insights.map(
              (insight) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(insight, style: theme.textTheme.bodyMedium),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisMetadataCard(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    ProjectAnalysis analysis,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  l10n.analysisDetails,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        l10n.confidenceLevelLabel,
                        style: theme.textTheme.bodySmall,
                      ),
                      Text(
                        '${(analysis.confidenceLevel * 100).toInt()}%',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(l10n.analysisDate, style: theme.textTheme.bodySmall),
                      Text(
                        _formatDate(analysis.analyzedAt),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (analysis.predictedCompletion != null) ...[
              const SizedBox(height: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.predictedCompletion,
                    style: theme.textTheme.bodySmall,
                  ),
                  Text(
                    _formatDate(analysis.predictedCompletion!),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getRiskLevel(double riskScore, AppLocalizations l10n) {
    if (riskScore >= 0.8) return l10n.critical;
    if (riskScore >= 0.6) return l10n.high;
    if (riskScore >= 0.4) return l10n.medium;
    return l10n.low;
  }

  Color _getRiskColor(ThemeData theme, double riskScore) {
    if (riskScore >= 0.8) return Colors.red;
    if (riskScore >= 0.6) return Colors.orange;
    if (riskScore >= 0.4) return Colors.yellow.shade700;
    return Colors.green;
  }

  String _formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}
