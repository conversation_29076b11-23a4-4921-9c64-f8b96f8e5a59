import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/widgets/base_scaffold.dart';
import '../../../../domain/entities/project.dart';
import '../../bloc/project/project_cubit.dart';
import '../../bloc/project_analysis/project_analysis_cubit.dart';
import '../../bloc/project_query/project_query_cubit.dart';
import 'components/project_detail_content_view.dart';

/// Project detail screen showing full project analysis and management
class ProjectDetailScreen extends StatelessWidget {
  /// The project to display details for
  final Project project;

  const ProjectDetailScreen({super.key, required this.project});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ProjectCubit>(
          create: (context) => sl<ProjectCubit>()..loadProject(project.id),
        ),
        BlocProvider<ProjectAnalysisCubit>(
          create:
              (context) =>
                  sl<ProjectAnalysisCubit>()..analyzeProject(project.id),
        ),
        BlocProvider<ProjectQueryCubit>(
          create: (context) => sl<ProjectQueryCubit>(),
        ),
      ],
      child: ProjectDetailView(project: project),
    );
  }
}

/// Project detail view implementation
class ProjectDetailView extends StatelessWidget {
  final Project project;

  const ProjectDetailView({super.key, required this.project});

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      title: project.name,
      body: ProjectDetailContentView(project: project),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () {
            context.read<ProjectAnalysisCubit>().refreshAnalysis();
          },
        ),
        IconButton(
          icon: const Icon(Icons.chat),
          onPressed: () {
            // Navigate to project query screen with this project context
            Navigator.of(
              context,
            ).pushNamed('/project-query', arguments: project);
          },
        ),
      ],
    );
  }
}
