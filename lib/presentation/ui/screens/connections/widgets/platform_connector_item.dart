import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import '../../../../../core/constants/api_constants.dart';
import '../../../../../core/constants/app_sizes.dart';
import '../../../../../domain/entities/platform_connector.dart';
import '../../../bloc/platform_connections/platform_connections_cubit.dart';

/// Widget that displays a single platform integration card with connect/disconnect options.
class PlatformConnectorItem extends StatelessWidget {
  final String platformId;
  final PlatformConnector? connector;
  final VoidCallback onConnectPressed;
  final VoidCallback? onDisconnectPressed;
  final VoidCallback? onDelete;
  final bool isLoading;
  final String? errorMessage;

  const PlatformConnectorItem({
    super.key,
    required this.platformId,
    this.connector,
    required this.onConnectPressed,
    this.onDisconnectPressed,
    this.onDelete,
    this.isLoading = false,
    this.errorMessage,
  });

  bool get isConnected => connector != null && connector!.isActive;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    final platformName = _getPlatformName(platformId);

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              isConnected
                  ? theme.colorScheme.primary.withValues(alpha: 128)
                  : Colors.transparent,
          width: isConnected ? 1.5 : 0,
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Platform Icon
                Container(
                  width: 48,
                  height: 48,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: _getPlatformLogo(platformId),
                ),
                const SizedBox(width: 16),
                // Platform Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        platformName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (errorMessage != null)
                        Text(
                          errorMessage!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        )
                      else if (isConnected &&
                          connector?.additionalAuth?['platform_info'] != null)
                        Text(
                          _getConnectionDetails(connector!),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        )
                      else
                        Text(
                          l10n.integrationAvailableFor(platformName),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                    ],
                  ),
                ),
                // Status and Button
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Status indicator
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color:
                                isConnected
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          isConnected ? l10n.connected : l10n.disconnected,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color:
                                isConnected
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // Connect/Disconnect button
                    SizedBox(
                      height: 36,
                      child:
                          isLoading
                              ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : OutlinedButton(
                                onPressed:
                                    isConnected
                                        ? onDisconnectPressed
                                        : onConnectPressed,
                                style: OutlinedButton.styleFrom(
                                  foregroundColor:
                                      isConnected
                                          ? theme.colorScheme.error
                                          : theme.colorScheme.primary,
                                  side: BorderSide(
                                    color:
                                        isConnected
                                            ? theme.colorScheme.error
                                            : theme.colorScheme.primary,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 0,
                                  ),
                                  minimumSize: const Size(100, 36),
                                ),
                                child: Text(
                                  isConnected
                                      ? l10n.disconnect
                                      : l10n.connectToPlatform(platformName),
                                  style: theme.textTheme.labelMedium,
                                ),
                              ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Content type chips
          if (isConnected)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.sendToThisPlatform,
                    style: theme.textTheme.bodySmall,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildContentTypeChip(
                        context,
                        'task',
                        Icons.task_alt,
                        l10n.tasks,
                      ),
                      _buildContentTypeChip(
                        context,
                        'note',
                        Icons.note,
                        l10n.notes,
                      ),
                      _buildContentTypeChip(
                        context,
                        'idea',
                        Icons.lightbulb_outline,
                        l10n.ideas,
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Builds a chip for toggling content type selection
  Widget _buildContentTypeChip(
    BuildContext context,
    String type,
    IconData icon,
    String label,
  ) {
    final theme = Theme.of(context);
    final isSelected = connector?.contentTypes[type] ?? false;

    return FilterChip(
      selected: isSelected,
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color:
                isSelected
                    ? theme.colorScheme.onPrimaryContainer
                    : theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
      showCheckmark: false,
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      selectedColor: theme.colorScheme.primaryContainer,
      onSelected:
          isConnected && connector != null
              ? (selected) {
                context.read<PlatformConnectionsCubit>().toggleContentType(
                  connector!,
                  type,
                );
              }
              : null,
    );
  }

  /// Returns platform name for display
  String _getPlatformName(String platformId) {
    switch (platformId) {
      case ApiConstants.clickUp:
        return 'ClickUp';
      case ApiConstants.notion:
        return 'Notion';

      case ApiConstants.jira:
        return 'Jira';
      case ApiConstants.monday:
        return 'Monday.com';
      case ApiConstants.asana:
        return 'Asana';
      case ApiConstants.trello:
        return 'Trello';
      default:
        return platformId.toUpperCase();
    }
  }

  /// Returns connection details for display
  String _getConnectionDetails(PlatformConnector connector) {
    final platformInfo = connector.additionalAuth?['platform_info'];
    if (platformInfo == null) return '';

    final name = platformInfo['name'] as String? ?? '';

    switch (connector.platformId) {
      case ApiConstants.clickUp:
        final workspace =
            platformInfo['workspace']?['teams']?[0]?['name'] as String? ?? '';
        return workspace.isNotEmpty
            ? 'Connected as $name to $workspace'
            : 'Connected as $name';

      case ApiConstants.notion:
        final databases = platformInfo['workspace']?['results'] as List? ?? [];
        final count = databases.length;
        return 'Connected as $name (${count > 0 ? '$count databases' : 'No databases'})';



      case ApiConstants.monday:
        final boards =
            platformInfo['workspace']?['data']?['boards'] as List? ?? [];
        final count = boards.length;
        return 'Connected as $name (${count > 0 ? '$count boards' : 'No boards'})';

      default:
        return 'Connected as $name';
    }
  }

  /// Returns the logo widget for a specific platform
  Widget _getPlatformLogo(String platformId) {
    // In a real implementation, you would use platform-specific SVG or PNG logos
    IconData iconData;
    switch (platformId) {
      case ApiConstants.clickUp:
        iconData = Icons.check_circle_outline;
        break;

      case ApiConstants.notion:
        iconData = Icons.book;
        break;
      case ApiConstants.asana:
        iconData = Icons.category;
        break;
      case ApiConstants.monday:
        iconData = Icons.calendar_today;
        break;
      case ApiConstants.jira:
        iconData = Icons.bug_report;
        break;
      case ApiConstants.trello:
        iconData = Icons.dashboard;
        break;
      default:
        iconData = Icons.link;
    }

    return Icon(iconData);
  }
}
