import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_web_auth/flutter_web_auth.dart';
import 'package:logger/logger.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import '../../../../core/constants/api_constants.dart';
import '../../../../data/remote/oauth_service.dart';
import '../../../../domain/entities/platform_connector.dart';
import '../../../../domain/repositories/api_key_repository.dart';

/// Handles OAuth authentication flow for external platforms.
class OAuthHandler {
  final Logger _logger = Logger();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final ApiKeyRepository? _apiKeyRepository;
  final OAuthService _oAuthService;

  // Fallback client IDs (sollten in einer Produktionsumgebung nicht im Code stehen)
  static const Map<String, String> _defaultClientIds = {
    ApiConstants.clickUp: 'YOUR_CLICKUP_CLIENT_ID',

    ApiConstants.notion: 'YOUR_NOTION_CLIENT_ID',
    ApiConstants.asana: 'YOUR_ASANA_CLIENT_ID',
    ApiConstants.monday: 'YOUR_MONDAY_CLIENT_ID',
    ApiConstants.jira: 'YOUR_JIRA_CLIENT_ID',
    ApiConstants.trello: 'YOUR_TRELLO_CLIENT_ID',
  };

  // Redirect URIs for various platforms
  static const Map<String, String> _redirectUris = {
    ApiConstants.clickUp: 'voicepilot://auth/clickup',

    ApiConstants.notion: 'voicepilot://auth/notion',
    ApiConstants.asana: 'voicepilot://auth/asana',
    ApiConstants.monday: 'voicepilot://auth/monday',
    ApiConstants.jira: 'voicepilot://auth/jira',
    ApiConstants.trello: 'voicepilot://auth/trello',
  };

  // OAuth endpoints for various platforms
  static const Map<String, String> _authEndpoints = {
    ApiConstants.clickUp: 'https://app.clickup.com/api',

    ApiConstants.notion: 'https://api.notion.com/v1/oauth/authorize',
    ApiConstants.asana: 'https://app.asana.com/-/oauth_authorize',
    ApiConstants.monday: 'https://auth.monday.com/oauth2/authorize',
    ApiConstants.jira: 'https://auth.atlassian.com/authorize',
    ApiConstants.trello: 'https://trello.com/1/authorize',
  };

  OAuthHandler({ApiKeyRepository? apiKeyRepository, OAuthService? oAuthService})
    : _apiKeyRepository = apiKeyRepository,
      _oAuthService = oAuthService ?? OAuthService();

  /// Versucht, einen Client-ID für die angegebene Plattform zu erhalten.
  /// Prüft in folgender Reihenfolge:
  /// 1. Supabase OAuth-Config (via OAuthService)
  /// 2. Benutzerdefinierter API-Key im ApiKeyRepository
  /// 3. Fallback zu lokalen Standard-Keys
  Future<String?> getClientId(String platformId) async {
    try {
      // 1. Versuche, Client-ID aus Supabase zu erhalten
      final supabaseClientId = await _oAuthService.getClientId(platformId);
      if (supabaseClientId != null && supabaseClientId.isNotEmpty) {
        _logger.i('Client ID für $platformId aus Supabase geladen');
        return supabaseClientId;
      }

      // 2. Wenn kein API-Key-Repository bereitgestellt wurde, verwende den Standard
      if (_apiKeyRepository == null) {
        return _defaultClientIds[platformId];
      }

      // Versuche, einen benutzerdefinierten API-Key zu erhalten
      final apiKeyResult = await _apiKeyRepository.getApiKey(platformId);

      return apiKeyResult.fold(
        // Bei einem Fehler auf den Standard zurückgreifen
        (failure) {
          _logger.i(
            'Kein benutzerdefinierter API-Key für $platformId gefunden, verwende Standard',
          );
          return _defaultClientIds[platformId];
        },
        // Bei Erfolg den benutzerdefinierten Schlüssel verwenden
        (apiKey) {
          if (apiKey != null && apiKey.keyValue.isNotEmpty) {
            return apiKey.keyValue;
          }
          return _defaultClientIds[platformId];
        },
      );
    } catch (e) {
      _logger.e('Fehler beim Abrufen des API-Keys für $platformId: $e');
      return _defaultClientIds[platformId];
    }
  }

  /// Start the OAuth flow for the specified platform.
  /// Returns a PlatformConnector with the OAuth tokens if successful.
  ///
  /// Throws detailed exceptions that can be displayed to the user.
  Future<PlatformConnector?> authenticatePlatform(
    BuildContext context,
    String platformId,
  ) async {
    try {
      // Show a loading indicator
      final scaffold = ScaffoldMessenger.of(context);
      scaffold.showSnackBar(
        SnackBar(
          content: Text('Connecting to ${_getPlatformName(platformId)}...'),
          duration: const Duration(seconds: 2),
        ),
      );

      // Get platform-specific configurations
      final clientId = await getClientId(platformId);
      final redirectUri = _redirectUris[platformId];
      final authEndpoint = _authEndpoints[platformId];

      if (clientId == null || redirectUri == null || authEndpoint == null) {
        final errorMsg = 'Invalid platform configuration for $platformId';
        _logger.e(errorMsg);
        throw Exception(errorMsg);
      }

      // Check if client ID is a placeholder
      if (clientId == 'YOUR_${platformId.toUpperCase()}_CLIENT_ID') {
        final errorMsg = 'Please configure a real client ID for $platformId';
        _logger.e(errorMsg);
        throw Exception(errorMsg);
      }

      // Build the authorization URL with parameters
      final authUrl = _buildAuthorizationUrl(
        platformId: platformId,
        clientId: clientId,
        redirectUri: redirectUri,
      );

      _logger.i('Starting OAuth flow for $platformId with URL: $authUrl');

      // Launch the authorization flow in the browser
      final result = await FlutterWebAuth.authenticate(
        url: authUrl,
        callbackUrlScheme: 'voicepilot',
      ).timeout(
        const Duration(minutes: 5),
        onTimeout: () {
          throw Exception('Authentication timed out. Please try again.');
        },
      );

      _logger.i(
        'Received OAuth callback: ${result.substring(0, min(50, result.length))}...',
      );

      // Extract the authorization code from the result
      final code = _extractAuthorizationCode(result);
      if (code == null) {
        final errorMsg = 'Failed to extract authorization code from response';
        _logger.e(errorMsg);
        throw Exception(errorMsg);
      }

      // Show progress
      scaffold.showSnackBar(
        SnackBar(
          content: Text(
            'Finalizing connection to ${_getPlatformName(platformId)}...',
          ),
          duration: const Duration(seconds: 2),
        ),
      );

      // Exchange the authorization code for an access token using the secure backend
      final tokenResponse = await _oAuthService.exchangeToken(
        platformId: platformId,
        code: code,
        redirectUri: redirectUri,
      );

      if (tokenResponse == null) {
        final errorMsg = 'Failed to exchange code for token';
        _logger.e(errorMsg);
        throw Exception(errorMsg);
      }

      // Check if the token response contains an error
      if (tokenResponse.containsKey('error')) {
        final errorMsg = 'Authentication error: ${tokenResponse['error']}';
        _logger.e(errorMsg);
        throw Exception(errorMsg);
      }

      // Save the tokens securely
      await _saveTokens(platformId, tokenResponse);

      // Get additional platform information if available
      final platformInfo = await _fetchPlatformInfo(platformId, tokenResponse);

      // Create and return a PlatformConnector with the tokens
      return PlatformConnector(
        id: '', // This will be filled by the repository
        platformId: platformId,
        name: platformInfo['name'] ?? _getPlatformName(platformId),
        apiKey:
            tokenResponse['access_token'] ??
            code, // Prefer access token over code
        additionalAuth: {
          ...tokenResponse,
          if (platformInfo.isNotEmpty) 'platform_info': platformInfo,
        },
        isActive: true,
      );
    } catch (e) {
      _logger.e('OAuth authentication error: $e');

      // Show error to user
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connection failed: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }

      return null;
    }
  }

  /// Revoke access tokens for a platform connector
  Future<bool> revokeAccess(PlatformConnector connector) async {
    try {
      final platformId = connector.platformId;

      // For some platforms, we would call a specific revoke endpoint
      // But for simplicity, we'll just delete the stored tokens

      await _secureStorage.delete(key: '${platformId}_access_token');
      await _secureStorage.delete(key: '${platformId}_refresh_token');

      return true;
    } catch (e) {
      _logger.e('Failed to revoke access: $e');
      return false;
    }
  }

  /// Build the authorization URL with the necessary parameters
  String _buildAuthorizationUrl({
    required String platformId,
    required String clientId,
    required String redirectUri,
  }) {
    final authEndpoint = _authEndpoints[platformId]!;

    // Common parameters for OAuth 2.0
    final params = {
      'client_id': clientId,
      'redirect_uri': redirectUri,
      'response_type': 'code',
      'scope': _getScopeForPlatform(platformId),
    };

    // Add platform-specific parameters
    switch (platformId) {
      case ApiConstants.clickUp:
        // ClickUp specific parameters
        break;

      // Add cases for other platforms
    }

    // Build the URL with parameters
    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return '$authEndpoint?$queryString';
  }

  /// Extract the authorization code from the redirect URI
  String? _extractAuthorizationCode(String redirectUri) {
    try {
      final uri = Uri.parse(redirectUri);
      return uri.queryParameters['code'];
    } catch (e) {
      _logger.e('Failed to extract code from redirect URI: $e');
      return null;
    }
  }

  /// Save the tokens securely
  Future<void> _saveTokens(
    String platformId,
    Map<String, dynamic> tokenResponse,
  ) async {
    try {
      final accessToken = tokenResponse['access_token'] as String?;
      final refreshToken = tokenResponse['refresh_token'] as String?;

      if (accessToken != null) {
        await _secureStorage.write(
          key: '${platformId}_access_token',
          value: accessToken,
        );
      }

      if (refreshToken != null) {
        await _secureStorage.write(
          key: '${platformId}_refresh_token',
          value: refreshToken,
        );
      }
    } catch (e) {
      _logger.e('Error saving tokens: $e');
    }
  }

  /// Get the appropriate scope for the platform
  String _getScopeForPlatform(String platformId) {
    switch (platformId) {
      case ApiConstants.clickUp:
        return 'tasks:write';

      case ApiConstants.notion:
        return 'page:write';
      case ApiConstants.asana:
        return 'tasks:write';
      case ApiConstants.monday:
        return 'items:write';
      case ApiConstants.jira:
        return 'issue:write';
      case ApiConstants.trello:
        return 'cards:write';
      default:
        return '';
    }
  }

  /// Get the display name for a platform
  String _getPlatformName(String platformId) {
    switch (platformId) {
      case ApiConstants.clickUp:
        return 'ClickUp';

      case ApiConstants.notion:
        return 'Notion';
      case ApiConstants.asana:
        return 'Asana';
      case ApiConstants.monday:
        return 'Monday.com';
      case ApiConstants.jira:
        return 'Jira';
      case ApiConstants.trello:
        return 'Trello';
      default:
        return platformId;
    }
  }

  /// Fetch additional platform information using the access token
  /// Returns a map with platform-specific information like user name, workspace, etc.
  Future<Map<String, dynamic>> _fetchPlatformInfo(
    String platformId,
    Map<String, dynamic> tokenResponse,
  ) async {
    try {
      final accessToken = tokenResponse['access_token'] as String?;
      if (accessToken == null) {
        return {};
      }

      // Use the OAuthService to fetch platform info
      final platformInfo = await _oAuthService.fetchPlatformInfo(
        platformId: platformId,
        accessToken: accessToken,
      );

      return platformInfo ?? {};
    } catch (e) {
      _logger.e('Error fetching platform info: $e');
      return {};
    }
  }
}
