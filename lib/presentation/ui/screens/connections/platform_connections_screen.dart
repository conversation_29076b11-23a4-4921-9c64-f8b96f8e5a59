import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';

import '../../../../core/constants/api_constants.dart';
import '../../../../core/widgets/base_scaffold.dart';
import '../../../../domain/entities/platform_connector.dart';
import '../../../../core/di/service_locator.dart';
import '../../bloc/platform_connections/platform_connections_cubit.dart';
import 'widgets/platform_connector_item.dart';

/// Screen that shows available platforms and allows users to connect/disconnect.
class PlatformConnectionsScreen extends StatelessWidget {
  const PlatformConnectionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<PlatformConnectionsCubit>()..loadConnections(),
      child: const PlatformConnectionsView(),
    );
  }
}

class PlatformConnectionsView extends StatelessWidget {
  const PlatformConnectionsView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return BaseScaffold(
      title: l10n.platformConnections,
      body: BlocConsumer<PlatformConnectionsCubit, PlatformConnectionsState>(
        listener: (context, state) {
          if (state is PlatformConnectionsError) {
            _showErrorSnackBar(context, state.message);
          } else if (state is PlatformConnectionSuccess) {
            _showSuccessSnackBar(context, state.message);
          }
        },
        builder: (context, state) {
          if (state is PlatformConnectionsInitial ||
              state is PlatformConnectionsLoading &&
                  state is! PlatformConnectionsConnecting) {
            return const Center(child: CircularProgressIndicator());
          }

          // Get the loaded connectors or empty list
          final List<PlatformConnector> connectors =
              state is PlatformConnectionsLoaded ? state.connectors : [];

          // Get all supported platform IDs
          final supportedPlatforms = ApiConstants.baseUrls.keys.toList();

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.managePlatformConnections,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      l10n.connectPlatformToSendTasks,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),

              // List of platforms
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.only(top: 8, bottom: 24),
                  itemCount: supportedPlatforms.length,
                  itemBuilder: (context, index) {
                    final platformId = supportedPlatforms[index];
                    final connector = _findConnectorByPlatformId(
                      connectors,
                      platformId,
                    );

                    // Check if this platform is currently connecting
                    final isConnecting =
                        state is PlatformConnectionsConnecting &&
                        state.platformId == platformId;

                    // Check if there's an error for this platform
                    String? errorMessage;
                    if (state is PlatformConnectionsError &&
                        state.platformId == platformId) {
                      errorMessage = state.message;
                    }

                    return PlatformConnectorItem(
                      platformId: platformId,
                      connector: connector,
                      isLoading: isConnecting,
                      errorMessage: errorMessage,
                      onConnectPressed:
                          () => _connectToPlatform(context, platformId),
                      onDisconnectPressed:
                          connector != null
                              ? () =>
                                  _disconnectFromPlatform(context, connector)
                              : null,
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Find a connector by its platform ID in the list of connectors
  PlatformConnector? _findConnectorByPlatformId(
    List<PlatformConnector> connectors,
    String platformId,
  ) {
    return connectors.cast<PlatformConnector?>().firstWhere(
      (connector) => connector?.platformId == platformId && connector!.isActive,
      orElse: () => null,
    );
  }

  /// Connect to a platform using OAuth
  void _connectToPlatform(BuildContext context, String platformId) {
    context.read<PlatformConnectionsCubit>().connectPlatform(
      platformId,
      context,
    );
  }

  /// Disconnect from a platform
  void _disconnectFromPlatform(
    BuildContext context,
    PlatformConnector connector,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        final l10n = AppLocalizations.of(context);
        return AlertDialog(
          title: Text(l10n.disconnect),
          content: Text('${l10n.disconnect} ${connector.name}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.cancelButton),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.read<PlatformConnectionsCubit>().disconnectPlatform(
                  connector,
                );
              },
              child: Text(
                l10n.disconnect,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Show an error snackbar with the given message
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show a success snackbar with the given message
  void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.primary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
