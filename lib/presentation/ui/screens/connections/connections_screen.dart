import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/api_constants.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/core/widgets/base_scaffold.dart';
import 'package:projectpilot/domain/entities/platform_connector.dart';
import 'package:projectpilot/presentation/ui/bloc/platform_connections/platform_connections_cubit.dart';
import 'package:projectpilot/presentation/ui/screens/connections/widgets/platform_connector_item.dart';

class ConnectionsScreen extends StatelessWidget {
  const ConnectionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) =>
              context.read<PlatformConnectionsCubit>()..loadConnections(),
      child: const ConnectionsView(),
    );
  }
}

class ConnectionsView extends StatelessWidget {
  const ConnectionsView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return BaseScaffold(
      title: l10n.platformConnections,
      body: BlocConsumer<PlatformConnectionsCubit, PlatformConnectionsState>(
        listener: (context, state) {
          if (state is PlatformConnectionsError) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          } else if (state is PlatformConnectionsConnected) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(l10n.connectionSuccessful)));
          }
        },
        builder: (context, state) {
          if (state is PlatformConnectionsLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is PlatformConnectionsLoaded) {
            return _buildConnectionsList(
              context,
              state.connectors,
              l10n,
              theme,
            );
          } else {
            return _buildConnectionsList(context, [], l10n, theme);
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddConnectionDialog(context),
        tooltip: l10n.connectPlatforms,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildConnectionsList(
    BuildContext context,
    List<PlatformConnector> connectors,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    if (connectors.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.link_off,
              size: 80,
              color: theme.colorScheme.onSurface.withValues(alpha: 77),
            ),
            const SizedBox(height: AppSizes.spacing2x),
            Text(
              l10n.noPlatformsConnected,
              style: theme.textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSizes.spacing1x),
            Text(
              l10n.connectPlatformToSendTasks,
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSizes.spacing4x),
            ElevatedButton.icon(
              onPressed: () => _showAddConnectionDialog(context),
              icon: const Icon(Icons.add),
              label: Text(l10n.connectPlatforms),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.spacing2x),
      itemCount: connectors.length,
      itemBuilder: (context, index) {
        final connector = connectors[index];
        return PlatformConnectorItem(
          platformId: connector.platformId,
          connector: connector,
          onConnectPressed:
              () => context.read<PlatformConnectionsCubit>().connectPlatform(
                connector.platformId,
                context,
              ),
          onDisconnectPressed:
              () => _confirmDeleteConnection(context, connector),
          onDelete: () => _confirmDeleteConnection(context, connector),
        );
      },
    );
  }

  void _showAddConnectionDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final platformConnectionsCubit = context.read<PlatformConnectionsCubit>();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(l10n.selectPlatform),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  ApiConstants.baseUrls.keys.map((platformId) {
                    return ListTile(
                      leading: Icon(
                        _getPlatformIcon(platformId),
                        color: theme.colorScheme.primary,
                      ),
                      title: Text(platformId),
                      onTap: () {
                        Navigator.of(context).pop();
                        platformConnectionsCubit.connectPlatform(
                          platformId,
                          context,
                        );
                      },
                    );
                  }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.cancel),
            ),
          ],
        );
      },
    );
  }

  void _confirmDeleteConnection(
    BuildContext context,
    PlatformConnector connector,
  ) {
    final l10n = AppLocalizations.of(context);
    final platformConnectionsCubit = context.read<PlatformConnectionsCubit>();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(l10n.disconnect),
          content: Text("${l10n.disconnected} ${connector.name}?"),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                platformConnectionsCubit.disconnectPlatform(connector);
              },
              child: Text(l10n.disconnect),
            ),
          ],
        );
      },
    );
  }

  IconData _getPlatformIcon(String platformId) {
    switch (platformId) {
      case ApiConstants.clickUp:
        return Icons.check_circle_outline;

      case ApiConstants.notion:
        return Icons.book;
      case ApiConstants.asana:
        return Icons.category;
      case ApiConstants.monday:
        return Icons.calendar_today;
      case ApiConstants.jira:
        return Icons.bug_report;
      case ApiConstants.trello:
        return Icons.dashboard;
      default:
        return Icons.link;
    }
  }
}
