import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:projectpilot/core/constants/app_colors.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/core/di/service_locator.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_state.dart';
import 'package:projectpilot/presentation/ui/screens/settings/settings_screen.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/usage_credits_sheet.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/platform_connections_sheet.dart';
import 'package:projectpilot/presentation/ui/bloc/platform_connections/platform_connections_cubit.dart';

/// Shows the profile sheet with user info and main navigation options
Future<void> showProfileSheet(BuildContext context) async {
  // Get the SettingsCubit from the service locator
  final settingsCubit = sl<SettingsCubit>();
  
  return showCupertinoModalBottomSheet(
    context: context,
    enableDrag: true,
    isDismissible: true,
    barrierColor: Colors.black54,
    topRadius: const Radius.circular(24),
    builder: (context) => BlocProvider.value(
      value: settingsCubit,
      child: const ProfileSheetContent(),
    ),
  );
}

/// Content for the profile bottom sheet
class ProfileSheetContent extends StatelessWidget {
  /// Creates a new ProfileSheetContent
  const ProfileSheetContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SettingsCubit, SettingsState>(
      builder: (context, state) {
        return _ProfileSheetView(state: state);
      },
    );
  }
}

class _ProfileSheetView extends StatelessWidget {
  final SettingsState state;

  const _ProfileSheetView({required this.state});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    final UserProfile userProfile = state.userProfile ?? UserProfile.empty();
    
    // Determine greeting text
    final String greeting = userProfile.username?.isNotEmpty == true 
        ? 'Hi ${userProfile.username}' 
        : 'Hi';

    return Material(
      color: theme.scaffoldBackgroundColor,
      child: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSizes.spacing3x,
              vertical: AppSizes.spacing4x,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Drag handle
                Center(
                  child: Container(
                    width: 36,
                    height: 5,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.onSurfaceVariant.withValues(
                        alpha: 0.3,
                      ),
                      borderRadius: BorderRadius.circular(2.5),
                    ),
                  ),
                ),
                
                const SizedBox(height: AppSizes.spacing3x),
                
                // User greeting
                Text(
                  greeting,
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: AppSizes.spacing4x),
                
                // Credits info
                Row(
                  children: [
                    Expanded(
                      child: _CreditsInfoCard(
                        title: 'Tokens',
                        value: state.tokenBalance.toString(),
                        icon: Icons.token,
                        color: AppColors.primary,
                        used: state.tokenBalance, // Annahme: tokenBalance ist bereits verbleibend
                        total: 500, // Beispielwert, sollte aus dem State kommen
                      ),
                    ),
                    const SizedBox(width: AppSizes.spacing2x),
                    Expanded(
                      child: _CreditsInfoCard(
                        title: 'Minutes',
                        value: '${state.minutesBalance} min',
                        icon: Icons.timer_outlined,
                        color: AppColors.accent,
                        used: state.minutesBalance, // Annahme: minutesBalance ist bereits verbleibend
                        total: 60, // Beispielwert, sollte aus dem State kommen
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppSizes.spacing4x),
                
                // Quick buy buttons
                Text(
                  'Quick Buy Minutes',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: AppSizes.spacing2x),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _QuickBuyButton(minutes: 5),
                    const SizedBox(width: AppSizes.spacing2x),
                    _QuickBuyButton(minutes: 10),
                    const SizedBox(width: AppSizes.spacing2x),
                    _QuickBuyButton(minutes: 15),
                  ],
                ),
                
                const SizedBox(height: AppSizes.spacing4x),
                
                // Main option buttons
                _OptionButton(
                  title: 'Usage and Credits',
                  icon: Icons.pie_chart_rounded,
                  color: Colors.blue,
                  onTap: () => _openUsageCreditsSheet(context),
                ),
                
                const SizedBox(height: AppSizes.spacing3x),
                
                _OptionButton(
                  title: l10n.platformConnections,
                  icon: Icons.link_rounded,
                  color: Colors.green,
                  onTap: () => _openPlatformConnectionsSheet(context),
                ),
                
                const SizedBox(height: AppSizes.spacing3x),
                
                _OptionButton(
                  title: l10n.settingsTitle,
                  icon: Icons.settings_rounded,
                  color: Colors.orange,
                  onTap: () => _openSettingsScreen(context),
                ),
                
                const SizedBox(height: AppSizes.spacing2x),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _openUsageCreditsSheet(BuildContext context) {
    Navigator.of(context).pop();
    // Get the SettingsCubit from the service locator
    final settingsCubit = sl<SettingsCubit>();
    
    showCupertinoModalBottomSheet(
      context: context,
      builder: (context) => BlocProvider.value(
        value: settingsCubit,
        child: const UsageCreditsSheet(),
      ),
      isDismissible: true,
      enableDrag: true,
      barrierColor: Colors.black54,
    );
  }

  void _openPlatformConnectionsSheet(BuildContext context) {
    Navigator.of(context).pop();
    // Get the required cubits from the service locator
    final settingsCubit = sl<SettingsCubit>();
    final platformConnectionsCubit = sl<PlatformConnectionsCubit>();
    
    showCupertinoModalBottomSheet(
      context: context,
      builder:
          (context) => MultiBlocProvider(
            providers: [
              BlocProvider.value(value: settingsCubit),
              BlocProvider.value(value: platformConnectionsCubit),
            ],
        child: const PlatformConnectionsSheet(),
      ),
      isDismissible: true,
      enableDrag: true,
      barrierColor: Colors.black54,
    );
  }

  void _openSettingsScreen(BuildContext context) {
    Navigator.of(context).pop();
    showCupertinoModalBottomSheet(
      context: context,
      builder: (context) => const SettingsScreen(),
      isDismissible: true,
      enableDrag: true,
      barrierColor: Colors.black54,
    );
  }
}

class _CreditsInfoCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final int used;
  final int total;

  const _CreditsInfoCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.used,
    required this.total,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final double progress = total > 0 ? used / total : 0.0;
    final String usageText = '$used / $total ${title.toLowerCase()}';
    final String leftText = '${total - used} left';
    
    return Container(
      padding: const EdgeInsets.all(AppSizes.spacing3x),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        border: Border.all(
          color: theme.dividerColor.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: AppSizes.spacing1x),
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.spacing2x),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                usageText,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              Text(
                leftText,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.spacing1x),
          ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: color.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 8,
            ),
          ),
          const SizedBox(height: AppSizes.spacing2x),
          Text(
            value,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}

class _QuickBuyButton extends StatelessWidget {
  final int minutes;

  const _QuickBuyButton({required this.minutes});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Expanded(
      child: ElevatedButton(
        onPressed: () {
          // TODO: Implement purchase functionality
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Buy $minutes minutes functionality coming soon'),
              duration: const Duration(seconds: 2),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: AppSizes.spacing2x),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
          ),
        ),
        child: Text('$minutes min'),
      ),
    );
  }
}

class _OptionButton extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const _OptionButton({
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.spacing3x),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
          border: Border.all(
            color: theme.dividerColor.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSizes.spacing2x),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color),
            ),
            const SizedBox(width: AppSizes.spacing3x),
            Expanded(
              child: Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.chevron_right_rounded,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    );
  }
}
