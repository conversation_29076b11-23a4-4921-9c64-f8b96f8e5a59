import 'package:flutter/material.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';

/// Widget that displays user profile information at the top of home screen
class UserProfileHeader extends StatelessWidget {
  final UserProfile? userProfile;
  final VoidCallback? onTap;

  const UserProfileHeader({super.key, this.userProfile, this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (userProfile == null) {
      return const SizedBox.shrink();
    }

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.spacing3x),
        margin: const EdgeInsets.symmetric(horizontal: AppSizes.spacing2x),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primaryContainer.withOpacity(0.3),
              theme.colorScheme.secondaryContainer.withOpacity(0.2),
            ],
          ),
          border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
        ),
        child: Row(
          children: [
            // User Avatar
            CircleAvatar(
              radius: 24,
              backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
              backgroundImage:
                  userProfile!.profileImageUrl != null
                      ? NetworkImage(userProfile!.profileImageUrl!)
                      : null,
              child:
                  userProfile!.profileImageUrl == null
                      ? Icon(
                        Icons.person,
                        color: theme.colorScheme.primary,
                        size: 28,
                      )
                      : null,
            ),
            const SizedBox(width: AppSizes.spacing3x),

            // User Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Greeting and name
                  Text(
                    _getGreeting(),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  Text(
                    userProfile!.username ?? 'Benutzer',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),

                  // Role
                  if (userProfile!.role != null)
                    Row(
                      children: [
                        Icon(
                          _getRoleIcon(userProfile!.role!),
                          color: theme.colorScheme.primary,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getRoleDisplayName(userProfile!.role!),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // Chevron icon
            Icon(
              Icons.chevron_right,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
          ],
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Guten Morgen,';
    } else if (hour < 17) {
      return 'Guten Tag,';
    } else {
      return 'Guten Abend,';
    }
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'developer':
        return 'Developer';
      case 'projectManager':
        return 'Project Manager';
      case 'productOwner':
        return 'Product Owner';
      case 'qaTester':
        return 'QA Tester';
      case 'ctoCeo':
        return 'CTO / CEO';
      default:
        return role;
    }
  }

  IconData _getRoleIcon(String role) {
    switch (role) {
      case 'developer':
        return Icons.code;
      case 'projectManager':
        return Icons.assignment;
      case 'productOwner':
        return Icons.lightbulb;
      case 'qaTester':
        return Icons.bug_report;
      case 'ctoCeo':
        return Icons.business;
      default:
        return Icons.person;
    }
  }
}
