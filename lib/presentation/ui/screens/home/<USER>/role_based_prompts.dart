import 'package:flutter/material.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';

/// Widget that displays role-based example prompts
class RoleBasedPrompts extends StatelessWidget {
  final String? userRole;
  final List<String> userGoals;
  final Function(String) onPromptTap;

  const RoleBasedPrompts({
    super.key,
    this.userRole,
    this.userGoals = const [],
    required this.onPromptTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    final prompts = _getPromptsForRole(l10n, userRole);

    if (prompts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing2x),
          child: Row(
            children: [
              Icon(
                _getRoleIcon(userRole),
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: AppSizes.spacing1x),
              Text(
                l10n.examplesForRole(_getRoleDisplayName(l10n, userRole)),
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppSizes.spacing2x),
        SizedBox(
          height: 120,
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing2x),
            scrollDirection: Axis.horizontal,
            itemCount: prompts.length,
            itemBuilder: (context, index) {
              final prompt = prompts[index];
              return Container(
                width: 280,
                margin: EdgeInsets.only(
                  right: index < prompts.length - 1 ? AppSizes.spacing2x : 0,
                ),
                child: _buildPromptCard(context, prompt, theme, l10n, index),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPromptCard(
    BuildContext context,
    String prompt,
    ThemeData theme,
    AppLocalizations l10n,
    int index,
  ) {
    final colors = [
      theme.colorScheme.primary,
      theme.colorScheme.secondary,
      theme.colorScheme.tertiary,
    ];

    final color = colors[index % colors.length];

    return InkWell(
      onTap: () => onPromptTap(prompt),
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.spacing3x),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
          ),
          border: Border.all(color: color.withOpacity(0.3), width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(Icons.lightbulb_outline, color: color, size: 24),
            const SizedBox(height: AppSizes.spacing2x),
            Expanded(
              child: Text(
                prompt,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: AppSizes.spacing1x),
            Row(
              children: [
                Text(
                  l10n.tapToUse,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward, color: color, size: 14),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<String> _getPromptsForRole(AppLocalizations l10n, String? role) {
    switch (role) {
      case 'developer':
        return [
          l10n.developerPrompt1,
          l10n.developerPrompt2,
          l10n.developerPrompt3,
          l10n.developerPrompt4,
        ];
      case 'projectManager':
        return [
          l10n.projectManagerPrompt1,
          l10n.projectManagerPrompt2,
          l10n.projectManagerPrompt3,
          l10n.projectManagerPrompt4,
        ];
      case 'productOwner':
        return [
          l10n.productOwnerPrompt1,
          l10n.productOwnerPrompt2,
          l10n.productOwnerPrompt3,
          l10n.productOwnerPrompt4,
        ];
      case 'qaTester':
        return [
          l10n.qaTesterPrompt1,
          l10n.qaTesterPrompt2,
          l10n.qaTesterPrompt3,
          l10n.qaTesterPrompt4,
        ];
      case 'ctoCeo':
        return [
          l10n.ctoCeoPrompt1,
          l10n.ctoCeoPrompt2,
          l10n.ctoCeoPrompt3,
          l10n.ctoCeoPrompt4,
        ];
      default:
        return [
          l10n.defaultPrompt1,
          l10n.defaultPrompt2,
          l10n.defaultPrompt3,
          l10n.defaultPrompt4,
        ];
    }
  }

  String _getRoleDisplayName(AppLocalizations l10n, String? role) {
    switch (role) {
      case 'developer':
        return l10n.roleDeveloper;
      case 'projectManager':
        return l10n.roleProjectManager;
      case 'productOwner':
        return l10n.roleProductOwner;
      case 'qaTester':
        return l10n.roleQaTester;
      case 'ctoCeo':
        return l10n.roleCtoCeo;
      default:
        return l10n.roleAllRoles;
    }
  }

  IconData _getRoleIcon(String? role) {
    switch (role) {
      case 'developer':
        return Icons.code;
      case 'projectManager':
        return Icons.assignment;
      case 'productOwner':
        return Icons.lightbulb;
      case 'qaTester':
        return Icons.bug_report;
      case 'ctoCeo':
        return Icons.business;
      default:
        return Icons.help_outline;
    }
  }
}
