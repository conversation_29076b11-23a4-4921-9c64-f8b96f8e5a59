import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:projectpilot/core/utils/performance_utils.dart';

/// A widget that displays a modern sound wave animation
/// with optimized performance and audio level visualization
class ModernSoundWave extends StatefulWidget {
  /// Whether the sound wave is active (recording)
  final bool isActive;

  /// The amplitude of the sound (0.0 to 1.0)
  final double amplitude;

  /// Optional custom colors for the wave
  final List<Color>? colors;

  /// Optional audio level stream for real-time visualization
  final Stream<double>? audioLevelStream;

  /// Height of the wave
  final double height;

  /// Creates a modern sound wave animation
  const ModernSoundWave({
    super.key,
    required this.isActive,
    this.amplitude = 0.5,
    this.colors,
    this.audioLevelStream,
    this.height = 100,
  });

  @override
  State<ModernSoundWave> createState() => _ModernSoundWaveState();
}

class _ModernSoundWaveState extends State<ModernSoundWave>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Current audio level from stream
  double _currentAudioLevel = 0.0;

  // Subscription to audio level stream
  StreamSubscription<double>? _audioLevelSubscription;

  // Whether we're monitoring performance
  bool _isMonitoringPerformance = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller properly
    _animationController = AnimationController(
      vsync: this,
      duration:
          widget.isActive
              ? const Duration(milliseconds: 2000)
              : const Duration(milliseconds: 5000),
    );
    
    // Create the animation
    _animation = Tween<double>(
      begin: 0.0,
      end: 2 * pi,
    ).animate(_animationController);
    
    // Start the animation
    _animationController.repeat();
    
    // Set up stream subscription if needed
    _subscribeToAudioLevelStream();

    // Monitoring in debug mode
    try {
      if (PerformanceUtils.enableLogging) {
        _isMonitoringPerformance = true;
        PerformanceUtils.startFrameMonitoring();
      }
    } catch (e) {
      // Ignore if performance monitoring isn't available
    }
  }

  void _subscribeToAudioLevelStream() {
    // Subscribe to audio level stream if provided
    if (widget.audioLevelStream != null) {
      _audioLevelSubscription = widget.audioLevelStream!.listen((level) {
        // Only update state if the level has changed significantly
        if ((level - _currentAudioLevel).abs() > 0.05) {
          setState(() {
            // Apply a minimum level to avoid completely flat lines when silent
            _currentAudioLevel = level < 0.05 ? 0.05 : level;
          });
        } else if (level < 0.05 && _currentAudioLevel > 0.1) {
          // If audio level is very low, gradually reduce the wave amplitude
          setState(() {
            _currentAudioLevel = _currentAudioLevel * 0.9;
          });
        }
      });
    }
  }

  @override
  void didUpdateWidget(ModernSoundWave oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Adjust animation speed based on isActive state
    if (widget.isActive != oldWidget.isActive) {
      _animationController.duration =
          widget.isActive
              ? const Duration(milliseconds: 2000)
              : const Duration(milliseconds: 5000);

      // Make sure the animation is running
      if (!_animationController.isAnimating) {
        _animationController.repeat();
      }
    }

    // Update audio level stream subscription if changed
    if (widget.audioLevelStream != oldWidget.audioLevelStream) {
      _audioLevelSubscription?.cancel();
      _subscribeToAudioLevelStream();
    }
  }

  @override
  void dispose() {
    // Make sure to clean up the controller and subscription
    _animationController.dispose();
    _audioLevelSubscription?.cancel();

    // Stop performance monitoring if it was started
    try {
      if (_isMonitoringPerformance) {
        PerformanceUtils.stopFrameMonitoring();
      }
    } catch (e) {
      // Ignore if performance monitoring isn't available
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultColors = [
      theme.colorScheme.primary,
      theme.colorScheme.secondary,
      theme.colorScheme.tertiary,
    ];

    final colors = widget.colors ?? defaultColors;

    // Calculate effective amplitude based on audio level if available
    final effectiveAmplitude =
        widget.audioLevelStream != null
            ? _currentAudioLevel.clamp(0.1, 1.0)
            : (widget.isActive ? widget.amplitude.clamp(0.1, 1.0) : 0.05);

    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            size: Size(MediaQuery.of(context).size.width, widget.height),
            painter: ModernSoundWavePainter(
              progress: _animation.value,
              amplitude: effectiveAmplitude,
              isActive: widget.isActive,
              color1: colors[0],
              color2: colors.length > 1 ? colors[1] : colors[0],
              color3: colors.length > 2 ? colors[2] : colors[0],
              barCount: _calculateOptimalBarCount(context),
            ),
          );
        },
      ),
    );
  }

  /// Calculate the optimal number of bars based on screen width
  int _calculateOptimalBarCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    // Adjust bar count based on screen width for better performance
    if (width < 300) {
      return 16; // Fewer bars for small screens
    } else if (width < 600) {
      return 24; // Medium number of bars for medium screens
    } else {
      return 32; // More bars for large screens
    }
  }
}

/// A custom painter that draws a modern, dynamic sound wave
class ModernSoundWavePainter extends CustomPainter {
  /// Progress of the animation (0.0 to 2π)
  final double progress;

  /// Amplitude of the wave (0.0 to 1.0)
  final double amplitude;

  /// Whether the wave is active
  final bool isActive;

  /// First color for the gradient
  final Color color1;

  /// Second color for the gradient
  final Color color2;

  /// Third color for the gradient
  final Color color3;

  /// Number of bars to draw
  final int barCount;

  ModernSoundWavePainter({
    required this.progress,
    required this.amplitude,
    required this.isActive,
    required this.color1,
    required this.color2,
    required this.color3,
    this.barCount = 32,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final centerY = size.height / 2;
    final width = size.width;

    // Width of each bar
    final barWidth = width / (barCount * 1.2);

    // Create a gradient for the bars
    final gradient = LinearGradient(
      colors: [color1, color2, color3],
      stops: const [0.0, 0.5, 1.0],
    );

    final paint = Paint()
      ..shader = gradient.createShader(
        Rect.fromLTWH(0, 0, width, size.height),
      )
      ..strokeCap = StrokeCap.round
      ..strokeWidth = barWidth * 0.85;

    // Draw each bar
    for (int i = 0; i < barCount; i++) {
      final normalized = i / (barCount - 1);

      // Calculate the height of the bar based on a sine wave
      // The formula creates a dynamic wave pattern that moves with the progress
      final wave = sin(
        progress + normalized * pi * 2 + cos(progress + normalized * 3) * 0.5,
      );

      // Apply amplitude to the wave
      // When active, use full amplitude; when inactive, use reduced but still visible amplitude
      final effectiveAmplitude = isActive ? amplitude : 0.05;

      // Add randomness to active waves for more natural look
      // More randomness for higher amplitudes to create more dynamic effect
      final randomFactor =
          isActive ? (Random().nextDouble() * 0.1 * effectiveAmplitude) : 0.0;

      // Calculate height factor with a minimum value to ensure bars are always visible
      final heightFactor =
          isActive
              ? 0.1 + (effectiveAmplitude + randomFactor) * wave.abs() * 0.9
              : 0.05 + (effectiveAmplitude) * wave.abs() * 0.2;

      // Calculate the height and position of the bar
      final barHeight = size.height * heightFactor;
      final x = i * (barWidth * 1.2) + barWidth / 2;

      // Draw the bar
      canvas.drawLine(
        Offset(x, centerY - barHeight / 2),
        Offset(x, centerY + barHeight / 2),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(ModernSoundWavePainter oldDelegate) {
    // Only repaint if necessary
    return oldDelegate.progress != progress ||
        (oldDelegate.amplitude - amplitude).abs() > 0.05 ||
        oldDelegate.isActive != isActive ||
        oldDelegate.color1 != color1 ||
        oldDelegate.color2 != color2 ||
        oldDelegate.color3 != color3 ||
        oldDelegate.barCount != barCount;
  }
}
