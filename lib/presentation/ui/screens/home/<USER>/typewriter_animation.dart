import 'dart:async';
import 'package:flutter/material.dart';

/// A widget that animates text like a typewriter
class TypewriterAnimation extends StatefulWidget {
  /// List of phrases to animate
  final List<String> phrases;
  
  /// Style for the text
  final TextStyle? textStyle;
  
  /// Typing speed in milliseconds
  final int typingSpeed;
  
  /// Pause between phrases in milliseconds
  final int pauseBetweenPhrases;
  
  /// Creates a typewriter animation widget
  const TypewriterAnimation({
    super.key,
    required this.phrases,
    this.textStyle,
    this.typingSpeed = 100,
    this.pauseBetweenPhrases = 2000,
  });

  @override
  State<TypewriterAnimation> createState() => _TypewriterAnimationState();
}

class _TypewriterAnimationState extends State<TypewriterAnimation> {
  late String _currentText;
  late int _currentPhraseIndex;
  late int _currentCharIndex;
  Timer? _timer;
  bool _typing = true;
  bool _pausing = false;

  @override
  void initState() {
    super.initState();
    _currentPhraseIndex = 0;
    _currentCharIndex = 0;
    _currentText = '';
    _startTyping();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTyping() {
    _timer?.cancel();
    _timer = Timer.periodic(Duration(milliseconds: widget.typingSpeed), (
      timer,
    ) {
      if (_typing) {
        final currentPhrase = widget.phrases[_currentPhraseIndex];
        if (_currentCharIndex < currentPhrase.length) {
          // Add one character
          setState(() {
            _currentText = currentPhrase.substring(0, _currentCharIndex + 1);
            _currentCharIndex++;
          });
        } else {
          // Finished typing current phrase
          _typing = false;
          _pausing = true;
          _timer?.cancel();

          // Pause before erasing
          _timer = Timer(
            Duration(milliseconds: widget.pauseBetweenPhrases),
            () {
              _pausing = false;
              _typing = false;
              _eraseText();
            },
          );
        }
      }
    });
  }

  void _eraseText() {
    _timer?.cancel();
    _timer = Timer.periodic(Duration(milliseconds: widget.typingSpeed), (
      timer,
    ) {
      if (!_typing && !_pausing) {
        if (_currentCharIndex > 0) {
          // Remove one character
          setState(() {
            _currentCharIndex--;
            _currentText = widget.phrases[_currentPhraseIndex].substring(
              0,
              _currentCharIndex,
            );
          });
        } else {
          // Finished erasing, move to next phrase
          _timer?.cancel();
          _currentPhraseIndex =
              (_currentPhraseIndex + 1) % widget.phrases.length;
          _typing = true;
          _startTyping();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _currentText,
      style: widget.textStyle,
    );
  }
}
