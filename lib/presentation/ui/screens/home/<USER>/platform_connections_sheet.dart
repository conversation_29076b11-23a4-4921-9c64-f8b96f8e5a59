import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/domain/entities/platform_connector.dart';
import 'package:projectpilot/presentation/ui/bloc/platform_connections/platform_connections_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_state.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/profile_sheet.dart';

/// Sheet that displays and manages platform connections
class PlatformConnectionsSheet extends StatefulWidget {
  /// Creates a new PlatformConnectionsSheet
  const PlatformConnectionsSheet({super.key});

  @override
  State<PlatformConnectionsSheet> createState() => _PlatformConnectionsSheetState();
}

class _PlatformConnectionsSheetState extends State<PlatformConnectionsSheet> {
  // Track which platforms are expanded
  final Map<String, bool> _expandedPlatforms = {};

  @override
  void initState() {
    super.initState();
    // Load platform connections on init
    context.read<PlatformConnectionsCubit>().loadConnections();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PlatformConnectionsCubit, PlatformConnectionsState>(
      builder: (context, platformState) {
        return BlocBuilder<SettingsCubit, SettingsState>(
          builder: (context, settingsState) {
            return Material(
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(context),
                    Expanded(
                      child:
                          platformState is PlatformConnectionsLoading
                              ? const Center(child: CircularProgressIndicator())
                              : _buildBody(context, settingsState),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.spacing3x,
        vertical: AppSizes.spacing2x,
      ),
      child: Column(
        children: [
          // Drag handle
          Container(
            width: 36,
            height: 5,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),
          const SizedBox(height: AppSizes.spacing2x),
          // Title with back button
          Row(
            children: [
              // Back button
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  Navigator.of(context).pop();
                  // Öffne das Profil-Sheet erneut
                  showProfileSheet(context);
                },
              ),
              Text(
                'Platform Connections',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context, SettingsState state) {
    final theme = Theme.of(context);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.spacing3x),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Auto-assignment section
          _buildAutoAssignmentSection(context, state),
          
          const SizedBox(height: AppSizes.spacing4x),
          
          // Platform connections
          Text(
            'Platforms',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSizes.spacing2x),
          
          // Platform connections list
          ..._buildPlatformConnectionsList(context, state),
          
          const SizedBox(height: AppSizes.spacing4x),
        ],
      ),
    );
  }

  Widget _buildAutoAssignmentSection(BuildContext context, SettingsState state) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Auto Assignment',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSizes.spacing2x),
        Container(
          padding: const EdgeInsets.all(AppSizes.spacing3x),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSizes.spacing1x),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.auto_awesome,
                      color: theme.colorScheme.primary,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: AppSizes.spacing2x),
                  Expanded(
                    child: Text(
                      'Auto Assignment',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Switch(
                    value: state.isAutoAssignEnabled,
                    onChanged: (value) {
                      // TODO: Implement auto assignment toggle
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Auto assignment toggle coming soon')),
                      );
                    },
                  ),
                ],
              ),
              if (state.isAutoAssignEnabled) ... [
                const SizedBox(height: AppSizes.spacing2x),
                Text(
                  'Automatically assign tasks to the most appropriate platform based on content',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildPlatformConnectionsList(BuildContext context, SettingsState state) {
    // Use actual platform connections from state if available, otherwise use placeholders
    final List<Map<String, dynamic>> platforms = [
      {
        'id': 'notion',
        'name': 'Notion',
        'icon': 'assets/svgs/notion.svg',
        'isConnected': state.platformConnectors.any((p) => p.id == 'notion'),
        'connectionPrefs': _getPlatformPreferences(state, 'notion'),
      },
      {
        'id': 'clickup',
        'name': 'ClickUp',
        'icon': 'assets/svgs/clickup.svg',
        'isConnected': state.platformConnectors.any((p) => p.id == 'clickup'),
        'connectionPrefs': _getPlatformPreferences(state, 'clickup'),
      },
      
      {
        'id': 'jira',
        'name': 'Jira',
        'icon': 'assets/svgs/jira.svg',
        'isConnected': state.platformConnectors.any((p) => p.id == 'jira'),
        'connectionPrefs': _getPlatformPreferences(state, 'jira'),
      },
      {
        'id': 'monday',
        'name': 'Monday',
        'icon': 'assets/svgs/monday.svg',
        'isConnected': state.platformConnectors.any((p) => p.id == 'monday'),
        'connectionPrefs': _getPlatformPreferences(state, 'monday'),
      },
      {
        'id': 'asana',
        'name': 'Asana',
        'icon': 'assets/svgs/asana.svg',
        'isConnected': state.platformConnectors.any((p) => p.id == 'asana'),
        'connectionPrefs': _getPlatformPreferences(state, 'asana'),
      },
      {
        'id': 'trello',
        'name': 'Trello',
        'icon': 'assets/svgs/trello.svg',
        'isConnected': state.platformConnectors.any((p) => p.id == 'trello'),
        'connectionPrefs': _getPlatformPreferences(state, 'trello'),
      },
    ];
    
    return platforms.map((platform) => _buildPlatformItem(context, platform)).toList();
  }

  Map<String, bool> _getPlatformPreferences(SettingsState state, String platformId) {
    // Get platform-specific preferences from state if available
    // Using hardcoded preferences since PlatformConnector doesn't have syncPreferences
    
    // Default preferences map
    return {
      'tasks': true,
      'notes': true,
      'ideas': true,
    };
  }

  Widget _buildPlatformItem(BuildContext context, Map<String, dynamic> platform) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    final bool isConnected = platform['isConnected'] as bool;
    final String platformId = platform['id'] as String;

    // For connected platforms, show their content directly
    if (isConnected) {
      return Card(
        elevation: 2,
        margin: const EdgeInsets.only(bottom: AppSizes.spacing2x),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
          side: BorderSide(
            color: theme.colorScheme.primary.withValues(alpha: 0.2),
            width: 1.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Platform header
            Padding(
              padding: const EdgeInsets.all(AppSizes.spacing3x),
              child: Row(
                children: [
                  // Platform icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: theme.colorScheme.primary.withValues(alpha: 0.3),
                      ),
                    ),
                    child: SvgPicture.asset(
                      platform['icon'] as String,
                      width: 24,
                      height: 24,
                    ),
                  ),
                  const SizedBox(width: AppSizes.spacing2x),
                  // Platform name
                  Expanded(
                    child: Text(
                      platform['name'] as String,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // Connected badge
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSizes.spacing2x,
                      vertical: AppSizes.spacing1x / 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                      border: Border.all(
                        color: Colors.green.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      l10n.connected,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const Divider(),

            // Disconnect button
            Padding(
              padding: const EdgeInsets.all(AppSizes.spacing3x),
              child: OutlinedButton(
                onPressed: () => _disconnectPlatform(context, platformId),
                style: OutlinedButton.styleFrom(
                  foregroundColor: theme.colorScheme.error,
                  side: BorderSide(
                    color: theme.colorScheme.error.withValues(alpha: 0.5),
                  ),
                ),
                child: Text(l10n.disconnect),
              ),
            ),
          ],
        ),
      );
    } else {
      // For non-connected platforms, use a simpler card with Connect button
      return Card(
        elevation: 1,
        margin: const EdgeInsets.only(bottom: AppSizes.spacing2x),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSizes.spacing3x,
            vertical: AppSizes.spacing2x,
          ),
          child: Row(
            children: [
              // Platform icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: theme.dividerColor.withValues(alpha: 0.2),
                  ),
                ),
                child: SvgPicture.asset(
                  platform['icon'] as String,
                  width: 24,
                  height: 24,
                ),
              ),
              const SizedBox(width: AppSizes.spacing2x),
              // Platform name
              Expanded(
                child: Text(
                  platform['name'] as String,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              // Connect button
              OutlinedButton(
                onPressed: () => _connectPlatform(context, platformId),
                child: Text(l10n.connect),
              ),
            ],
          ),
        ),
      );
    }
  }

  void _connectPlatform(BuildContext context, String platformId) {
    // TODO: Implement actual platform connection
    // For now, show a placeholder message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Connect to $platformId functionality coming soon'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  void _disconnectPlatform(BuildContext context, String platformId) {
    // TODO: Implement actual platform disconnection
    final l10n = AppLocalizations.of(context);
    
    showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text('Disconnect Platform'),
        content: Text('Are you sure you want to disconnect from $platformId?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: Text(l10n.cancel),
          ),
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            onPressed: () => Navigator.of(dialogContext).pop(true),
            child: Text(l10n.disconnect),
          ),
        ],
      ),
    ).then((confirmed) {
      if (confirmed == true) {
        // Handle disconnection
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Disconnect from $platformId functionality coming soon'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    });
  }
}
