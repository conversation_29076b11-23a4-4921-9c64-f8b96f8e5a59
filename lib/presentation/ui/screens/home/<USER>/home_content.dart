import 'package:flutter/material.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/domain/entities/project.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/user_profile_header.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/role_based_prompts.dart';

/// Modern, clean HomeContent for ProjectPilot
/// Focused on AI assistant functionality and clean design
class HomeContent extends StatelessWidget {
  final VoidCallback? onTaskCardTap;
  final ScrollController? scrollController;
  final bool showTitle;
  final List<Project> recentEntries;
  final AnimationController animationController;
  final VoidCallback onRecordPressed;
  final UserProfile? userProfile;
  final Function(String)? onPromptTap;
  final VoidCallback? onProfileTap;

  const HomeContent({
    super.key,
    this.onTaskCardTap,
    this.scrollController,
    this.showTitle = true,
    this.recentEntries = const [],
    required this.animationController,
    required this.onRecordPressed,
    this.userProfile,
    this.onPromptTap,
    this.onProfileTap,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return CustomScrollView(
      controller: scrollController,
      slivers: [
        SliverToBoxAdapter(child: _buildMainContent(context, l10n, theme)),
      ],
    );
  }

  Widget _buildMainContent(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Container(
      color: theme.colorScheme.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppSizes.spacing2x),

          // Clean welcome section
          _buildWelcomeSection(context, l10n, theme),

          const SizedBox(height: AppSizes.spacing4x),

          // AI Assistant CTA Section
          _buildAIAssistantSection(context, l10n, theme),

          const SizedBox(height: AppSizes.spacing3x),

          // Role-based prompts (if available)
          if (userProfile?.role != null && onPromptTap != null)
            _buildPromptsSection(context, l10n, theme),

          // Recent projects or empty state
          if (recentEntries.isNotEmpty)
            _buildRecentProjectsSection(context, l10n, theme)
          else
            _buildEmptyProjectsState(context, l10n, theme),

          const SizedBox(height: AppSizes.spacing3x),

          // Platform connections hint
          _buildPlatformHint(context, l10n, theme),

          // Bottom spacing for recording controls
          const SizedBox(height: AppSizes.spacing6x),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing3x),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Greeting with user info
          if (userProfile != null) ...[
            Text(
              _getGreeting(),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: AppSizes.spacing1x),
            Text(
              userProfile!.username ?? 'User',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: AppSizes.spacing1x),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.spacing2x,
                vertical: AppSizes.spacing1x,
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _formatRole(userProfile!.role ?? 'developer'),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAIAssistantSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing3x),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.spacing4x),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withOpacity(0.1),
              theme.colorScheme.secondary.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        theme.colorScheme.primary,
                        theme.colorScheme.secondary,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.psychology_outlined,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppSizes.spacing2x),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'ProjectPilot AI',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        'Your intelligent project assistant',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.spacing3x),
            Text(
              'Ask me anything about your projects:',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.8),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: AppSizes.spacing1x),
            Text(
              '• What\'s blocking my progress?\n• Which tasks need attention?\n• How is my team performing?',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPromptsSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    final role = userProfile?.role ?? 'developer';
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing3x),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Questions',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: AppSizes.spacing2x),
          RoleBasedPrompts(
            userRole: role,
            userGoals: userProfile?.goals ?? [],
            onPromptTap: onPromptTap!,
          ),
          const SizedBox(height: AppSizes.spacing3x),
        ],
      ),
    );
  }

  Widget _buildRecentProjectsSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing3x),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Projects',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              TextButton(
                onPressed: onTaskCardTap,
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.spacing2x),
          ...recentEntries
              .take(3)
              .map((project) => _buildProjectCard(context, project, theme)),
        ],
      ),
    );
  }

  Widget _buildProjectCard(
    BuildContext context,
    Project project,
    ThemeData theme,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.spacing2x),
      padding: const EdgeInsets.all(AppSizes.spacing3x),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTaskCardTap,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              project.name,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            if (project.description.isNotEmpty == true) ...[
              const SizedBox(height: AppSizes.spacing1x),
              Text(
                project.description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyProjectsState(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppSizes.spacing4x),
      child: Center(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSizes.spacing3x),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.mic_none_outlined,
                size: 48,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: AppSizes.spacing3x),
            Text(
              'No projects yet',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: AppSizes.spacing1x),
            Text(
              'Start recording to create your first project\nwith AI-powered analysis',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlatformHint(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing3x),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.spacing3x),
        decoration: BoxDecoration(
          color: theme.colorScheme.tertiary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: theme.colorScheme.tertiary.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.link, color: theme.colorScheme.tertiary, size: 20),
            const SizedBox(width: AppSizes.spacing2x),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Platform Connections',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    'Connect Notion, ClickUp, Jira, and more',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: theme.colorScheme.tertiary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning,';
    } else if (hour < 17) {
      return 'Good Afternoon,';
    } else {
      return 'Good Evening,';
    }
  }

  String _formatRole(String role) {
    switch (role.toLowerCase()) {
      case 'developer':
        return '👨‍💻 Developer';
      case 'manager':
        return '👩‍💼 Project Manager';
      case 'cto':
        return '🚀 CTO';
      case 'ceo':
        return '🌟 CEO';
      default:
        return '⭐ $role';
    }
  }
}
