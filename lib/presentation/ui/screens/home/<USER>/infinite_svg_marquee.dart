import 'package:flutter/material.dart';

/// A widget that creates an infinite scrolling marquee effect
class InfiniteMarquee extends StatefulWidget {
  /// The child widget to display in the marquee
  final Widget child;

  /// The animation controller to drive the marquee
  final AnimationController? controller;

  /// The direction of the marquee scroll
  final TextDirection direction;

  /// The width of a single child widget
  final double singleChildWidth;

  /// The speed of the marquee (multiplier)
  final double speed;

  /// Creates an infinite marquee
  const InfiniteMarquee({
    super.key,
    required this.child,
    this.controller,
    this.direction = TextDirection.ltr,
    required this.singleChildWidth,
    this.speed = 1.0,
  });

  @override
  State<InfiniteMarquee> createState() => _InfiniteMarqueeState();
}

class _InfiniteMarqueeState extends State<InfiniteMarquee>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;

  @override
  void initState() {
    super.initState();
    
    // Use provided controller or create one
    _controller =
        widget.controller ??
        AnimationController(vsync: this, duration: const Duration(seconds: 20));

    if (widget.controller == null) {
      _controller.repeat();
    }
    
    _offsetAnimation = Tween<Offset>(
      begin:
          widget.direction == TextDirection.ltr
              ? const Offset(0, 0)
              : const Offset(-1, 0),
      end:
          widget.direction == TextDirection.ltr
              ? const Offset(-1, 0)
              : const Offset(0, 0),
    ).animate(_controller);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: OverflowBox(
        maxWidth: widget.singleChildWidth,
        child: SlideTransition(
          position: _offsetAnimation,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              widget.child, widget.child],
          ),
        ),
      ),
    );
  }
}
