import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/core/constants/app_colors.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_state.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/profile_sheet.dart';

/// Sheet that displays detailed usage information and subscription management
class UsageCreditsSheet extends StatelessWidget {
  /// Creates a new UsageCreditsSheet
  const UsageCreditsSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SettingsCubit, SettingsState>(
      builder: (context, state) {
        return Material(
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(context),
                Expanded(child: _buildBody(context, state)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.spacing3x,
        vertical: AppSizes.spacing2x,
      ),
      child: Column(
        children: [
          // Drag handle
          Container(
            width: 36,
            height: 5,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),
          const SizedBox(height: AppSizes.spacing2x),
          // Title with back button
          Row(
            children: [
              // Back button
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  Navigator.of(context).pop();
                  // Öffne das Profil-Sheet erneut
                  showProfileSheet(context);
                },
              ),
              Text(
                'Usage and Credits',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context, SettingsState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.spacing3x),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Usage summary
          _buildUsageSummary(context, state),

          const SizedBox(height: AppSizes.spacing4x),

          // Current plan
          _buildCurrentPlan(context, state),

          const SizedBox(height: AppSizes.spacing4x),

          // Available plans
          _buildAvailablePlans(context),

          const SizedBox(height: AppSizes.spacing4x),

          // Purchase history
          _buildPurchaseHistory(context, state),

          const SizedBox(height: AppSizes.spacing4x),
        ],
      ),
    );
  }

  Widget _buildUsageSummary(BuildContext context, SettingsState state) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(AppSizes.spacing3x),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(Icons.pie_chart_rounded, color: theme.colorScheme.primary),
              const SizedBox(width: AppSizes.spacing2x),
              Text(
                'Usage and Credits',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const Divider(height: AppSizes.spacing4x),

          // Minutes progress bar
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Remaining Minutes',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    '${state.minutesBalance}/500',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: LinearProgressIndicator(
                  value: state.minutesBalance / 500,
                  backgroundColor: theme.colorScheme.primaryContainer
                      .withOpacity(0.3),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                  minHeight: 10,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppSizes.spacing3x),

          // Usage items (keep tokens display)
          _buildUsageItem(
            context,
            icon: Icons.token,
            title: 'Remaining Tokens',
            value: state.tokenBalance.toString(),
            color: AppColors.primary,
          ),

          // No subscription date info for now
        ],
      ),
    );
  }

  Widget _buildUsageItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: AppSizes.spacing2x),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              Text(
                value,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentPlan(BuildContext context, SettingsState state) {
    final theme = Theme.of(context);
    // Placeholder for subscription plan
    // Using placeholder value for subscription status
    final bool hasSubscription = false; // Placeholder
    final String planName = hasSubscription ? 'Premium Plan' : 'Free Plan';
    final String planDescription =
        hasSubscription
            ? 'Unlimited access to all features'
            : 'Limited access to basic features';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Current Plan',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSizes.spacing2x),
        Container(
          padding: const EdgeInsets.all(AppSizes.spacing3x),
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
            border: Border.all(
              color: theme.dividerColor.withValues(alpha: 0.2),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSizes.spacing2x),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      hasSubscription
                          ? Icons.workspace_premium_rounded
                          : Icons.person_outline_rounded,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: AppSizes.spacing2x),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          planName,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          planDescription,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.7,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSizes.spacing2x),
              if (hasSubscription)
                OutlinedButton(
                  onPressed: () {
                    // TODO: Implement cancel subscription
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Cancel subscription functionality coming soon',
                        ),
                      ),
                    );
                  },
                  child: Text('Cancel'),
                )
              else
                ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Implement upgrade plan
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Upgrade plan functionality coming soon'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.arrow_upward),
                  label: Text('Upgrade'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvailablePlans(BuildContext context) {
    final theme = Theme.of(context);

    // Sample plan data
    final plans = [
      {
        'name': 'Basic',
        'price': '€4.99',
        'period': 'monthly',
        'features': ['100 minutes/month', 'Basic features'],
      },
      {
        'name': 'Pro',
        'price': '€9.99',
        'period': 'monthly',
        'features': ['300 minutes/month', 'All features', 'Priority support'],
        'popular': true,
      },
      {
        'name': 'Premium',
        'price': '€19.99',
        'period': 'monthly',
        'features': ['Unlimited minutes', 'All features', '24/7 support'],
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Plans',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSizes.spacing2x),
        ...plans.map((plan) => _buildPlanItem(context, plan)),
      ],
    );
  }

  Widget _buildPlanItem(BuildContext context, Map<String, dynamic> plan) {
    final theme = Theme.of(context);
    final bool isPopular = plan['popular'] == true;

    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.spacing2x),
      padding: const EdgeInsets.all(AppSizes.spacing3x),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        border: Border.all(
          color:
              isPopular
                  ? AppColors.primary
                  : theme.dividerColor.withValues(alpha: 0.2),
          width: isPopular ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                plan['name'] as String,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (isPopular)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSizes.spacing2x,
                    vertical: AppSizes.spacing1x / 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                  ),
                  child: Text(
                    'Popular',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: AppSizes.spacing1x),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                plan['price'] as String,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '/${plan['period']}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.spacing2x),
          ...List.generate(
            (plan['features'] as List).length,
            (index) => Padding(
              padding: const EdgeInsets.only(bottom: AppSizes.spacing1x),
              child: Row(
                children: [
                  const Icon(
                    Icons.check_circle_outline,
                    size: 16,
                    color: Colors.green,
                  ),
                  const SizedBox(width: AppSizes.spacing1x),
                  Text(
                    plan['features'][index] as String,
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: AppSizes.spacing2x),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // TODO: Implement subscribe functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Subscribe functionality coming soon'),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: isPopular ? AppColors.primary : null,
                foregroundColor: isPopular ? theme.colorScheme.onPrimary : null,
              ),
              child: Text('Subscribe'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPurchaseHistory(BuildContext context, SettingsState state) {
    final theme = Theme.of(context);

    // Placeholder purchase history
    final purchases = [
      {
        'date': DateTime.now().subtract(const Duration(days: 15)),
        'description': 'Premium Plan Subscription',
        'amount': '€19.99',
      },
      {
        'date': DateTime.now().subtract(const Duration(days: 45)),
        'description': '10 Minutes Package',
        'amount': '€2.99',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Purchase History',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSizes.spacing2x),
        Container(
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
            border: Border.all(
              color: theme.dividerColor.withValues(alpha: 0.2),
            ),
          ),
          child:
              purchases.isEmpty
                  ? Padding(
                    padding: const EdgeInsets.all(AppSizes.spacing3x),
                    child: Center(child: Text('No purchases yet')),
                  )
                  : ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: purchases.length,
                    separatorBuilder:
                        (_, __) => Divider(
                          height: 1,
                          color: theme.dividerColor.withValues(alpha: 0.2),
                        ),
                    itemBuilder: (context, index) {
                      final purchase = purchases[index];
                      return ListTile(
                        title: Text(
                          purchase['description'] as String,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text(
                          _formatDate(purchase['date'] as DateTime),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.7,
                            ),
                          ),
                        ),
                        trailing: Text(
                          purchase['amount'] as String,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  // Original _buildUsageItem method is used

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year}';
  }
}
