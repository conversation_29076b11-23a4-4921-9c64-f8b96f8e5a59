import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/core/di/service_locator.dart';
import 'package:projectpilot/core/widgets/base_scaffold.dart';
import 'package:projectpilot/domain/entities/transcription_result.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';
import 'package:projectpilot/presentation/ui/bloc/recording/recording_cubit.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/home_content.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/profile_sheet.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>/modern_sound_wave.dart';
import 'package:ambient_gradient_border/ambient_gradient_border.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_cubit.dart';

class HomeScreen extends StatefulWidget {
  /// Creates the modern ProjectPilot home screen
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _waveAnimationController;
  late AnimationController _pulseAnimationController;
  
  // Scroll controller to track scroll position
  final ScrollController _scrollController = ScrollController();

  // Mock user profile for demonstration
  final UserProfile _mockUserProfile = const UserProfile(
    id: 'demo_user',
    username: 'Max Mustermann',
    email: '<EMAIL>',
    profileImageUrl: null,
    role: 'developer',
    goals: ['organizeTasksEfficiently', 'trackProjectProgress'],
    completedOnboarding: true,
  );

  @override
  void initState() {
    super.initState();
    _waveAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    _pulseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  @override
  void dispose() {
    _waveAnimationController.dispose();
    _pulseAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Load initial data from all sources
  void _loadInitialData() {
    try {
      final settingsCubit = context.read<SettingsCubit>();
      settingsCubit.loadSettings();
    } catch (e) {
      debugPrint('Error loading settings: $e');
    }
  }

  // Handle pull-to-refresh
  Future<void> _onRefresh() async {
    try {
      final settingsCubit = context.read<SettingsCubit>();
      await settingsCubit.loadSettings();
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      debugPrint('Error during refresh: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => sl<SettingsCubit>()..loadSettings()),
      ],
      child: BlocBuilder<RecordingCubit, RecordingState>(
        builder: (context, recordingState) {
          // Start/stop wave animation based on recording state
          if (recordingState.status == RecordingStatus.recording) {
            _waveAnimationController.repeat();
            _pulseAnimationController.repeat(reverse: true);
          } else {
            _waveAnimationController.stop();
            _pulseAnimationController.stop();
          }
          
          return BaseScaffold(
            title: null,
            showBackButton: false,
            showProfile: false,
            showSyncIndicator: false,
            backgroundColor: theme.colorScheme.surface,
            // Clean, minimal app bar
            actions: [
              IconButton(
                icon: Icon(
                  Icons.person_outline,
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
                onPressed: () => showProfileSheet(context),
              ),
            ],
            // Simple app logo without headphone container
            leading: _buildCleanAppLogo(context),
            body: Stack(
              children: [
                // Main content
                _buildMainContent(context, recordingState, l10n, theme),

                // Bottom recording controls with inline wave
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: _buildBottomRecordingControls(
                    context,
                    recordingState,
                    theme,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCleanAppLogo(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Container(
        width: 28,
        height: 28,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [theme.colorScheme.primary, theme.colorScheme.secondary],
          ),
        ),
        child: const Icon(
          Icons.psychology_outlined,
          color: Colors.white,
          size: 16,
        ),
      ),
    );
  }

  Widget _buildMainContent(
    BuildContext context,
    RecordingState recordingState,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      color: theme.colorScheme.primary,
      backgroundColor: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.only(
          bottom: 140,
        ), // Space for bottom controls
        child: HomeContent(
          onTaskCardTap: () => debugPrint('Navigate to projects'),
          scrollController: _scrollController,
          showTitle: true,
          recentEntries: const [],
          animationController: _waveAnimationController,
          onRecordPressed:
              () => _handleRecordButtonTap(context, recordingState),
          userProfile: _mockUserProfile,
          onPromptTap: (prompt) => _handlePromptSelection(prompt),
          onProfileTap: () => showProfileSheet(context),
        ),
      ),
    );
  }

  Widget _buildBottomRecordingControls(
    BuildContext context,
    RecordingState recordingState,
    ThemeData theme,
  ) {
    final bool isRecording = recordingState.status == RecordingStatus.recording;
    final bool isPaused = recordingState.status == RecordingStatus.paused;
    final bool hasActiveSession = isRecording || isPaused;

    return Container(
      padding: const EdgeInsets.all(AppSizes.spacing3x),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Recording session info and controls
            if (hasActiveSession) ...[
              _buildRecordingSessionInfo(context, recordingState, theme),
              const SizedBox(height: AppSizes.spacing2x),
            ],

            // Main recording controls with inline wave
            Row(
              children: [
                // Sound wave visualization (takes remaining space)
                if (isRecording)
                  Expanded(
                    child: Container(
                      height: 40,
                      margin: const EdgeInsets.only(right: AppSizes.spacing2x),
                      child: ModernSoundWave(
                        isActive: true,
                        amplitude: 0.6,
                        colors: [
                          theme.colorScheme.primary,
                          theme.colorScheme.secondary,
                          theme.colorScheme.tertiary,
                        ],
                      ),
                    ),
                  )
                else
                  // Spacer when not recording
                  const Expanded(child: SizedBox()),

                // Recording controls
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Stop button (only show during active session)
                    if (hasActiveSession) ...[
                      _buildControlButton(
                        icon: Icons.stop,
                        onPressed: () => _stopRecording(context),
                        color: Colors.red,
                        theme: theme,
                      ),
                      const SizedBox(width: AppSizes.spacing2x),
                    ],

                    // Pause/Resume button (only during recording)
                    if (isRecording) ...[
                      _buildControlButton(
                        icon: Icons.pause,
                        onPressed: () => _pauseRecording(context),
                        color: theme.colorScheme.secondary,
                        theme: theme,
                      ),
                      const SizedBox(width: AppSizes.spacing2x),
                    ],

                    // Main record/resume button
                    _buildMainRecordButton(context, recordingState, theme),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingSessionInfo(
    BuildContext context,
    RecordingState recordingState,
    ThemeData theme,
  ) {
    final bool isPaused = recordingState.status == RecordingStatus.paused;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.spacing2x,
        vertical: AppSizes.spacing1x,
      ),
      decoration: BoxDecoration(
        color:
            isPaused
                ? theme.colorScheme.secondary.withOpacity(0.1)
                : theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            isPaused ? Icons.pause_circle : Icons.fiber_manual_record,
            color:
                isPaused
                    ? theme.colorScheme.secondary
                    : theme.colorScheme.primary,
            size: 16,
          ),
          const SizedBox(width: AppSizes.spacing1x),
          Text(
            isPaused ? 'Recording Paused' : 'Recording Active',
            style: theme.textTheme.bodySmall?.copyWith(
              color:
                  isPaused
                      ? theme.colorScheme.secondary
                      : theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          Text(
            _formatDuration(recordingState.duration),
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
    required ThemeData theme,
  }) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withOpacity(0.1),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: IconButton(
        icon: Icon(icon, color: color, size: 20),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildMainRecordButton(
    BuildContext context,
    RecordingState recordingState,
    ThemeData theme,
  ) {
    final bool isRecording = recordingState.status == RecordingStatus.recording;
    final bool isPaused = recordingState.status == RecordingStatus.paused;

    Color buttonColor;
    IconData buttonIcon;

    if (isRecording) {
      buttonColor = theme.colorScheme.primary;
      buttonIcon = Icons.mic;
    } else if (isPaused) {
      buttonColor = theme.colorScheme.secondary;
      buttonIcon = Icons.play_arrow;
    } else {
      buttonColor = theme.colorScheme.primary;
      buttonIcon = Icons.mic;
    }

    return AnimatedBuilder(
      animation: _pulseAnimationController,
      builder: (context, child) {
        return Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              if (isRecording)
                BoxShadow(
                  color: buttonColor.withOpacity(0.3),
                  blurRadius: 20 * (1 + _pulseAnimationController.value * 0.5),
                  spreadRadius: 2 * (1 + _pulseAnimationController.value * 0.5),
                ),
            ],
          ),
          child: AmbientGradientBorder(
            width: 64,
            height: 64,
            strokeWidth: 3.0,
            radius: 32,
            gradientColors: [
              buttonColor,
              buttonColor.withOpacity(0.8),
              buttonColor,
            ],
            glowSpread: isRecording ? 12.0 : 8.0,
            glowWidthMultiplier: isRecording ? 2.0 : 1.5,
            showSharpBorder: true,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(32),
                onTap: () => _handleRecordButtonTap(context, recordingState),
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [buttonColor, buttonColor.withOpacity(0.8)],
                    ),
                  ),
                  child: Icon(
                    buttonIcon, color: Colors.white, size: 28),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Recording control methods
  void _stopRecording(BuildContext context) {
    final recordingCubit = context.read<RecordingCubit>();
    recordingCubit.stopRecording();
  }

  void _pauseRecording(BuildContext context) {
    final recordingCubit = context.read<RecordingCubit>();
    recordingCubit.pauseRecording();
  }

  void _resumeRecording(BuildContext context) {
    final recordingCubit = context.read<RecordingCubit>();
    recordingCubit.resumeRecording();
  }

  /// Handle record button tap based on current state
  void _handleRecordButtonTap(BuildContext context, RecordingState state) {
    final recordingCubit = context.read<RecordingCubit>();

    switch (state.status) {
      case RecordingStatus.recording:
        // Main button acts as mic indicator when recording
        break;
      case RecordingStatus.paused:
        // Resume recording
        _resumeRecording(context);
        break;
      case RecordingStatus.readyToRecord:
      case RecordingStatus.stopped:
        // Start recording
        recordingCubit.startRecording();
        break;
      default:
        // Initialize recorder for all other states
        recordingCubit.initRecorder();
        break;
    }
  }

  /// Handle prompt selection from role-based prompts
  void _handlePromptSelection(String prompt) {
    debugPrint('Selected prompt: $prompt');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected: "$prompt"'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Format a duration as MM:SS
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
}
