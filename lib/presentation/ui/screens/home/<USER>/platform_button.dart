import 'package:flutter/material.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';

/// A styled button with an icon and a label
class PlatformButton extends StatelessWidget {
  final Widget icon;
  final String label;
  final VoidCallback onTap;

  const PlatformButton({
    super.key,
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSizes.spacing2x,
            vertical: AppSizes.spacing1x,
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerLow,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(width: 24, height: 24, child: icon),
              const SizedBox(width: AppSizes.spacing1x),
              Text(
                label,
                style: theme.textTheme.labelMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
