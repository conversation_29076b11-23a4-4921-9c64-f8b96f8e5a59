import 'package:flutter/material.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/core/widgets/base_scaffold.dart';


/// Screen for displaying frequently asked questions
class FAQScreen extends StatefulWidget {
  /// Creates a new [FAQScreen] instance
  const FAQScreen({super.key});

  @override
  State<FAQScreen> createState() => _FAQScreenState();
}

class _FAQScreenState extends State<FAQScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    
    return BaseScaffold(
      title: l10n.faqTitle,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.spacing4x),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.frequentlyAskedQuestions,
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.spacing4x),
            
            // FAQ items with animations
            _buildFAQItem(
              theme,
              l10n.whatIsProjectPilot,
              l10n.whatIsProjectPilotAnswer,
            ),
            _buildFAQItem(
              theme,
              l10n.howToConnectPlatforms,
              l10n.howToConnectPlatformsAnswer,
            ),
            _buildFAQItem(
              theme,
              l10n.whatLanguagesSupported,
              l10n.whatLanguagesSupportedAnswer,
            ),
            _buildFAQItem(
              theme,
              l10n.howTokensWork,
              l10n.howTokensWorkAnswer,
            ),
            _buildFAQItem(
              theme,
              l10n.canUseOffline,
              l10n.canUseOfflineAnswer,
            ),
            _buildFAQItem(
              theme,
              l10n.howCustomizeProfile,
              l10n.howCustomizeProfileAnswer,
            ),
            _buildFAQItem(
              theme,
              l10n.isDataSecure,
              l10n.isDataSecureAnswer,
            ),
            _buildFAQItem(
              theme,
              l10n.howCancelSubscription,
              l10n.howCancelSubscriptionAnswer,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildFAQItem(ThemeData theme, String question, String answer) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.spacing3x),
      child: Card(
        elevation: 2,
        child: Theme(
          data: Theme.of(context).copyWith(
            dividerColor: Colors.transparent,
          ),
          child: ExpansionTile(
            title: Text(
              question,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(AppSizes.spacing3x),
                child: Text(
                  answer,
                  style: theme.textTheme.bodyLarge,
                ),
              ),
            ],
            onExpansionChanged: (expanded) {
              if (expanded) {
                _animationController.forward();
              } else {
                _animationController.reverse();
              }
            },
          ),
        ),
      ),
    );
  }
}
