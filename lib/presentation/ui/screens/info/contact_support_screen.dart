import 'package:flutter/material.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/core/widgets/base_scaffold.dart';


/// Screen for contacting support
class ContactSupportScreen extends StatefulWidget {
  /// Creates a new [ContactSupportScreen] instance
  const ContactSupportScreen({super.key});

  @override
  State<ContactSupportScreen> createState() => _ContactSupportScreenState();
}

class _ContactSupportScreenState extends State<ContactSupportScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _messageController = TextEditingController();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );
    
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _messageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    
    return BaseScaffold(
      title: l10n.contactSupport,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSizes.spacing4x),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.contactSupport,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppSizes.spacing2x),
              Text(
                l10n.contactSupportDesc,
                style: theme.textTheme.bodyLarge,
              ),
              const SizedBox(height: AppSizes.spacing6x),
              
              Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTextField(
                      controller: _nameController,
                      label: l10n.nameField,
                      hint: l10n.nameFieldHint,
                      icon: Icons.person,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.nameValidationError;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppSizes.spacing4x),
                    
                    _buildTextField(
                      controller: _emailController,
                      label: l10n.emailField,
                      hint: l10n.emailFieldHint,
                      icon: Icons.email,
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.emailValidationError;
                        }
                        
                        // Simple email validation
                        if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                          return l10n.emailValidationError;
                        }
                        
                        return null;
                      },
                    ),
                    const SizedBox(height: AppSizes.spacing4x),
                    
                    _buildTextField(
                      controller: _messageController,
                      label: l10n.messageField,
                      hint: l10n.messageFieldHint,
                      icon: Icons.message,
                      maxLines: 5,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.messageValidationError;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppSizes.spacing6x),
                    
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _submitForm,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                        ),
                        child: Text(l10n.submitButton),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: AppSizes.spacing6x),
              
              // Alternative contact methods
              Text(
                'Alternative Contact Methods',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppSizes.spacing2x),
              
              _buildContactMethod(
                theme,
                'Email',
                '<EMAIL>',
                Icons.email,
              ),
              _buildContactMethod(
                theme,
                'Phone',
                '+****************',
                Icons.phone,
              ),
              _buildContactMethod(
                theme,
                'Twitter',
                '@VoicePilotApp',
                Icons.chat_bubble,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSizes.spacing1x),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          maxLines: maxLines,
          keyboardType: keyboardType,
          validator: validator,
        ),
      ],
    );
  }
  
  Widget _buildContactMethod(ThemeData theme, String title, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.spacing2x),
      child: Row(
        children: [
          Icon(icon, color: theme.colorScheme.primary),
          const SizedBox(width: AppSizes.spacing2x),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              Text(
                value,
                style: theme.textTheme.titleMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      // Simulate sending the form
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Your message has been sent. We\'ll get back to you soon!'),
          backgroundColor: Colors.green,
        ),
      );
      
      // Clear the form
      _nameController.clear();
      _emailController.clear();
      _messageController.clear();
    }
  }
}
