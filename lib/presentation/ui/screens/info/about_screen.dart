import 'package:flutter/material.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_constants.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/core/widgets/base_scaffold.dart';


/// Screen for displaying information about the app
class AboutScreen extends StatefulWidget {
  /// Creates a new [AboutScreen] instance
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
      ),
    );
    
    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );
    
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    
    return BaseScaffold(
      title: l10n.aboutAppTitle(l10n.appTitle),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          // Ensure opacity is always in valid range (0.0-1.0)
          final clampedOpacity = _fadeAnimation.value.clamp(0.0, 1.0);
          return Transform.translate(
            offset: Offset(0, _slideAnimation.value),
            child: Opacity(
              opacity: clampedOpacity,
              child: child,
            ),
          );
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSizes.spacing4x),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // App logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.mic,
                  size: 60,
                  color: theme.colorScheme.onPrimary,
                ),
              ),
              const SizedBox(height: AppSizes.spacing4x),
              
              // App name and version
              Text(
                l10n.appTitle,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppSizes.spacing1x),
              Text(
                l10n.appVersion(AppConstants.appVersion),
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: AppSizes.spacing6x),
              
              // App description
              Text(
                l10n.appDescription,
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppSizes.spacing6x),
              
              // Features
              _buildFeatureSection(theme, l10n),
              const SizedBox(height: AppSizes.spacing6x),
              
              // Team
              _buildTeamSection(theme, l10n),
              const SizedBox(height: AppSizes.spacing6x),
              
              // Copyright
              Text(
                l10n.copyright('2025'),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildFeatureSection(ThemeData theme, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.keyFeatures,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSizes.spacing3x),
        _buildFeatureItem(
          theme,
          l10n.voiceToTask,
          l10n.voiceToTaskDesc,
          Icons.record_voice_over,
        ),
        _buildFeatureItem(
          theme,
          l10n.multiPlatform,
          l10n.multiPlatformDesc,
          Icons.link,
        ),
        _buildFeatureItem(
          theme,
          l10n.multiLanguage,
          l10n.multiLanguageDesc,
          Icons.language,
        ),
        _buildFeatureItem(
          theme,
          l10n.aiPowered,
          l10n.aiPoweredDesc,
          Icons.psychology,
        ),
      ],
    );
  }
  
  Widget _buildTeamSection(ThemeData theme, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.ourTeam,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSizes.spacing3x),
        _buildTeamMember(
          theme,
          'John Doe',
          'Founder & CEO',
        ),
        _buildTeamMember(
          theme,
          'Jane Smith',
          'Lead Developer',
        ),
        _buildTeamMember(
          theme,
          'Michael Johnson',
          'UX Designer',
        ),
        _buildTeamMember(
          theme,
          'Sarah Williams',
          'Marketing Manager',
        ),
      ],
    );
  }
  
  Widget _buildFeatureItem(ThemeData theme, String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.spacing3x),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: AppSizes.spacing3x),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppSizes.spacing1x),
                Text(
                  description,
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTeamMember(ThemeData theme, String name, String role) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.spacing3x),
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.1),
            child: Text(
              name.substring(0, 1),
              style: TextStyle(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: AppSizes.spacing3x),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppSizes.spacing1x),
              Text(
                role,
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
