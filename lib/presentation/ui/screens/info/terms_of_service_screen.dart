import 'package:flutter/material.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/core/widgets/base_scaffold.dart';


/// Screen for displaying the terms of service
class TermsOfServiceScreen extends StatelessWidget {
  /// Creates a new [TermsOfServiceScreen] instance
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    
    return BaseScaffold(
      title: l10n.termsOfServiceTitle,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.spacing4x),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.termsOfServiceTitle,
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.spacing4x),
            Text(
              l10n.lastUpdated('January 1, 2023'),
              style: theme.textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: AppSizes.spacing4x),
            Text(
              l10n.termsOfServiceIntro,
              style: theme.textTheme.bodyLarge,
            ),
            const SizedBox(height: AppSizes.spacing2x),
            _buildSection(
              theme,
              l10n.acceptanceOfTerms,
              l10n.acceptanceOfTermsContent,
            ),
            _buildSection(
              theme,
              l10n.useOfService,
              l10n.useOfServiceContent,
            ),
            _buildSection(
              theme,
              l10n.intellectualProperty,
              l10n.intellectualPropertyContent,
            ),
            _buildSection(
              theme,
              l10n.termination,
              l10n.terminationContent,
            ),
            _buildSection(
              theme,
              l10n.limitationOfLiability,
              l10n.limitationOfLiabilityContent,
            ),
            _buildSection(
              theme,
              l10n.changesToTerms,
              l10n.changesToTermsContent,
            ),
            _buildSection(
              theme,
              l10n.contactUs,
              l10n.contactUsTermsContent,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSection(ThemeData theme, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.spacing4x),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSizes.spacing2x),
          Text(
            content,
            style: theme.textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
