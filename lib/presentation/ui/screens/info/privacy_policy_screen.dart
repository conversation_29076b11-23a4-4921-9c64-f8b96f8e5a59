import 'package:flutter/material.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/core/widgets/base_scaffold.dart';


/// Screen for displaying the privacy policy
class PrivacyPolicyScreen extends StatelessWidget {
  /// Creates a new [PrivacyPolicyScreen] instance
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    
    return BaseScaffold(
      title: l10n.privacyPolicyTitle,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.spacing4x),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.privacyPolicyTitle,
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.spacing4x),
            Text(
              l10n.lastUpdated('January 1, 2023'),
              style: theme.textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: AppSizes.spacing4x),
            Text(
              l10n.privacyPolicyIntro,
              style: theme.textTheme.bodyLarge,
            ),
            const SizedBox(height: AppSizes.spacing2x),
            _buildSection(
              theme,
              l10n.infoWeCollect,
              l10n.infoWeCollectContent,
            ),
            _buildSection(
              theme,
              l10n.howWeUseInfo,
              l10n.howWeUseInfoContent,
            ),
            _buildSection(
              theme,
              l10n.sharingOfInfo,
              l10n.sharingOfInfoContent,
            ),
            _buildSection(
              theme,
              l10n.yourChoices,
              l10n.yourChoicesContent,
            ),
            _buildSection(
              theme,
              l10n.contactUs,
              l10n.contactUsPrivacyContent,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSection(ThemeData theme, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.spacing4x),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSizes.spacing2x),
          Text(
            content,
            style: theme.textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
