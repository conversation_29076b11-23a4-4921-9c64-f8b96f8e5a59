import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:projectpilot/core/widgets/responsive_builder.dart';
import 'package:projectpilot/presentation/ui/bloc/api_key/api_key_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/api_key/api_key_state.dart';
import 'package:projectpilot/presentation/ui/bloc/language/language_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_state.dart';
import 'package:projectpilot/presentation/ui/bloc/theme/theme_cubit.dart';
import 'package:projectpilot/presentation/ui/screens/settings/widgets/settings_item.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:projectpilot/core/constants/app_colors.dart';
import 'package:projectpilot/core/services/voice/audio_file_manager.dart';
import 'package:projectpilot/core/di/service_locator.dart';

import 'package:projectpilot/presentation/ui/bloc/auth/auth_cubit.dart';

/// Settings screen with application preferences
class SettingsScreen extends StatelessWidget {
  /// Creates a new [SettingsScreen] instance
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => GetIt.I<SettingsCubit>()..loadSettings(),
        ),
        BlocProvider(create: (context) => GetIt.I<LanguageCubit>()),
        BlocProvider(create: (context) => GetIt.I<ThemeCubit>()),
        BlocProvider(
          create: (context) => GetIt.I<ApiKeyCubit>()..loadApiKeys(),
        ),
      ],
      child: const SettingsView(),
    );
  }
}

/// View component for the Settings screen
class SettingsView extends StatefulWidget {
  /// Creates a new [SettingsView] instance
  const SettingsView({super.key});

  @override
  State<SettingsView> createState() => _SettingsViewState();
}

class _SettingsViewState extends State<SettingsView>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: BlocConsumer<SettingsCubit, SettingsState>(
        listener: (context, state) {
          if (state.error != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.error!),
                backgroundColor: theme.colorScheme.error,
              ),
            );
            context.read<SettingsCubit>().clearError();
          }
        },
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // iOS-style drag handle and header
                Container(
                  padding: const EdgeInsets.only(top: 12),
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      // Drag handle
                      Container(
                        width: 36,
                        height: 5,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.onSurfaceVariant.withValues(
                            alpha:
                            0.3,
                          ),
                          borderRadius: BorderRadius.circular(2.5),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Header with title and close button
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              l10n.settingsTitle,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () => Navigator.of(context).pop(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Main content
                Expanded(
                  child: ResponsiveBuilder(
                    mobile: (_) => _buildMobileLayout(context, state),
                    tablet: (_) => _buildTabletLayout(context, state),
                    desktop: (_) => _buildTabletLayout(context, state),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context, SettingsState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Auto-animated sections with proper delays for staggered animation effect
          _buildAnimatedSection(
            context,
            _buildLanguageSection(context),
            delay: 50,
          ),
          const SizedBox(height: 24),
          _buildAnimatedSection(
            context,
            _buildThemeSection(context),
            delay: 100,
          ),
          const SizedBox(height: 24),
          _buildAnimatedSection(
            context,
            _buildStorageManagementSection(context),
            delay: 250,
          ),
          const SizedBox(height: 24),
          _buildAnimatedSection(
            context,
            _buildApiKeysSection(context),
            delay: 350,
          ),
          const SizedBox(height: 24),
          _buildAnimatedSection(
            context,
            _buildInfoAndSupportSection(context),
            delay: 450,
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context, SettingsState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          _buildAnimatedSection(
            context,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: _buildLanguageSection(context)),
                const SizedBox(width: 24),
                Expanded(child: _buildThemeSection(context)),
              ],
            ),
            delay: 0,
          ),
          const SizedBox(height: 24),
          _buildAnimatedSection(
            context,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: _buildStorageManagementSection(context)),
                const SizedBox(width: 24),
                Expanded(child: _buildApiKeysSection(context)),
              ],
            ),
            delay: 200,
          ),
          const SizedBox(height: 24),
          _buildAnimatedSection(
            context,
            _buildInfoAndSupportSection(context),
            delay: 300,
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  // Removed unused _buildUserProfileSection
  // This is a method that supports logout functionality
  void performLogout() {
    // Get the cubit
    final settingsCubit = context.read<SettingsCubit>();

    // Perform the logout operation
    settingsCubit.logout().then((_) {
      // Check if the context is still valid after the async operation
      if (context.mounted) {
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    });
  }

  /// Build language section with available language options
  Widget _buildLanguageSection(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SettingsSectionHeader(
          title: l10n.languageTitle, icon: Icons.language,
        ),
        const SizedBox(height: 16),
        _LanguageSettingsItem(), // Not a const constructor
      ],
    );
  }

  /// Build theme section with appearance options
  Widget _buildThemeSection(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SettingsSectionHeader(
          title: l10n.themeTitle,
          icon: Icons.color_lens_outlined,
        ),
        const SizedBox(height: 16),
        _ThemeModeSettingsItem(), // Removed const
        _AutoAssignSettingsItem(), // Removed const
      ],
    );
  }

  
  Widget _buildApiKeysSection(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final apiKeyCubit = context.watch<ApiKeyCubit>();

    // Check if we should show the API key section
    if (apiKeyCubit.state is ApiKeyInitial ||
        apiKeyCubit.state is ApiKeyLoading) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SettingsSectionHeader(title: l10n.apiKeysSection, icon: Icons.key),
          const SizedBox(height: 16),
          const Center(child: CircularProgressIndicator()),
        ],
      );
    }

    Map<String, dynamic> apiKeys = {};
    if (apiKeyCubit.state is ApiKeyLoaded) {
      apiKeys = (apiKeyCubit.state as ApiKeyLoaded).apiKeys;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SettingsSectionHeader(title: l10n.apiKeysSection, icon: Icons.key),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: _buildApiKeyInput(
            context,
            'openai',
            l10n.openaiApiKey,
            l10n.apiKeyHint,
            apiKeys['openai']?.keyValue ?? '',
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: _buildApiKeyInput(
            context,
            'whisper',
            l10n.whisperApiKey,
            l10n.apiKeyHint,
            apiKeys['whisper']?.keyValue ?? '',
          ),
        ),
      ],
    );
  }

  Widget _buildApiKeyInput(
    BuildContext context,
    String keyType,
    String label,
    String hint,
    String initialValue,
  ) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    final controller = TextEditingController(text: initialValue);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: hint,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                ),
                obscureText: true,
                enableSuggestions: false,
                autocorrect: false,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                if (controller.text.isNotEmpty) {
                  context.read<SettingsCubit>().saveApiKey(
                    keyType,
                    controller.text,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                backgroundColor: AppColors.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(l10n.saveButton),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoAndSupportSection(BuildContext context) {
    final settingsCubit = context.read<SettingsCubit>();
    final l10n = AppLocalizations.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SettingsSectionHeader(
          title: l10n.infoAndSupport,
          icon: Icons.info_outline,
        ),
        const SizedBox(height: 16),
        Column(
          children: [
            SettingsItem(
              title: l10n.helpAndSupport,
              subtitle:
                  '${l10n.privacyPolicy}, ${l10n.termsOfService}, ${l10n.faq}',
              icon: Icons.help_outline,
              onTap: () => _showSupportOptionsDialog(context),
            ),
            SettingsItem(
              title: l10n.aboutApp,
              icon: Icons.info_outline,
              showDivider: false,
              onTap: () => settingsCubit.navigateToAbout(context),
            ),
          ],
        ),
      ],
    );
  }

  void _showSupportOptionsDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final settingsCubit = context.read<SettingsCubit>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Text(
                  l10n.helpAndSupport,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                _buildSupportOption(
                  context,
                  Icons.privacy_tip_outlined,
                  l10n.privacyPolicy,
                  () {
                    Navigator.pop(context);
                    settingsCubit.navigateToPrivacyPolicy(context);
                  },
                ),
                _buildSupportOption(
                  context,
                  Icons.description_outlined,
                  l10n.termsOfService,
                  () {
                    Navigator.pop(context);
                    settingsCubit.navigateToTermsOfService(context);
                  },
                ),
                _buildSupportOption(
                  context,
                  Icons.question_answer_outlined,
                  l10n.faq,
                  () {
                    Navigator.pop(context);
                    settingsCubit.navigateToFAQ(context);
                  },
                ),
                _buildSupportOption(
                  context,
                  Icons.email_outlined,
                  l10n.contactSupport,
                  () {
                    Navigator.pop(context);
                    settingsCubit.navigateToContactSupport(context);
                  },
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(l10n.cancel),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildSupportOption(
    BuildContext context,
    IconData icon,
    String title,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: ListTile(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primaryLight.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: AppColors.primary, size: 20),
        ),
        title: Text(title, style: theme.textTheme.titleMedium),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
        ),
        onTap: onTap,
      ),
    );
  }

  /// Builds the storage management section with audio file cleanup options
  Widget _buildStorageManagementSection(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SettingsSectionHeader(
          title: l10n.storageManagement,
          icon: Icons.storage_outlined,
        ),
        const SizedBox(height: 16),
        FutureBuilder<int>(
          future: _getAudioFileSize(),
          builder: (context, snapshot) {
            final size = snapshot.data ?? 0;
            final sizeDisplay = _formatFileSize(size);

            return SettingsItem(
              title: l10n.voiceRecordings,
              subtitle: l10n.usingStorage(sizeDisplay),
              icon: Icons.mic_outlined,
              onTap: () => _showDeleteAudioFilesDialog(context),
              trailing: IconButton(
                icon: const Icon(Icons.delete_outline, color: Colors.red),
                onPressed: () => _showDeleteAudioFilesDialog(context),
                tooltip: l10n.deleteAllAudioFilesTooltip,
              ),
            );
          },
        ),
      ],
    );
  }

  /// Shows a dialog to confirm deletion of all audio files
  void _showDeleteAudioFilesDialog(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    final settingsCubit = context.read<SettingsCubit>();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.deleteAllAudioFiles),
            content: Text(l10n.deleteAllAudioFilesContent),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(l10n.cancel),
              ),
              TextButton(
                style: TextButton.styleFrom(
                  foregroundColor: theme.colorScheme.error,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  settingsCubit.deleteAllAudioFiles();
                },
                child: Text(l10n.deleteAll),
              ),
            ],
          ),
    );
  }

  /// Get the total size of all audio files
  Future<int> _getAudioFileSize() async {
    final audioFileManager = sl<AudioFileManager>();
    return await audioFileManager.getTotalAudioFilesSize();
  }

  /// Format file size for display
  String _formatFileSize(int sizeInBytes) {
    if (sizeInBytes < 1024) {
      return '$sizeInBytes B';
    } else if (sizeInBytes < 1024 * 1024) {
      return '${(sizeInBytes / 1024).toStringAsFixed(1)} KB';
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(sizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}

/// Widget for language settings item
class _LanguageSettingsItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final languageCubit = context.watch<LanguageCubit>();
    final languageCode = languageCubit.state.languageCode;
    final l10n = AppLocalizations.of(context);

    // Map language codes to their display names
    final languages = {
      'en': 'English',
      'de': 'Deutsch',
      'ru': 'Русский',
      'tr': 'Türkçe',
      'ar': 'العربية',
    };

    return SettingsItem(
      title: l10n.languageTitle,
      subtitle: languages[languageCode] ?? 'Unknown',
      icon: EvaIcons.globe2Outline,
      onTap: () => _showLanguagePicker(context),
    );
  }

  void _showLanguagePicker(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);
    final languageCode = context.read<LanguageCubit>().state.languageCode;
    // Holen des SettingsCubit, um ihn an den Dialog weiterzugeben
    final settingsCubit = context.read<SettingsCubit>();

    // Map language codes to their display names
    final languages = {
      'en': 'English',
      'de': 'Deutsch',
      'ru': 'Русский',
      'tr': 'Türkçe',
      'ar': 'العربية',
    };

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder:
          (dialogContext) => BlocProvider.value(
            value: settingsCubit,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  Text(
                    l10n.selectLanguageDialogTitle,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  ...languages.entries.map(
                    (entry) => Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color:
                            entry.key == languageCode
                                ? AppColors.primaryLight.withValues(alpha: 0.3)
                                : theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color:
                              entry.key == languageCode
                                  ? AppColors.primary
                                  : theme.colorScheme.outline.withValues(
                                    alpha: 0.3,
                                  ),
                        ),
                      ),
                      child: ListTile(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        title: Text(
                          entry.value,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight:
                                entry.key == languageCode
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                          ),
                        ),
                        leading: Radio<String>(
                          value: entry.key,
                          groupValue: languageCode,
                          activeColor: AppColors.primary,
                          onChanged: (value) {
                            if (value != null) {
                              context.read<SettingsCubit>().changeLanguage(
                                value,
                              );
                              Navigator.pop(context);
                            }
                          },
                        ),
                        trailing:
                            entry.key == languageCode
                                ? Icon(
                                  Icons.check_circle,
                                  color: AppColors.primary,
                                )
                                : null,
                        onTap: () {
                          context.read<SettingsCubit>().changeLanguage(
                            entry.key,
                          );
                          Navigator.pop(context);
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(l10n.cancel),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}

/// Widget for theme mode settings item
class _ThemeModeSettingsItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final themeCubit = context.watch<ThemeCubit>();
    final themeMode = themeCubit.state.themeMode;
    final isDarkMode = themeMode == ThemeMode.dark;
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return SettingsItem(
      title: l10n.darkMode,
      subtitle: isDarkMode ? 'Enabled' : 'Disabled',
      icon: isDarkMode ? Icons.dark_mode : Icons.light_mode,
      iconColor: AppColors.primary,
      trailing: Switch.adaptive(
        value: isDarkMode,
        onChanged: (_) => context.read<ThemeCubit>().toggleTheme(),
        activeColor: theme.colorScheme.primary,
        activeTrackColor: theme.colorScheme.primaryContainer,
      ),
    );
  }
}

/// Widget for automatic assignment settings item
class _AutoAssignSettingsItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final settingsCubit = context.watch<SettingsCubit>();
    final isAutoAssignEnabled = settingsCubit.state.isAutoAssignEnabled;
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return SettingsItem(
      title: l10n.automaticAssignment,
      subtitle:
          isAutoAssignEnabled
              ? l10n.autoAssignEnabledDesc
              : l10n.autoAssignDisabledDesc,
      icon: Icons.assignment_turned_in,
      iconColor: AppColors.primary,
      trailing: Switch.adaptive(
        value: isAutoAssignEnabled,
        activeColor: theme.colorScheme.primary,
        activeTrackColor: theme.colorScheme.primaryContainer,
        inactiveThumbColor: theme.colorScheme.outline,
        inactiveTrackColor: theme.colorScheme.surfaceContainerHighest,
        onChanged: (value) => settingsCubit.toggleAutoAssign(value),
      ),
    );
  }
}

/// Wraps a widget with a staggered animation
Widget _buildAnimatedSection(
  BuildContext context,
  Widget child, {
  required int delay,
}) {
  return TweenAnimationBuilder<double>(
    tween: Tween<double>(begin: 0.0, end: 1.0),
    duration: const Duration(milliseconds: 500),
    curve: Curves.easeOutBack,
    // Delay the animation based on the section's position
    onEnd: () {
      // Animation completed
    },
    builder: (context, value, child) {
      // Ensure opacity is clamped between 0.0 and 1.0
      final clampedOpacity = value.clamp(0.0, 1.0);
      return Opacity(
        opacity: clampedOpacity,
        child: Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: child,
        ),
      );
    },
    child: child,
  );
}

Future<void> _verifyDatabaseAccess(BuildContext context) async {
  final authCubit = sl<AuthCubit>();
  final theme = Theme.of(context);

  // Show loading indicator
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(
      content: Text('Verifying database access...'),
      duration: Duration(seconds: 1),
    ),
  );

  // Verify database access
  final hasAccess = await authCubit.verifyDatabaseAccess();

  // Show result
  if (context.mounted) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          hasAccess
              ? 'Access verified: You have authenticated database access'
              : 'Access denied: You don\'t have authenticated database access',
        ),
        backgroundColor: hasAccess ? Colors.green : theme.colorScheme.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
