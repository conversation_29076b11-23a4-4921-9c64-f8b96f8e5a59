import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/presentation/ui/bloc/language/language_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/language/language_state.dart';

/// A widget that allows the user to select the application language
class LanguageSelector extends StatelessWidget {
  /// Creates a new [LanguageSelector] instance
  const LanguageSelector({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final languageCubit = context.read<LanguageCubit>();

    return BlocBuilder<LanguageCubit, LanguageState>(
      builder: (context, state) {
        return ListTile(
          title: Text(l10n.languageTitle),
          subtitle: Text(_getLanguageName(state.languageCode, l10n)),
          trailing: const Icon(Icons.language),
          onTap:
              () => _showLanguageDialog(
                context,
                languageCubit,
                state.languageCode,
                l10n,
              ),
        );
      },
    );
  }

  /// Shows a dialog for selecting the language
  void _showLanguageDialog(
    BuildContext context,
    LanguageCubit languageCubit,
    String currentLanguageCode,
    AppLocalizations l10n,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(l10n.selectLanguage),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildLanguageOption(
                  context,
                  'en',
                  l10n.englishLanguage,
                  currentLanguageCode,
                  languageCubit,
                ),
                _buildLanguageOption(
                  context,
                  'de',
                  l10n.germanLanguage,
                  currentLanguageCode,
                  languageCubit,
                ),
                _buildLanguageOption(
                  context,
                  'ru',
                  l10n.russianLanguage,
                  currentLanguageCode,
                  languageCubit,
                ),
                _buildLanguageOption(
                  context,
                  'tr',
                  l10n.turkishLanguage,
                  currentLanguageCode,
                  languageCubit,
                ),
                _buildLanguageOption(
                  context,
                  'ar',
                  l10n.arabicLanguage,
                  currentLanguageCode,
                  languageCubit,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.cancelButton),
            ),
          ],
        );
      },
    );
  }

  /// Builds a language option for the dialog
  Widget _buildLanguageOption(
    BuildContext context,
    String languageCode,
    String languageName,
    String currentLanguageCode,
    LanguageCubit languageCubit,
  ) {
    final isSelected = languageCode == currentLanguageCode;

    return ListTile(
      title: Text(languageName),
      trailing:
          isSelected ? const Icon(Icons.check, color: Colors.green) : null,
      onTap: () {
        languageCubit.changeLanguage(languageCode);
        Navigator.of(context).pop();
      },
    );
  }

  /// Gets the name of the language based on the language code
  String _getLanguageName(String languageCode, AppLocalizations l10n) {
    switch (languageCode) {
      case 'en':
        return l10n.englishLanguage;
      case 'de':
        return l10n.germanLanguage;
      case 'ru':
        return l10n.russianLanguage;
      case 'tr':
        return l10n.turkishLanguage;
      case 'ar':
        return l10n.arabicLanguage;
      default:
        return l10n.englishLanguage;
    }
  }
}
