import 'dart:io';
import 'package:flutter/material.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_colors.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/theme/theme_cubit.dart';
import 'dart:ui';

/// A widget displaying user profile information with edit capabilities
class UserProfileSection extends StatefulWidget {
  /// The user profile to display
  final UserProfile userProfile;

  /// Callback for when the logout button is pressed
  final VoidCallback? onLogout;

  /// Creates a new [UserProfileSection] instance
  const UserProfileSection({
    super.key,
    required this.userProfile,
    this.onLogout,
  });

  @override
  State<UserProfileSection> createState() => _UserProfileSectionState();
}

class _UserProfileSectionState extends State<UserProfileSection>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _avatarAnimation;
  final TextEditingController _usernameController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _avatarAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _usernameController.text = widget.userProfile.username ?? '';
    _animationController.forward();
  }

  @override
  void didUpdateWidget(UserProfileSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userProfile.username != widget.userProfile.username) {
      _usernameController.text = widget.userProfile.username ?? '';
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _usernameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final themeMode = context.select<ThemeCubit, ThemeMode>(
      (cubit) => cubit.state.themeMode,
    );
    final isDarkMode = themeMode == ThemeMode.dark;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Avatar with animation and edit button
          Center(
            child: Stack(
              children: [
                AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _avatarAnimation.value,
                      child: child,
                    );
                  },
                  child: _buildAvatar(context),
                ),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: GestureDetector(
                    onTap: _selectAndCropImage,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColors.primary, AppColors.accent,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.accent.withValues(alpha:
                              0.6,
                            ),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.camera_alt,
                        color: theme.colorScheme.onPrimary,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppSizes.spacing4x),

          // Username edit form
          Form(
            key: _formKey,
            child:
                _isEditing
                    ? _buildEditableUsername(context)
                    : _buildDisplayUsername(context),
          ),

          const SizedBox(height: AppSizes.spacing2x),

          // Email display
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              vertical: AppSizes.spacing3x,
              horizontal: AppSizes.spacing4x,
            ),
            decoration: BoxDecoration(
              color:
                  isDarkMode
                      ? theme.colorScheme.surfaceContainerHighest
                          .withValues(
                        alpha: 0.2,
                      )
                      : theme.colorScheme.surfaceContainerHighest
                          .withValues(
                        alpha: 0.1,
                      ),
              borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.email,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppSizes.spacing1x),
                Text(
                  widget.userProfile.email ?? l10n.notAvailable,
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
          ),
          const SizedBox(height: AppSizes.spacing4x),

          // Spacer to push logout button to bottom
          const Spacer(),

          // Logout button
          Container(
            width: double.infinity,
            height: 50,
            margin: const EdgeInsets.only(top: AppSizes.spacing4x),
            child: ElevatedButton.icon(
              icon: const Icon(Icons.logout),
              label: Text(l10n.logoutButton),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.error,
                foregroundColor: theme.colorScheme.onError,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              onPressed: widget.onLogout,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the editable username field with save and cancel buttons
  Widget _buildEditableUsername(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            vertical: AppSizes.spacing2x,
            horizontal: AppSizes.spacing3x,
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
            ),
          ),
          child: TextFormField(
            controller: _usernameController,
            style: theme.textTheme.titleMedium,
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: l10n.username,
              contentPadding: EdgeInsets.zero,
              prefixIcon: Icon(
                Icons.person_outline,
                color: AppColors.primary,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return l10n.usernameRequired;
              }
              return null;
            },
          ),
        ),
        const SizedBox(height: AppSizes.spacing2x),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            OutlinedButton.icon(
              icon: const Icon(Icons.cancel_outlined),
              label: Text(l10n.cancel),
              onPressed: () {
                setState(() {
                  _isEditing = false;
                  _usernameController.text = widget.userProfile.username ?? '';
                });
              },
            ),
            const SizedBox(width: AppSizes.spacing2x),
            ElevatedButton.icon(
              icon: const Icon(Icons.save),
              label: Text(l10n.save),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: theme.colorScheme.onPrimary,
              ),
              onPressed: _saveUsername,
            ),
          ],
        ),
      ],
    );
  }

  /// Builds the display username with edit button
  Widget _buildDisplayUsername(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    return ClipRRect(
      borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            vertical: AppSizes.spacing3x,
            horizontal: AppSizes.spacing4x,
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
            border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.username,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppSizes.spacing1x),
                    Text(
                      widget.userProfile.username ?? l10n.notAvailable,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.primaryLight.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(Icons.edit_outlined, size: 20),
                  onPressed: () {
                    setState(() {
                      _isEditing = true;
                    });
                  },
                  tooltip: l10n.edit,
                  color: AppColors.primary,
                  style: IconButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the user avatar with profile image or initials
  Widget _buildAvatar(BuildContext context) {
    final theme = Theme.of(context);
    final String initials = _getInitials();
    final double size = 110;

    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: AppColors.accent.withValues(alpha: 0.6),
            blurRadius: 20,
            spreadRadius: 2,
          ),
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.4),
            blurRadius: 15,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: AppColors.accentGradient,
          image:
              widget.userProfile.profileImageUrl != null
                  ? DecorationImage(
                  image: NetworkImage(widget.userProfile.profileImageUrl!),
                  fit: BoxFit.cover,
                )
                  : null,
        ),
        child:
            widget.userProfile.profileImageUrl == null
                ? Center(
                child: Text(
                  initials,
                  style: theme.textTheme.headlineMedium?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
                : null,
      ),
    );
  }

  /// Gets the user's initials from their username or email
  String _getInitials() {
    if (widget.userProfile.username != null &&
        widget.userProfile.username!.isNotEmpty) {
      final nameParts = widget.userProfile.username!.split(' ');
      if (nameParts.length > 1) {
        return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
      }
      return widget.userProfile.username![0].toUpperCase();
    } else if (widget.userProfile.email != null &&
        widget.userProfile.email!.isNotEmpty) {
      return widget.userProfile.email![0].toUpperCase();
    }
    return '?';
  }

  /// Save the username changes
  void _saveUsername() {
    if (_formKey.currentState!.validate()) {
      final newUsername = _usernameController.text.trim();
      context.read<SettingsCubit>().updateUserProfile(username: newUsername);
      setState(() {
        _isEditing = false;
      });
    }
  }

  /// Select image from gallery and crop it before uploading
  Future<void> _selectAndCropImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
    );

    if (pickedFile != null) {
      final croppedFile = await _cropImage(File(pickedFile.path));
      if (croppedFile != null && mounted) {
        await context.read<SettingsCubit>().uploadProfileImage(croppedFile);
      }
    }
  }

  /// Crop the selected image
  Future<File?> _cropImage(File imageFile) async {
    final theme = Theme.of(context);

    final CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: imageFile.path,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Crop Profile Image',
          toolbarColor: AppColors.primary,
          toolbarWidgetColor: theme.colorScheme.onPrimary,
          activeControlsWidgetColor: AppColors.primary,
          initAspectRatio: CropAspectRatioPreset.square,
          lockAspectRatio: true,
        ),
        IOSUiSettings(
          title: 'Crop Profile Image',
          aspectRatioLockEnabled: true,
          minimumAspectRatio: 1.0,
        ),
      ],
    );

    if (croppedFile != null) {
      return File(croppedFile.path);
    }
    return null;
  }
}
