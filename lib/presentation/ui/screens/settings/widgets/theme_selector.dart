import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/presentation/ui/bloc/theme/theme_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/theme/theme_state.dart';

/// A widget that allows the user to select the application theme
class ThemeSelector extends StatelessWidget {
  /// Creates a new [ThemeSelector] instance
  const ThemeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final themeCubit = context.read<ThemeCubit>();

    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, state) {
        return ListTile(
          title: Text(l10n.themeTitle),
          subtitle: Text(_getThemeName(state.themeMode, l10n)),
          trailing: const Icon(Icons.brightness_4),
          onTap:
              () =>
                  _showThemeDialog(context, themeCubit, state.themeMode, l10n),
        );
      },
    );
  }

  /// Shows a dialog for selecting the theme
  void _showThemeDialog(
    BuildContext context,
    ThemeCubit themeCubit,
    ThemeMode currentThemeMode,
    AppLocalizations l10n,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(l10n.selectTheme),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildThemeOption(
                  context,
                  ThemeMode.system,
                  l10n.systemTheme,
                  currentThemeMode,
                  themeCubit,
                ),
                _buildThemeOption(
                  context,
                  ThemeMode.light,
                  l10n.lightTheme,
                  currentThemeMode,
                  themeCubit,
                ),
                _buildThemeOption(
                  context,
                  ThemeMode.dark,
                  l10n.darkTheme,
                  currentThemeMode,
                  themeCubit,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.cancelButton),
            ),
          ],
        );
      },
    );
  }

  /// Builds a theme option for the dialog
  Widget _buildThemeOption(
    BuildContext context,
    ThemeMode themeMode,
    String themeName,
    ThemeMode currentThemeMode,
    ThemeCubit themeCubit,
  ) {
    final isSelected = themeMode == currentThemeMode;

    return ListTile(
      title: Text(themeName),
      trailing:
          isSelected ? const Icon(Icons.check, color: Colors.green) : null,
      onTap: () {
        themeCubit.changeTheme(themeMode);
        Navigator.of(context).pop();
      },
    );
  }

  /// Gets the name of the theme based on the theme mode
  String _getThemeName(ThemeMode themeMode, AppLocalizations l10n) {
    switch (themeMode) {
      case ThemeMode.system:
        return l10n.systemTheme;
      case ThemeMode.light:
        return l10n.lightTheme;
      case ThemeMode.dark:
        return l10n.darkTheme;
    }
  }
}
