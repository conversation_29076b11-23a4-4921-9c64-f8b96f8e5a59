import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/domain/entities/api_key.dart';
import 'package:projectpilot/presentation/ui/bloc/api_key/api_key_cubit.dart';

/// A widget for entering API keys
class ApiKeyInput extends StatefulWidget {
  /// Type of API key (OpenAI or Whisper)
  final String keyType;

  /// Label for the input field
  final String label;

  /// Initial value for the input field
  final String initialValue;

  /// Creates a new [ApiKeyInput] instance
  const ApiKeyInput({
    super.key,
    required this.keyType,
    required this.label,
    this.initialValue = '',
  });

  @override
  State<ApiKeyInput> createState() => _ApiKeyInputState();
}

class _ApiKeyInputState extends State<ApiKeyInput> {
  late final TextEditingController _controller;
  bool _obscureText = true;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final apiKeyCubit = context.read<ApiKeyCubit>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(widget.label, style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _controller,
                obscureText: _obscureText,
                decoration: InputDecoration(
                  hintText: l10n.apiKeyHint,
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureText ? Icons.visibility : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureText = !_obscureText;
                      });
                    },
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                final apiKey = _controller.text.trim();
                if (apiKey.isNotEmpty) {
                  if (widget.keyType == 'openai') {
                    apiKeyCubit.saveApiKey(
                      ApiKey(keyType: 'OpenAI', keyValue: apiKey),
                    );
                  } else if (widget.keyType == 'whisper') {
                    apiKeyCubit.saveApiKey(
                      ApiKey(keyType: 'Whisper', keyValue: apiKey),
                    );
                  }

                  ScaffoldMessenger.of(
                    context,
                  ).showSnackBar(SnackBar(content: Text(l10n.apiKeySaved)));
                }
              },
              child: Text(l10n.saveButton),
            ),
          ],
        ),
      ],
    );
  }
}
