import 'package:dartz/dartz.dart';
import 'package:projectpilot/domain/entities/api_key.dart';
import 'package:projectpilot/domain/failure/failures.dart';

/// Repository interface for API key management operations
abstract class ApiKeyRepository {
  /// Save an API key securely
  Future<Either<Failure, bool>> saveApiKey(ApiKey apiKey);

  /// Get an API key by type
  Future<Either<Failure, ApiKey?>> getApiKey(String keyType);

  /// Delete an API key by type
  Future<Either<Failure, bool>> deleteApiKey(String keyType);

  /// Check if a specific API key exists
  Future<Either<Failure, bool>> hasApi<PERSON>ey(String keyType);
}
