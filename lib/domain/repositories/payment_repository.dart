import 'package:dartz/dartz.dart';
import '../failure/failures.dart';

/// Repository for handling payment operations
abstract class PaymentRepository {
  /// Process a payment for token purchase
  Future<Either<Failure, Map<String, dynamic>>> processPayment({
    required String userId,
    required int tokenAmount,
    required double price,
    required String paymentMethodId,
  });

  /// Get available token packages
  Future<Either<Failure, List<Map<String, dynamic>>>> getTokenPackages();

  /// Verify a payment transaction
  Future<Either<Failure, bool>> verifyPayment(String transactionId);

  /// Get payment history for a user
  Future<Either<Failure, List<Map<String, dynamic>>>> getPaymentHistory(
    String userId,
  );
}
