import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';
import 'package:projectpilot/domain/failure/failures.dart';

/// Repository interface for managing user data
abstract class UserRepository {
  /// Gets the current user profile
  Future<Either<Failure, UserProfile?>> getCurrentUser();

  /// Updates the user profile
  Future<Either<Failure, UserProfile>> updateUserProfile(UserProfile profile);

  /// Deletes the user account
  Future<Either<Failure, void>> deleteUserAccount(String userId);

  /// Checks if a user exists
  Future<Either<Failure, bool>> userExists(String userId);

  /// Gets user preferences
  Future<Either<Failure, Map<String, dynamic>>> getUserPreferences(
    String userId,
  );

  /// Updates user preferences
  Future<Either<Failure, void>> updateUserPreferences(
    String userId,
    Map<String, dynamic> preferences,
  );

  /// Get the current user profile
  Future<Either<Failure, UserProfile>> getCurrentUserProfile();

  /// Get the token balance for the current user
  Future<Either<Failure, int>> getTokenBalance();

  /// Get the minutes balance for the current user
  Future<Either<Failure, int>> getMinutesBalance();

  /// Upload a profile image and return the URL
  Future<Either<Failure, String>> uploadProfileImage(File imageFile);

  /// Log out the current user
  Future<Either<Failure, void>> logout();

  /// Upgrade the user's subscription package
  Future<Either<Failure, void>> upgradePackage(String packageId);
}
