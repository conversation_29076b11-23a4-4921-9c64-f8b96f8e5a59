import 'package:dartz/dartz.dart';
import '../failure/failures.dart';

/// Repository for managing user token balance
abstract class TokenRepository {
  /// Get user's token balance
  Future<Either<Failure, Map<String, dynamic>?>> getTokenBalance(String userId);

  /// Update token balance after usage
  Future<Either<Failure, void>> updateTokenBalance({
    required String userId,
    required int tokensUsed,
  });

  /// Add tokens to user's balance
  Future<Either<Failure, void>> addTokens({
    required String userId,
    required int tokensToAdd,
  });
}
