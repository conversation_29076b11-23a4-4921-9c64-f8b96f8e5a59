import 'package:flutter/material.dart';

/// Model class representing intro screen data
class IntroScreen {
  /// Title text for the intro screen
  final String title;

  /// Animation asset path or widget
  final Widget animation;

  /// Image asset path or widget
  final Widget? image;

  /// Additional description text
  final String? description;

  /// Creates an intro screen with required title and animation
  const IntroScreen({
    required this.title,
    required this.animation,
    this.image,
    this.description,
  });
}
