import 'package:equatable/equatable.dart';
import 'project.dart';
import 'project_task.dart';

/// Team member information
class TeamMember extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? role;
  final List<String> skills;
  final double workload; // 0.0 to 1.0
  final bool isActive;

  const TeamMember({
    required this.id,
    required this.name,
    required this.email,
    this.role,
    this.skills = const [],
    this.workload = 0.0,
    this.isActive = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'skills': skills,
      'workload': workload,
      'isActive': isActive,
    };
  }

  factory TeamMember.fromJson(Map<String, dynamic> json) {
    return TeamMember(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      role: json['role'] as String?,
      skills: List<String>.from(json['skills'] as List? ?? []),
      workload: (json['workload'] as num?)?.toDouble() ?? 0.0,
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    email,
    role,
    skills,
    workload,
    isActive,
  ];
}

/// Budget information for a project
class BudgetData extends Equatable {
  final double total;
  final double spent;
  final double remaining;
  final Map<String, double> categoryBreakdown;
  final DateTime lastUpdated;

  const BudgetData({
    required this.total,
    required this.spent,
    this.categoryBreakdown = const {},
    required this.lastUpdated,
  }) : remaining = total - spent;

  double get utilizationPercentage => total > 0 ? spent / total : 0.0;
  bool get isOverBudget => spent > total;

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'spent': spent,
      'remaining': remaining,
      'categoryBreakdown': categoryBreakdown,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory BudgetData.fromJson(Map<String, dynamic> json) {
    return BudgetData(
      total: (json['total'] as num).toDouble(),
      spent: (json['spent'] as num).toDouble(),
      categoryBreakdown: Map<String, double>.from(
        json['categoryBreakdown'] as Map? ?? {},
      ),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  @override
  List<Object?> get props => [
    total,
    spent,
    remaining,
    categoryBreakdown,
    lastUpdated,
  ];
}

/// Activity log entry
class ActivityLog extends Equatable {
  final String id;
  final String projectId;
  final String userId;
  final String userName;
  final String action;
  final String description;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;

  const ActivityLog({
    required this.id,
    required this.projectId,
    required this.userId,
    required this.userName,
    required this.action,
    required this.description,
    this.metadata = const {},
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'userId': userId,
      'userName': userName,
      'action': action,
      'description': description,
      'metadata': metadata,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory ActivityLog.fromJson(Map<String, dynamic> json) {
    return ActivityLog(
      id: json['id'] as String,
      projectId: json['projectId'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      action: json['action'] as String,
      description: json['description'] as String,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  @override
  List<Object?> get props => [
    id,
    projectId,
    userId,
    userName,
    action,
    description,
    metadata,
    timestamp,
  ];
}

/// Comprehensive project data for AI analysis
class ProjectData extends Equatable {
  /// Core project information
  final Project project;

  /// All tasks in the project
  final List<ProjectTask> tasks;

  /// Team members working on the project
  final List<TeamMember> teamMembers;

  /// Budget information
  final BudgetData? budget;

  /// Recent activity logs
  final List<ActivityLog> recentActivities;

  /// Platform-specific metadata
  final Map<String, dynamic> platformMetadata;

  /// When this data was last fetched
  final DateTime lastSync;

  const ProjectData({
    required this.project,
    this.tasks = const [],
    this.teamMembers = const [],
    this.budget,
    this.recentActivities = const [],
    this.platformMetadata = const {},
    required this.lastSync,
  });

  ProjectData copyWith({
    Project? project,
    List<ProjectTask>? tasks,
    List<TeamMember>? teamMembers,
    BudgetData? budget,
    List<ActivityLog>? recentActivities,
    Map<String, dynamic>? platformMetadata,
    DateTime? lastSync,
  }) {
    return ProjectData(
      project: project ?? this.project,
      tasks: tasks ?? this.tasks,
      teamMembers: teamMembers ?? this.teamMembers,
      budget: budget ?? this.budget,
      recentActivities: recentActivities ?? this.recentActivities,
      platformMetadata: platformMetadata ?? this.platformMetadata,
      lastSync: lastSync ?? this.lastSync,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'project': project.toJson(),
      'tasks': tasks.map((t) => t.toJson()).toList(),
      'teamMembers': teamMembers.map((tm) => tm.toJson()).toList(),
      'budget': budget?.toJson(),
      'recentActivities': recentActivities.map((a) => a.toJson()).toList(),
      'platformMetadata': platformMetadata,
      'lastSync': lastSync.toIso8601String(),
    };
  }

  factory ProjectData.fromJson(Map<String, dynamic> json) {
    return ProjectData(
      project: Project.fromJson(json['project'] as Map<String, dynamic>),
      tasks:
          (json['tasks'] as List?)
              ?.map((t) => ProjectTask.fromJson(t as Map<String, dynamic>))
              .toList() ??
          [],
      teamMembers:
          (json['teamMembers'] as List?)
              ?.map((tm) => TeamMember.fromJson(tm as Map<String, dynamic>))
              .toList() ??
          [],
      budget:
          json['budget'] != null
              ? BudgetData.fromJson(json['budget'] as Map<String, dynamic>)
              : null,
      recentActivities:
          (json['recentActivities'] as List?)
              ?.map((a) => ActivityLog.fromJson(a as Map<String, dynamic>))
              .toList() ??
          [],
      platformMetadata: Map<String, dynamic>.from(
        json['platformMetadata'] as Map? ?? {},
      ),
      lastSync: DateTime.parse(json['lastSync'] as String),
    );
  }

  // Analytics methods

  /// Get tasks by status
  List<ProjectTask> getTasksByStatus(ProjectTaskStatus status) =>
      tasks.where((task) => task.status == status).toList();

  /// Get blocked tasks
  List<ProjectTask> get blockedTasks =>
      tasks.where((task) => task.isBlocked).toList();

  /// Get overdue tasks
  List<ProjectTask> get overdueTasks =>
      tasks.where((task) => task.isOverdue).toList();

  /// Get tasks by assignee
  List<ProjectTask> getTasksByAssignee(String assignee) =>
      tasks.where((task) => task.assignee == assignee).toList();

  /// Get tasks by priority
  List<ProjectTask> getTasksByPriority(ProjectTaskPriority priority) =>
      tasks.where((task) => task.priority == priority).toList();

  /// Calculate overall progress based on task completion
  double get calculatedProgress {
    if (tasks.isEmpty) return 0.0;

    final completedTasks = tasks.where((task) => task.isCompleted).length;
    return completedTasks / tasks.length;
  }

  /// Get team workload distribution
  Map<String, double> get teamWorkload {
    final workloadMap = <String, double>{};

    for (final member in teamMembers) {
      final memberTasks = getTasksByAssignee(member.name);
      final activeTasks = memberTasks.where((t) => !t.isCompleted).length;
      workloadMap[member.name] = activeTasks.toDouble();
    }

    return workloadMap;
  }

  /// Get tasks that are at risk (approaching deadline)
  List<ProjectTask> get riskyTasks {
    final now = DateTime.now();
    return tasks.where((task) {
      if (task.deadline == null) return false;
      final daysRemaining = task.deadline!.difference(now).inDays;
      return daysRemaining <= 3 && daysRemaining >= 0 && !task.isCompleted;
    }).toList();
  }

  /// Get critical path tasks (tasks with no slack time)
  List<ProjectTask> get criticalPathTasks {
    // Simplified critical path: tasks that are not completed and have urgent priority
    return tasks
        .where(
          (task) =>
              !task.isCompleted &&
              task.priority == ProjectTaskPriority.critical,
        )
        .toList();
  }

  /// Check if project is data-stale (needs refresh)
  bool get isDataStale {
    final now = DateTime.now();
    const staleThreshold = Duration(hours: 1);
    return now.difference(lastSync) > staleThreshold;
  }

  /// Get task completion velocity (tasks completed per day)
  double get taskVelocity {
    final completedTasks = getTasksByStatus(ProjectTaskStatus.done);
    if (completedTasks.isEmpty) return 0.0;

    final projectDuration = DateTime.now().difference(project.createdAt).inDays;
    if (projectDuration <= 0) return 0.0;

    return completedTasks.length / projectDuration;
  }

  /// Predict project completion based on current velocity
  DateTime? get predictedCompletion {
    final velocity = taskVelocity;
    if (velocity <= 0) return null;

    final remainingTasks = tasks.where((t) => !t.isCompleted).length;
    final daysToComplete = (remainingTasks / velocity).ceil();

    return DateTime.now().add(Duration(days: daysToComplete));
  }

  @override
  List<Object?> get props => [
    project,
    tasks,
    teamMembers,
    budget,
    recentActivities,
    platformMetadata,
    lastSync,
  ];
}
