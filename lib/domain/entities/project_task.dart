import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Task status for project tasks - extended from regular task status
enum ProjectTaskStatus { toDo, inProgress, blocked, review, done, cancelled }

/// Task priority levels aligned with project priorities
enum ProjectTaskPriority { low, medium, high, urgent, critical }

/// Blocker severity levels
enum BlockerSeverity { minor, moderate, major, critical }

/// Represents a task within a project context.
class ProjectTask extends Equatable {
  /// Unique identifier for the task
  final String id;

  /// Project ID this task belongs to
  final String projectId;

  /// External ID from the platform
  final String? externalId;

  /// The title of the task
  final String name;

  /// Detailed description of the task
  final String? description;

  /// The current status of the task
  final ProjectTaskStatus status;

  /// Who is assigned to this task
  final String? assignee;

  /// Task priority level
  final ProjectTaskPriority priority;

  /// Optional deadline for the task
  final DateTime? deadline;

  /// List of blocker IDs or descriptions
  final List<String> blockers;

  /// Reason why the task is blocked (if applicable)
  final String? blockerReason;

  /// Severity of the blocker
  final BlockerSeverity? blockerSeverity;

  /// Estimated time to complete (in hours)
  final double? estimatedHours;

  /// Actual time spent (in hours)
  final double? actualHours;

  /// Progress percentage (0.0 to 1.0)
  final double progress;

  /// Additional metadata specific to the platform
  final Map<String, dynamic> metadata;

  /// When the task was created
  final DateTime createdAt;

  /// When the task was last updated
  final DateTime updatedAt;

  /// Parent task ID if this is a subtask
  final String? parentTaskId;

  /// List of subtask IDs
  final List<String> subtasks;

  /// Tags for categorization
  final List<String> tags;

  /// User ID who created this task
  final String? userId;

  const ProjectTask({
    required this.id,
    required this.projectId,
    this.externalId,
    required this.name,
    this.description,
    this.status = ProjectTaskStatus.toDo,
    this.assignee,
    this.priority = ProjectTaskPriority.medium,
    this.deadline,
    this.blockers = const [],
    this.blockerReason,
    this.blockerSeverity,
    this.estimatedHours,
    this.actualHours,
    this.progress = 0.0,
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    this.parentTaskId,
    this.subtasks = const [],
    this.tags = const [],
    this.userId,
  });

  ProjectTask copyWith({
    String? id,
    String? projectId,
    String? externalId,
    String? name,
    String? description,
    ProjectTaskStatus? status,
    String? assignee,
    ProjectTaskPriority? priority,
    DateTime? deadline,
    List<String>? blockers,
    String? blockerReason,
    BlockerSeverity? blockerSeverity,
    double? estimatedHours,
    double? actualHours,
    double? progress,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? parentTaskId,
    List<String>? subtasks,
    List<String>? tags,
    String? userId,
  }) {
    return ProjectTask(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      externalId: externalId ?? this.externalId,
      name: name ?? this.name,
      description: description ?? this.description,
      status: status ?? this.status,
      assignee: assignee ?? this.assignee,
      priority: priority ?? this.priority,
      deadline: deadline ?? this.deadline,
      blockers: blockers ?? this.blockers,
      blockerReason: blockerReason ?? this.blockerReason,
      blockerSeverity: blockerSeverity ?? this.blockerSeverity,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      actualHours: actualHours ?? this.actualHours,
      progress: progress ?? this.progress,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      parentTaskId: parentTaskId ?? this.parentTaskId,
      subtasks: subtasks ?? this.subtasks,
      tags: tags ?? this.tags,
      userId: userId ?? this.userId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'externalId': externalId,
      'name': name,
      'description': description,
      'status': status.name,
      'assignee': assignee,
      'priority': priority.name,
      'deadline': deadline?.toIso8601String(),
      'blockers': blockers,
      'blockerReason': blockerReason,
      'blockerSeverity': blockerSeverity?.name,
      'estimatedHours': estimatedHours,
      'actualHours': actualHours,
      'progress': progress,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'parentTaskId': parentTaskId,
      'subtasks': subtasks,
      'tags': tags,
      'userId': userId,
    };
  }

  factory ProjectTask.fromJson(Map<String, dynamic> json) {
    return ProjectTask(
      id: json['id'] as String,
      projectId: json['projectId'] as String,
      externalId: json['externalId'] as String?,
      name: json['name'] as String,
      description: json['description'] as String?,
      status: ProjectTaskStatus.values.firstWhere(
        (e) => e.name == (json['status'] as String),
        orElse: () => ProjectTaskStatus.toDo,
      ),
      assignee: json['assignee'] as String?,
      priority: ProjectTaskPriority.values.firstWhere(
        (e) => e.name == (json['priority'] as String),
        orElse: () => ProjectTaskPriority.medium,
      ),
      deadline:
          json['deadline'] != null
              ? DateTime.parse(json['deadline'] as String)
              : null,
      blockers: List<String>.from(json['blockers'] as List? ?? []),
      blockerReason: json['blockerReason'] as String?,
      blockerSeverity:
          json['blockerSeverity'] != null
              ? BlockerSeverity.values.firstWhere(
                (e) => e.name == (json['blockerSeverity'] as String),
                orElse: () => BlockerSeverity.minor,
              )
              : null,
      estimatedHours: (json['estimatedHours'] as num?)?.toDouble(),
      actualHours: (json['actualHours'] as num?)?.toDouble(),
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      parentTaskId: json['parentTaskId'] as String?,
      subtasks: List<String>.from(json['subtasks'] as List? ?? []),
      tags: List<String>.from(json['tags'] as List? ?? []),
      userId: json['userId'] as String?,
    );
  }

  /// Creates a new project task with a generated UUID
  factory ProjectTask.create({
    required String projectId,
    required String name,
    String? description,
    String? assignee,
    ProjectTaskPriority priority = ProjectTaskPriority.medium,
    DateTime? deadline,
    String? userId,
    String? parentTaskId,
    List<String> tags = const [],
  }) {
    final now = DateTime.now();
    return ProjectTask(
      id: const Uuid().v4(),
      projectId: projectId,
      name: name,
      description: description,
      assignee: assignee,
      priority: priority,
      deadline: deadline,
      createdAt: now,
      updatedAt: now,
      userId: userId,
      parentTaskId: parentTaskId,
      tags: tags,
    );
  }

  /// Check if task is overdue
  bool get isOverdue => deadline != null && DateTime.now().isAfter(deadline!);

  /// Check if task is blocked
  bool get isBlocked =>
      status == ProjectTaskStatus.blocked || blockers.isNotEmpty;

  /// Check if task is in progress
  bool get isInProgress => status == ProjectTaskStatus.inProgress;

  /// Check if task is completed
  bool get isCompleted => status == ProjectTaskStatus.done;

  /// Get remaining days until deadline
  int? get remainingDays => deadline?.difference(DateTime.now()).inDays;

  /// Get time efficiency (actual vs estimated hours)
  double? get timeEfficiency {
    if (estimatedHours == null || actualHours == null) return null;
    if (estimatedHours == 0) return 1.0;
    return estimatedHours! / actualHours!;
  }

  /// Check if task has subtasks
  bool get hasSubtasks => subtasks.isNotEmpty;

  /// Check if this is a subtask
  bool get isSubtask => parentTaskId != null;

  @override
  List<Object?> get props => [
    id,
    projectId,
    externalId,
    name,
    description,
    status,
    assignee,
    priority,
    deadline,
    blockers,
    blockerReason,
    blockerSeverity,
    estimatedHours,
    actualHours,
    progress,
    metadata,
    createdAt,
    updatedAt,
    parentTaskId,
    subtasks,
    tags,
    userId,
  ];
}
