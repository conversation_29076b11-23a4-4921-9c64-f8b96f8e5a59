import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Project status representing the current state of a project
enum ProjectStatus { planning, active, onHold, completed, cancelled }

/// Progress tracking for different aspects of a project
enum ProjectProgressType { tasks, budget, timeline, resources }

/// Represents a project that can be managed across different platforms.
class Project extends Equatable {
  /// Unique identifier for the project
  final String id;

  /// The name of the project
  final String name;

  /// Detailed description of the project
  final String description;

  /// Platform ID where this project is managed (e.g., "clickup", "notion")
  final String platformId;

  /// External ID from the platform
  final String externalId;

  /// Overall progress percentage (0.0 to 1.0)
  final double progress;

  /// The current status of the project
  final ProjectStatus status;

  /// Optional deadline for the project
  final DateTime? deadline;

  /// List of team member IDs or names
  final List<String> teamMembers;

  /// Additional metadata specific to the platform
  final Map<String, dynamic> metadata;

  /// User ID who owns this project
  final String? userId;

  /// When the project was created
  final DateTime createdAt;

  /// When the project was last updated
  final DateTime updatedAt;

  /// Project budget information (if available)
  final double? budget;

  /// Spent budget amount
  final double? budgetSpent;

  /// Project priority level
  final String priority;

  /// Project tags for categorization
  final List<String> tags;

  const Project({
    required this.id,
    required this.name,
    required this.description,
    required this.platformId,
    required this.externalId,
    this.progress = 0.0,
    this.status = ProjectStatus.planning,
    this.deadline,
    this.teamMembers = const [],
    this.metadata = const {},
    this.userId,
    required this.createdAt,
    required this.updatedAt,
    this.budget,
    this.budgetSpent,
    this.priority = 'medium',
    this.tags = const [],
  });

  Project copyWith({
    String? id,
    String? name,
    String? description,
    String? platformId,
    String? externalId,
    double? progress,
    ProjectStatus? status,
    DateTime? deadline,
    List<String>? teamMembers,
    Map<String, dynamic>? metadata,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? budget,
    double? budgetSpent,
    String? priority,
    List<String>? tags,
  }) {
    return Project(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      platformId: platformId ?? this.platformId,
      externalId: externalId ?? this.externalId,
      progress: progress ?? this.progress,
      status: status ?? this.status,
      deadline: deadline ?? this.deadline,
      teamMembers: teamMembers ?? this.teamMembers,
      metadata: metadata ?? this.metadata,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      budget: budget ?? this.budget,
      budgetSpent: budgetSpent ?? this.budgetSpent,
      priority: priority ?? this.priority,
      tags: tags ?? this.tags,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'platformId': platformId,
      'externalId': externalId,
      'progress': progress,
      'status': status.name,
      'deadline': deadline?.toIso8601String(),
      'teamMembers': teamMembers,
      'metadata': metadata,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'budget': budget,
      'budgetSpent': budgetSpent,
      'priority': priority,
      'tags': tags,
    };
  }

  factory Project.fromJson(Map<String, dynamic> json) {
    return Project(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      platformId: json['platformId'] as String,
      externalId: json['externalId'] as String,
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      status: ProjectStatus.values.firstWhere(
        (e) => e.name == (json['status'] as String),
        orElse: () => ProjectStatus.planning,
      ),
      deadline:
          json['deadline'] != null
              ? DateTime.parse(json['deadline'] as String)
              : null,
      teamMembers: List<String>.from(json['teamMembers'] as List? ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      userId: json['userId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      budget: (json['budget'] as num?)?.toDouble(),
      budgetSpent: (json['budgetSpent'] as num?)?.toDouble(),
      priority: json['priority'] as String? ?? 'medium',
      tags: List<String>.from(json['tags'] as List? ?? []),
    );
  }

  /// Creates a new project with a generated UUID
  factory Project.create({
    required String name,
    required String description,
    required String platformId,
    required String externalId,
    String? userId,
    double? budget,
    String priority = 'medium',
    List<String> tags = const [],
  }) {
    final now = DateTime.now();
    return Project(
      id: const Uuid().v4(),
      name: name,
      description: description,
      platformId: platformId,
      externalId: externalId,
      userId: userId,
      createdAt: now,
      updatedAt: now,
      budget: budget,
      priority: priority,
      tags: tags,
    );
  }

  /// Check if project is overdue
  bool get isOverdue => deadline != null && DateTime.now().isAfter(deadline!);

  /// Check if project is on track (progress vs time elapsed)
  bool get isOnTrack {
    if (deadline == null) return true;

    final now = DateTime.now();
    final totalDuration = deadline!.difference(createdAt).inDays;
    final elapsedDuration = now.difference(createdAt).inDays;

    if (totalDuration <= 0) return true;

    final expectedProgress = elapsedDuration / totalDuration;
    return progress >= expectedProgress * 0.9; // 10% tolerance
  }

  /// Get remaining days until deadline
  int? get remainingDays => deadline?.difference(DateTime.now()).inDays;

  /// Get budget utilization percentage
  double? get budgetUtilization {
    if (budget == null || budgetSpent == null) return null;
    if (budget == 0) return 0.0;
    return budgetSpent! / budget!;
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    platformId,
    externalId,
    progress,
    status,
    deadline,
    teamMembers,
    metadata,
    userId,
    createdAt,
    updatedAt,
    budget,
    budgetSpent,
    priority,
    tags,
  ];
}
