import 'package:equatable/equatable.dart';

/// Represents an API key
class <PERSON><PERSON><PERSON><PERSON> extends Equatable {
  /// Type of the API key (e.g. 'OpenAI', 'Whisper')
  final String keyType;
  
  /// Value of the API key
  final String keyValue;
  
  /// Description of the API key
  final String? description;
  
  /// Last update timestamp
  final DateTime lastUpdated;
  
  /// Creates a new [ApiKey] instance
  ApiKey({
    required this.keyType,
    required this.keyValue,
    this.description,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();
  
  @override
  List<Object?> get props => [keyType, keyValue, description, lastUpdated];
}
