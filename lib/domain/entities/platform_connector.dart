import 'package:equatable/equatable.dart';

/// Represents a connected integration platform with its authentication details.
class PlatformConnector extends Equatable {
  final String id;
  final String platformId;
  final String name;
  final String apiKey;
  final Map<String, dynamic>? additionalAuth;
  final bool isActive;
  final Map<String, bool> contentTypes;
  final String? defaultProject;

  /// Returns a user-friendly label for the connector,
  /// which can be used for display purposes.
  String get label => name != platformId ? name : '';

  const PlatformConnector({
    required this.id,
    required this.platformId,
    required this.name,
    required this.apiKey,
    this.additionalAuth,
    this.isActive = true,
    this.contentTypes = const {'task': false, 'note': false, 'idea': false},
    this.defaultProject,
  });

  PlatformConnector copyWith({
    String? id,
    String? platformId,
    String? name,
    String? apiKey,
    Map<String, dynamic>? additionalAuth,
    bool? isActive,
    Map<String, bool>? contentTypes,
    String? defaultProject,
  }) {
    return PlatformConnector(
      id: id ?? this.id,
      platformId: platformId ?? this.platformId,
      name: name ?? this.name,
      apiKey: apiKey ?? this.apiKey,
      additionalAuth: additionalAuth ?? this.additionalAuth,
      isActive: isActive ?? this.isActive,
      contentTypes: contentTypes ?? this.contentTypes,
      defaultProject: defaultProject ?? this.defaultProject,
    );
  }

  PlatformConnector toggleContentType(String type) {
    final updatedContentTypes = Map<String, bool>.from(contentTypes);
    updatedContentTypes[type] = !(contentTypes[type] ?? false);
    return copyWith(contentTypes: updatedContentTypes);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'platformId': platformId,
      'name': name,
      'apiKey': apiKey,
      'additionalAuth': additionalAuth,
      'isActive': isActive,
      'contentTypes': contentTypes,
      'defaultProject': defaultProject,
    };
  }

  factory PlatformConnector.fromJson(Map<String, dynamic> json) {
    return PlatformConnector(
      id: json['id'] as String,
      platformId: json['platformId'] as String,
      name: json['name'] as String,
      apiKey: json['apiKey'] as String,
      additionalAuth: json['additionalAuth'] as Map<String, dynamic>?,
      isActive: json['isActive'] as bool? ?? true,
      contentTypes:
          json['contentTypes'] != null
              ? Map<String, bool>.from(json['contentTypes'] as Map)
              : const {'task': false, 'note': false, 'idea': false},
      defaultProject: json['defaultProject'] as String?,
    );
  }

  @override
  List<Object?> get props => [
    id,
    platformId,
    name,
    apiKey,
    isActive,
    contentTypes,
    defaultProject,
  ];
}
