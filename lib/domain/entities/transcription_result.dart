enum ContentCategory { task, idea, note, unknown }

class TranscriptionResult {
  final String originalTranscription;
  final String optimizedContent;
  final ContentCategory category;
  final double confidence;

  const TranscriptionResult({
    required this.originalTranscription,
    required this.optimizedContent,
    required this.category,
    required this.confidence,
  });

  factory TranscriptionResult.fromJson(Map<String, dynamic> json) {
    ContentCategory parseCategory(String? category) {
      if (category == null) return ContentCategory.unknown;

      switch (category.toUpperCase()) {
        case 'TASK':
          return ContentCategory.task;
        case 'IDEA':
          return ContentCategory.idea;
        case 'NOTE':
          return ContentCategory.note;
        default:
          return ContentCategory.unknown;
      }
    }

    return TranscriptionResult(
      originalTranscription: json['transcription'] ?? '',
      optimizedContent: json['optimizedContent'] ?? '',
      category: parseCategory(json['category']),
      confidence: (json['confidence'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() => {
    'transcription': originalTranscription,
    'optimizedContent': optimizedContent,
    'category': category.toString().split('.').last.toUpperCase(),
    'confidence': confidence,
  };
}
