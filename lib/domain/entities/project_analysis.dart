import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';
import 'project_task.dart'; // Import BlockerSeverity from project_task.dart

/// Recommendation priority levels
enum RecommendationPriority { low, medium, high, urgent, critical }

/// Analysis types that can be performed
enum AnalysisType { status, blockers, timeline, budget, resources, risks }

/// Budget status categories
enum BudgetStatus { underBudget, onTrack, overBudget, critical }

/// Represents a project blocker identified by AI analysis.
class ProjectBlocker extends Equatable {
  /// Unique identifier for the blocker
  final String id;

  /// Task ID that is blocked
  final String taskId;

  /// Task name for reference
  final String taskName;

  /// Description of the blocker
  final String description;

  /// Severity level of the blocker
  final BlockerSeverity severity;

  /// Person assigned to resolve the blocker
  final String? assignee;

  /// Estimated time to resolve (ETA)
  final DateTime? eta;

  /// List of dependent task IDs affected by this blocker
  final List<String> dependentTasks;

  /// Suggested resolution actions
  final List<String> suggestedActions;

  /// When the blocker was identified
  final DateTime identifiedAt;

  const ProjectBlocker({
    required this.id,
    required this.taskId,
    required this.taskName,
    required this.description,
    required this.severity,
    this.assignee,
    this.eta,
    this.dependentTasks = const [],
    this.suggestedActions = const [],
    required this.identifiedAt,
  });

  ProjectBlocker copyWith({
    String? id,
    String? taskId,
    String? taskName,
    String? description,
    BlockerSeverity? severity,
    String? assignee,
    DateTime? eta,
    List<String>? dependentTasks,
    List<String>? suggestedActions,
    DateTime? identifiedAt,
  }) {
    return ProjectBlocker(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      taskName: taskName ?? this.taskName,
      description: description ?? this.description,
      severity: severity ?? this.severity,
      assignee: assignee ?? this.assignee,
      eta: eta ?? this.eta,
      dependentTasks: dependentTasks ?? this.dependentTasks,
      suggestedActions: suggestedActions ?? this.suggestedActions,
      identifiedAt: identifiedAt ?? this.identifiedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'taskName': taskName,
      'description': description,
      'severity': severity.name,
      'assignee': assignee,
      'eta': eta?.toIso8601String(),
      'dependentTasks': dependentTasks,
      'suggestedActions': suggestedActions,
      'identifiedAt': identifiedAt.toIso8601String(),
    };
  }

  factory ProjectBlocker.fromJson(Map<String, dynamic> json) {
    return ProjectBlocker(
      id: json['id'] as String,
      taskId: json['taskId'] as String,
      taskName: json['taskName'] as String,
      description: json['description'] as String,
      severity: BlockerSeverity.values.firstWhere(
        (e) => e.name == (json['severity'] as String),
        orElse: () => BlockerSeverity.minor,
      ),
      assignee: json['assignee'] as String?,
      eta: json['eta'] != null ? DateTime.parse(json['eta'] as String) : null,
      dependentTasks: List<String>.from(json['dependentTasks'] as List? ?? []),
      suggestedActions: List<String>.from(
        json['suggestedActions'] as List? ?? [],
      ),
      identifiedAt: DateTime.parse(json['identifiedAt'] as String),
    );
  }

  @override
  List<Object?> get props => [
    id,
    taskId,
    taskName,
    description,
    severity,
    assignee,
    eta,
    dependentTasks,
    suggestedActions,
    identifiedAt,
  ];
}

/// Represents an AI-generated action recommendation for a project.
class ActionRecommendation extends Equatable {
  /// Unique identifier for the recommendation
  final String id;

  /// Title of the recommendation
  final String title;

  /// Detailed description of the recommendation
  final String description;

  /// Priority level of this recommendation
  final RecommendationPriority priority;

  /// List of task IDs that would be affected by this action
  final List<String> affectedTasks;

  /// Estimated impact score (0.0 to 1.0)
  final double impactScore;

  /// Expected effort to implement (in hours)
  final double? effortHours;

  /// Expected completion time frame
  final String? timeframe;

  /// Category of the recommendation
  final String category;

  /// Whether this recommendation has been acted upon
  final bool isActedUpon;

  /// When the recommendation was generated
  final DateTime generatedAt;

  const ActionRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.priority,
    this.affectedTasks = const [],
    this.impactScore = 0.0,
    this.effortHours,
    this.timeframe,
    this.category = 'general',
    this.isActedUpon = false,
    required this.generatedAt,
  });

  ActionRecommendation copyWith({
    String? id,
    String? title,
    String? description,
    RecommendationPriority? priority,
    List<String>? affectedTasks,
    double? impactScore,
    double? effortHours,
    String? timeframe,
    String? category,
    bool? isActedUpon,
    DateTime? generatedAt,
  }) {
    return ActionRecommendation(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      affectedTasks: affectedTasks ?? this.affectedTasks,
      impactScore: impactScore ?? this.impactScore,
      effortHours: effortHours ?? this.effortHours,
      timeframe: timeframe ?? this.timeframe,
      category: category ?? this.category,
      isActedUpon: isActedUpon ?? this.isActedUpon,
      generatedAt: generatedAt ?? this.generatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'priority': priority.name,
      'affectedTasks': affectedTasks,
      'impactScore': impactScore,
      'effortHours': effortHours,
      'timeframe': timeframe,
      'category': category,
      'isActedUpon': isActedUpon,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  factory ActionRecommendation.fromJson(Map<String, dynamic> json) {
    return ActionRecommendation(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      priority: RecommendationPriority.values.firstWhere(
        (e) => e.name == (json['priority'] as String),
        orElse: () => RecommendationPriority.medium,
      ),
      affectedTasks: List<String>.from(json['affectedTasks'] as List? ?? []),
      impactScore: (json['impactScore'] as num?)?.toDouble() ?? 0.0,
      effortHours: (json['effortHours'] as num?)?.toDouble(),
      timeframe: json['timeframe'] as String?,
      category: json['category'] as String? ?? 'general',
      isActedUpon: json['isActedUpon'] as bool? ?? false,
      generatedAt: DateTime.parse(json['generatedAt'] as String),
    );
  }

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    priority,
    affectedTasks,
    impactScore,
    effortHours,
    timeframe,
    category,
    isActedUpon,
    generatedAt,
  ];
}

/// Represents a critical path in project execution.
class CriticalPath extends Equatable {
  /// Unique identifier for the critical path
  final String id;

  /// List of task IDs in the critical path
  final List<String> taskIds;

  /// Total duration of the critical path (in days)
  final int totalDuration;

  /// Risk level of this critical path
  final String riskLevel;

  /// Description of the critical path
  final String description;

  const CriticalPath({
    required this.id,
    required this.taskIds,
    required this.totalDuration,
    this.riskLevel = 'medium',
    this.description = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskIds': taskIds,
      'totalDuration': totalDuration,
      'riskLevel': riskLevel,
      'description': description,
    };
  }

  factory CriticalPath.fromJson(Map<String, dynamic> json) {
    return CriticalPath(
      id: json['id'] as String,
      taskIds: List<String>.from(json['taskIds'] as List),
      totalDuration: json['totalDuration'] as int,
      riskLevel: json['riskLevel'] as String? ?? 'medium',
      description: json['description'] as String? ?? '',
    );
  }

  @override
  List<Object?> get props => [
    id,
    taskIds,
    totalDuration,
    riskLevel,
    description,
  ];
}

/// Represents a comprehensive AI analysis of a project.
class ProjectAnalysis extends Equatable {
  /// Unique identifier for the analysis
  final String id;

  /// Project ID this analysis belongs to
  final String projectId;

  /// Overall progress percentage calculated by AI
  final double progressPercentage;

  /// List of identified blockers
  final List<ProjectBlocker> blockers;

  /// List of AI-generated recommendations
  final List<ActionRecommendation> recommendations;

  /// Critical paths identified in the project
  final List<CriticalPath> criticalPaths;

  /// Budget status assessment
  final BudgetStatus? budgetStatus;

  /// Summary text generated by AI
  final String summary;

  /// Key insights from the analysis
  final List<String> keyInsights;

  /// Risk assessment score (0.0 to 1.0)
  final double riskScore;

  /// Predicted completion date based on current progress
  final DateTime? predictedCompletion;

  /// Confidence level of the analysis (0.0 to 1.0)
  final double confidenceLevel;

  /// When the analysis was performed
  final DateTime analyzedAt;

  /// Additional metadata from the analysis
  final Map<String, dynamic> metadata;

  const ProjectAnalysis({
    required this.id,
    required this.projectId,
    required this.progressPercentage,
    this.blockers = const [],
    this.recommendations = const [],
    this.criticalPaths = const [],
    this.budgetStatus,
    this.summary = '',
    this.keyInsights = const [],
    this.riskScore = 0.0,
    this.predictedCompletion,
    this.confidenceLevel = 0.0,
    required this.analyzedAt,
    this.metadata = const {},
  });

  ProjectAnalysis copyWith({
    String? id,
    String? projectId,
    double? progressPercentage,
    List<ProjectBlocker>? blockers,
    List<ActionRecommendation>? recommendations,
    List<CriticalPath>? criticalPaths,
    BudgetStatus? budgetStatus,
    String? summary,
    List<String>? keyInsights,
    double? riskScore,
    DateTime? predictedCompletion,
    double? confidenceLevel,
    DateTime? analyzedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ProjectAnalysis(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      blockers: blockers ?? this.blockers,
      recommendations: recommendations ?? this.recommendations,
      criticalPaths: criticalPaths ?? this.criticalPaths,
      budgetStatus: budgetStatus ?? this.budgetStatus,
      summary: summary ?? this.summary,
      keyInsights: keyInsights ?? this.keyInsights,
      riskScore: riskScore ?? this.riskScore,
      predictedCompletion: predictedCompletion ?? this.predictedCompletion,
      confidenceLevel: confidenceLevel ?? this.confidenceLevel,
      analyzedAt: analyzedAt ?? this.analyzedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'progressPercentage': progressPercentage,
      'blockers': blockers.map((b) => b.toJson()).toList(),
      'recommendations': recommendations.map((r) => r.toJson()).toList(),
      'criticalPaths': criticalPaths.map((c) => c.toJson()).toList(),
      'budgetStatus': budgetStatus?.name,
      'summary': summary,
      'keyInsights': keyInsights,
      'riskScore': riskScore,
      'predictedCompletion': predictedCompletion?.toIso8601String(),
      'confidenceLevel': confidenceLevel,
      'analyzedAt': analyzedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory ProjectAnalysis.fromJson(Map<String, dynamic> json) {
    return ProjectAnalysis(
      id: json['id'] as String,
      projectId: json['projectId'] as String,
      progressPercentage: (json['progressPercentage'] as num).toDouble(),
      blockers:
          (json['blockers'] as List?)
              ?.map((b) => ProjectBlocker.fromJson(b as Map<String, dynamic>))
              .toList() ??
          [],
      recommendations:
          (json['recommendations'] as List?)
              ?.map(
                (r) => ActionRecommendation.fromJson(r as Map<String, dynamic>),
              )
              .toList() ??
          [],
      criticalPaths:
          (json['criticalPaths'] as List?)
              ?.map((c) => CriticalPath.fromJson(c as Map<String, dynamic>))
              .toList() ??
          [],
      budgetStatus:
          json['budgetStatus'] != null
              ? BudgetStatus.values.firstWhere(
                (e) => e.name == json['budgetStatus'],
                orElse: () => BudgetStatus.onTrack,
              )
              : null,
      summary: json['summary'] as String? ?? '',
      keyInsights: List<String>.from(json['keyInsights'] as List? ?? []),
      riskScore: (json['riskScore'] as num?)?.toDouble() ?? 0.0,
      predictedCompletion:
          json['predictedCompletion'] != null
              ? DateTime.parse(json['predictedCompletion'] as String)
              : null,
      confidenceLevel: (json['confidenceLevel'] as num?)?.toDouble() ?? 0.0,
      analyzedAt: DateTime.parse(json['analyzedAt'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  /// Creates a new analysis with a generated UUID
  factory ProjectAnalysis.create({
    required String projectId,
    required double progressPercentage,
    List<ProjectBlocker> blockers = const [],
    List<ActionRecommendation> recommendations = const [],
    String summary = '',
    double riskScore = 0.0,
    Map<String, dynamic> metadata = const {},
  }) {
    return ProjectAnalysis(
      id: const Uuid().v4(),
      projectId: projectId,
      progressPercentage: progressPercentage,
      blockers: blockers,
      recommendations: recommendations,
      summary: summary,
      riskScore: riskScore,
      analyzedAt: DateTime.now(),
      metadata: metadata,
    );
  }

  /// Get high priority blockers
  List<ProjectBlocker> get highPriorityBlockers =>
      blockers
          .where(
            (b) =>
                b.severity == BlockerSeverity.major ||
                b.severity == BlockerSeverity.critical,
          )
          .toList();

  /// Get urgent recommendations
  List<ActionRecommendation> get urgentRecommendations =>
      recommendations
          .where(
            (r) =>
                r.priority == RecommendationPriority.urgent ||
                r.priority == RecommendationPriority.critical,
          )
          .toList();

  /// Check if project is at risk
  bool get isAtRisk => riskScore > 0.7;

  /// Check if project has critical blockers
  bool get hasCriticalBlockers =>
      blockers.any((b) => b.severity == BlockerSeverity.critical);

  @override
  List<Object?> get props => [
    id,
    projectId,
    progressPercentage,
    blockers,
    recommendations,
    criticalPaths,
    budgetStatus,
    summary,
    keyInsights,
    riskScore,
    predictedCompletion,
    confidenceLevel,
    analyzedAt,
    metadata,
  ];
}
