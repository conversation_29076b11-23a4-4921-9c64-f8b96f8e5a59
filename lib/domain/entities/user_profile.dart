import 'package:equatable/equatable.dart';

/// Represents a user profile
class UserProfile extends Equatable {
  /// Unique identifier for the user
  final String id;

  /// User's username
  final String? username;

  /// User's email address
  final String? email;

  /// User's profile image URL
  final String? profileImageUrl;

  /// User's birth year
  final int? birthYear;

  /// User's role (developer, project_manager, product_owner, qa_tester, cto_ceo)
  final String? role;

  /// User's goals/purposes as a list of strings
  final List<String> goals;
  
  /// Whether onboarding has been completed
  final bool completedOnboarding;

  /// Creates a new [UserProfile] instance
  const UserProfile({
    required this.id,
    this.username,
    this.email,
    this.profileImageUrl,
    this.birthYear,
    this.role,
    this.goals = const [],
    this.completedOnboarding = false,
  });

  /// Creates an empty user profile
  factory UserProfile.empty() => const UserProfile(id: '');

  /// Creates a copy of this profile with the given fields replaced
  UserProfile copyWith({
    String? id,
    String? username,
    String? email,
    String? profileImageUrl,
    int? birthYear,
    String? role,
    List<String>? goals,
    bool? completedOnboarding,
  }) {
    return UserProfile(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      birthYear: birthYear ?? this.birthYear,
      role: role ?? this.role,
      goals: goals ?? this.goals,
      completedOnboarding: completedOnboarding ?? this.completedOnboarding,
    );
  }

  @override
  List<Object?> get props => [
    id,
    username,
    email,
    profileImageUrl,
    birthYear,
    role,
    goals,
    completedOnboarding,
  ];
}
