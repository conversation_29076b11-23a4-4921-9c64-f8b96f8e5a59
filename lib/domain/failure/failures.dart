import 'package:equatable/equatable.dart';

/// Base failure class for domain layer error handling.
abstract class Failure extends Equatable {
  final String message;
  final StackTrace? stackTrace;

  const Failure({required this.message, this.stackTrace});

  @override
  List<Object?> get props => [message, stackTrace];
}

/// Failure related to server errors.
class ServerFailure extends Failure {
  final int? statusCode;

  const ServerFailure({
    required super.message,
    this.statusCode,
    super.stackTrace,
  });

  @override
  List<Object?> get props => [message, statusCode, stackTrace];
}

/// Failure related to cache/local storage errors.
class CacheFailure extends Failure {
  const CacheFailure({required super.message, super.stackTrace});
}

/// Failure related to network connectivity issues.
class NetworkFailure extends Failure {
  const NetworkFailure({required super.message, super.stackTrace});
}

/// Failure related to invalid input or validation errors.
class ValidationFailure extends Failure {
  final Map<String, String>? fieldErrors;

  const ValidationFailure({
    required super.message,
    this.fieldErrors,
    super.stackTrace,
  });

  @override
  List<Object?> get props => [message, fieldErrors, stackTrace];
}

/// Failure related to authentication errors.
class AuthFailure extends Failure {
  const AuthFailure({required super.message, super.stackTrace});
}

/// Failure related to payment processing errors.
class PaymentFailure extends Failure {
  final String? paymentIntentId;
  final String? errorCode;

  const PaymentFailure({
    required super.message,
    this.paymentIntentId,
    this.errorCode,
    super.stackTrace,
  });

  @override
  List<Object?> get props => [message, paymentIntentId, errorCode, stackTrace];
}

/// Failure related to insufficient tokens.
class InsufficientTokensFailure extends Failure {
  final int available;
  final int required;

  const InsufficientTokensFailure({
    required super.message,
    required this.available,
    required this.required,
    super.stackTrace,
  });

  @override
  List<Object?> get props => [message, available, required, stackTrace];
}

/// Failure related to API key management.
class ApiKeyFailure extends Failure {
  final String? keyType;

  const ApiKeyFailure({required super.message, this.keyType, super.stackTrace});

  @override
  List<Object?> get props => [message, keyType, stackTrace];
}

/// Failure related to resource not found errors.
class NotFoundFailure extends Failure {
  const NotFoundFailure({required super.message, super.stackTrace});
}
