import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_swipe_action_cell/flutter_swipe_action_cell.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:projectpilot/core/config/env_config.dart';
import 'package:projectpilot/core/constants/app_constants.dart';
import 'package:projectpilot/core/di/service_locator.dart';
import 'package:projectpilot/core/navigation/routes/routes.dart';
import 'package:projectpilot/core/themes/app_theme.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/presentation/ui/bloc/api_key/api_key_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/audio/audio_player_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/connectivity/connectivity_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/intro/intro_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/language/language_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/language/language_state.dart';
import 'package:projectpilot/presentation/ui/bloc/recording/recording_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/theme/theme_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/theme/theme_state.dart';
import 'package:projectpilot/presentation/ui/bloc/platform_connections/platform_connections_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/auth/auth_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/auth/auth_state.dart'
    as app_auth;
import 'package:projectpilot/domain/repositories/user_repository.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:projectpilot/presentation/ui/screens/info/error_screen.dart';
import 'package:projectpilot/core/services/notification/notification_service.dart';
import 'dart:async';

/// Application entry point
void main() async {
  // Ensure Flutter bindings are initialized
  WidgetsFlutterBinding.ensureInitialized();
  
  // Configure system UI overlays to show status bar
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );

  // Ensure system overlays are visible
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
    overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
  );
  
  // Initialize logger
  final logger = Logger();

  try {
    // Initialize environment configuration
    await EnvConfig.init();
    logger.i('Environment initialized successfully');

    // Attempt to clear a possibly corrupted tasks database on startup
    await resetDatabaseIfCorrupted();

    // Initialize Firebase FIRST - required for Google Sign-In
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    logger.i('Firebase initialized successfully');

    // Initialize Supabase AFTER Firebase
    await Supabase.initialize(
      url: AppConstants.supabaseUrl,
      anonKey: AppConstants.supabaseAnonKey,
      debug: true,
    );
    logger.i('Supabase initialized successfully');

    // Initialize all dependencies with the consolidated service locator
    // This will handle Hive and other services initialization
    await initServiceLocator();
    logger.i('Service locator initialized');

    // Initialize notification service
    await sl<NotificationService>().init();
    logger.i('Notification service initialized');

    // Notification service is initialized
    // Note: Unassigned entries check removed as we focus on project management now

    // Run the actual app once everything is initialized
    runApp(
      MultiBlocProvider(
        providers: [
          BlocProvider.value(value: sl<ThemeCubit>()),
          BlocProvider.value(value: sl<LanguageCubit>()),
          BlocProvider.value(value: sl<IntroCubit>()),
          BlocProvider.value(value: sl<RecordingCubit>()),
          BlocProvider.value(value: sl<AudioPlayerCubit>()),
          BlocProvider.value(value: sl<ConnectivityCubit>()),
          BlocProvider.value(value: sl<ApiKeyCubit>()),
          BlocProvider.value(value: sl<AuthCubit>()),
          BlocProvider.value(value: sl<PlatformConnectionsCubit>()),
          BlocProvider.value(value: sl<SettingsCubit>()),
        ],
        child: const MyApp(),
      ),
    );

    // Uncomment this to run the actual app:
    /*
    runApp(
      MultiBlocProvider(
        providers: [
          BlocProvider.value(value: sl<ThemeCubit>()),
          BlocProvider.value(value: sl<LanguageCubit>()),
          BlocProvider.value(value: sl<IntroCubit>()),
          BlocProvider.value(value: sl<RecordingCubit>()),
          BlocProvider.value(value: sl<AudioPlayerCubit>()),
          BlocProvider.value(value: sl<ConnectivityCubit>()),
          BlocProvider.value(value: sl<ApiKeyCubit>()),
          BlocProvider.value(value: sl<AuthCubit>()),
          BlocProvider.value(value: sl<PlatformConnectionsCubit>()),
          BlocProvider.value(value: sl<SettingsCubit>()),
        ],
        child: const MyApp(),
      ),
    );
    */
  } catch (e) {
    logger.e('Error during initialization: $e');
    // Show error screen if initialization fails
    runApp(ErrorScreen(error: e.toString(), onRetry: main));
  }
}

/// Attempt to reset database files if they might be corrupted
Future<void> resetDatabaseIfCorrupted() async {
  try {
    // Only do this for the tasks database which is causing errors
    final appDocDir = await getApplicationDocumentsDirectory();
    final hivePath =
        '${appDocDir.path}/hive_data'; // Matches the path in _initializeHive
    final boxPath = '$hivePath/tasks.hive';
    final boxFile = File(boxPath);
    if (await boxFile.exists()) {
      // Move to backup instead of deleting, just in case
      await boxFile.copy(
        '$boxPath.backup-${DateTime.now().millisecondsSinceEpoch}',
      );
      await boxFile.delete();
      debugPrint('Removed potentially corrupted tasks database');
    }

    // Also remove the lock file
    final lockPath = '$hivePath/tasks.lock';
    final lockFile = File(lockPath);
    if (await lockFile.exists()) {
      await lockFile.delete();
      debugPrint('Removed tasks database lock file');
    }
  } catch (e) {
    debugPrint('Error when trying to reset database: $e');
    // Continue even if this fails
  }
}

/// Main app widget
class MyApp extends StatefulWidget {
  /// Creates the main app
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool _showIntro = true;
  bool _checking = true;
  bool _onboardingCompleted = false;

  @override
  void initState() {
    super.initState();
    _checkAuthAndIntroStatus();
  }

  /// Wait for AuthCubit to complete initial session check
  Future<bool> _waitForAuthInitialization(AuthCubit authCubit) async {
    print('DEBUG - Waiting for AuthCubit initialization');

    // If we already have an auth state that's not initial/loading, return immediately
    if (authCubit.state.status != app_auth.AuthStatus.initial &&
        authCubit.state.status != app_auth.AuthStatus.loading) {
      print(
        'DEBUG - AuthCubit already initialized with status: ${authCubit.state.status}',
      );
      return authCubit.state.isAuthenticated;
    }

    // Otherwise, wait for the auth state to change from initial
    // But don't wait forever - use a timeout
    final completer = Completer<bool>();
    late StreamSubscription subscription;
    Timer? timeout;

    subscription = authCubit.stream.listen((state) {
      print('DEBUG - AuthCubit state update during wait: ${state.status}');
      // Once we get a non-initial/non-loading state, we're done
      if (state.status != app_auth.AuthStatus.initial &&
          state.status != app_auth.AuthStatus.loading) {
        print(
          'DEBUG - AuthCubit initialization complete: ${state.isAuthenticated}',
        );
        timeout?.cancel();
        subscription.cancel();
        if (!completer.isCompleted) {
          completer.complete(state.isAuthenticated);
        }
      }
    });

    // Set a timeout of 3 seconds max
    timeout = Timer(const Duration(seconds: 3), () {
      print('DEBUG - AuthCubit initialization timeout');
      subscription.cancel();
      if (!completer.isCompleted) {
        completer.complete(authCubit.state.isAuthenticated);
      }
    });

    return completer.future;
  }

  Future<void> _checkAuthAndIntroStatus() async {
    final introCubit = sl<IntroCubit>();
    final authCubit = sl<AuthCubit>();
    final userRepository = sl<UserRepository>();

    print('DEBUG - Starting auth and intro status check');

    // First check if intro has been completed
    final introCompleted = await introCubit.checkIntroCompleted();
    print('DEBUG - Intro completed: $introCompleted');

    // Wait for AuthCubit to complete its session check
    final isAuthenticated = await _waitForAuthInitialization(authCubit);
    print('DEBUG - Final authentication state: $isAuthenticated');

    // Check if user has completed onboarding
    bool onboardingCompleted = false;
    if (isAuthenticated) {
      print('DEBUG - User is authenticated, checking onboarding status');
      final userResult = await userRepository.getCurrentUserProfile();
      userResult.fold(
        (failure) {
          print('DEBUG - Failed to get user profile: ${failure.message}');
          onboardingCompleted = false;
        },
        (userProfile) {
          onboardingCompleted = userProfile.completedOnboarding;
          print(
            'DEBUG - User profile loaded, onboarding completed: $onboardingCompleted',
          );
          print(
            'DEBUG - User email: ${userProfile.email}, username: ${userProfile.username}',
          );
        },
      );
    }
    
    print(
      'DEBUG - Setting final state: showIntro: ${!introCompleted}, onboardingCompleted: $onboardingCompleted',
    );
    
    setState(() {
      _showIntro = !introCompleted;
      _onboardingCompleted = onboardingCompleted;
      _checking = false;
    });
    
    print('DEBUG - Final route will be: ${_getInitialRoute()}');
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LanguageCubit, LanguageState>(
      builder: (context, languageState) {
        return BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, themeState) {
            // Set system UI style based on theme
            final isDarkMode = themeState.themeMode == ThemeMode.dark;
            SystemChrome.setSystemUIOverlayStyle(
              SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness:
                    isDarkMode ? Brightness.light : Brightness.dark,
                statusBarBrightness:
                    isDarkMode ? Brightness.dark : Brightness.light,
              ),
            );
            
            return BlocListener<AuthCubit, app_auth.AuthState>(
              listener: (context, authState) {
                // Update navigation if auth state changes
                print(
                  'DEBUG - AuthCubit state changed: ${authState.status}, isAuthenticated: ${authState.isAuthenticated}',
                );
                if (!_checking) {
                  print('DEBUG - Auth state changed, rechecking status');
                  _checkAuthAndIntroStatus();
                }
              },
              child: MaterialApp(
                builder: (context, child) {
                  return child!;
                },
                navigatorKey:
                    navigatorKey, // Use navigator key from RecordingCubit
                title: 'ProjectPilot',
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: themeState.themeMode,
                debugShowCheckedModeBanner: false,
                // Add SwipeActionNavigatorObserver to close swipe actions when navigating
                navigatorObservers: [SwipeActionNavigatorObserver()],
                // Localization setup
                locale: Locale(languageState.languageCode),
                localizationsDelegates: [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: const [
                  Locale('en'), // English
                  Locale('de'), // German
                  Locale('ru'), // Russian
                  Locale('tr'), // Turkish
                  Locale('ar'), // Arabic
                ],
                // Define routes based on intro and auth state
                initialRoute: _getInitialRoute(),
                onGenerateRoute: AppRoutes.generateRoute,
              ),
            );
          },
        );
      },
    );
  }
  
  String _getInitialRoute() {
    print('DEBUG - _getInitialRoute called:');
    print('  - _checking: $_checking');
    print('  - _showIntro: $_showIntro');
    print('  - _onboardingCompleted: $_onboardingCompleted');
    
    if (_checking) {
      // While checking, we should show a splash screen instead of immediately going to auth
      print('  - Returning intro (still checking)');
      return AppRoutes.intro;
    }

    // Always show intro first for new installations
    if (_showIntro) {
      print('  - Returning intro (show intro = true)');
      return AppRoutes.intro;
    }

    // After intro is completed, check authentication
    final authCubit = sl<AuthCubit>();
    final isAuthenticated = authCubit.state.isAuthenticated;
    print('  - isAuthenticated: $isAuthenticated');

    // If not authenticated, show auth screen
    if (!isAuthenticated) {
      print('  - Returning auth (not authenticated)');
      return AppRoutes.auth;
    }

    // If authenticated but not completed onboarding, show onboarding
    if (!_onboardingCompleted) {
      print(
        '  - Returning onboarding (authenticated but onboarding not completed)',
      );
      return AppRoutes.onboarding;
    }

    // If authenticated and completed onboarding, proceed to home screen
    print('  - Returning home (authenticated and onboarding completed)');
    return AppRoutes.home;
  }
}


