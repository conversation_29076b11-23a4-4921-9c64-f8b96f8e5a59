import 'package:flutter/services.dart';
import 'package:logger/logger.dart';

/// Service for providing haptic feedback
class HapticFeedbackService {
  final Logger _logger = Logger();
  
  /// Whether haptic feedback is enabled
  bool _isEnabled = true;
  
  /// Singleton instance
  static final HapticFeedbackService _instance = HapticFeedbackService._internal();
  
  /// Factory constructor
  factory HapticFeedbackService() => _instance;
  
  /// Private constructor
  HapticFeedbackService._internal();
  
  /// Enable or disable haptic feedback
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }
  
  /// Check if haptic feedback is enabled
  bool get isEnabled => _isEnabled;
  
  /// Provide light impact feedback
  Future<void> lightImpact() async {
    if (!_isEnabled) return;
    
    try {
      await HapticFeedback.lightImpact();
    } catch (e) {
      _logger.e('Error providing light impact haptic feedback: $e');
    }
  }
  
  /// Provide medium impact feedback
  Future<void> mediumImpact() async {
    if (!_isEnabled) return;
    
    try {
      await HapticFeedback.mediumImpact();
    } catch (e) {
      _logger.e('Error providing medium impact haptic feedback: $e');
    }
  }
  
  /// Provide heavy impact feedback
  Future<void> heavyImpact() async {
    if (!_isEnabled) return;
    
    try {
      await HapticFeedback.heavyImpact();
    } catch (e) {
      _logger.e('Error providing heavy impact haptic feedback: $e');
    }
  }
  
  /// Provide selection click feedback
  Future<void> selectionClick() async {
    if (!_isEnabled) return;
    
    try {
      await HapticFeedback.selectionClick();
    } catch (e) {
      _logger.e('Error providing selection click haptic feedback: $e');
    }
  }
  
  /// Provide vibration feedback
  Future<void> vibrate() async {
    if (!_isEnabled) return;
    
    try {
      await HapticFeedback.vibrate();
    } catch (e) {
      _logger.e('Error providing vibration haptic feedback: $e');
    }
  }
  
  /// Provide feedback for recording start
  Future<void> recordingStart() async {
    await mediumImpact();
  }
  
  /// Provide feedback for recording stop
  Future<void> recordingStop() async {
    await heavyImpact();
  }
  
  /// Provide feedback for error
  Future<void> error() async {
    // Double vibration for error
    await vibrate();
    await Future.delayed(const Duration(milliseconds: 100));
    await vibrate();
  }
  
  /// Provide feedback for success
  Future<void> success() async {
    await lightImpact();
  }
}
