import 'dart:async';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:logger/logger.dart';

/// Utility class for performance optimizations
class PerformanceUtils {
  /// Private constructor to prevent instantiation
  PerformanceUtils._();

  /// Whether to enable performance optimizations
  static bool enableOptimizations = true;

  /// Whether to log performance metrics
  static bool enableLogging = kDebugMode;

  /// Logger instance
  static final Logger _logger = Logger();

  /// Frame timing history for monitoring animation performance
  static final List<Duration> _frameTimes = <Duration>[];

  /// Maximum number of frame times to keep
  static const int _maxFrameTimeHistory = 120; // About 2 seconds of frames

  /// Whether frame timing is being monitored
  static bool _isMonitoringFrames = false;

  /// Start monitoring frame times
  static void startFrameMonitoring() {
    if (_isMonitoringFrames) return;

    _frameTimes.clear();
    _isMonitoringFrames = true;

    SchedulerBinding.instance.addTimingsCallback(_recordFrameTiming);

    if (enableLogging) {
      _logger.i('Started frame monitoring');
    }
  }

  /// Stop monitoring frame times
  static void stopFrameMonitoring() {
    if (!_isMonitoringFrames) return;

    SchedulerBinding.instance.removeTimingsCallback(_recordFrameTiming);
    _isMonitoringFrames = false;

    if (enableLogging) {
      final stats = getFrameStatistics();
      _logger.i(
        'Stopped frame monitoring. Average FPS: ${stats.averageFps.toStringAsFixed(1)}, '
        'Jank count: ${stats.jankCount}',
      );
    }
  }

  /// Record frame timing information
  static void _recordFrameTiming(List<FrameTiming> timings) {
    if (!_isMonitoringFrames) return;

    for (final timing in timings) {
      final duration = timing.totalSpan;
      _frameTimes.add(duration);

      // Keep only the most recent frames
      if (_frameTimes.length > _maxFrameTimeHistory) {
        _frameTimes.removeAt(0);
      }
    }
  }

  /// Get statistics about frame performance
  static FrameStatistics getFrameStatistics() {
    if (_frameTimes.isEmpty) {
      return FrameStatistics(
        averageFps: 60.0,
        worstFps: 60.0,
        jankCount: 0,
        frameCount: 0,
      );
    }

    // Calculate average frame time
    final totalDuration = _frameTimes.fold<Duration>(
      Duration.zero,
      (total, duration) => total + duration,
    );
    final averageFrameTimeMs =
        totalDuration.inMicroseconds / _frameTimes.length / 1000;
    final averageFps = 1000 / averageFrameTimeMs;

    // Calculate worst frame time
    final worstFrameTimeMs = _frameTimes
        .map((duration) => duration.inMicroseconds / 1000)
        .reduce((a, b) => a > b ? a : b);
    final worstFps = 1000 / worstFrameTimeMs;

    // Count janky frames (frames that take more than 16.67ms, which is below 60fps)
    final jankCount =
        _frameTimes.where((duration) => duration.inMicroseconds > 16670).length;

    return FrameStatistics(
      averageFps: averageFps,
      worstFps: worstFps,
      jankCount: jankCount,
      frameCount: _frameTimes.length,
    );
  }

  /// Measure the execution time of a function
  static Future<T> measureExecutionTime<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    if (!enableOptimizations || !enableLogging) {
      return operation();
    }

    final stopwatch = Stopwatch()..start();
    final result = await operation();
    stopwatch.stop();

    _logger.d('$operationName took ${stopwatch.elapsedMilliseconds}ms');

    return result;
  }

  /// Measure the execution time of a synchronous function
  static T measureSyncExecutionTime<T>(
    String operationName,
    T Function() operation,
  ) {
    if (!enableOptimizations || !enableLogging) {
      return operation();
    }

    final stopwatch = Stopwatch()..start();
    final result = operation();
    stopwatch.stop();

    _logger.d('$operationName took ${stopwatch.elapsedMilliseconds}ms');

    return result;
  }

  /// Schedule a task to run when the UI is idle
  static void scheduleTask(VoidCallback task) {
    if (!enableOptimizations) {
      task();
      return;
    }

    SchedulerBinding.instance.scheduleTask(
      task,
      Priority.animation,
    );
  }

  /// Run a task in a separate isolate if it's computationally intensive
  static Future<R> computeInBackground<M, R>(
    ComputeCallback<M, R> callback,
    M message,
  ) {
    if (!enableOptimizations) {
      return Future.value(callback(message));
    }

    return compute(callback, message);
  }

  /// Debounce a function call
  static Function() debounce(
    Function() func,
    Duration duration,
  ) {
    Timer? timer;

    return () {
      if (timer != null) {
        timer!.cancel();
      }

      timer = Timer(duration, func);
    };
  }

  /// Throttle a function call
  static Function() throttle(
    Function() func,
    Duration duration,
  ) {
    DateTime? lastCall;

    return () {
      final now = DateTime.now();

      if (lastCall == null || now.difference(lastCall!) > duration) {
        func();
        lastCall = now;
      }
    };
  }

  /// Optimize a widget rebuild by skipping unnecessary rebuilds
  static bool shouldRebuild({
    required Object? oldData,
    required Object? newData,
  }) {
    if (identical(oldData, newData)) {
      return false;
    }

    if (oldData == newData) {
      return false;
    }

    return true;
  }
}

/// Statistics about frame performance
class FrameStatistics {
  /// Average frames per second
  final double averageFps;

  /// Worst frames per second
  final double worstFps;

  /// Number of janky frames (frames that take more than 16.67ms)
  final int jankCount;

  /// Total number of frames measured
  final int frameCount;

  /// Creates a new frame statistics object
  const FrameStatistics({
    required this.averageFps,
    required this.worstFps,
    required this.jankCount,
    required this.frameCount,
  });

  /// Whether the frame rate is acceptable (above 55 FPS)
  bool get isAcceptable => averageFps > 55.0;

  /// Whether the frame rate is poor (below 30 FPS)
  bool get isPoor => averageFps < 30.0;

  /// Percentage of janky frames
  double get jankPercentage =>
      frameCount > 0 ? (jankCount / frameCount) * 100 : 0.0;
}

/// A widget that optimizes animations by using RepaintBoundary
class OptimizedAnimationContainer extends StatelessWidget {
  /// The child widget
  final Widget child;

  /// Whether to use a repaint boundary
  final bool useRepaintBoundary;

  /// Creates a new optimized animation container
  const OptimizedAnimationContainer({
    super.key,
    required this.child,
    this.useRepaintBoundary = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!PerformanceUtils.enableOptimizations || !useRepaintBoundary) {
      return child;
    }

    return RepaintBoundary(child: child);
  }
}

/// A widget that optimizes list items by using RepaintBoundary
class OptimizedListItem extends StatelessWidget {
  /// The child widget
  final Widget child;

  /// Whether to use a repaint boundary
  final bool useRepaintBoundary;

  /// Creates a new optimized list item
  const OptimizedListItem({
    super.key,
    required this.child,
    this.useRepaintBoundary = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!PerformanceUtils.enableOptimizations || !useRepaintBoundary) {
      return child;
    }

    return RepaintBoundary(child: child);
  }
}

/// A widget that optimizes image loading
class OptimizedImage extends StatelessWidget {
  /// The image provider
  final ImageProvider imageProvider;

  /// The width of the image
  final double? width;

  /// The height of the image
  final double? height;

  /// The fit of the image
  final BoxFit? fit;

  /// Creates a new optimized image
  const OptimizedImage({
    super.key,
    required this.imageProvider,
    this.width,
    this.height,
    this.fit,
  });

  @override
  Widget build(BuildContext context) {
    return Image(
      image: imageProvider,
      width: width,
      height: height,
      fit: fit,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }

        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
          child: child,
        );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          return child;
        }

        return Center(
          child: CircularProgressIndicator(
            value: loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded /
                    loadingProgress.expectedTotalBytes!
                : null,
          ),
        );
      },
    );
  }
}
