import 'package:flutter/material.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>';
import 'package:projectpilot/presentation/ui/screens/auth/auth_screen.dart';

/// Helper class for app navigation
class NavigationHelper {
  /// Navigate to home screen, replacing the current screen
  static void navigateToHome(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const HomeScreen()),
    );
  }
  
  /// Navigate to auth screen, replacing the current screen
  static void navigateToAuth(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const AuthScreen()),
    );
  }
}
