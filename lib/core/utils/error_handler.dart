import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

/// A standardized error handler for the application
class ErrorHandler {
  final Logger _logger = Logger();
  
  /// Singleton instance
  static final ErrorHandler _instance = ErrorHandler._internal();
  
  /// Factory constructor
  factory ErrorHandler() => _instance;
  
  /// Private constructor
  ErrorHandler._internal();
  
  /// Handle an error and return a user-friendly message
  String handleError(dynamic error, {String? context}) {
    // Log the error
    _logger.e('Error in $context: $error');
    
    // Determine the type of error and return a user-friendly message
    if (error.toString().contains('SocketException') || 
        error.toString().contains('Connection refused') ||
        error.toString().contains('Network is unreachable')) {
      return 'Network error. Please check your internet connection.';
    } else if (error.toString().contains('Permission')) {
      return 'Permission denied. Please grant the required permissions.';
    } else if (error.toString().contains('File not found')) {
      return 'File not found. Please try again.';
    } else if (error.toString().contains('Failed to download')) {
      return 'Failed to download. Please check your internet connection and try again.';
    } else if (error.toString().contains('Failed to initialize')) {
      return 'Failed to initialize. Please restart the app and try again.';
    } else if (error.toString().contains('Transcription')) {
      return 'Transcription failed. Please try again.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }
  
  /// Show an error snackbar
  void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  
  /// Show a retry dialog
  Future<bool> showRetryDialog(BuildContext context, String message) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error'),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: const Text('Retry'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    );
    
    return result ?? false;
  }
  
  /// Handle a specific error type with a custom handler
  T? handleErrorWithFallback<T>(
    Function() operation,
    T? fallback,
    {String? context}
  ) {
    try {
      return operation() as T?;
    } catch (e) {
      _logger.e('Error in $context: $e');
      return fallback;
    }
  }
  
  /// Retry an operation with exponential backoff
  Future<T?> retryWithBackoff<T>({
    required Future<T> Function() operation,
    required int maxRetries,
    Duration initialDelay = const Duration(milliseconds: 500),
    String? context,
  }) async {
    int retryCount = 0;
    Duration delay = initialDelay;
    
    while (retryCount < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        retryCount++;
        _logger.w('Retry $retryCount/$maxRetries in $context: $e');
        
        if (retryCount >= maxRetries) {
          _logger.e('Max retries reached in $context: $e');
          rethrow;
        }
        
        await Future.delayed(delay);
        delay *= 2; // Exponential backoff
      }
    }
    
    return null;
  }
}
