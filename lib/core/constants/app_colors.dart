import 'package:flutter/material.dart';

/// App color constants used throughout the application
/// This is the single source of truth for all colors in the app
class AppColors {
  // Primary color palette - Pixel blue
  static const primary = Color(0xFF4285F4); // Google Pixel blue as primary
  static const primaryLight = Color(0xFF64A0FF); // Lighter Pixel blue
  static const primaryDark = Color(0xFF2A75E8); // Darker Pixel blue

  // Secondary color palette - Purple
  static const secondary = Color(
    0xFF6C63FF,
  ); // Deep purple (was in AppTheme.deepPurple)
  static const secondaryLight = Color.fromARGB(
    255,
    114,
    107,
    243,
  ); // Lighter purple
  static const secondaryDark = Color(0xFF5046E4); // Darker purple

  // Tertiary color palette - Turquoise
  static const tertiary = Color(
    0xFF1FE2C1,
  ); // Neon turquoise (was in AppTheme.neonTurquoise)
  static const tertiaryLight = Color(0xFF5CEBD2); // Lighter turquoise
  static const tertiaryDark = Color(0xFF00B3A0); // Darker turquoise

  // Accent color palette - Vibrant blue tones
  static const accent = Color.fromARGB(255, 22, 122, 252); // Pixel accent blue
  static const accentLight = Color(0xFF4F97FF); // Lighter accent
  static const accentDark = Color(0xFF0D5BD7); // Darker accent
  
  // Electric blue from previous AppTheme
  static const electricBlue = Color(0xFF2ACAEA);

  // Background colors
  static const backgroundDark = Color(0xFF0F1A2A); // Darker navy blue
  static const backgroundLight = Color(0xFFF5FAFF); // Light blue tinted white
  static const darkNavy = Color(0xFF0F111A); // From AppTheme.darkNavy
  static const cleanWhite = Color(0xFFFAFAFA); // From AppTheme.cleanWhite

  // Dark theme specific surface colors
  static const darkSurface = Color(0xFF141626); // Dark theme surface color
  static const darkSurfaceHigher = Color(
    0xFF1E2033,
  ); // Dark theme container color
  static const darkBottomNavBar = Color(
    0xFF151825,
  ); // Dark theme bottom nav bar color

  // Neutral colors for text
  static const textPrimary = Color(0xFF1C1C1C); // For headlines (dark mode)
  static const textSecondary = Color(0xFFA3A3A3); // Secondary text (dark mode)
  static const textPrimaryLight = Color(
    0xFFF1F1F1,
  ); // For headlines (light mode)
  static const textSecondaryLight = Color(
    0xFFD4D4D4,
  ); // Secondary text (light mode)
  static const neutralGrey = Color(0xFFA3A3A3); // From AppTheme.neutralGrey
  static const headlineGrey = Color(0xFF1C1C1C); // From AppTheme.headlineGrey

  // Status colors
  static const error = Color(0xFFFF5252); // Error color
  static const success = Color(0xFF4CAF50); // Success color
  static const warning = Color(0xFFFFC107); // Warning color
  static const info = Color(0xFF2196F3); // Info blue
  
  // Status colors from AppTheme for compatibility
  static const errorRed = Color(0xFFE53935); // From AppTheme.errorRed
  static const successGreen = Color(0xFF43A047); // From AppTheme.successGreen
  static const warningAmber = Color(0xFFFFA000); // From AppTheme.warningAmber

  // Category colors - Using Pixel blue tones
  static const taskColor = Color(0xFF4285F4); // Pixel blue for tasks
  static const noteColor = Color(0xFF5E97F6); // Medium Pixel blue for notes
  static const ideaColor = Color(0xFF6C63FF); // Purple for ideas

  // Gradients - Enhanced with Pixel blues
  static const primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryDark], // Pixel blue gradient
  );

  static const accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, accent], // Pixel blue to accent
  );

  // Additional blue-focused gradients
  static const blueGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF4285F4), Color(0xFF0D47A1)], // Pixel to dark blue
  );

  static const waterGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF5E97F6),
      Color(0xFF1A73E8),
    ], // Light to medium Pixel blue
  );
  
  // Brand gradient from AppTheme
  static const brandGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, electricBlue], // Purple to electric blue gradient
  );
}

/// Extension method to make working with opacity more consistent
extension ColorOpacity on Color {
  /// Returns a new color with the given alpha value (0.0 to 1.0)
  Color withValues({double? alpha}) {
    return withValues(alpha: alpha ?? 1.0);
  }
}
