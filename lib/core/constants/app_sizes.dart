/// Constants for spacing, margins, paddings and sizes used throughout the app
class AppSizes {
  // Base spacing unit (all other spacing values are multiples of this)
  static const double spacing = 4.0;

  // Spacing multipliers
  static const double spacing1x = spacing * 1; // 4.0
  static const double spacing2x = spacing * 2; // 8.0
  static const double spacing3x = spacing * 3; // 12.0
  static const double spacing4x = spacing * 4; // 16.0
  static const double spacing5x = spacing * 5; // 20.0
  static const double spacing6x = spacing * 6; // 24.0
  static const double spacing8x = spacing * 8; // 32.0
  static const double spacing10x = spacing * 10; // 40.0
  static const double spacing12x = spacing * 12; // 48.0
  static const double spacing16x = spacing * 16; // 64.0

  // Border radius
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 16.0;
  static const double radiusExtraLarge = 24.0;
  static const double radiusCircular = 999.0;

  // Button sizes
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeightMedium = 44.0;
  static const double buttonHeightLarge = 56.0;

  // Card elevations
  static const double elevationSmall = 1.0;
  static const double elevationMedium = 2.0;
  static const double elevationLarge = 4.0;
  static const double elevationExtraLarge = 8.0;

  // Icon sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeExtraLarge = 48.0;

  // Text field heights
  static const double textFieldHeight = 48.0;

  // Avatar sizes
  static const double avatarSizeSmall = 24.0;
  static const double avatarSizeMedium = 40.0;
  static const double avatarSizeLarge = 56.0;
  static const double avatarSizeExtraLarge = 80.0;

  // Component heights
  static const double appBarHeight = 56.0;
  static const double tabBarHeight = 48.0;
  static const double bottomNavBarHeight = 64.0;

  // Screen padding
  static const double screenPaddingHorizontal = spacing4x;
  static const double screenPaddingVertical = spacing4x;

  // Card padding
  static const double cardPadding = spacing4x;

  // Feature card sizes
  static const double featureCardHeight = 150.0;
  static const double featureCardWidth = 250.0;

  // Stat card sizes
  static const double statCardSize = 160.0;

  // Animation durations (in milliseconds)
  static const int animFast = 150;
  static const int animMedium = 300;
  static const int animSlow = 500;
}
