/// Constants for supported integration platforms and their API endpoints.
class ApiConstants {
  // Platform identifiers
  static const String clickUp = 'clickup';

  static const String notion = 'notion';
  static const String asana = 'asana';
  static const String monday = 'monday';
  static const String jira = 'jira';
  static const String trello = 'trello';

  // Supabase Edge Functions base URL
  // TODO: Replace with your actual Supabase project URL from the Supabase Dashboard
  // Format: https://<your-project-id>.supabase.co/functions/v1
  static const String baseUrl =
      'https://your-project-id.supabase.co/functions/v1';

  // API base URLs
  static const Map<String, String> baseUrls = {
    clickUp: 'https://api.clickup.com/api/v2',
    notion: 'https://api.notion.com/v1',
    asana: 'https://app.asana.com/api/1.0',
    monday: 'https://api.monday.com/v2',
    jira: 'https://api.atlassian.com',
    trello: 'https://api.trello.com/1',
  };

  // API endpoints
  static const Map<String, String> taskEndpoints = {
    clickUp: '/task',
    notion: '/pages',
    asana: '/tasks',
    monday: '/items',
    jira: '/issue',
    trello: '/cards',
  };
}
