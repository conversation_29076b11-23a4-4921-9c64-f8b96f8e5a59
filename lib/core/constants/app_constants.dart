import 'package:projectpilot/core/config/env_config.dart';

/// Constants for general application information
class AppConstants {
  /// Application name
  static const String appName = 'ProjectPilot';

  /// Application version
  static const String appVersion = '1.0.0';

  /// Function getters for mocking in tests
  static String Function() stripePublishableKeyGetter =
      () => EnvConfig.stripePublishableKey;
  static String Function() supabaseUrlGetter = () => EnvConfig.supabaseUrl;
  static String Function() supabaseAnonKeyGetter =
      () => EnvConfig.supabaseAnonKey;
  static String Function() openaiApiKeyGetter = () => EnvConfig.openaiApiKey;
  static bool Function() enableOfflineTranscriptionGetter =
      () => EnvConfig.enableOfflineTranscription;
  static bool Function() enableOnlineTranscriptionGetter =
      () => EnvConfig.enableOnlineTranscription;
  static bool Function() enableAnalyticsGetter =
      () => EnvConfig.enableAnalytics;
  static String Function() defaultLanguageGetter =
      () => EnvConfig.defaultLanguage;

  /// Get the Stripe publishable key from environment variables
  /// Falls back to a placeholder for development
  static String get stripePublishableKey => stripePublishableKeyGetter();

  /// For backward compatibility - will be removed in future versions
  @Deprecated('Use stripePublishableKey instead')
  static String get stripePublishableKeyPlaceholder => stripePublishableKey;

  /// Get the Supabase URL from environment variables
  static String get supabaseUrl => supabaseUrlGetter();

  /// Get the Supabase anonymous key from environment variables
  static String get supabaseAnonKey => supabaseAnonKeyGetter();

  /// Get the OpenAI API key from environment variables
  static String get openaiApiKey => openaiApiKeyGetter();

  /// Feature flags from environment variables
  static bool get enableOfflineTranscription =>
      enableOfflineTranscriptionGetter();
  static bool get enableOnlineTranscription =>
      enableOnlineTranscriptionGetter();
  static bool get enableAnalytics => enableAnalyticsGetter();

  /// Default language from environment variables
  static String get defaultLanguage => defaultLanguageGetter();

  /// Stripe token costs constants
  static const int whisperTranscriptionTokenCost = 5;
  static const int gptOptimizationTokenCost = 10;
  static const int platformDispatchTokenCost = 2;

  /// Audio processing constants
  static const int audioBufferSize = 4096;
  static const int audioSampleRate = 16000;
  static const int maxRecordingDurationSeconds = 300; // 5 minutes

  /// Model download constants
  static const int downloadTimeoutSeconds = 180; // 3 minutes
  static const int extractionTimeoutSeconds = 120; // 2 minutes

  /// Default token values
  static const int defaultFreeTokens = 3;

  /// Performance optimization constants
  static const int animationFrameThreshold = 16; // 60 FPS threshold in ms
  static const int heavyAnimationFrameThreshold = 32; // 30 FPS threshold in ms
  static const bool enableFrameMonitoring = true;

  /// Audio visualization constants
  static const bool useMockAudioLevels = false; // Set to true for testing
  static const int audioVisualizationUpdateInterval = 100; // in milliseconds
  static const double audioSmoothingFactor = 0.3; // 0.0 to 1.0

  /// Error handling constants
  static const int maxRetryAttempts = 3;
  static const int retryBaseDelayMs = 500;
  static const int errorNotificationDurationMs = 5000;

  /// Caching constants
  static const int maxCachedTranscriptions = 50;
  static const Duration transcriptionCacheExpiry = Duration(days: 7);
  static const bool enableTranscriptionCaching = true;
}
