import 'package:flutter/material.dart';

/// Theme extension for custom container styling used across the app
/// Provides consistent container decoration options
@immutable
class ContainerTheme extends ThemeExtension<ContainerTheme> {
  /// Default corner radius for containers
  final double cornerRadius;

  /// Shadow color for elevated containers
  final Color shadowColor;

  /// Shadow opacity for elevated containers
  final double shadowOpacity;

  /// Shadow blur radius for elevated containers
  final double shadowBlurRadius;

  /// Shadow spread radius for elevated containers
  final double shadowSpreadRadius;

  /// Shadow offset for elevated containers
  final Offset shadowOffset;

  /// Border color for containers
  final Color borderColor;

  /// Border width for containers
  final double borderWidth;

  /// Default constructor
  const ContainerTheme({
    this.cornerRadius = 16.0,
    required this.shadowColor,
    this.shadowOpacity = 0.1,
    this.shadowBlurRadius = 15.0,
    this.shadowSpreadRadius = 2.0,
    this.shadowOffset = const Offset(0, 3),
    required this.borderColor,
    this.borderWidth = 1.0,
  });

  /// Light theme container styling
  static ContainerTheme light(ThemeData theme) {
    return ContainerTheme(
      shadowColor: const Color(0xFF000000),
      borderColor: theme.colorScheme.outline.withValues(alpha: 0.2),
    );
  }

  /// Dark theme container styling
  static ContainerTheme dark(ThemeData theme) {
    return ContainerTheme(
      shadowColor: const Color(0xFF000000),
      borderColor: theme.colorScheme.outline.withValues(alpha: 0.1),
    );
  }

  /// Returns a BoxDecoration for an elevated container
  BoxDecoration elevatedDecoration({
    Color? color,
    double? customRadius,
    bool hasBorder = false,
    Color? customBorderColor,
    BorderRadius? customBorderRadius,
    List<BoxShadow>? customShadows,
  }) {
    return BoxDecoration(
      color: color,
      borderRadius:
          customBorderRadius ??
          BorderRadius.circular(customRadius ?? cornerRadius),
      border:
          hasBorder
              ? Border.all(
                color: customBorderColor ?? borderColor,
                width: borderWidth,
              )
              : null,
      boxShadow:
          customShadows ??
          [
            BoxShadow(
              color: shadowColor.withValues(alpha: shadowOpacity),
              blurRadius: shadowBlurRadius,
              spreadRadius: shadowSpreadRadius,
              offset: shadowOffset,
            ),
          ],
    );
  }

  /// Returns a BoxDecoration for a flat container (no shadow)
  BoxDecoration flatDecoration({
    Color? color,
    double? customRadius,
    bool hasBorder = true,
    Color? customBorderColor,
    BorderRadius? customBorderRadius,
  }) {
    return BoxDecoration(
      color: color,
      borderRadius:
          customBorderRadius ??
          BorderRadius.circular(customRadius ?? cornerRadius),
      border:
          hasBorder
              ? Border.all(
                color: customBorderColor ?? borderColor,
                width: borderWidth,
              )
              : null,
    );
  }

  /// Returns a BoxDecoration for top rounded container
  BoxDecoration topRoundedDecoration({
    Color? color,
    double? customRadius,
    bool hasShadow = true,
    bool hasBorder = false,
    Color? customBorderColor,
  }) {
    return BoxDecoration(
      color: color,
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(customRadius ?? cornerRadius),
        topRight: Radius.circular(customRadius ?? cornerRadius),
      ),
      border:
          hasBorder
              ? Border.all(
                color: customBorderColor ?? borderColor,
                width: borderWidth,
              )
              : null,
      boxShadow:
          hasShadow
              ? [
                BoxShadow(
                  color: shadowColor.withValues(alpha: shadowOpacity),
                  blurRadius: shadowBlurRadius,
                  spreadRadius: shadowSpreadRadius,
                  offset: const Offset(0, -3),
                ),
              ]
              : null,
    );
  }

  @override
  ThemeExtension<ContainerTheme> copyWith({
    double? cornerRadius,
    Color? shadowColor,
    double? shadowOpacity,
    double? shadowBlurRadius,
    double? shadowSpreadRadius,
    Offset? shadowOffset,
    Color? borderColor,
    double? borderWidth,
  }) {
    return ContainerTheme(
      cornerRadius: cornerRadius ?? this.cornerRadius,
      shadowColor: shadowColor ?? this.shadowColor,
      shadowOpacity: shadowOpacity ?? this.shadowOpacity,
      shadowBlurRadius: shadowBlurRadius ?? this.shadowBlurRadius,
      shadowSpreadRadius: shadowSpreadRadius ?? this.shadowSpreadRadius,
      shadowOffset: shadowOffset ?? this.shadowOffset,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
    );
  }

  @override
  ThemeExtension<ContainerTheme> lerp(
    ThemeExtension<ContainerTheme>? other,
    double t,
  ) {
    if (other is! ContainerTheme) {
      return this;
    }

    return ContainerTheme(
      cornerRadius: lerpDouble(cornerRadius, other.cornerRadius, t)!,
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t)!,
      shadowOpacity: lerpDouble(shadowOpacity, other.shadowOpacity, t)!,
      shadowBlurRadius:
          lerpDouble(shadowBlurRadius, other.shadowBlurRadius, t)!,
      shadowSpreadRadius:
          lerpDouble(shadowSpreadRadius, other.shadowSpreadRadius, t)!,
      shadowOffset: Offset.lerp(shadowOffset, other.shadowOffset, t)!,
      borderColor: Color.lerp(borderColor, other.borderColor, t)!,
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t)!,
    );
  }

  /// Helper method for lerping double values
  double? lerpDouble(double? a, double? b, double t) {
    if (a == null && b == null) return null;
    a ??= 0.0;
    b ??= 0.0;
    return a + (b - a) * t;
  }
}
