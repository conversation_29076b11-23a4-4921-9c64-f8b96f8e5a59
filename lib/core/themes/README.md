# ProjectPilot Design System

This document provides guidelines for using the ProjectPilot design system consistently throughout the application.

## Brand Colors

The ProjectPilot brand is built around these key colors:

- **Pixel Blue** (`#4285F4`) - Primary brand color
- **Deep Purple** (`#6C63FF`) - Secondary brand color 
- **Neon Turquoise** (`#1FE2C1`) - Tertiary/accent color
- **Dark Navy** (`#0F1A2A`) - Background color for dark mode
- **Clean White** (`#F5FAFF`) - Background color for light mode

## Using Theme Colors

There are two ways to access colors in the ProjectPilot app:

### 1. Through AppColors (recommended)

`AppColors` is the single source of truth for all colors in the app:

```dart
import 'package:voicepilot/core/constants/app_colors.dart';

// Direct access to app colors
Container(
  color: AppColors.primary,
  child: Text('Primary color background'),
)
```

### 2. Through the Theme System

Access colors through the theme system to benefit from automatic light/dark mode adaptation:

```dart
final theme = Theme.of(context);

// Accessing standard color scheme colors
Container(
  color: theme.colorScheme.primary,
  child: Text('Primary color background'),
)

// Standard theme colors access
Container(
  color: theme.colorScheme.secondary, // Standard theme access
  child: Text('Secondary color background'),
)
```

## Color Opacity

When using opacity with colors, use the `withValues()` extension method:

```dart
// CORRECT
color: AppColors.primary.withValues(alpha: 0.7)
// OR
color: theme.colorScheme.onSurface.withValues(alpha: 0.7)

// AVOID
color: AppColors.primary.withOpacity(0.7)
```

## Typography

Use the text theme provided by the `ThemeData` to ensure consistent typography:

```dart
Text(
  'Heading',
  style: theme.textTheme.headlineMedium,
)

Text(
  'Body text',
  style: theme.textTheme.bodyMedium,
)
```

For modified text styles, use `copyWith` to maintain the base style:

```dart
Text(
  'Custom heading',
  style: theme.textTheme.headlineMedium?.copyWith(
    fontWeight: FontWeight.bold,
    color: theme.colorScheme.primary,
  ),
)
```

## UI Components

### Cards

Use the standard `Card` widget which is already styled through the theme:

```dart
Card(
  child: Padding(
    padding: const EdgeInsets.all(16.0),
    child: Text('Card content'),
  ),
)
```

### Buttons

Use the themed button components:

```dart
// Primary action
ElevatedButton(
  onPressed: () {},
  child: Text('Primary Action'),
)

// Secondary action
OutlinedButton(
  onPressed: () {},
  child: Text('Secondary Action'),
)

// Tertiary action
TextButton(
  onPressed: () {},
  child: Text('Tertiary Action'),
)
```

### Input Fields

Use the themed `TextField` or `TextFormField`:

```dart
TextField(
  decoration: InputDecoration(
    labelText: 'Input Label',
    hintText: 'Hint text',
  ),
)
```

## Containers

Use the `ContainerTheme` extension for consistent container styling:

```dart
final containerTheme = Theme.of(context).extension<ContainerTheme>()!;

Container(
  decoration: containerTheme.elevatedDecoration(
    color: theme.colorScheme.surface,
  ),
  child: Padding(
    padding: const EdgeInsets.all(16.0),
    child: Text('Elevated container'),
  ),
)
```

## Color Reference

### Primary Colors

- `AppColors.primary` - Pixel Blue, the primary brand color
- `AppColors.primaryLight` - Lighter Pixel Blue
- `AppColors.primaryDark` - Darker Pixel Blue

### Secondary Colors

- `AppColors.secondary` - Deep Purple (previously known as deepPurple)
- `AppColors.secondaryLight` - Lighter Purple
- `AppColors.secondaryDark` - Darker Purple

### Tertiary Colors

- `AppColors.tertiary` - Neon Turquoise (previously known as neonTurquoise)
- `AppColors.tertiaryLight` - Lighter Turquoise
- `AppColors.tertiaryDark` - Darker Turquoise

### Status Colors

- `AppColors.error` / `AppColors.errorRed` - Error color
- `AppColors.success` / `AppColors.successGreen` - Success color
- `AppColors.warning` / `AppColors.warningAmber` - Warning color
- `AppColors.info` - Info color

### Background Colors

- `AppColors.backgroundDark` - Dark background 
- `AppColors.backgroundLight` - Light background
- `AppColors.darkNavy` - Dark navy background (for dark mode)
- `AppColors.cleanWhite` - Clean white background (for light mode)

### Text Colors

- `AppColors.textPrimary` - Primary text color for dark themes
- `AppColors.textSecondary` - Secondary text color for dark themes
- `AppColors.textPrimaryLight` - Primary text color for light themes
- `AppColors.textSecondaryLight` - Secondary text color for light themes

### Category Colors

- `AppColors.taskColor` - For tasks
- `AppColors.noteColor` - For notes
- `AppColors.ideaColor` - For ideas

### Gradients

- `AppColors.primaryGradient` - Primary color gradient
- `AppColors.accentGradient` - Accent color gradient
- `AppColors.blueGradient` - Blue gradient
- `AppColors.waterGradient` - Water-like gradient
- `AppColors.brandGradient` - Brand identity gradient

## BaseScaffold

Use the `BaseScaffold` for consistent screen layouts:

```dart
// Regular scaffold
BaseScaffold(
  title: 'Screen Title',
  showBackButton: true,
  body: Container(
    child: Text('Screen content'),
  ),
)

// Rounded top scaffold
BaseScaffold.rounded(
  title: 'Screen Title',
  roundedColor: AppColors.primary,
  roundedHeight: 180,
  body: Container(
    child: Text('Screen content'),
  ),
)
```

## Light/Dark Mode

The theme system automatically handles light and dark mode. Ensure your UI adapts properly by:

1. Always using theme colors instead of hard-coded colors
2. Testing your UI in both light and dark mode
3. Using semantic colors (surface, onSurface, etc.) where appropriate

## Accessibility

- Ensure sufficient contrast between text and backgrounds
- Use adequate touch targets (min 44×44 points)
- Provide descriptive labels for screen readers

## Internationalization

Always use the localization system for user-facing text:

```dart
final l10n = AppLocalizations.of(context)!;

Text(l10n.welcomeMessage)
```

---

For questions or clarifications about the design system, contact the UX team. 