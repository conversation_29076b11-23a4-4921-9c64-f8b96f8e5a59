import 'package:flutter/material.dart';
import 'package:projectpilot/core/themes/container_theme.dart';

/// Helper class for theme related functionality
class ThemeHelper {
  /// Registers all custom theme extensions with the provided theme
  static ThemeData registerExtensions(ThemeData theme) {
    return theme.copyWith(
      extensions: <ThemeExtension<dynamic>>[
        // Register the container theme extension
        theme.brightness == Brightness.light
            ? ContainerTheme.light(theme)
            : ContainerTheme.dark(theme),
        // Add other extensions here as needed
      ],
    );
  }
}


