import 'package:flutter/material.dart';

/// A widget that builds different UI based on screen size
class ResponsiveBuilder extends StatelessWidget {
  /// Widget builder for mobile screens
  final Widget Function(BuildContext) mobile;

  /// Widget builder for tablet screens
  final Widget Function(BuildContext) tablet;

  /// Widget builder for desktop screens
  final Widget Function(BuildContext) desktop;

  /// Screen width breakpoint for mobile -> tablet
  final double mobileBreakpoint;

  /// Screen width breakpoint for tablet -> desktop
  final double desktopBreakpoint;

  /// Creates a new ResponsiveBuilder
  const ResponsiveBuilder({
    super.key,
    required this.mobile,
    required this.tablet,
    required this.desktop,
    this.mobileBreakpoint = 600,
    this.desktopBreakpoint = 1200,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;

        if (width < mobileBreakpoint) {
          return mobile(context);
        } else if (width < desktopBreakpoint) {
          return tablet(context);
        } else {
          return desktop(context);
        }
      },
    );
  }
}
