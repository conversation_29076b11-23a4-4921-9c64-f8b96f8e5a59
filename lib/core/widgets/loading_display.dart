import 'package:flutter/material.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';

/// A reusable widget for displaying loading states across the app
class LoadingDisplay extends StatelessWidget {
  /// The message to show during loading
  final String? message;

  /// Whether to display as a full page (true) or inline (false)
  final bool isFullPage;

  /// The color of the loading indicator
  final Color? color;

  /// The size of the loading indicator
  final double size;

  /// The stroke width of the loading indicator
  final double strokeWidth;

  /// Whether to show a transparent background overlay (for modal-like loading)
  final bool showOverlay;

  /// Creates a loading display widget
  const LoadingDisplay({
    super.key,
    this.message,
    this.isFullPage = false,
    this.color,
    this.size = 40.0,
    this.strokeWidth = 4.0,
    this.showOverlay = false,
  });

  /// Creates a full-page loading display
  factory LoadingDisplay.fullPage({
    Key? key,
    String? message,
    Color? color,
    bool showOverlay = false,
  }) {
    return LoadingDisplay(
      key: key,
      message: message,
      isFullPage: true,
      color: color,
      size: 50.0,
      showOverlay: showOverlay,
    );
  }

  /// Creates a small loading display for inline use
  factory LoadingDisplay.small({Key? key, String? message, Color? color}) {
    return LoadingDisplay(
      key: key,
      message: message,
      isFullPage: false,
      color: color,
      size: 24.0,
      strokeWidth: 2.5,
    );
  }

  /// Creates a loading overlay that covers its parent widget
  factory LoadingDisplay.overlay({Key? key, String? message, Color? color}) {
    return LoadingDisplay(
      key: key,
      message: message,
      isFullPage: true,
      color: color,
      showOverlay: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final actualColor = color ?? theme.colorScheme.primary;

    final Widget loadingContent = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: isFullPage ? MainAxisSize.max : MainAxisSize.min,
      children: [
        SizedBox.fromSize(
          size: Size(size, size),
          child: CircularProgressIndicator(
            strokeWidth: strokeWidth,
            valueColor: AlwaysStoppedAnimation<Color>(actualColor),
          ),
        ),
        if (message != null) ...[
          SizedBox(
            height: isFullPage ? AppSizes.spacing4x : AppSizes.spacing2x,
          ),
          Text(
            message!,
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ],
    );

    if (showOverlay) {
      return Container(
        color: Colors.black.withValues(alpha: 0.5),
        child: Center(child: loadingContent),
      );
    }

    return isFullPage
        ? Center(
          child: Padding(
            padding: const EdgeInsets.all(AppSizes.spacing4x),
            child: loadingContent,
          ),
        )
        : loadingContent;
  }
}
