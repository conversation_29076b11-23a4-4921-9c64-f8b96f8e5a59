import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';
import 'package:projectpilot/presentation/ui/bloc/connectivity/connectivity_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/connectivity/connectivity_state.dart';
// Removed sync-related imports as SyncCubit doesn't exist
import 'package:projectpilot/presentation/ui/widgets/offline_indicator.dart';
import 'package:flutter/services.dart';


/// A base scaffold widget that provides a consistent UI structure across the app
class BaseScaffold extends StatelessWidget {
  /// The title of the AppBar
  final dynamic title;

  /// The body of the scaffold
  final Widget body;

  /// Whether to show the sync indicator in the AppBar
  final bool showSyncIndicator;

  /// Optional leading widget for the AppBar
  final Widget? leading;

  /// Optional action widgets for the AppBar
  final List<Widget>? actions;

  /// Optional floating action button
  final Widget? floatingActionButton;

  /// Optional floating action button location
  final FloatingActionButtonLocation? floatingActionButtonLocation;

  /// Optional bottom navigation bar
  final Widget? bottomNavigationBar;

  /// Whether to show a back button in the AppBar
  final bool showBackButton;

  /// Whether to show the profile button in the AppBar
  final bool showProfile;

  /// Whether to show quick access actions (tasks & entries)
  final bool showQuickActions;

  /// Background color for the scaffold
  final Color? backgroundColor;

  /// Background color for the app bar
  final Color? appBarBackgroundColor;

  /// Content padding inside the body
  final EdgeInsets contentPadding;

  /// Whether to use rounded style for the app
  final bool useRoundedStyle;

  /// Height of the rounded top section (null means auto/default)
  final double? roundedHeight;

  /// The color for the rounded part (if useRoundedStyle is true)
  final Color? roundedColor;

  /// Radius size for the rounded corner
  final double roundedRadius;

  /// Creates a base scaffold with the given parameters
  const BaseScaffold({
    super.key,
    this.title,
    required this.body,
    this.showSyncIndicator = false,
    this.leading,
    this.actions,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.bottomNavigationBar,
    this.showBackButton = true,
    this.showProfile = true,
    this.showQuickActions = true,
    this.backgroundColor,
    this.appBarBackgroundColor,
    this.contentPadding = EdgeInsets.zero,
    this.useRoundedStyle = false,
    this.roundedHeight,
    this.roundedColor,
    this.roundedRadius = 24.0,
  });

  /// Creates a base scaffold with a title
  factory BaseScaffold.withTitle({
    Key? key,
    required String title,
    required Widget body,
    List<Widget>? actions,
    Widget? floatingActionButton,
    FloatingActionButtonLocation? floatingActionButtonLocation,
    Widget? bottomNavigationBar,
    bool showBackButton = true,
    bool showProfile = true,
    bool showQuickActions = true,
    Color? backgroundColor,
    Color? appBarBackgroundColor,
    EdgeInsets contentPadding = EdgeInsets.zero,
  }) {
    return BaseScaffold(
      key: key,
      title: title,
      body: body,
      actions: actions,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      bottomNavigationBar: bottomNavigationBar,
      showBackButton: showBackButton,
      showProfile: showProfile,
      showQuickActions: showQuickActions,
      backgroundColor: backgroundColor,
      appBarBackgroundColor: appBarBackgroundColor,
      contentPadding: contentPadding,
    );
  }

  /// Creates a base scaffold with a stylish, rounded top section like modern apps
  factory BaseScaffold.rounded({
    Key? key,
    required String title,
    required Widget body,
    List<Widget>? actions,
    Widget? floatingActionButton,
    FloatingActionButtonLocation? floatingActionButtonLocation,
    Widget? bottomNavigationBar,
    bool showBackButton = true,
    bool showProfile = false,
    bool showQuickActions = false,
    bool showSyncIndicator = true,
    Color? backgroundColor,
    Color? roundedColor,
    double? roundedHeight,
    double roundedRadius = 24.0,
    EdgeInsets contentPadding = EdgeInsets.zero,
  }) {
    return BaseScaffold(
      key: key,
      title: title,
      body: body,
      showSyncIndicator: showSyncIndicator,
      actions: actions,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      bottomNavigationBar: bottomNavigationBar,
      showBackButton: showBackButton,
      showProfile: showProfile,
      showQuickActions: showQuickActions,
      backgroundColor: backgroundColor,
      appBarBackgroundColor:
          roundedColor, // Use roundedColor as appBar background
      contentPadding: contentPadding,
      // Still set useRoundedStyle to true to avoid breaking existing code
      useRoundedStyle: true,
      // Keep but ignore these parameters
      roundedHeight: roundedHeight,
      roundedColor: roundedColor,
      roundedRadius: roundedRadius,
    );
  }

  /// Creates a base scaffold with no app bar and a clean body
  factory BaseScaffold.clean({
    Key? key,
    required Widget body,
    Widget? floatingActionButton,
    FloatingActionButtonLocation? floatingActionButtonLocation,
    Widget? bottomNavigationBar,
    Color? backgroundColor,
    EdgeInsets contentPadding = EdgeInsets.zero,
  }) {
    return BaseScaffold(
      key: key,
      body: body,
      showBackButton: false,
      showProfile: false,
      showQuickActions: false,
      backgroundColor: backgroundColor,
      appBarBackgroundColor: Colors.transparent,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      bottomNavigationBar: bottomNavigationBar,
      contentPadding: contentPadding,
    );
  }

  /// Creates a base scaffold for modal screens (like dialogs) with a close button
  factory BaseScaffold.modal({
    Key? key,
    required String title,
    required Widget body,
    List<Widget>? actions,
    Widget? floatingActionButton,
    Color? backgroundColor,
    EdgeInsets contentPadding = EdgeInsets.zero,
  }) {
    return BaseScaffold(
      key: key,
      title: title,
      body: body,
      actions: actions,
      showBackButton: false,
      showProfile: false,
      showQuickActions: false,
      leading: const CloseButton(),
      floatingActionButton: floatingActionButton,
      backgroundColor: backgroundColor,
      contentPadding: contentPadding,
    );
  }

  /// Creates a standard list view scaffold with common patterns
  factory BaseScaffold.list({
    Key? key,
    required String title,
    required Widget body,
    List<Widget>? actions,
    Widget? floatingActionButton,
    bool showBackButton = true,
    bool showProfile = true,
    Color? backgroundColor,
  }) {
    return BaseScaffold(
      key: key,
      title: title,
      body: body,
      actions: actions,
      floatingActionButton: floatingActionButton,
      showBackButton: showBackButton,
      showProfile: showProfile,
      backgroundColor: backgroundColor,
      contentPadding: const EdgeInsets.only(top: AppSizes.spacing2x),
    );
  }

  /// Creates a scaffolding for handling loading, error and success states
  factory BaseScaffold.withStates({
    Key? key,
    required String title,
    required bool isLoading,
    String? errorMessage,
    required Widget Function() contentBuilder,
    VoidCallback? onRetry,
    List<Widget>? actions,
    Widget? floatingActionButton,
    bool showBackButton = true,
    bool showProfile = true,
    bool showQuickActions = true,
    Color? backgroundColor,
  }) {
    Widget stateBody;

    if (isLoading) {
      stateBody = Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: AppSizes.spacing4x),
            Text('Loading...', style: TextStyle(color: Colors.grey[600])),
          ],
        ),
      );
    } else if (errorMessage != null) {
      stateBody = Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            const SizedBox(height: AppSizes.spacing2x),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: AppSizes.spacing4x),
            if (onRetry != null)
              ElevatedButton(onPressed: onRetry, child: const Text('Retry')),
          ],
        ),
      );
    } else {
      stateBody = contentBuilder();
    }

    return BaseScaffold(
      key: key,
      title: title,
      body: stateBody,
      actions: actions,
      floatingActionButton: floatingActionButton,
      showBackButton: showBackButton,
      showProfile: showProfile,
      showQuickActions: showQuickActions,
      backgroundColor: backgroundColor,
    );
  }

  /// Creates a scaffold with an empty state display
  factory BaseScaffold.empty({
    Key? key,
    required String title,
    required String emptyMessage,
    required IconData emptyIcon,
    String? actionLabel,
    VoidCallback? onActionPressed,
    List<Widget>? actions,
    Widget? floatingActionButton,
    bool showBackButton = true,
    bool showProfile = true,
    Color? backgroundColor,
  }) {
    return BaseScaffold(
      key: key,
      title: title,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(emptyIcon, size: 64, color: Colors.grey[400]),
            const SizedBox(height: AppSizes.spacing4x),
            Text(
              emptyMessage,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: AppSizes.spacing4x),
            if (actionLabel != null && onActionPressed != null)
              ElevatedButton(
                onPressed: onActionPressed,
                child: Text(actionLabel),
              ),
          ],
        ),
      ),
      actions: actions,
      floatingActionButton: floatingActionButton,
      showBackButton: showBackButton,
      showProfile: showProfile,
      backgroundColor: backgroundColor,
    );
  }

  /// Get the leading widget based on configuration
  Widget? _getLeading(
    BuildContext context,
    bool showBackButton,
  ) {
    // If a custom leading widget is provided, use it first
    if (leading != null) {
      return leading;
    }
    
    // Show back button if enabled and there are routes to pop
    if (showBackButton && Navigator.of(context).canPop()) {
      return IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      );
    }

    // No leading widget
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    // Determine the leading widget for the app bar
    Widget? leadingWidget = _getLeading(context, showBackButton);

    // Build quick access action buttons if enabled
    List<Widget> actionButtons = [];
    if (showQuickActions) {
      actionButtons.addAll([
        // TODO: Add collection screen when available
        IconButton(
          icon: const Icon(Icons.list_alt),
          tooltip: l10n.tasks,
          onPressed: () {
            // TODO: Navigate to collection screen
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Collection feature coming soon')),
            );
          },
        ),
      ]);
    }

    // Add custom actions provided by the consumer
    if (actions != null) {
      actionButtons.addAll(actions!);
    }

    // TODO: Add sync functionality when SyncCubit is available
    if (showSyncIndicator) {
      actionButtons.add(
        IconButton(
          icon: const Icon(Icons.sync),
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Sync feature coming soon')),
            );
          },
        ),
      );
    }

    return BlocBuilder<ConnectivityCubit, ConnectivityState>(
      builder: (context, connectivityState) {
        // Simplified without sync functionality
        return Scaffold(
              backgroundColor: backgroundColor ?? theme.colorScheme.surface,
              appBar:
                  (showProfile ||
                          showBackButton ||
                          title != null ||
                          actionButtons.isNotEmpty ||
                          leading != null)
                      ? AppBar(
                        title:
                            title != null
                                ? title is String
                                    ? Text(
                                      title,
                                      style: theme.textTheme.titleLarge,
                                    )
                                    : title
                                : null,
                        backgroundColor:
                            backgroundColor ??
                            theme.colorScheme.primary,
                        elevation: 0,
                        leading: leadingWidget,
                        leadingWidth:
                            leading != null
                                ? 200
                                : null, // Give more space for logo and app name
                        forceMaterialTransparency: true,
                        systemOverlayStyle: SystemUiOverlayStyle(
                          statusBarColor: Colors.transparent,
                          statusBarIconBrightness:
                              theme.brightness == Brightness.dark
                                  ? Brightness.light
                                  : Brightness.dark,
                          statusBarBrightness:
                              theme.brightness == Brightness.dark
                                  ? Brightness.dark
                                  : Brightness.light,
                        ),
                        actions: actionButtons.isEmpty ? null : actionButtons,
                      )
                      : null,
              body: OfflineIndicator(
                showBanner: true,
                child: Padding(
                  padding:
                      contentPadding == EdgeInsets.zero
                          ? EdgeInsets.zero
                          : contentPadding.copyWith(bottom: 0),
                  child: body,
                ),
              ),
              floatingActionButton: floatingActionButton,
              floatingActionButtonLocation: floatingActionButtonLocation,
          bottomNavigationBar: bottomNavigationBar,
        );
      },
    );
  }
}
