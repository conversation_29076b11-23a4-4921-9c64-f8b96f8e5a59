import 'package:flutter/material.dart';

/// A widget that displays a row of dots to indicate the current page in a PageView
class DotIndicator extends StatelessWidget {
  /// Total number of dots
  final int count;

  /// Index of the currently selected dot
  final int current;

  /// Size of each dot
  final double dotSize;

  /// Size of the currently selected dot
  final double activeDotSize;

  /// Color of the dots
  final Color dotColor;

  /// Color of the currently selected dot
  final Color activeDotColor;

  /// Spacing between dots
  final double spacing;

  /// Creates a dot indicator with the given parameters
  const DotIndicator({
    super.key,
    required this.count,
    required this.current,
    this.dotSize = 8.0,
    this.activeDotSize = 10.0,
    this.spacing = 8.0,
    this.dotColor = Colors.grey,
    this.activeDotColor = Colors.blue,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(count, (index) {
        final isActive = index == current;
        return Container(
          margin: EdgeInsets.symmetric(horizontal: spacing / 2),
          height: isActive ? activeDotSize : dotSize,
          width: isActive ? activeDotSize : dotSize,
          decoration: BoxDecoration(
            color: isActive ? activeDotColor : dotColor,
            shape: BoxShape.circle,
          ),
        );
      }),
    );
  }
}
