import 'package:flutter/material.dart';

/// A mixin to standardize animation controller management across the app
mixin StandardAnimationControllerMixin<T extends StatefulWidget>
    on State<T>, TickerProviderStateMixin<T> {
  /// The standard animation controller
  late AnimationController animationController;

  /// The standard fade animation
  late Animation<double> fadeAnimation;

  /// The standard slide animation
  late Animation<Offset> slideAnimation;

  /// The standard scale animation
  late Animation<double> scaleAnimation;

  /// Initialize animation controllers with standard durations
  void initializeAnimations({
    Duration duration = const Duration(milliseconds: 800),
    Duration reverseDuration = const Duration(milliseconds: 400),
    Curve curve = Curves.easeInOut,
    bool autoStart = true,
    bool repeat = false,
    bool reversible = false,
  }) {
    animationController = AnimationController(
      vsync: this,
      duration: duration,
      reverseDuration: reverseDuration,
    );

    fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: animationController, curve: curve));

    slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: animationController, curve: curve));

    scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(parent: animationController, curve: curve));

    if (autoStart) {
      animationController.forward();
    }

    if (repeat) {
      if (reversible) {
        animationController.repeat(reverse: true);
      } else {
        animationController.repeat();
      }
    }
  }

  /// Create a staggered animation with delay
  Animation<double> createStaggeredFadeAnimation({
    required int index,
    int itemCount = 1,
    Duration baseDuration = const Duration(milliseconds: 800),
    double delayFactor = 0.2,
    Curve curve = Curves.easeInOut,
  }) {
    final double startInterval = delayFactor * index / itemCount;
    final double endInterval = startInterval + (1 - delayFactor) / itemCount;

    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: animationController,
        curve: Interval(startInterval, endInterval, curve: curve),
      ),
    );
  }

  /// Play the animation in reverse and call a function when complete
  Future<void> reverseAnimationWithCallback(VoidCallback onComplete) async {
    await animationController.reverse();
    onComplete();
  }

  /// Dispose animation controller properly
  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }
}
