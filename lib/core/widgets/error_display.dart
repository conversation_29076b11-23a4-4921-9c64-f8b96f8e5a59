import 'package:flutter/material.dart';
import 'package:projectpilot/l10n/app_localizations.dart';
import 'package:projectpilot/core/constants/app_sizes.dart';

/// A reusable widget for displaying error messages across the app
class ErrorDisplay extends StatelessWidget {
  /// The error message to display
  final String message;

  /// Whether to display as a standalone widget (true) or in-line (false)
  final bool isFullPage;

  /// Icon to display with the error
  final IconData icon;

  /// Size of the icon
  final double iconSize;

  /// Color of the icon
  final Color? iconColor;

  /// Optional retry callback
  final VoidCallback? onRetry;

  /// Optional additional action callback
  final VoidCallback? onAction;

  /// Label for the additional action
  final String? actionLabel;

  /// Creates an error display widget
  const ErrorDisplay({
    super.key,
    required this.message,
    this.isFullPage = false,
    this.icon = Icons.error_outline,
    this.iconSize = 48,
    this.iconColor,
    this.onRetry,
    this.onAction,
    this.actionLabel,
  });

  /// Creates a full-page error display
  factory ErrorDisplay.fullPage({
    Key? key,
    required String message,
    IconData icon = Icons.error_outline,
    double iconSize = 64,
    Color? iconColor,
    VoidCallback? onRetry,
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    return ErrorDisplay(
      key: key,
      message: message,
      isFullPage: true,
      icon: icon,
      iconSize: iconSize,
      iconColor: iconColor,
      onRetry: onRetry,
      onAction: onAction,
      actionLabel: actionLabel,
    );
  }

  /// Creates an in-line error display for form fields
  factory ErrorDisplay.formField({
    Key? key,
    required String message,
    VoidCallback? onRetry,
  }) {
    return ErrorDisplay(
      key: key,
      message: message,
      isFullPage: false,
      icon: Icons.info_outline,
      iconSize: 16,
      iconColor: Colors.red[700],
      onRetry: onRetry,
    );
  }

  /// Creates a network error display
  factory ErrorDisplay.network({
    Key? key,
    String? message,
    VoidCallback? onRetry,
  }) {
    return ErrorDisplay(
      key: key,
      message: message ?? 'Network connection error',
      icon: Icons.wifi_off,
      iconColor: Colors.orange[700],
      onRetry: onRetry,
    );
  }

  /// Creates a permission error display
  factory ErrorDisplay.permission({
    Key? key,
    String? message,
    VoidCallback? onAction,
  }) {
    return ErrorDisplay(
      key: key,
      message: message ?? 'Permission denied',
      icon: Icons.no_accounts,
      iconColor: Colors.red[700],
      onAction: onAction,
      actionLabel: 'Open Settings',
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    final content = Column(
      mainAxisSize: isFullPage ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, size: iconSize, color: iconColor ?? theme.colorScheme.error),
        SizedBox(height: isFullPage ? AppSizes.spacing4x : AppSizes.spacing2x),
        Text(
          message,
          textAlign: TextAlign.center,
          style:
              isFullPage
                  ? theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  )
                  : theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.error,
                  ),
        ),
        if (onRetry != null || onAction != null) ...[
          SizedBox(
            height: isFullPage ? AppSizes.spacing4x : AppSizes.spacing2x,
          ),
          if (onRetry != null)
            ElevatedButton(onPressed: onRetry, child: Text('Retry')),
          if (onAction != null && actionLabel != null) ...[
            const SizedBox(height: AppSizes.spacing2x),
            TextButton(onPressed: onAction, child: Text(actionLabel!)),
          ],
        ],
      ],
    );

    return isFullPage
        ? Center(
          child: Padding(
            padding: const EdgeInsets.all(AppSizes.spacing4x),
            child: content,
          ),
        )
        : Padding(
          padding: const EdgeInsets.symmetric(vertical: AppSizes.spacing2x),
          child: content,
        );
  }
}
