import 'package:flutter/material.dart';
import 'package:projectpilot/presentation/ui/screens/auth/auth_screen.dart';
import 'package:projectpilot/presentation/ui/screens/auth/onboarding_screen.dart';
import 'package:projectpilot/presentation/ui/screens/home/<USER>';
import 'package:projectpilot/presentation/ui/screens/intro/widgets/intro_screen.dart';
import 'package:projectpilot/presentation/ui/screens/settings/settings_screen.dart';
import 'package:projectpilot/presentation/ui/screens/project/project_dashboard_screen.dart';
import 'package:projectpilot/presentation/ui/screens/project/project_query_screen.dart';
import 'package:projectpilot/presentation/ui/screens/project/project_detail_screen.dart';
import 'package:projectpilot/domain/entities/project.dart';

/// App route names
class AppRoutes {
  /// Home screen route
  static const String home = '/';

  /// Intro screen route
  static const String intro = '/intro';

  /// Settings screen route
  static const String settings = '/settings';

  /// Speech history screen route (legacy - redirects to home)
  static const String speechHistory = '/speech-history';

  /// Auth screen route
  static const String auth = '/auth';

  /// Onboarding screen route
  static const String onboarding = '/onboarding';

  /// Project dashboard screen route
  static const String projectDashboard = '/project-dashboard';

  /// Project query screen route
  static const String projectQuery = '/project-query';

  /// Project detail screen route
  static const String projectDetail = '/project-detail';

  /// Generate route based on settings
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case home:
        return MaterialPageRoute(builder: (_) => const HomeScreen());
      case intro:
        return MaterialPageRoute(builder: (_) => const IntroScreen());
      case AppRoutes.settings:
        return MaterialPageRoute(builder: (_) => const SettingsScreen());
      case AppRoutes.speechHistory:
        // Redirect to home screen since SpeechHistoryScreen is now integrated into HomeScreen
        return MaterialPageRoute(builder: (_) => const HomeScreen());
      case auth:
        return MaterialPageRoute(builder: (_) => const AuthScreen());
      case onboarding:
        return MaterialPageRoute(builder: (_) => const OnboardingScreen());
      case projectDashboard:
        return MaterialPageRoute(
          builder: (_) => const ProjectDashboardScreen(),
        );
      case projectQuery:
        return MaterialPageRoute(builder: (_) => const ProjectQueryScreen());
      case projectDetail:
        final project = settings.arguments as Project?;
        if (project != null) {
          return MaterialPageRoute(
            builder: (_) => ProjectDetailScreen(project: project),
          );
        }
        return MaterialPageRoute(
          builder:
              (_) => Scaffold(body: Center(child: Text('Project not found'))),
        );
      default:
        return MaterialPageRoute(
          builder:
              (_) => Scaffold(
                body: Center(
                  child: Text('No route defined for ${settings.name}'),
                ),
              ),
        );
    }
  }
}
