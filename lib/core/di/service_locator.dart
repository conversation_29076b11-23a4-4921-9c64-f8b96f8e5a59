import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:projectpilot/core/services/cache/transcription_cache_service.dart';
import 'package:projectpilot/core/services/intent/intent_parser_service.dart';
import 'package:projectpilot/core/services/network/connectivity_service.dart';
import 'package:projectpilot/core/services/network/network_info.dart';
import 'package:projectpilot/core/services/voice/audio_level_service.dart';
import 'package:projectpilot/core/services/voice/audio_service.dart';
import 'package:projectpilot/core/services/voice/audio_file_manager.dart';
import 'package:projectpilot/core/services/weather/weather_service.dart';
import 'package:projectpilot/core/utils/haptic_feedback_service.dart';
import 'package:projectpilot/data/local/api_key_secure_storage.dart';
import 'package:projectpilot/data/local/platform_connector_local_source.dart';
import 'package:projectpilot/data/repositories/api_key_repository_impl.dart';
import 'package:projectpilot/data/repositories/storage_repository.dart';
import 'package:projectpilot/data/repositories/user_repository_impl.dart';
import 'package:projectpilot/data/repositories/token_repository_impl.dart';
import 'package:projectpilot/domain/repositories/api_key_repository.dart';
import 'package:projectpilot/domain/repositories/user_repository.dart';
import 'package:projectpilot/domain/repositories/token_repository.dart';
import 'package:projectpilot/data/local/platform_config_local_datasource.dart';
import 'package:dio/dio.dart';
import 'package:projectpilot/presentation/ui/bloc/api_key/api_key_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/connectivity/connectivity_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/intro/intro_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/language/language_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/recording/recording_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/theme/theme_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/auth/auth_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/platform_connections/platform_connections_cubit.dart';
import 'package:projectpilot/presentation/ui/screens/connections/oauth_handler.dart';
import 'package:projectpilot/data/remote/oauth_service.dart';
import 'package:flutter/foundation.dart';
import 'package:projectpilot/presentation/ui/bloc/audio/audio_player_cubit.dart';
import 'package:projectpilot/core/services/notification/notification_service.dart';
import 'package:projectpilot/core/services/auth/social_auth_service.dart';
import 'package:projectpilot/presentation/ui/bloc/settings/settings_cubit.dart';
import 'package:projectpilot/presentation/ui/bloc/onboarding/onboarding_cubit.dart';

final sl = GetIt.instance;
bool _isHiveInitialized = false;

/// Initialize the service locator for dependency injection
Future<void> initServiceLocator() async {
  // Initialize Hive and path provider - safe to call once
  await _initializeHive();

  // Initialize Core Services
  await initCoreServices();

  // Get Supabase client from the app
  final supabaseClient = Supabase.instance.client;

  // Initialize Repositories
  initRepositories(supabaseClient);

  // Initialize Cubits
  initCubits();

  // Register notification service
  sl.registerLazySingleton<NotificationService>(() => NotificationService());
}

/// Initialize Hive safely (only once)
Future<void> _initializeHive() async {
  if (_isHiveInitialized) return;

  try {
    final appDocDir = await getApplicationDocumentsDirectory();
    final hivePath = '${appDocDir.path}/hive_data';
    await Hive.initFlutter(hivePath);
    
    _isHiveInitialized = true;
    debugPrint('🐝 Hive initialized successfully');
  } catch (e) {
    debugPrint('❌ Error initializing Hive: $e');
    rethrow;
  }
}

/// Initialize core services
Future<void> initCoreServices() async {
  // Core services that work
  sl.registerLazySingleton<ConnectivityService>(() => ConnectivityService());
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfo());
  sl.registerLazySingleton<HapticFeedbackService>(
    () => HapticFeedbackService(),
  );
  sl.registerLazySingleton<WeatherService>(() => WeatherService());
  sl.registerLazySingleton<AudioService>(() => AudioService());
  sl.registerLazySingleton<AudioLevelService>(() => AudioLevelService());
  sl.registerLazySingleton<AudioFileManager>(() => AudioFileManager());
  sl.registerLazySingleton<TranscriptionCacheService>(
    () => TranscriptionCacheService(),
  );
  sl.registerLazySingleton<IntentParserService>(() => IntentParserService());

  // Shared preferences
  final sharedPrefs = await SharedPreferences.getInstance();
  sl.registerSingleton<SharedPreferences>(sharedPrefs);

  // Local data sources
  sl.registerLazySingleton<ApiKeySecureStorage>(() => ApiKeySecureStorage());
  sl.registerLazySingleton<PlatformConnectorLocalSource>(
    () => PlatformConnectorLocalSource(),
  );
  sl.registerLazySingleton<PlatformConfigLocalDataSource>(
    () => PlatformConfigLocalDataSource(),
  );

  debugPrint('✅ Core services initialized');
}

/// Initialize repositories
void initRepositories(SupabaseClient supabaseClient) {
  // Only register repositories that work
  sl.registerLazySingleton<ApiKeyRepository>(() => ApiKeyRepositoryImpl(sl()));
  sl.registerLazySingleton<StorageRepository>(
    () => StorageRepository(supabaseClient: supabaseClient),
  );
  sl.registerLazySingleton<TokenRepository>(
    () => TokenRepositoryImpl(supabaseClient: supabaseClient),
  );
  sl.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(
      supabaseClient: supabaseClient,
      tokenRepository: sl(),
    ),
  );
  
  // Basic services
  sl.registerLazySingleton<OAuthService>(
    () => OAuthService(supabaseClient: supabaseClient),
  );
  sl.registerLazySingleton<OAuthHandler>(() => OAuthHandler());
  sl.registerLazySingleton<SocialAuthService>(() => SocialAuthService());

  // Dio for HTTP requests
  sl.registerLazySingleton<Dio>(() => Dio());

  debugPrint('✅ Repositories initialized');
}

/// Initialize cubits
void initCubits() {
  // Only register cubits that definitely work
  sl.registerLazySingleton<ThemeCubit>(() => ThemeCubit());
  sl.registerLazySingleton<LanguageCubit>(() => LanguageCubit());
  sl.registerLazySingleton<IntroCubit>(() => IntroCubit(preferences: sl()));
  sl.registerLazySingleton<RecordingCubit>(
    () => RecordingCubit(
      audioService: sl(),
      storageRepository: sl(),
      languageCubit: sl(),
    ),
  );
  sl.registerLazySingleton<AudioPlayerCubit>(() => AudioPlayerCubit());
  sl.registerLazySingleton<ConnectivityCubit>(
    () => ConnectivityCubit(connectivityService: sl()),
  );
  sl.registerLazySingleton<ApiKeyCubit>(() => ApiKeyCubit(sl()));
  sl.registerLazySingleton<AuthCubit>(() => AuthCubit(userRepository: sl()));
  sl.registerLazySingleton<PlatformConnectionsCubit>(
    () => PlatformConnectionsCubit(localSource: sl()),
  );

  // Add SettingsCubit with all required dependencies
  sl.registerLazySingleton<SettingsCubit>(
    () => SettingsCubit(
      userRepository: sl(),
      languageCubit: sl(),
      themeCubit: sl(),
      apiKeyCubit: sl(),
      oAuthHandler: sl(),
    ),
  );

  // Add OnboardingCubit
  sl.registerLazySingleton<OnboardingCubit>(
    () => OnboardingCubit(userRepository: sl()),
  );

  debugPrint('✅ Cubits initialized');
}
