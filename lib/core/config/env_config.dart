import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:logger/logger.dart';

/// Environment configuration class for secure access to environment variables
/// This class provides a secure way to access environment variables from .env files
class EnvConfig {
  static final Logger _logger = Logger();
  static bool _isInitialized = false;

  /// Initialize the environment configuration
  /// This should be called before accessing any environment variables
  static Future<void> init() async {
    if (_isInitialized) return;

    try {
      // Specify the filename explicitly to ensure it's found
      await dotenv.load(fileName: '.env');

      // Check if the environment was loaded correctly
      if (dotenv.env.isEmpty) {
        _logger.e('Environment loaded but empty, using default values');
        _createDefaultValues();
      } else {
        _isInitialized = true;
        _logger.i('Environment configuration loaded successfully');
      }
    } catch (e) {
      _logger.e('Failed to load environment configuration: $e');
      // Create default values for development
      _createDefaultValues();
    }
  }

  /// Initialize with test values for testing
  static void initForTesting() {
    if (_isInitialized) return;

    _createDefaultValues();
    _logger.i('Environment configuration initialized with test values');
  }

  /// Create default values for development
  /// This is used when the .env file is not found
  static void _createDefaultValues() {
    try {
      // Initialize dotenv with empty map if not already initialized
      dotenv.testLoad(
        fileInput: '''
SUPABASE_URL=https://oivqmtsotvaypcucnrgm.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9pdnFtdHNvdHZheXBjdWNucmdtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1Njg2MjEsImV4cCI6MjA2MjE0NDYyMX0.XIzN-Q3rvU4FhgqatK7FJ4VJEXDn_heopmF8ilp4a8I
STRIPE_PUBLISHABLE_KEY=pk_test_51RMSMbD6QfrznbP1VdIzWfvZrQWfJcgMNnbDMStpXkURpow7pJR2dEKYlvuWDO9uaBt23BZEyZg6skaUVF1Ed7GP00wx95bihu
OPENAI_API_KEY=********************************************************************************************************************************************************************
ENABLE_OFFLINE_TRANSCRIPTION=true
ENABLE_ONLINE_TRANSCRIPTION=true
ENABLE_ANALYTICS=true
DEFAULT_LANGUAGE=en
''',
      );
      _logger.i('Default environment values loaded successfully');
    } catch (e) {
      _logger.e('Failed to load default environment values: $e');

      // Fallback to manual initialization if testLoad fails
      // We need to use a different approach since dotenv.env is read-only
      final fallbackEnv = '''
SUPABASE_URL=https://oivqmtsotvaypcucnrgm.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9pdnFtdHNvdHZheXBjdWNucmdtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1Njg2MjEsImV4cCI6MjA2MjE0NDYyMX0.XIzN-Q3rvU4FhgqatK7FJ4VJEXDn_heopmF8ilp4a8I
STRIPE_PUBLISHABLE_KEY=pk_test_51RMSMbD6QfrznbP1VdIzWfvZrQWfJcgMNnbDMStpXkURpow7pJR2dEKYlvuWDO9uaBt23BZEyZg6skaUVF1Ed7GP00wx95bihu
OPENAI_API_KEY=********************************************************************************************************************************************************************
ENABLE_OFFLINE_TRANSCRIPTION=true
ENABLE_ONLINE_TRANSCRIPTION=true
ENABLE_ANALYTICS=true
DEFAULT_LANGUAGE=en
''';
      dotenv.testLoad(fileInput: fallbackEnv);
      _logger.w('Using hardcoded environment values as fallback');
    }

    _isInitialized = true;
    _logger.w('Using default environment values for development');
  }

  /// Get a string value from the environment
  /// Returns the default value if the key is not found
  static String getString(String key, {String defaultValue = ''}) {
    _ensureInitialized();
    return dotenv.env[key] ?? defaultValue;
  }

  /// Get a boolean value from the environment
  /// Returns the default value if the key is not found
  static bool getBool(String key, {bool defaultValue = false}) {
    _ensureInitialized();
    final value = dotenv.env[key]?.toLowerCase();
    if (value == null) return defaultValue;
    return value == 'true' || value == '1' || value == 'yes';
  }

  /// Get an integer value from the environment
  /// Returns the default value if the key is not found or if the value is not a valid integer
  static int getInt(String key, {int defaultValue = 0}) {
    _ensureInitialized();
    final value = dotenv.env[key];
    if (value == null) return defaultValue;
    return int.tryParse(value) ?? defaultValue;
  }

  /// Get a double value from the environment
  /// Returns the default value if the key is not found or if the value is not a valid double
  static double getDouble(String key, {double defaultValue = 0.0}) {
    _ensureInitialized();
    final value = dotenv.env[key];
    if (value == null) return defaultValue;
    return double.tryParse(value) ?? defaultValue;
  }

  /// Ensure that the environment configuration is initialized
  static void _ensureInitialized() {
    if (!_isInitialized) {
      _logger.w(
        'Environment configuration not initialized. Using default values.',
      );
      _createDefaultValues();
    }

    // Double-check that dotenv.env is not empty
    if (dotenv.env.isEmpty) {
      _logger.e('Environment variables are still empty after initialization!');
      // Try one more time with the fallback approach
      _createDefaultValues();
    }
  }

  /// Get the Supabase URL
  static String get supabaseUrl => getString('SUPABASE_URL');

  /// Get the Supabase anonymous key
  static String get supabaseAnonKey => getString('SUPABASE_ANON_KEY');

  /// Get the Stripe publishable key
  static String get stripePublishableKey =>
      getString('STRIPE_PUBLISHABLE_KEY', defaultValue: 'pk_test_placeholder');

  /// Get the OpenAI API key
  static String get openaiApiKey => getString('OPENAI_API_KEY');

  /// Check if offline transcription is enabled
  static bool get enableOfflineTranscription =>
      getBool('ENABLE_OFFLINE_TRANSCRIPTION', defaultValue: true);

  /// Check if online transcription is enabled
  static bool get enableOnlineTranscription =>
      getBool('ENABLE_ONLINE_TRANSCRIPTION', defaultValue: true);

  /// Check if analytics is enabled
  static bool get enableAnalytics =>
      getBool('ENABLE_ANALYTICS', defaultValue: false);

  /// Get the default language
  static String get defaultLanguage =>
      getString('DEFAULT_LANGUAGE', defaultValue: 'en');
}
