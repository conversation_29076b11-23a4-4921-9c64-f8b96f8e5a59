import 'dart:convert';
import 'package:hive/hive.dart';
import 'package:logger/logger.dart';
import 'package:projectpilot/domain/entities/transcription_result.dart';

/// Service for caching transcription results
class TranscriptionCacheService {
  final Logger _logger = Logger();

  /// Box for storing transcription results
  late Box<String> _transcriptionBox;

  /// Maximum number of cached transcriptions
  final int _maxCachedItems = 50;

  /// Singleton instance
  static final TranscriptionCacheService _instance =
      TranscriptionCacheService._internal();

  /// Factory constructor
  factory TranscriptionCacheService() => _instance;

  /// Private constructor
  TranscriptionCacheService._internal();

  /// Initialize the cache service
  Future<void> init() async {
    try {
      _transcriptionBox = await Hive.openBox<String>('transcription_cache');
      _logger.i('TranscriptionCacheService initialized');

      // Clean up old cache entries if we exceed the maximum
      _cleanupCache();
    } catch (e) {
      _logger.e('Error initializing TranscriptionCacheService: $e');
      rethrow;
    }
  }

  /// Cache a transcription result with the audio file hash as the key
  Future<void> cacheTranscriptionResult(
    String audioFileHash,
    TranscriptionResult result,
  ) async {
    try {
      // Convert the result to JSON
      final jsonResult = jsonEncode({
        'transcription': result.originalTranscription,
        'optimizedContent': result.optimizedContent,
        'category': result.category.toString(),
        'confidence': result.confidence,
        'timestamp': DateTime.now().toIso8601String(),
      });

      // Store in cache
      await _transcriptionBox.put(audioFileHash, jsonResult);

      _logger.i('Cached transcription result for $audioFileHash');

      // Clean up old cache entries if we exceed the maximum
      _cleanupCache();
    } catch (e) {
      _logger.e('Error caching transcription result: $e');
    }
  }

  /// Get a cached transcription result
  TranscriptionResult? getCachedTranscriptionResult(String audioFileHash) {
    try {
      final jsonString = _transcriptionBox.get(audioFileHash);

      if (jsonString == null) {
        return null;
      }

      // Parse the JSON
      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;

      // Convert category string to enum
      final categoryString = jsonData['category'] as String;
      final category = ContentCategory.values.firstWhere(
        (e) => e.toString() == categoryString,
        orElse: () => ContentCategory.task,
      );

      // Create and return the result
      return TranscriptionResult(
        originalTranscription: jsonData['transcription'] as String,
        optimizedContent: jsonData['optimizedContent'] as String,
        category: category,
        confidence: (jsonData['confidence'] as num).toDouble(),
      );
    } catch (e) {
      _logger.e('Error getting cached transcription result: $e');
      return null;
    }
  }

  /// Check if a transcription result is cached
  bool isTranscriptionCached(String audioFileHash) {
    return _transcriptionBox.containsKey(audioFileHash);
  }

  /// Clear all cached transcription results
  Future<void> clearCache() async {
    try {
      await _transcriptionBox.clear();
      _logger.i('Cleared transcription cache');
    } catch (e) {
      _logger.e('Error clearing transcription cache: $e');
    }
  }

  /// Clean up old cache entries if we exceed the maximum
  Future<void> _cleanupCache() async {
    try {
      if (_transcriptionBox.length <= _maxCachedItems) {
        return;
      }

      // Get all keys and their timestamps
      final entries = <MapEntry<String, DateTime>>[];

      for (final key in _transcriptionBox.keys) {
        final jsonString = _transcriptionBox.get(key.toString());
        if (jsonString != null) {
          final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
          final timestamp = DateTime.parse(jsonData['timestamp'] as String);
          entries.add(MapEntry(key.toString(), timestamp));
        }
      }

      // Sort by timestamp (oldest first)
      entries.sort((a, b) => a.value.compareTo(b.value));

      // Remove oldest entries until we're under the limit
      final entriesToRemove = entries.length - _maxCachedItems;
      if (entriesToRemove > 0) {
        for (int i = 0; i < entriesToRemove; i++) {
          await _transcriptionBox.delete(entries[i].key);
        }
        _logger.i('Removed $entriesToRemove old cache entries');
      }
    } catch (e) {
      _logger.e('Error cleaning up cache: $e');
    }
  }

  /// Close the cache
  Future<void> close() async {
    if (_transcriptionBox.isOpen) {
      await _transcriptionBox.close();
    }
  }
}
