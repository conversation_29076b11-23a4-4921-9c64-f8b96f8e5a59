import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// Service for managing tokens for premium features (Whisper & GPT)
class TokenService {
  final Logger _logger = Logger();

  // Constants for SharedPreferences keys
  static const String _whisperMinutesKey = 'whisper_minutes';
  static const String _gptTokensKey = 'gpt_tokens';
  static const String _userApiKeyKey = 'user_api_key';

  // Default token values for new users
  static const int _defaultWhisperMinutes = 10;
  static const int _defaultGptTokens = 1000;

  /// Get remaining Whisper minutes
  Future<int> getRemainingWhisperMinutes() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_whisperMinutesKey) ?? _defaultWhisperMinutes;
    } catch (e) {
      _logger.e('Error getting remaining Whisper minutes');
      return 0;
    }
  }

  /// Get remaining GPT tokens
  Future<int> getRemainingGptTokens() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_gptTokensKey) ?? _defaultGptTokens;
    } catch (e) {
      _logger.e('Error getting remaining GPT tokens');
      return 0;
    }
  }

  /// Check if user has Whisper minutes available
  Future<bool> hasWhisperMinutes() async {
    final int minutes = await getRemainingWhisperMinutes();
    return minutes > 0;
  }

  /// Check if user has GPT tokens available
  Future<bool> hasGptTokens() async {
    final int tokens = await getRemainingGptTokens();
    return tokens > 0;
  }

  /// Deduct Whisper minutes
  Future<bool> deductWhisperMinutes(int minutes) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final int remaining =
          prefs.getInt(_whisperMinutesKey) ?? _defaultWhisperMinutes;

      if (remaining < minutes) {
        return false; // Not enough minutes
      }

      await prefs.setInt(_whisperMinutesKey, remaining - minutes);
      _logger.i(
        'Deducted $minutes Whisper minutes. Remaining: ${remaining - minutes}',
      );
      return true;
    } catch (e) {
      _logger.e('Error deducting Whisper minutes');
      return false;
    }
  }

  /// Deduct GPT tokens
  Future<bool> deductGptTokens(int tokens) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final int remaining = prefs.getInt(_gptTokensKey) ?? _defaultGptTokens;

      if (remaining < tokens) {
        return false; // Not enough tokens
      }

      await prefs.setInt(_gptTokensKey, remaining - tokens);
      _logger.i(
        'Deducted $tokens GPT tokens. Remaining: ${remaining - tokens}',
      );
      return true;
    } catch (e) {
      _logger.e('Error deducting GPT tokens');
      return false;
    }
  }

  /// Add Whisper minutes (for purchasing or promotions)
  Future<bool> addWhisperMinutes(int minutes) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final int current =
          prefs.getInt(_whisperMinutesKey) ?? _defaultWhisperMinutes;
      await prefs.setInt(_whisperMinutesKey, current + minutes);
      _logger.i(
        'Added $minutes Whisper minutes. New total: ${current + minutes}',
      );
      return true;
    } catch (e) {
      _logger.e('Error adding Whisper minutes');
      return false;
    }
  }

  /// Add GPT tokens (for purchasing or promotions)
  Future<bool> addGptTokens(int tokens) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final int current = prefs.getInt(_gptTokensKey) ?? _defaultGptTokens;
      await prefs.setInt(_gptTokensKey, current + tokens);
      _logger.i('Added $tokens GPT tokens. New total: ${current + tokens}');
      return true;
    } catch (e) {
      _logger.e('Error adding GPT tokens');
      return false;
    }
  }

  /// Get user's API key if available
  Future<String?> getUserApiKey() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userApiKeyKey);
    } catch (e) {
      _logger.e('Error getting user API key');
      return null;
    }
  }

  /// Save user's API key
  Future<bool> saveUserApiKey(String apiKey) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userApiKeyKey, apiKey);
      _logger.i('Saved user API key');
      return true;
    } catch (e) {
      _logger.e('Error saving user API key');
      return false;
    }
  }

  /// Remove user's API key
  Future<bool> removeUserApiKey() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userApiKeyKey);
      _logger.i('Removed user API key');
      return true;
    } catch (e) {
      _logger.e('Error removing user API key');
      return false;
    }
  }

  /// Check if user has enough Whisper minutes
  Future<bool> hasEnoughWhisperMinutes(double required) async {
    final int remaining = await getRemainingWhisperMinutes();
    return remaining >= required.ceil();
  }

  /// Check if user has enough GPT tokens
  Future<bool> hasEnoughGptTokens(int required) async {
    final int remaining = await getRemainingGptTokens();
    return remaining >= required;
  }

  /// Use (deduct) specified Whisper minutes
  Future<bool> useWhisperMinutes(double amount) async {
    try {
      final int currentMinutes = await getRemainingWhisperMinutes();
      final int amountToDeduct = amount.ceil();

      if (currentMinutes < amountToDeduct) {
        _logger.w(
          'Not enough Whisper minutes. Required: $amountToDeduct, Available: $currentMinutes',
        );
        return false;
      }

      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final int newMinutes = currentMinutes - amountToDeduct;
      await prefs.setInt(_whisperMinutesKey, newMinutes);

      _logger.i('Used $amountToDeduct Whisper minutes. Remaining: $newMinutes');
      return true;
    } catch (e) {
      _logger.e('Error using Whisper minutes: $e');
      return false;
    }
  }

  /// Use (deduct) specified GPT tokens
  Future<bool> useGptTokens(int amount) async {
    try {
      final int currentTokens = await getRemainingGptTokens();

      if (currentTokens < amount) {
        _logger.w(
          'Not enough GPT tokens. Required: $amount, Available: $currentTokens',
        );
        return false;
      }

      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final int newTokens = currentTokens - amount;
      await prefs.setInt(_gptTokensKey, newTokens);

      _logger.i('Used $amount GPT tokens. Remaining: $newTokens');
      return true;
    } catch (e) {
      _logger.e('Error using GPT tokens: $e');
      return false;
    }
  }

  /// Reset token counts (for testing purposes)
  Future<bool> resetTokenCounts() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_whisperMinutesKey, _defaultWhisperMinutes);
      await prefs.setInt(_gptTokensKey, _defaultGptTokens);
      _logger.i('Reset token counts to defaults');
      return true;
    } catch (e) {
      _logger.e('Error resetting token counts: $e');
      return false;
    }
  }

  // Legacy method to ensure backward compatibility
  Future<int> getRemainingTokens() async {
    return getRemainingGptTokens();
  }

  // Legacy method to ensure backward compatibility
  Future<bool> hasEnoughTokens(int required) async {
    return hasEnoughGptTokens(required);
  }

  // Legacy method to ensure backward compatibility
  Future<bool> useTokens(int amount) async {
    return useGptTokens(amount);
  }

  // Legacy method to ensure backward compatibility
  Future<bool> addTokens(int amount) async {
    return addGptTokens(amount);
  }

  // Legacy method to ensure backward compatibility
  Future<bool> resetTokens([int? amount]) async {
    return resetTokenCounts();
  }
}
