import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// A service to handle social authentication methods like Google and Apple
class SocialAuthService {
  final Logger _logger = Logger();
  final SupabaseClient _supabaseClient;
  final GoogleSignIn _googleSignIn;

  /// Creates a new SocialAuthService
  SocialAuthService({SupabaseClient? supabaseClient, GoogleSignIn? googleSignIn})
    : _supabaseClient = supabaseClient ?? Supabase.instance.client,
      _googleSignIn =
          googleSignIn ??
          GoogleSignIn(
            // Use the client IDs from the new Firebase project (projectpilot-19c73)
            // Project number: 92846616463
            clientId:
                Platform.isIOS
                    ? '92846616463-c7mg7vmrgtivdnub0v60t873n99steml.apps.googleusercontent.com' // iOS client ID from new Firebase
                    : '92846616463-0b8jd048n71u8kl288deeg473cu9ms9q.apps.googleusercontent.com', // Android client ID from new Firebase
            scopes: ['email', 'profile'],
          );

  /// Sign in with Google using native Google Sign-In SDK
  /// This approach is more reliable than OAuth redirect on mobile
  Future<User?> signInWithGoogle() async {
    try {
      _logger.i('Starting native Google Sign-In flow');

      // Use native Google Sign-In
      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        _logger.i('Google Sign-In was canceled by user');
        return null;
      }

      final googleAuth = await googleUser.authentication;
      final accessToken = googleAuth.accessToken;
      final idToken = googleAuth.idToken;

      if (accessToken == null) {
        throw Exception('Failed to get Google access token');
      }
      if (idToken == null) {
        throw Exception('Failed to get Google ID token');
      }

      _logger.i('Google tokens obtained, signing in to Supabase');

      try {
        // Extract Google user information before sending to Supabase
        final googleUserData = {
          'email': googleUser.email,
          'full_name': googleUser.displayName,
          'name': googleUser.displayName,
          'display_name': googleUser.displayName,
          'picture': googleUser.photoUrl,
          'avatar_url': googleUser.photoUrl,
          'provider': 'google',
        };

        _logger.i('Google user data extracted: ${googleUserData.keys.join(', ')}');

        // Try with ID token approach and include user data
        final response = await _supabaseClient.auth.signInWithIdToken(
          provider: OAuthProvider.google,
          idToken: idToken,
          accessToken: accessToken,
          nonce: null,
        );

        final user = response.user;
        if (user != null) {
          _logger.i('Successfully signed in with Google using ID token');
          
          // Update user metadata with Google information
          try {
            await _supabaseClient.auth.updateUser(
              UserAttributes(
                data: googleUserData,
              ),
            );
            _logger.i('Updated user metadata with Google data');
          } catch (e) {
            _logger.w('Failed to update user metadata: $e');
            // Continue anyway - the main sign-in was successful
          }
          
          return user;
        }
      } catch (e) {
        _logger.w('ID token approach failed: $e');
        
        // Handle specific error cases
        if (e.toString().contains('Unacceptable audience')) {
          _logger.e('Google Sign-In configuration error: Audience mismatch');
          throw Exception(
            'Google Sign-In Konfigurationsfehler\n\n'
            '⚠️ Problem: Client ID Konflikt\n'
            'Die verwendete Client ID stimmt nicht mit der Supabase Konfiguration überein.\n\n'
            '🔧 Sofortige Lösung:\n'
            '1. Supabase Dashboard → Authentication → Providers → Google\n'
            '2. Verwende eine der folgenden Client IDs:\n'
            '   • iOS: 92846616463-c7mg7vmrgtivdnub0v60t873n99steml.apps.googleusercontent.com\n'
            '   • Android: 92846616463-0b8jd048n71u8kl288deeg473cu9ms9q.apps.googleusercontent.com\n'
            '   • Oder eine Web Client ID vom Firebase-Projekt 92846616463\n\n'
            '📋 Hinweis:\n'
            'Nutze das Firebase-Projekt (92846616463), nicht das andere Google Cloud Projekt.',
          );
        } else if (e.toString().contains('Invalid login credentials')) {
          throw Exception(
            'Google Sign-In Anmeldedaten ungültig\n\n'
            'Bitte versuche es erneut oder verwende eine andere Anmeldemethode.',
          );
        } else if (e.toString().contains('network') ||
            e.toString().contains('timeout')) {
          throw Exception(
            'Netzwerkfehler bei Google Sign-In\n\n'
            'Bitte überprüfe deine Internetverbindung und versuche es erneut.',
          );
        } else {
          // Re-throw other errors with the original message
          throw Exception('Google Sign-In Fehler: ${e.toString()}');
        }
      }

      return null;
    } catch (e) {
      _logger.e('Error signing in with Google: $e');

      // Handle specific platform errors
      if (e.toString().contains('PlatformException')) {
        if (e.toString().contains('Error while launching')) {
          throw Exception(
            'Google Sign-In Plattformfehler\n\n'
            '⚠️ Problem: URL kann nicht geöffnet werden\n'
            'Die App kann den Google Sign-In Prozess nicht starten.\n\n'
            '🔧 Mögliche Lösungen:\n'
            '1. App neu starten\n'
            '2. Google Play Services aktualisieren\n'
            '3. Gerät neu starten\n'
            '4. Alternative Anmeldemethode verwenden',
          );
        } else if (e.toString().contains('sign_in_failed') ||
            e.toString().contains('sign_in_canceled')) {
          throw Exception('Google Sign-In wurde abgebrochen');
        }
      }
      
      rethrow;
    }
  }

  /// Sign in with Apple and connect to Supabase
  /// Returns the User object if successful, null otherwise
  Future<User?> signInWithApple() async {
    try {
      _logger.i('Starting Apple sign-in flow');

      // Check if we're on iOS platform first
      if (!Platform.isIOS && !Platform.isMacOS) {
        _logger.w('Apple Sign In is only available on iOS/macOS');
        throw Exception(
          'Apple Sign In is only available on iOS and macOS devices.',
        );
      }

      // Check if Apple Sign In is available
      final isAvailable = await SignInWithApple.isAvailable();
      _logger.i('Apple Sign In available: $isAvailable');

      if (!isAvailable) {
        _logger.e('Apple Sign In is not available on this device');
        throw Exception(
          'Apple Sign In is not available.\n'
          '\n'
          '🚨 Most likely cause:\n'
          '• Running on iOS Simulator\n'
          '\n'
          '📱 Solution:\n'
          '• Test on a real iPhone/iPad device\n'
          '• Apple Sign In only works on physical devices\n'
          '\n'
          '⚠️ Other possible causes:\n'
          '• iOS version < 13.0\n'
          '• Missing Apple Developer configuration',
        );
      }

      // Generate a random string to prevent CSRF attacks
      final rawNonce = _generateNonce();
      final nonce = _sha256ofString(rawNonce);

      _logger.i('Generated nonce for Apple sign-in');

      // Request credentials from Apple
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
      );

      _logger.i('Apple credentials obtained');

      // Check if we have the required identity token
      if (appleCredential.identityToken == null) {
        _logger.e('Failed to get Apple identity token');
        throw Exception('Failed to get Apple identity token');
      }

      // If we're on iOS, use the Supabase Apple OAuth provider
      if (Platform.isIOS || Platform.isMacOS) {
        // Extract Apple user information
        final appleUserData = <String, dynamic>{
          'provider': 'apple',
        };

        // Add name if provided (only on first sign-in)
        if (appleCredential.givenName != null || appleCredential.familyName != null) {
          final fullName = '${appleCredential.givenName ?? ''} ${appleCredential.familyName ?? ''}'.trim();
          if (fullName.isNotEmpty) {
            appleUserData['full_name'] = fullName;
            appleUserData['name'] = fullName;
            appleUserData['display_name'] = fullName;
          }
        }

        // Add email if provided
        if (appleCredential.email != null) {
          appleUserData['email'] = appleCredential.email;
        }

        _logger.i('Apple user data extracted: ${appleUserData.keys.join(', ')}');

        // Sign in with the Apple credentials
        final response = await _supabaseClient.auth.signInWithIdToken(
          provider: OAuthProvider.apple,
          idToken: appleCredential.identityToken!,
          nonce: rawNonce,
        );

        final user = response.user;
        if (user != null) {
          _logger.i('Successfully signed in to Supabase with Apple');
          
          // Update user metadata with Apple information (only if we have data)
          if (appleUserData.length > 1) { // More than just 'provider'
            try {
              await _supabaseClient.auth.updateUser(
                UserAttributes(
                  data: appleUserData,
                ),
              );
              _logger.i('Updated user metadata with Apple data');
            } catch (e) {
              _logger.w('Failed to update user metadata: $e');
              // Continue anyway - the main sign-in was successful
            }
          }
          
          return user;
        }

        return null;
      } else {
        // On other platforms, use the Supabase Apple OAuth provider
        // with the redirect flow
        await _supabaseClient.auth.signInWithOAuth(
          OAuthProvider.apple,
          queryParams: {
            'id_token': appleCredential.identityToken!,
            'nonce': rawNonce,
          },
        );

        // The result will be handled by the Supabase auth listener
        _logger.i('Started Apple OAuth flow for non-iOS platform');
        return null;
      }
    } catch (e) {
      _logger.e('Error signing in with Apple: $e');

      // Provide better error messages for common Apple Sign-In issues
      String errorMessage = e.toString();
      if (errorMessage.contains('SignInWithAppleAuthorizationException')) {
        if (errorMessage.contains('canceled') ||
            errorMessage.contains('1001')) {
          throw Exception('Apple Sign-In wurde abgebrochen');
        } else if (errorMessage.contains('invalid_request') ||
            errorMessage.contains('1000')) {
          throw Exception(
            'Apple Sign-In Konfigurationsfehler\n\n'
            '📋 Bundle ID: com.innovatio.projectpilot\n\n'
            '⚠️ Mögliche Ursachen:\n'
            '• App ID nicht im Apple Developer Portal registriert\n'
            '• "Sign In with Apple" Capability nicht aktiviert\n'
            '• Service ID fehlt oder falsch konfiguriert\n\n'
            '🔧 Lösung:\n'
            '1. Apple Developer Portal öffnen\n'
            '2. Identifiers → App IDs → com.innovatio.projectpilot\n'
            '3. "Sign In with Apple" Capability aktivieren\n'
            '4. Service ID für diese App erstellen',
          );
        }
      } else if (errorMessage.contains('1000')) {
        throw Exception(
          'Apple Sign In ist nicht verfügbar:\n'
          '• iOS Simulator (benötigt echtes Gerät)\n'
          '• Fehlende Apple Developer Konfiguration\n'
          '• App nicht für Apple Sign In konfiguriert'
        );
      }

      rethrow;
    }
  }

  /// Generate a random nonce for Apple sign-in
  String _generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = DateTime.now().microsecondsSinceEpoch;
    return List.generate(
      length,
      (i) => charset[random % charset.length],
    ).join();
  }

  /// Returns the SHA-256 hash of [input] in base64 encoding.
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
