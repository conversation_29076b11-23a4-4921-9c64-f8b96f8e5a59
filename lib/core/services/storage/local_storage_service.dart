import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

class LocalStorageException implements Exception {
  final String message;
  LocalStorageException(this.message);

  @override
  String toString() => message;
}

/// Service for local storage operations using Hive
/// Simplified for ProjectPilot - focused on project data
class LocalStorageService {
  /// Box for storing app settings
  late Box<String> _settingsBox;
  
  /// Box for storing project cache
  late Box<String> _projectCacheBox;
  
  LocalStorageService();
  
  /// Initialize the local storage service
  Future<void> init() async {
    try {
      await _closeOpenBoxes();
      _settingsBox = await Hive.openBox<String>('settings');
      _projectCacheBox = await Hive.openBox<String>('project_cache');
      debugPrint('LocalStorageService initialized for ProjectPilot');
    } catch (e) {
      debugPrint('Error initializing LocalStorageService: $e');
      rethrow;
    }
  }
  
  /// Close any open boxes to prevent errors
  Future<void> _closeOpenBoxes() async {
    try {
      if (Hive.isBoxOpen('settings')) {
        await Hive.box('settings').close();
      }
      if (Hive.isBoxOpen('project_cache')) {
        await Hive.box('project_cache').close();
      }
    } catch (e) {
      debugPrint('Error closing boxes: $e');
    }
  }
  
  // Settings methods
  
  /// Save a setting
  Future<void> saveSetting(String key, String value) async {
    try {
      await _settingsBox.put(key, value);
    } catch (e) {
      debugPrint('Error saving setting: $e');
      rethrow;
    }
  }
  
  /// Get a setting
  Future<String?> getSetting(String key) async {
    try {
      return _settingsBox.get(key);
    } catch (e) {
      debugPrint('Error getting setting: $e');
      return null;
    }
  }
  
  /// Delete a setting
  Future<void> deleteSetting(String key) async {
    try {
      await _settingsBox.delete(key);
    } catch (e) {
      debugPrint('Error deleting setting: $e');
      rethrow;
    }
  }
  
  // Project cache methods
  
  /// Save project data to cache
  Future<void> saveProjectCache(String key, Map<String, dynamic> data) async {
    try {
      await _projectCacheBox.put(key, jsonEncode(data));
    } catch (e) {
      debugPrint('Error saving project cache: $e');
      rethrow;
    }
  }
  
  /// Get project data from cache
  Future<Map<String, dynamic>?> getProjectCache(String key) async {
    try {
      final String? jsonString = _projectCacheBox.get(key);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting project cache: $e');
      return null;
    }
  }
  
  /// Delete project cache
  Future<void> deleteProjectCache(String key) async {
    try {
      await _projectCacheBox.delete(key);
    } catch (e) {
      debugPrint('Error deleting project cache: $e');
      rethrow;
    }
  }
  
  /// Clear all project cache
  Future<void> clearProjectCache() async {
    try {
      await _projectCacheBox.clear();
    } catch (e) {
      debugPrint('Error clearing project cache: $e');
      rethrow;
    }
  }
  
  /// Clear all data
  Future<void> clearAll() async {
    try {
      await _settingsBox.clear();
      await _projectCacheBox.clear();
      debugPrint('Cleared all local storage data');
    } catch (e) {
      debugPrint('Error clearing all data: $e');
      rethrow;
    }
  }
  
  /// Close all boxes
  Future<void> close() async {
    try {
      await _settingsBox.close();
      await _projectCacheBox.close();
    } catch (e) {
      debugPrint('Error closing boxes: $e');
    }
  }
}
