import 'dart:developer' as developer;

/// A service for logging messages
class LoggerService {
  /// Creates a new logger service
  LoggerService();

  /// Log a debug message
  void d(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    developer.log('DEBUG: $message', name: 'ProjectPilot');
    if (error != null) developer.log('ERROR: $error', name: 'ProjectPilot');
    if (stackTrace != null)
      developer.log('STACK: $stackTrace', name: 'ProjectPilot');
  }

  /// Log an info message
  void i(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    developer.log('INFO: $message', name: 'ProjectPilot');
    if (error != null) developer.log('ERROR: $error', name: 'ProjectPilot');
    if (stackTrace != null)
      developer.log('STACK: $stackTrace', name: 'ProjectPilot');
  }

  /// Log a warning message
  void w(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    developer.log('WARNING: $message', name: 'ProjectPilot', level: 900);
    if (error != null) developer.log('ERROR: $error', name: 'ProjectPilot');
    if (stackTrace != null)
      developer.log('STACK: $stackTrace', name: 'ProjectPilot');
  }

  /// Log an error message
  void e(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    developer.log('ERROR: $message', name: 'ProjectPilot');
    if (error != null) developer.log('ERROR: $error', name: 'ProjectPilot');
    if (stackTrace != null)
      developer.log('STACK: $stackTrace', name: 'ProjectPilot');
  }

  /// Log a verbose message
  void v(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    developer.log('VERBOSE: $message', name: 'ProjectPilot');
    if (error != null) developer.log('ERROR: $error', name: 'ProjectPilot');
    if (stackTrace != null)
      developer.log('STACK: $stackTrace', name: 'ProjectPilot');
  }

  /// Log a wtf message
  void wtf(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    developer.log('WTF: $message', name: 'ProjectPilot');
    if (error != null) developer.log('ERROR: $error', name: 'ProjectPilot');
    if (stackTrace != null)
      developer.log('STACK: $stackTrace', name: 'ProjectPilot');
  }

  /// Log info message
  static void logInfo(String message, {String? tag}) {
    developer.log(message, name: tag ?? 'ProjectPilot');
  }

  /// Log debug message
  static void logDebug(String message, {String? tag}) {
    developer.log(message, name: tag ?? 'ProjectPilot');
  }

  /// Log warning message
  static void logWarning(String message, {String? tag}) {
    developer.log(message, name: tag ?? 'ProjectPilot', level: 900);
  }

  /// Log error message
  static void logError(String message, {String? tag, Object? error}) {
    developer.log(
      message,
      name: tag ?? 'ProjectPilot',
      level: 1000,
      error: error,
    );
  }

  /// Log fatal error message
  static void logFatal(String message, {String? tag, Object? error}) {
    developer.log(
      message,
      name: tag ?? 'ProjectPilot',
      level: 1200,
      error: error,
    );
  }
}
