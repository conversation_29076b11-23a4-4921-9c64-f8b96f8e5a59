import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Weather condition model class
class WeatherInfo {
  final String condition;
  final String description;
  final String icon;
  final double temperature;
  final double feelsLike;
  final double humidity;
  final String cityName;

  WeatherInfo({
    required this.condition,
    required this.description,
    required this.icon,
    required this.temperature,
    required this.feelsLike,
    required this.humidity,
    required this.cityName,
  });

  String get iconUrl => 'https://openweathermap.org/img/wn/$<EMAIL>';

  factory WeatherInfo.fromJson(Map<String, dynamic> json) {
    return WeatherInfo(
      condition: json['weather'][0]['main'],
      description: json['weather'][0]['description'],
      icon: json['weather'][0]['icon'],
      temperature: (json['main']['temp'] as num).toDouble(),
      feelsLike: (json['main']['feels_like'] as num).toDouble(),
      humidity: (json['main']['humidity'] as num).toDouble(),
      cityName: json['name'],
    );
  }
}

/// Service to fetch weather information from OpenWeather API
class WeatherService {
  final Dio _dio = Dio();
  final String _baseUrl = 'https://api.openweathermap.org/data/2.5';

  /// Get API key from environment variables
  String get _apiKey => dotenv.env['OPENWEATHER_API_KEY'] ?? '';

  /// Get weather by city name
  Future<WeatherInfo> getWeatherByCity(String city) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/weather',
        queryParameters: {
          'q': city,
          'appid': _apiKey,
          'units': 'metric', // Use metric units for temperature
        },
      );

      if (response.statusCode == 200) {
        return WeatherInfo.fromJson(response.data);
      } else {
        throw Exception('Failed to load weather data');
      }
    } catch (e) {
      throw Exception('Error fetching weather: $e');
    }
  }

  /// Get weather by geographic coordinates
  Future<WeatherInfo> getWeatherByLocation(double lat, double lon) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/weather',
        queryParameters: {
          'lat': lat,
          'lon': lon,
          'appid': _apiKey,
          'units': 'metric', // Use metric units for temperature
        },
      );

      if (response.statusCode == 200) {
        return WeatherInfo.fromJson(response.data);
      } else {
        throw Exception('Failed to load weather data');
      }
    } catch (e) {
      throw Exception('Error fetching weather: $e');
    }
  }
}
