import 'package:logger/logger.dart';
import 'package:projectpilot/core/constants/api_constants.dart';

/// Typ des erkannten Befehls
enum CommandType {
  /// Aufgabe erstellen
  createTask,

  /// Notiz hinzufügen
  addNote,

  /// Idee speichern
  saveIdea,

  /// Unbekannter Befehl
  unknown,
}

/// Result of intent parsing containing detected platform and project information
class IntentParsingResult {
  /// The detected platform ID (e.g., 'jira', 'clickup')
  final String? platformId;

  /// The detected project name or ID
  final String? projectName;

  /// Erkannter Befehlstyp
  final CommandType commandType;

  /// Confidence level of the detection (0.0 to 1.0)
  final double confidence;

  /// Ob der Befehl vollständig ist (Plattform, Projekt und Befehl erkannt)
  final bool isComplete;

  /// Fehlende Informationen (z.B. "platform", "project", "command")
  final List<String> missingInfo;

  const IntentParsingResult({
    this.platformId,
    this.projectName,
    this.commandType = CommandType.unknown,
    this.confidence = 0.0,
    this.isComplete = false,
    this.missingInfo = const [],
  });

  /// Whether a platform was detected with sufficient confidence
  bool get hasPlatform => platformId != null && confidence > 0.3;

  /// Whether both platform and project were detected with sufficient confidence
  bool get hasCompleteDestination =>
      platformId != null && projectName != null && confidence > 0.5;

  /// Whether a command type was detected with sufficient confidence
  bool get hasCommand => commandType != CommandType.unknown && confidence > 0.3;

  /// Whether all required information is available to proceed directly
  bool get canProceedDirectly => isComplete && confidence > 0.6;

  /// Creates a copy of this result with the given fields replaced
  IntentParsingResult copyWith({
    String? platformId,
    String? projectName,
    CommandType? commandType,
    double? confidence,
    bool? isComplete,
    List<String>? missingInfo,
  }) {
    return IntentParsingResult(
      platformId: platformId ?? this.platformId,
      projectName: projectName ?? this.projectName,
      commandType: commandType ?? this.commandType,
      confidence: confidence ?? this.confidence,
      isComplete: isComplete ?? this.isComplete,
      missingInfo: missingInfo ?? this.missingInfo,
    );
  }
}

/// Service for parsing intents from transcribed text to extract platform and project information
class IntentParserService {
  final Logger _logger = Logger();

  // Platform name variations for detection (lowercase)
  final Map<String, List<String>> _platformVariations = {
    ApiConstants.clickUp: ['clickup', 'click up', 'click-up', 'klickup', 'klick up'],

    ApiConstants.notion: ['notion'],
    ApiConstants.asana: ['asana'],
    ApiConstants.monday: ['monday', 'monday.com'],
    ApiConstants.jira: ['jira', 'jira cloud'],
    ApiConstants.trello: ['trello'],
  };

  // Prepositions that might precede project names in different languages
  final Map<String, List<String>> _projectPrepositions = {
    'en': ['in', 'to', 'into', 'under', 'for', 'on'],
    'de': ['in', 'zu', 'unter', 'für', 'an', 'auf'],
    'ru': ['в', 'на', 'для', 'к'],
    'tr': ['içinde', 'için', 'üzerinde'],
    'ar': ['في', 'إلى', 'على', 'ل'],
  };

  // Platform-related phrases in different languages
  final Map<String, List<String>> _platformPhrases = {
    'en': ['add to', 'create in', 'save to', 'send to', 'put in', 'store in'],
    'de': ['hinzufügen zu', 'erstellen in', 'speichern in', 'senden an', 'ablegen in'],
    'ru': ['добавить в', 'создать в', 'сохранить в', 'отправить в'],
    'tr': ['ekle', 'oluştur', 'kaydet', 'gönder'],
    'ar': ['أضف إلى', 'إنشاء في', 'حفظ في', 'إرسال إلى'],
  };

  // Command-related phrases in different languages
  final Map<String, Map<CommandType, List<String>>> _commandPhrases = {
    'en': {
      CommandType.createTask: [
        'create task',
        'add task',
        'new task',
        'make task',
        'create a task',
        'add a task',
      ],
      CommandType.addNote: [
        'create note',
        'add note',
        'new note',
        'make note',
        'create a note',
        'add a note',
      ],
      CommandType.saveIdea: [
        'save idea',
        'add idea',
        'new idea',
        'create idea',
        'save an idea',
        'add an idea',
      ],
    },
    'de': {
      CommandType.createTask: [
        'erstelle aufgabe',
        'neue aufgabe',
        'aufgabe erstellen',
        'aufgabe anlegen',
        'task erstellen',
        'todo erstellen',
        'to-do erstellen',
      ],
      CommandType.addNote: [
        'erstelle notiz',
        'neue notiz',
        'notiz erstellen',
        'notiz anlegen',
        'notiz hinzufügen',
        'notiz speichern',
      ],
      CommandType.saveIdea: [
        'speichere idee',
        'neue idee',
        'idee erstellen',
        'idee anlegen',
        'idee hinzufügen',
        'idee speichern',
      ],
    },
    'ru': {
      CommandType.createTask: [
        'создать задачу',
        'добавить задачу',
        'новая задача',
      ],
      CommandType.addNote: [
        'создать заметку',
        'добавить заметку',
        'новая заметка',
      ],
      CommandType.saveIdea: ['сохранить идею', 'добавить идею', 'новая идея'],
    },
    'tr': {
      CommandType.createTask: ['görev oluştur', 'görev ekle', 'yeni görev'],
      CommandType.addNote: ['not oluştur', 'not ekle', 'yeni not'],
      CommandType.saveIdea: ['fikir kaydet', 'fikir ekle', 'yeni fikir'],
    },
    'ar': {
      CommandType.createTask: ['إنشاء مهمة', 'إضافة مهمة', 'مهمة جديدة'],
      CommandType.addNote: ['إنشاء ملاحظة', 'إضافة ملاحظة', 'ملاحظة جديدة'],
      CommandType.saveIdea: ['حفظ فكرة', 'إضافة فكرة', 'فكرة جديدة'],
    },
  };

  /// Parse intent from transcribed text to extract platform and project information
  ///
  /// [text] The transcribed text to analyze
  /// [languageCode] ISO 639-1 language code (e.g., 'en', 'de')
  IntentParsingResult parseIntent(String text, String languageCode) {
    try {
      final String normalizedText = text.toLowerCase().trim();

      // Extract platform information
      final platformResult = _extractPlatform(normalizedText);
      final String? platformId = platformResult['platformId'] as String?;
      final double platformConfidence = platformResult['confidence'] as double? ?? 0.0;

      // Extract project information if platform was detected
      String? projectName;
      double projectConfidence = 0.0;

      if (platformId != null) {
        final projectResult = _extractProject(
          normalizedText,
          platformId,
          languageCode,
        );
        projectName = projectResult['projectName'] as String?;
        projectConfidence = projectResult['confidence'] as double? ?? 0.0;
      }

      // Extract command type
      final commandResult = _extractCommandType(normalizedText, languageCode);
      final CommandType commandType =
          commandResult['commandType'] as CommandType;
      final double commandConfidence =
          commandResult['confidence'] as double? ?? 0.0;

      // Calculate overall confidence
      double overallConfidence = 0.0;
      if (platformId != null && commandType != CommandType.unknown) {
        overallConfidence = (platformConfidence + commandConfidence) / 2;
        if (projectName != null) {
          overallConfidence =
              (platformConfidence + projectConfidence + commandConfidence) / 3;
        }
      } else if (platformId != null) {
        overallConfidence = platformConfidence;
        if (projectName != null) {
          overallConfidence = (platformConfidence + projectConfidence) / 2;
        }
      } else if (commandType != CommandType.unknown) {
        overallConfidence = commandConfidence;
      }

      // Determine if the intent is complete and what information is missing
      final List<String> missingInfo = [];
      if (platformId == null || platformConfidence < 0.3) {
        missingInfo.add('platform');
      }
      if (projectName == null || projectConfidence < 0.3) {
        missingInfo.add('project');
      }
      if (commandType == CommandType.unknown || commandConfidence < 0.3) {
        missingInfo.add('command');
      }

      final bool isComplete = missingInfo.isEmpty;

      _logger.i(
        'Intent parsed: Platform=$platformId (${platformConfidence.toStringAsFixed(2)}), '
        'Project=$projectName (${projectConfidence.toStringAsFixed(2)}), '
        'Command=${commandType.toString().split('.').last} (${commandConfidence.toStringAsFixed(2)}), '
        'Overall=${overallConfidence.toStringAsFixed(2)}, '
        'Complete=$isComplete, Missing=${missingInfo.join(', ')}'
      );

      return IntentParsingResult(
        platformId: platformId,
        projectName: projectName,
        commandType: commandType,
        confidence: overallConfidence,
        isComplete: isComplete,
        missingInfo: missingInfo,
      );
    } catch (e) {
      _logger.e('Error parsing intent: $e');
      return const IntentParsingResult(
        confidence: 0.0,
        missingInfo: ['platform', 'project', 'command'],
      );
    }
  }

  /// Extract platform information from text
  Map<String, dynamic> _extractPlatform(String text) {
    double highestConfidence = 0.0;
    String? detectedPlatformId;

    // Check for each platform variation
    for (final entry in _platformVariations.entries) {
      final String platformId = entry.key;
      final List<String> variations = entry.value;

      for (final variation in variations) {
        if (text.contains(variation)) {
          // Calculate confidence based on context
          double confidence = 0.6; // Base confidence for exact match

          // Increase confidence if platform is mentioned with typical phrases
          for (final phrases in _platformPhrases.values) {
            for (final phrase in phrases) {
              if (text.contains('$phrase $variation') ||
                  text.contains('$variation $phrase')) {
                confidence += 0.2;
                break;
              }
            }
          }

          // If this platform has higher confidence, update the result
          if (confidence > highestConfidence) {
            highestConfidence = confidence;
            detectedPlatformId = platformId;
          }
        }
      }
    }

    return {
      'platformId': detectedPlatformId,
      'confidence': highestConfidence,
    };
  }

  /// Extract project information from text
  Map<String, dynamic> _extractProject(
    String text,
    String platformId,
    String languageCode,
  ) {
    // Get platform variations for the detected platform
    final List<String> platformVariations =
        _platformVariations[platformId] ?? [platformId];

    // Get prepositions for the specified language
    final List<String> prepositions =
        _projectPrepositions[languageCode] ?? _projectPrepositions['en']!;

    String? projectName;
    double confidence = 0.0;

    // Try to extract project name using prepositions
    for (final platformVariation in platformVariations) {
      for (final preposition in prepositions) {
        final RegExp pattern = RegExp(
          '$platformVariation\\s+$preposition\\s+([\\w\\s-]+)',
          caseSensitive: false,
        );

        final match = pattern.firstMatch(text);
        if (match != null && match.groupCount >= 1) {
          final extractedName = match.group(1)?.trim();
          if (extractedName != null && extractedName.isNotEmpty) {
            projectName = extractedName;
            confidence = 0.7; // Base confidence for pattern match
            break;
          }
        }
      }

      if (projectName != null) break;
    }

    // If no project found with prepositions, try to find project after platform name
    if (projectName == null) {
      for (final platformVariation in platformVariations) {
        final RegExp pattern = RegExp(
          '$platformVariation\\s+([\\w\\s-]+)',
          caseSensitive: false,
        );

        final match = pattern.firstMatch(text);
        if (match != null && match.groupCount >= 1) {
          final extractedName = match.group(1)?.trim();
          if (extractedName != null &&
              extractedName.isNotEmpty &&
              !prepositions.contains(extractedName)) {
            projectName = extractedName;
            confidence = 0.5; // Lower confidence without preposition
            break;
          }
        }
      }
    }

    return {
      'projectName': projectName,
      'confidence': confidence,
    };
  }

  /// Extract command type from text
  Map<String, dynamic> _extractCommandType(String text, String languageCode) {
    // Get command phrases for the specified language or fall back to English
    final commandPhrases =
        _commandPhrases[languageCode] ?? _commandPhrases['en']!;

    CommandType detectedCommandType = CommandType.unknown;
    double highestConfidence = 0.0;

    // Check for each command type
    for (final entry in commandPhrases.entries) {
      final CommandType commandType = entry.key;
      final List<String> phrases = entry.value;

      for (final phrase in phrases) {
        if (text.contains(phrase)) {
          // Calculate confidence based on exact match
          double confidence = 0.7; // Base confidence for exact match

          // Increase confidence if phrase is at the beginning of the text
          if (text.startsWith(phrase)) {
            confidence += 0.2;
          }

          // If this command has higher confidence, update the result
          if (confidence > highestConfidence) {
            highestConfidence = confidence;
            detectedCommandType = commandType;
          }
        }
      }
    }

    // If no command was detected but we can infer from content category
    if (detectedCommandType == CommandType.unknown &&
        highestConfidence == 0.0) {
      // Check for task-related keywords
      if (_containsAny(text, [
        'task',
        'todo',
        'to-do',
        'to do',
        'aufgabe',
        'задача',
        'görev',
        'مهمة',
      ])) {
        detectedCommandType = CommandType.createTask;
        highestConfidence = 0.5;
      }
      // Check for note-related keywords
      else if (_containsAny(text, [
        'note',
        'notiz',
        'заметка',
        'not',
        'ملاحظة',
      ])) {
        detectedCommandType = CommandType.addNote;
        highestConfidence = 0.5;
      }
      // Check for idea-related keywords
      else if (_containsAny(text, ['idea', 'idee', 'идея', 'fikir', 'فكرة'])) {
        detectedCommandType = CommandType.saveIdea;
        highestConfidence = 0.5;
      }
    }

    return {
      'commandType': detectedCommandType,
      'confidence': highestConfidence,
    };
  }

  /// Check if text contains any of the given keywords
  bool _containsAny(String text, List<String> keywords) {
    for (final keyword in keywords) {
      if (text.contains(keyword)) {
        return true;
      }
    }
    return false;
  }
}
