import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart';

/// Service that monitors network connectivity status
class NetworkInfo {
  final Connectivity _connectivity;
  final Logger _logger = Logger();

  bool _isConnected = false;
  final StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();

  /// Stream of connectivity status changes (true when connected, false when disconnected)
  Stream<bool> get connectionStatusStream => _connectionStatusController.stream;

  /// Current connectivity status
  bool get isConnected => _isConnected;

  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;

  NetworkInfo({Connectivity? connectivity})
    : _connectivity = connectivity ?? Connectivity() {
    // Initialize with a default value, will be updated in init()
    _isConnected = false;
    // Call init asynchronously
    Future.microtask(() => _init());
  }

  /// Initialize the connectivity monitoring
  Future<void> _init() async {
    try {
      // Get initial connectivity status
      _isConnected = await _checkConnection();
      _connectionStatusController.add(_isConnected);

      // Listen for connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _updateConnectionStatus,
      );

      _logger.i(
        'NetworkInfo initialized. Initial status: ${_isConnected ? 'Connected' : 'Disconnected'}',
      );
    } catch (e) {
      _logger.e('Error initializing NetworkInfo: $e');
      // Ensure we have a default value in case of error
      _isConnected = false;
      _connectionStatusController.add(_isConnected);
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(List<ConnectivityResult> results) async {
    final bool wasConnected = _isConnected;
    // Consider connected if any result is not ConnectivityResult.none
    _isConnected = results.any((r) => r != ConnectivityResult.none);

    // Only emit event if status changed
    if (wasConnected != _isConnected) {
      _connectionStatusController.add(_isConnected);
      _logger.i(
        'Connection status changed: [38;5;208m${_isConnected ? 'Connected' : 'Disconnected'}',
      );
    }
  }

  /// Check the current connectivity status
  Future<bool> _checkConnection() async {
    try {
      final results = await _connectivity.checkConnectivity();
      // Check if any result is not none (i.e., we have some form of connectivity)
      return results.any((r) => r != ConnectivityResult.none);
    } catch (e) {
      _logger.e('Error checking connectivity: $e');
      return false;
    }
  }

  /// Manually check current connection status and update if needed
  Future<bool> checkConnection() async {
    final bool isCurrentlyConnected = await _checkConnection();
    if (_isConnected != isCurrentlyConnected) {
      _isConnected = isCurrentlyConnected;
      _connectionStatusController.add(_isConnected);
      _logger.i(
        'Connection status manually updated: ${_isConnected ? 'Connected' : 'Disconnected'}',
      );
    }
    return _isConnected;
  }

  /// Dispose of resources
  void dispose() {
    _connectivitySubscription.cancel();
    _connectionStatusController.close();
  }
}
