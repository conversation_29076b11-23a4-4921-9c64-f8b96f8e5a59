import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

/// Service to monitor and manage network connectivity
class ConnectivityService {
  /// Connectivity instance
  final Connectivity _connectivity = Connectivity();

  /// Stream controller for connectivity status
  final StreamController<ConnectivityStatus> _controller =
      StreamController<ConnectivityStatus>.broadcast();

  /// Current connectivity status
  ConnectivityStatus _currentStatus = ConnectivityStatus.unknown;

  /// Stream of connectivity status changes
  Stream<ConnectivityStatus> get statusStream => _controller.stream;

  /// Current connectivity status
  ConnectivityStatus get currentStatus => _currentStatus;

  /// Whether the device is currently online
  bool get isOnline => _currentStatus == ConnectivityStatus.online;

  /// Constructor
  ConnectivityService() {
    // Initialize
    _init();
  }

  /// Initialize the service
  Future<void> _init() async {
    // Get initial connectivity status
    await _checkConnectivity();

    // Listen for connectivity changes
    _connectivity.onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      // Use the first result or none if empty
      final result =
          results.isNotEmpty ? results.first : ConnectivityResult.none;
      _updateStatus(result);
    });
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final List<ConnectivityResult> results =
          await _connectivity.checkConnectivity();
      // Use the first result or none if empty
      final result =
          results.isNotEmpty ? results.first : ConnectivityResult.none;
      _updateStatus(result);
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      _controller.add(ConnectivityStatus.unknown);
    }
  }

  /// Update connectivity status based on result
  void _updateStatus(ConnectivityResult result) {
    ConnectivityStatus status;

    switch (result) {
      case ConnectivityResult.none:
        status = ConnectivityStatus.offline;
        break;
      case ConnectivityResult.mobile:
      case ConnectivityResult.wifi:
      case ConnectivityResult.ethernet:
      case ConnectivityResult.bluetooth:
      case ConnectivityResult.vpn:
      case ConnectivityResult.other:
        status = ConnectivityStatus.online;
        break;
    }

    // Only notify if status changed
    if (status != _currentStatus) {
      _currentStatus = status;
      _controller.add(status);
    }
  }

  /// Manually check connectivity
  Future<ConnectivityStatus> checkConnectivity() async {
    await _checkConnectivity();
    return _currentStatus;
  }

  /// Dispose resources
  void dispose() {
    _controller.close();
  }
}

/// Connectivity status enum
enum ConnectivityStatus {
  /// Device is online
  online,

  /// Device is offline
  offline,

  /// Connectivity status is unknown
  unknown,
}
