import 'dart:async';
import 'dart:math';
import 'package:logger/logger.dart';
import 'package:projectpilot/core/constants/app_constants.dart';

/// Service for providing real-time audio level information
class AudioLevelService {
  final Logger _logger = Logger();
  
  /// Stream controller for audio level updates
  final StreamController<double> _audioLevelController = 
      StreamController<double>.broadcast();
  
  /// Stream of audio level updates (0.0 to 1.0)
  Stream<double> get audioLevelStream => _audioLevelController.stream;
  
  /// Whether the service is currently processing audio
  bool _isProcessing = false;
  
  /// Timer for simulating audio levels in mock mode
  Timer? _mockTimer;
  
  /// Last processed audio level
  double _lastAudioLevel = 0.0;
  
  /// Singleton instance
  static final AudioLevelService _instance = AudioLevelService._internal();
  
  /// Factory constructor
  factory AudioLevelService() => _instance;
  
  /// Private constructor
  AudioLevelService._internal();
  
  /// Initialize the service
  Future<void> init() async {
    _logger.i('AudioLevelService initialized');
  }
  
  /// Process audio data and emit audio level
  void processAudioData(List<int> audioData) {
    if (!_isProcessing) return;
    
    try {
      // Calculate RMS (Root Mean Square) of audio data
      // This gives us a measure of the audio level
      double sumOfSquares = 0.0;
      
      // Process 16-bit PCM audio data
      for (int i = 0; i < audioData.length; i += 2) {
        if (i + 1 < audioData.length) {
          // Convert two bytes to a 16-bit sample
          final sample = audioData[i] | (audioData[i + 1] << 8);
          // Convert to signed value
          final signedSample = sample < 32768 ? sample : sample - 65536;
          // Normalize to [-1, 1] range
          final normalizedSample = signedSample / 32768.0;
          // Square the sample
          sumOfSquares += normalizedSample * normalizedSample;
        }
      }
      
      // Calculate RMS
      final rms = sqrt(sumOfSquares / (audioData.length / 2));
      
      // Apply some smoothing to avoid jumpy visualization
      final smoothedLevel = _smoothAudioLevel(rms);
      
      // Emit the audio level
      _audioLevelController.add(smoothedLevel);
    } catch (e) {
      _logger.e('Error processing audio data: $e');
    }
  }
  
  /// Start processing audio
  void startProcessing() {
    _isProcessing = true;
    _logger.i('Started audio level processing');
    
    // If we're in mock mode, start simulating audio levels
    if (AppConstants.useMockAudioLevels) {
      _startMockAudioLevels();
    }
  }
  
  /// Stop processing audio
  void stopProcessing() {
    _isProcessing = false;
    _logger.i('Stopped audio level processing');
    
    // Stop mock timer if running
    _mockTimer?.cancel();
    _mockTimer = null;
    
    // Reset audio level
    _lastAudioLevel = 0.0;
    _audioLevelController.add(0.0);
  }
  
  /// Start simulating audio levels for testing/mock mode
  void _startMockAudioLevels() {
    _mockTimer?.cancel();
    
    // Emit random audio levels every 100ms
    _mockTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_isProcessing) {
        timer.cancel();
        return;
      }
      
      // Generate random audio level with some smoothing
      final randomLevel = Random().nextDouble() * 0.8 + 0.2; // 0.2 to 1.0
      final smoothedLevel = _smoothAudioLevel(randomLevel);
      
      // Emit the audio level
      _audioLevelController.add(smoothedLevel);
    });
  }
  
  /// Apply smoothing to audio level to avoid jumpy visualization
  double _smoothAudioLevel(double newLevel) {
    // Apply exponential smoothing
    const alpha = 0.3; // Smoothing factor (0.0 to 1.0)
    final smoothedLevel = alpha * newLevel + (1 - alpha) * _lastAudioLevel;
    
    // Update last level
    _lastAudioLevel = smoothedLevel;
    
    return smoothedLevel;
  }
  
  /// Dispose the service
  void dispose() {
    _mockTimer?.cancel();
    _audioLevelController.close();
  }
}
