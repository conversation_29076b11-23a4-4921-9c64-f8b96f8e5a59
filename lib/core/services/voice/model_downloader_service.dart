import 'dart:async';
import 'dart:io';
import 'package:archive/archive.dart';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:projectpilot/core/constants/app_constants.dart';
import 'package:projectpilot/core/utils/error_handler.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Service for downloading and extracting speech recognition models
class ModelDownloaderService {
  final Logger _logger = Logger();

  /// Download progress stream controller
  final StreamController<double> _progressController =
      StreamController<double>.broadcast();
  Stream<double> get progressStream => _progressController.stream;

  /// Status stream controller
  final StreamController<String> _statusController =
      StreamController<String>.broadcast();
  Stream<String> get statusStream => _statusController.stream;

  /// Error handler
  final ErrorHandler _errorHandler = ErrorHandler();

  /// Connectivity instance
  final Connectivity _connectivity = Connectivity();

  /// Maximum number of retry attempts
  final int _maxRetryAttempts = AppConstants.maxRetryAttempts;

  /// Download and extract a model from a URL
  ///
  /// Returns the path to the extracted model directory
  Future<String> downloadAndExtractModel(
    String url,
    String modelName,
    String languageCode,
  ) async {
    try {
      _logger.i('Downloading model: $modelName for language: $languageCode');
      _statusController.add('Preparing to download model for $languageCode');

      // Check connectivity before starting
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult.every(
        (result) => result == ConnectivityResult.none,
      )) {
        throw Exception(
          'No internet connection available. Please connect to the internet and try again.',
        );
      }

      // Get application documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final modelDir = '${appDir.path}/speech_models/$languageCode';
      final modelFile = '$modelDir/$modelName.tar.bz2';

      // Create directory if it doesn't exist
      final dir = Directory(modelDir);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      // Check if model already exists
      if (await _isModelAlreadyExtracted(modelDir)) {
        _logger.i('Model already exists, skipping download');
        _statusController.add('Model already available');
        return modelDir;
      }

      // Download the model with retry logic
      _statusController.add('Downloading model...');
      bool downloadSuccess = false;
      Exception? lastException;

      for (int attempt = 1; attempt <= _maxRetryAttempts; attempt++) {
        try {
          if (attempt > 1) {
            _statusController.add(
              'Retrying download (attempt $attempt of $_maxRetryAttempts)...',
            );
            _logger.w(
              'Retrying download, attempt $attempt of $_maxRetryAttempts',
            );

            // Check connectivity again before retry
            final retryConnectivity = await _connectivity.checkConnectivity();
            if (retryConnectivity.every(
              (result) => result == ConnectivityResult.none,
            )) {
              throw Exception('No internet connection available for retry.');
            }

            // Wait before retrying with exponential backoff
            final delay = Duration(
              milliseconds: AppConstants.retryBaseDelayMs * attempt,
            );
            await Future.delayed(delay);
          }

          await _downloadFile(url, modelFile);
          downloadSuccess = true;
          break;
        } catch (e) {
          lastException = e is Exception ? e : Exception(e.toString());
          _logger.e('Download attempt $attempt failed: $e');

          // Clean up partial downloads
          final partialFile = File(modelFile);
          if (await partialFile.exists()) {
            try {
              await partialFile.delete();
            } catch (deleteError) {
              _logger.w('Failed to delete partial download: $deleteError');
            }
          }

          // If this is the last attempt, we'll rethrow outside the loop
          if (attempt == _maxRetryAttempts) {
            _statusController.add(
              'Download failed after $_maxRetryAttempts attempts',
            );
          }
        }
      }

      if (!downloadSuccess) {
        throw lastException ??
            Exception(
              'Failed to download model after $_maxRetryAttempts attempts',
            );
      }

      // Extract the model with timeout
      _statusController.add('Extracting model...');
      try {
        await _extractTarBz2(modelFile, modelDir);
      } catch (e) {
        _logger.e('Error extracting model: $e');
        _statusController.add('Extraction failed, cleaning up...');

        // Clean up failed extraction
        try {
          final extractedDir = Directory(modelDir);
          if (await extractedDir.exists()) {
            await extractedDir.delete(recursive: true);
            await extractedDir.create(recursive: true);
          }
        } catch (cleanupError) {
          _logger.w('Failed to clean up after extraction error: $cleanupError');
        }

        throw Exception('Failed to extract model: ${e.toString()}');
      }

      // Delete the compressed file to save space
      try {
        await File(modelFile).delete();
      } catch (e) {
        _logger.w('Failed to delete compressed file: $e');
        // Non-critical error, continue
      }

      // Verify the extraction was successful
      if (!await _isModelAlreadyExtracted(modelDir)) {
        throw Exception('Model extraction verification failed');
      }

      _logger.i('Model downloaded and extracted successfully to: $modelDir');
      _statusController.add('Model ready for use');
      return modelDir;
    } catch (e) {
      final errorMessage = _errorHandler.handleError(
        e,
        context: 'Model download',
      );
      _logger.e('Error downloading model: $errorMessage');
      _statusController.add('Error: $errorMessage');
      throw Exception('Failed to download speech model: $errorMessage');
    }
  }

  /// Check if model is already extracted
  Future<bool> _isModelAlreadyExtracted(String modelDir) async {
    final dir = Directory(modelDir);
    if (!await dir.exists()) {
      return false;
    }

    // Check if directory contains model files
    final files = await dir.list().toList();

    // Look for .onnx files which indicate model files
    return files.any((file) => file.path.endsWith('.onnx'));
  }

  /// Download a file from a URL
  Future<void> _downloadFile(String url, String savePath) async {
    try {
      _logger.i('Downloading file from: $url to: $savePath');

      // Create request
      final request = http.Request('GET', Uri.parse(url));
      final response = await http.Client().send(request);

      // Check response
      if (response.statusCode != 200) {
        throw Exception('Failed to download file: ${response.statusCode}');
      }

      // Get content length for progress tracking
      final contentLength = response.contentLength ?? 0;

      // Create file
      final file = File(savePath);
      final sink = file.openWrite();

      // Download with progress tracking
      int received = 0;
      await response.stream.forEach((bytes) {
        sink.add(bytes);
        received += bytes.length;

        // Update progress
        if (contentLength > 0) {
          final progress = received / contentLength;
          _progressController.add(progress);
        }
      });

      // Close file
      await sink.close();

      _logger.i('File downloaded successfully');
    } catch (e) {
      _logger.e('Error downloading file: $e');
      rethrow;
    }
  }

  /// Extract a tar.bz2 file
  Future<void> _extractTarBz2(String filePath, String extractPath) async {
    try {
      _logger.i('Extracting file: $filePath to: $extractPath');

      // Read file
      final file = File(filePath);
      final bytes = await file.readAsBytes();

      // Decompress BZip2
      final bzip2 = BZip2Decoder().decodeBytes(bytes);

      // Extract TAR
      final archive = TarDecoder().decodeBytes(bzip2);

      // Extract files
      for (final file in archive) {
        final filename = file.name;
        if (file.isFile) {
          final data = file.content as List<int>;
          final outFile = File('$extractPath/$filename');

          // Create parent directory if it doesn't exist
          await outFile.parent.create(recursive: true);

          // Write file
          await outFile.writeAsBytes(data);
        } else {
          // Create directory
          await Directory('$extractPath/$filename').create(recursive: true);
        }
      }

      _logger.i('File extracted successfully');
    } catch (e) {
      _logger.e('Error extracting file: $e');
      rethrow;
    }
  }

  /// Dispose resources
  void dispose() {
    _progressController.close();
    _statusController.close();
  }
}
