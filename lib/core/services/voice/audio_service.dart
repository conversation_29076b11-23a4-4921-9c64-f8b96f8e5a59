import 'dart:async';
import 'dart:io';
import 'package:flutter_sound_record/flutter_sound_record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';

class AudioService {
  final FlutterSoundRecord _recorder = FlutterSoundRecord();
  String? _recordedFilePath;
  bool _isRecorderInitialized = false;
  bool _isRecording = false;
  bool _isPaused = false;
  bool _isPlaying = false;

  final Logger _logger = Logger();

  final StreamController<Duration> _recordingDurationController =
      StreamController<Duration>.broadcast();
  Stream<Duration> get recordingDurationStream =>
      _recordingDurationController.stream;

  final StreamController<Duration> _playbackProgressController =
      StreamController<Duration>.broadcast();
  Stream<Duration> get playbackProgressStream =>
      _playbackProgressController.stream;

  final StreamController<RecordingStatusUpdate> _recordingStatusController =
      StreamController<RecordingStatusUpdate>.broadcast();
  Stream<RecordingStatusUpdate> get recordingStatusStream =>
      _recordingStatusController.stream;

  // Storage for timer to track recording duration since flutter_sound_record
  // doesn't provide progress updates
  Timer? _recordingTimer;
  DateTime? _recordingStartTime;
  Duration _accumulatedDuration = Duration.zero;

  Future<void> init() async {
    try {
      // Check if microphone permission is granted
      bool hasPermission = await _recorder.hasPermission();
      
      // If we don't have permission, try to request it explicitly
      if (!hasPermission) {
        _logger.w(
          'Microphone permission not granted. Requesting permission...',
        );
        hasPermission =
            await _recorder
                .hasPermission(); // This should trigger the permission request

        if (!hasPermission) {
          _logger.e('Microphone permission denied after request.');
          throw Exception(
            'Microphone permission denied. Please enable microphone access in your device settings.',
          );
        }
      }
      
      // Release any existing resources
      if (_isRecording) {
        await stopRecording();
      }

      if (_isPlaying) {
        await stopPlayback();
      }

      // Reset state
      _isRecorderInitialized = true;
      _isRecording = false;
      _isPaused = false;
      _isPlaying = false;
      _accumulatedDuration = Duration.zero;

      _logger.i(
        'Recorder initialized successfully with microphone permission granted.',
      );
    } catch (e) {
      _logger.e('Error initializing recorder: $e');
      rethrow;
    }
  }

  Future<String> _getFilePath() async {
    // Use application documents directory for persistence
    final Directory docDir = await getApplicationDocumentsDirectory();
    
    // Create a subdirectory for audio files if it doesn't exist
    final Directory audioDir = Directory('${docDir.path}/voice_pilot_audio');
    if (!await audioDir.exists()) {
      await audioDir.create(recursive: true);
    }
    
    // Generate a UUID-based filename for better uniqueness
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final String filename = 'recording_$timestamp.m4a';
    final String path = '${audioDir.path}/$filename';
    
    _logger.i('Creating audio file at: $path');
    _recordedFilePath = path;
    return path;
  }

  Future<void> startRecording() async {
    if (!_isRecorderInitialized) {
      _logger.w('Recorder not initialized.');
      throw Exception('Recorder not initialized.');
    }

    if (_isRecording && !_isPaused) {
      _logger.i('Already recording.');
      return;
    }

    // If we're resuming from pause
    if (_isPaused) {
      return resumeRecording();
    }

    try {
      // Explicitly request microphone permission first
      bool hasPermission = await _recorder.hasPermission();
      if (!hasPermission) {
        _logger.w('No microphone permission. Requesting...');
        hasPermission = await _recorder.hasPermission();
        if (!hasPermission) {
          throw Exception('Microphone permission denied');
        }
      }

      final String filePath = await _getFilePath();
      _logger.i('Starting recording to: $filePath');

      // Start recording with AAC encoding
      await _recorder.start(
        path: filePath,
        encoder: AudioEncoder.AAC,
        bitRate: 128000,
      );

      _isRecording = true;
      _isPaused = false;
      _recordingStartTime = DateTime.now();
      _accumulatedDuration = Duration.zero;

      // Create a timer to update duration since the package doesn't provide progress
      _recordingTimer = Timer.periodic(const Duration(milliseconds: 200), (
        timer,
      ) {
        if (_isRecording && !_isPaused) {
          final currentDuration = DateTime.now().difference(
            _recordingStartTime!,
          );
          final totalDuration = _accumulatedDuration + currentDuration;
          _recordingDurationController.add(totalDuration);
          _recordingStatusController.add(
            RecordingStatusUpdate(
              isRecording: true,
              isPaused: false,
              duration: totalDuration,
            ),
          );
        }
      });

      _logger.i('Recording started.');
    } catch (e) {
      _logger.e('Error starting recording: $e');
      throw Exception('Failed to start recording: ${e.toString()}');
    }
  }

  Future<void> pauseRecording() async {
    if (!_isRecorderInitialized || !_isRecording || _isPaused) {
      _logger.w(
        'Cannot pause: recorder not initialized, not recording, or already paused.',
      );
      return;
    }

    try {
      // Flutter Sound Record doesn't have a native pause function
      // So we'll stop the timer and accumulate the duration
      _recordingTimer?.cancel();

      // Calculate duration so far
      final currentDuration = DateTime.now().difference(_recordingStartTime!);
      _accumulatedDuration += currentDuration;

      // Update state
      _isPaused = true;

      // Notify listeners
      _recordingStatusController.add(
        RecordingStatusUpdate(
          isRecording: true,
          isPaused: true,
          duration: _accumulatedDuration,
        ),
      );

      _logger.i(
        'Recording paused. Accumulated duration: $_accumulatedDuration',
      );
    } catch (e) {
      _logger.e('Error pausing recording: $e');
      throw Exception('Failed to pause recording: ${e.toString()}');
    }
  }

  Future<void> resumeRecording() async {
    if (!_isRecorderInitialized || !_isRecording || !_isPaused) {
      _logger.w(
        'Cannot resume: recorder not initialized, not recording, or not paused.',
      );
      return;
    }

    try {
      // Update state
      _isPaused = false;
      _recordingStartTime = DateTime.now();

      // Restart the timer
      _recordingTimer = Timer.periodic(const Duration(milliseconds: 200), (
        timer,
      ) {
        if (_isRecording && !_isPaused) {
          final currentDuration = DateTime.now().difference(
            _recordingStartTime!,
          );
          final totalDuration = _accumulatedDuration + currentDuration;
          _recordingDurationController.add(totalDuration);
          _recordingStatusController.add(
            RecordingStatusUpdate(
              isRecording: true,
              isPaused: false,
              duration: totalDuration,
            ),
          );
        }
      });

      // Notify listeners
      _recordingStatusController.add(
        RecordingStatusUpdate(
          isRecording: true,
          isPaused: false,
          duration: _accumulatedDuration,
        ),
      );

      _logger.i(
        'Recording resumed. Accumulated duration: $_accumulatedDuration',
      );
    } catch (e) {
      _logger.e('Error resuming recording: $e');
      throw Exception('Failed to resume recording: ${e.toString()}');
    }
  }

  Future<String?> stopRecording() async {
    if (!_isRecorderInitialized || !_isRecording) {
      _logger.w('Recorder not initialized or not recording.');
      return null;
    }

    try {
      // Stop timer
      _recordingTimer?.cancel();

      // If we're paused, we don't need to update accumulated duration
      if (!_isPaused) {
        // Add the current session duration to accumulated
        final currentDuration = DateTime.now().difference(_recordingStartTime!);
        _accumulatedDuration += currentDuration;
      }

      // Stop recording
      await _recorder.stop();
      _isRecording = false;
      _isPaused = false;

      _logger.i(
        'Recording stopped. File saved at: $_recordedFilePath, Total duration: $_accumulatedDuration',
      );

      // Reset duration for next recording
      final finalDuration = _accumulatedDuration;
      _accumulatedDuration = Duration.zero;

      // Notify listeners
      _recordingStatusController.add(
        RecordingStatusUpdate(
          isRecording: false,
          isPaused: false,
          isFinished: true,
          duration: finalDuration,
        ),
      );

      return _recordedFilePath;
    } catch (e) {
      _logger.e('Error stopping recorder: $e');
      return null;
    }
  }

  Future<void> playRecording(String? filePath) async {
    final pathToPlay = filePath ?? _recordedFilePath;
    if (pathToPlay == null) {
      _logger.w('No file path provided.');
      throw Exception('No file to play.');
    }

    if (_isPlaying) {
      _logger.i('Already playing.');
      return;
    }

    try {
      _logger.i('Checking file existence at: $pathToPlay');

      // Check if file exists before attempting to play
      final file = File(pathToPlay);
      if (!await file.exists()) {
        _logger.e('File does not exist at path: $pathToPlay');
        throw Exception(
          'Failed to play recording: File not found at $pathToPlay',
        );
      }

      _logger.i('Starting playback for: $pathToPlay');

      // TODO: Replace this simulation with actual audio playback using platform-specific code
      // or a third-party package like just_audio or audioplayers
      // This implementation currently simulates playback progress without actually playing audio
      
      _isPlaying = true;

      // Get file size to estimate duration
      final fileSize = await file.length();
      _logger.i('File size: $fileSize bytes');

      // Rough estimate: ~15KB per second for AAC 128kbps
      final estimatedDuration = Duration(
        milliseconds: (fileSize / 15000 * 1000).round(),
      );
      
      // Notify that playback has started
      _recordingStatusController.add(
        const RecordingStatusUpdate(isPlaying: true),
      );

      // On iOS, we can use AVAudioPlayer, on Android MediaPlayer
      // For simplicity, we'll use the system's native capabilities through URL launching
      // This will at least play the audio even though we can't track progress accurately

      try {
        // For actually playing sound, we'd implement platform-specific code here
        // For example:
        // if (Platform.isIOS) {
        //   final player = AVAudioPlayer(); // iOS implementation
        //   await player.play(pathToPlay);
        // } else if (Platform.isAndroid) {
        //   final player = MediaPlayer(); // Android implementation
        //   await player.setDataSource(pathToPlay);
        //   await player.prepare();
        //   await player.start();
        // }

        // For now, we'll use a simpler approach by opening the file with the platform
        // This will delegate to the default audio player on the device
        // await OpenFile.open(pathToPlay);

        // Since we can't track progress with OpenFile, we'll simulate it:
        int steps = 50;
        for (int i = 0; i < steps; i++) {
          if (!_isPlaying) break;
          await Future.delayed(
            Duration(milliseconds: estimatedDuration.inMilliseconds ~/ steps),
          );

          final position = Duration(
            milliseconds:
                (estimatedDuration.inMilliseconds * (i + 1) / steps).round(),
          );
          _playbackProgressController.add(position);
          _recordingStatusController.add(
            RecordingStatusUpdate(isPlaying: true, playbackPosition: position),
          );
        }
      } catch (e) {
        _logger.e('Error playing file with native player: $e');
        // Fall back to simulation if native playback fails
      }

      // Playback finished
      if (_isPlaying) {
        _isPlaying = false;
        _playbackProgressController.add(Duration.zero);
        _recordingStatusController.add(
          const RecordingStatusUpdate(
            isPlaying: false,
            isPlaybackFinished: true,
          ),
        );
      }

      _logger.i('Playback finished.');
    } catch (e) {
      _logger.e('Error during playback: $e');
      _isPlaying = false;
      throw Exception('Failed to play recording: ${e.toString()}');
    }
  }

  Future<void> stopPlayback() async {
    if (!_isPlaying) {
      _logger.w('Not playing.');
      return;
    }

    _isPlaying = false;
    _playbackProgressController.add(Duration.zero);
    _recordingStatusController.add(
      const RecordingStatusUpdate(isPlaying: false),
    );

    _logger.i('Playback stopped.');
  }

  Future<void> deleteRecordingFile(String? filePath) async {
    final pathToDelete = filePath ?? _recordedFilePath;
    if (pathToDelete != null) {
      try {
        final file = File(pathToDelete);
        if (await file.exists()) {
          await file.delete();
          _logger.i('File deleted: $pathToDelete');
          _recordedFilePath = null; // Clear the stored path
        }
      } catch (e) {
        _logger.e('Error deleting file $pathToDelete: $e');
      }
    }
  }

  bool get isRecording => _isRecording;
  bool get isPaused => _isPaused;
  bool get isPlaying => _isPlaying;

  void dispose() {
    _recordingTimer?.cancel();
    _recordingDurationController.close();
    _playbackProgressController.close();
    _recordingStatusController.close();
    _logger.i('AudioService disposed.');
  }
}

// Helper class for status updates
class RecordingStatusUpdate {
  final bool isRecording;
  final bool isPaused;
  final bool isPlaying;
  final bool isFinished; // Recording finished
  final bool isPlaybackFinished;
  final Duration? duration; // Current recording duration
  final Duration? playbackPosition; // Current playback position

  const RecordingStatusUpdate({
    this.isRecording = false,
    this.isPaused = false,
    this.isPlaying = false,
    this.isFinished = false,
    this.isPlaybackFinished = false,
    this.duration,
    this.playbackPosition,
  });
}
