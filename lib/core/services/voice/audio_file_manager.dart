import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

/// Service for managing audio files consistently across the app.
///
/// This service provides methods for working with audio files,
/// ensuring they are stored in a consistent location with standardized
/// naming conventions.
class AudioFileManager {
  final Logger _logger = Logger();
  final _uuid = const Uuid();

  /// Base directory for all audio files
  static const String _audioBaseDirName = 'voice_pilot_audio';

  /// Get the base directory for audio storage
  Future<Directory> getAudioBaseDirectory() async {
    final docDir = await getApplicationDocumentsDirectory();
    final audioDir = Directory('${docDir.path}/$_audioBaseDirName');

    // Create directory if it doesn't exist
    if (!await audioDir.exists()) {
      await audioDir.create(recursive: true);
    }

    return audioDir;
  }

  /// Generate a file path for a new recording
  /// Returns the absolute path to the new file
  Future<String> generateRecordingFilePath() async {
    final audioDir = await getAudioBaseDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final uniqueId = _uuid.v4().substring(
      0,
      8,
    ); // Use part of UUID for shorter filename
    final filename = 'recording_${timestamp}_$uniqueId.m4a';
    final path = '${audioDir.path}/$filename';

    _logger.i('Generated audio file path: $path');
    return path;
  }

  /// Copy an audio file from a temporary location to permanent storage
  /// Returns the new absolute path
  Future<String?> moveToStorage(String sourcePath) async {
    try {
      // Check if source file exists
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        _logger.e('Source file not found: $sourcePath');
        return null;
      }

      // Generate destination path
      final destPath = await generateRecordingFilePath();
      final destFile = File(destPath);

      // Copy file to new location
      await sourceFile.copy(destPath);

      // Verify file was copied successfully
      if (await destFile.exists()) {
        _logger.i('Audio file copied successfully to: $destPath');

        // Delete original file if it's in a temporary location
        if (sourcePath.contains('cache') || sourcePath.contains('temp')) {
          try {
            await sourceFile.delete();
            _logger.i('Deleted temporary file: $sourcePath');
          } catch (e) {
            _logger.w('Could not delete temporary file: $e');
          }
        }

        return destPath;
      } else {
        _logger.e('Failed to copy audio file to: $destPath');
        return null;
      }
    } catch (e) {
      _logger.e('Error moving audio file to storage: $e');
      return null;
    }
  }

  /// Check if an audio file exists
  Future<bool> fileExists(String? filePath) async {
    if (filePath == null) return false;

    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      _logger.e('Error checking file existence: $e');
      return false;
    }
  }

  /// Delete an audio file
  Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        _logger.i('Deleted audio file: $filePath');
        return true;
      }
      return false;
    } catch (e) {
      _logger.e('Error deleting audio file: $e');
      return false;
    }
  }

  /// Get the file size in bytes
  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      _logger.e('Error getting file size: $e');
      return 0;
    }
  }

  /// Get all saved recordings
  Future<List<String>> getAllRecordings() async {
    try {
      final audioDir = await getAudioBaseDirectory();
      final dirContents = audioDir.listSync();

      final List<String> audioFiles = [];
      for (var entity in dirContents) {
        if (entity is File && entity.path.endsWith('.m4a')) {
          audioFiles.add(entity.path);
        }
      }

      // Sort by creation date (newest first)
      audioFiles.sort((a, b) {
        final fileA = File(a);
        final fileB = File(b);
        return fileB.lastModifiedSync().compareTo(fileA.lastModifiedSync());
      });

      return audioFiles;
    } catch (e) {
      _logger.e('Error getting saved recordings: $e');
      return [];
    }
  }

  /// Delete all audio files
  Future<bool> deleteAllAudioFiles() async {
    try {
      final audioDir = await getAudioBaseDirectory();
      final dirContents = audioDir.listSync();
      int successCount = 0;
      int totalCount = 0;

      for (var entity in dirContents) {
        if (entity is File && entity.path.endsWith('.m4a')) {
          totalCount++;
          try {
            await entity.delete();
            successCount++;
          } catch (e) {
            _logger.e('Error deleting file ${entity.path}: $e');
          }
        }
      }

      _logger.i('Deleted $successCount/$totalCount audio files');
      return successCount > 0;
    } catch (e) {
      _logger.e('Error deleting all audio files: $e');
      return false;
    }
  }

  /// Get the total size of all audio files in bytes
  Future<int> getTotalAudioFilesSize() async {
    try {
      final audioDir = await getAudioBaseDirectory();
      final dirContents = audioDir.listSync();
      int totalSize = 0;

      for (var entity in dirContents) {
        if (entity is File && entity.path.endsWith('.m4a')) {
          totalSize += await entity.length();
        }
      }

      return totalSize;
    } catch (e) {
      _logger.e('Error calculating total audio files size: $e');
      return 0;
    }
  }
}
