import 'dart:io';
import 'package:logger/logger.dart';
import 'package:projectpilot/core/constants/app_constants.dart';
import 'package:projectpilot/core/services/voice/sherpa_service.dart';

/// Enum representing different speech recognition engines
enum SpeechRecognitionEngine {
  /// Sherpa-ONNX - works on both iOS and Android
  sherpaOnnx,
}

/// Factory for creating speech recognition services
class SpeechRecognitionServiceFactory {
  static final Logger _logger = Logger();
  
  /// Create a speech recognition service based on platform and configuration
  static dynamic createService() {
    // Determine which engine to use based on platform and configuration
    final SpeechRecognitionEngine engine = _determineEngine();
    
    _logger.i('Creating speech recognition service with engine: $engine');
    
    // Create and return the appropriate service
    switch (engine) {
      case SpeechRecognitionEngine.sherpaOnnx:
        return SherpaService();
    }
  }
  
  /// Determine which speech recognition engine to use
  static SpeechRecognitionEngine _determineEngine() {
    // Check if offline transcription is enabled
    final bool enableOfflineTranscription = AppConstants.enableOfflineTranscription;
    
    // If running on iOS, always use Sherpa-ONNX as Vosk doesn't support iOS
    if (Platform.isIOS) {
      _logger.i('Running on iOS, using Sherpa-ONNX for speech recognition');
      return SpeechRecognitionEngine.sherpaOnnx;
    }
    
    // If offline transcription is enabled, use the appropriate engine
    if (enableOfflineTranscription) {
      // For now, prefer Sherpa-ONNX as it's more modern and works on both platforms
      return SpeechRecognitionEngine.sherpaOnnx;
    } else {
      // If offline transcription is disabled, still use Sherpa-ONNX as fallback
      // In a real implementation, you might want to use an online service here
      return SpeechRecognitionEngine.sherpaOnnx;
    }
  }
}
