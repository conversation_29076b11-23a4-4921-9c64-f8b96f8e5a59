import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

/// Service for managing notifications in ProjectPilot
class NotificationService {
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  /// Initialize the notification service
  Future<void> init() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(initSettings);
  }

  /// Show a project notification
  Future<void> showProjectNotification({
    required String title,
    required String body,
    String? projectId,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'project_notifications',
      'Project Notifications',
      channelDescription: 'Notifications for project updates and reminders',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      notificationDetails,
      payload: projectId,
    );
  }

  /// Show project analysis complete notification
  Future<void> showAnalysisCompleteNotification({
    required String projectName,
    required int insightsCount,
  }) async {
    await showProjectNotification(
      title: 'Analysis Complete',
      body: 'Found $insightsCount insights for $projectName',
    );
  }

  /// Show project update notification
  Future<void> showProjectUpdateNotification({
    required String projectName,
    required String updateMessage,
  }) async {
    await showProjectNotification(
      title: 'Project Updated',
      body: '$projectName: $updateMessage',
    );
  }

  /// Cancel all notifications
  Future<void> cancelAll() async {
    await _localNotifications.cancelAll();
  }

  /// Cancel notification by ID
  Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }
}
