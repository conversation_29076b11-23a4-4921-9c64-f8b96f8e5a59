import 'package:projectpilot/domain/entities/transcription_result.dart';
import 'package:logger/logger.dart';

/// A simple text optimizer service supporting multiple languages.
/// Optimizes and categorizes transcription texts without external API calls.
class SimpleOptimizerService {
  final Logger _logger = Logger();

  /// Patterns to categorize texts by language
  final Map<String, Map<ContentCategory, List<RegExp>>> _localizedCategoryPatterns = {
    'de': {
      ContentCategory.task: [
        RegExp(r'(muss|müssen|soll|sollte|erledige|erledigen|erstelle|kaufe|besorge|machen|todo)', caseSensitive: false),
        RegExp(r'(termin|erinnerung|nicht vergessen|bis morgen|bis heute|wann|anrufen)', caseSensitive: false),
        RegExp(r'(vereinbare|treffen|fällig|bitte|anfangen|starten|planen|organisieren)', caseSensitive: false),
      ],
      ContentCategory.idea: [
        RegExp(r'(idee|konzept|vielleicht|möglicherweise|könnte|wäre es|wie wäre|innovativ|neu)', caseSensitive: false),
        RegExp(r'(erfindung|entwicklung|besser machen|verbessern|kreativ|denke über|überlege|plan)', caseSensitive: false),
      ],
    },
    'en': {
      ContentCategory.task: [
        RegExp(r'(must|should|need|buy|get|complete|finish|do|task|todo)', caseSensitive: false),
        RegExp(r'(appointment|reminder|dont forget|by tomorrow|by today|when|call)', caseSensitive: false),
        RegExp(r'(schedule|meeting|due|please|start|begin|plan|organize)', caseSensitive: false),
      ],
      ContentCategory.idea: [
        RegExp(r'(idea|concept|maybe|perhaps|could|innovative|new|improve|optimize)', caseSensitive: false),
        RegExp(r'(invention|development|make better|enhance|creative|think about|consider|plan for)', caseSensitive: false),
      ],
    },
    'tr': {
      ContentCategory.task: [
        RegExp(r'(yap|al|tamamla|bitir|görev|gerekli|satın al)', caseSensitive: false),
        RegExp(r'(randevu|hatırlatma|unutma|yarına kadar|bugüne kadar|ne zaman|ara)', caseSensitive: false),
      ],
      ContentCategory.idea: [
        RegExp(r'(fikir|belki|konsept|yenilik|geliştir|optimize)', caseSensitive: false),
        RegExp(r'(buluş|gelişim|daha iyi|yaratıcı|düşün|incele)', caseSensitive: false),
      ],
    },
    'ru': {
      ContentCategory.task: [
        RegExp(r'(должен|сделать|купить|задача|завершить|надо)', caseSensitive: false),
        RegExp(r'(встреча|напоминание|не забыть|к завтрашнему|сегодня|когда|позвонить)', caseSensitive: false),
      ],
      ContentCategory.idea: [
        RegExp(r'(идея|может быть|концепт|новый|улучшить|оптимизировать)', caseSensitive: false),
        RegExp(r'(изобретение|разработка|улучшение|творческий|подумать|рассмотреть)', caseSensitive: false),
      ],
    },
    'ar': {
      ContentCategory.task: [
        RegExp(r'(يجب|أكمل|اشتري|مهمة|افعل|قم)', caseSensitive: false),
        RegExp(r'(موعد|تذكير|لا تنسى|غدا|اليوم|متى|اتصل)', caseSensitive: false),
      ],
      ContentCategory.idea: [
        RegExp(r'(فكرة|ربما|مفهوم|جديد|تحسين|ابتكار)', caseSensitive: false),
        RegExp(r'(اختراع|تطوير|تحسين|إبداعي|فكر في|اعتبار)', caseSensitive: false),
      ],
    },
    'es': {
      ContentCategory.task: [
        RegExp(r'(debo|debe|debemos|comprar|hacer|completar|terminar|tarea)', caseSensitive: false),
        RegExp(r'(cita|recordatorio|no olvidar|para mañana|hoy|cuando|llamar)', caseSensitive: false),
      ],
      ContentCategory.idea: [
        RegExp(r'(idea|concepto|quizás|tal vez|podría|innovador|nuevo|mejorar)', caseSensitive: false),
        RegExp(r'(invención|desarrollo|optimizar|creativo|pensar en|considerar)', caseSensitive: false),
      ],
    },
    'fr': {
      ContentCategory.task: [
        RegExp(r'(dois|doit|devons|acheter|faire|compléter|terminer|tâche)', caseSensitive: false),
        RegExp(r'(rendez-vous|rappel|ne pas oublier|pour demain|aujourd hui|quand|appeler)', caseSensitive: false),
      ],
      ContentCategory.idea: [
        RegExp(r'(idée|concept|peut-être|pourrait|innovant|nouveau|améliorer)', caseSensitive: false),
        RegExp(r'(invention|développement|optimiser|créatif|penser à|considérer)', caseSensitive: false),
      ],
    },
    'it': {
      ContentCategory.task: [
        RegExp(r'(devo|deve|dobbiamo|comprare|fare|completare|finire|compito)', caseSensitive: false),
        RegExp(r'(appuntamento|promemoria|non dimenticare|per domani|oggi|quando|chiamare)', caseSensitive: false),
      ],
      ContentCategory.idea: [
        RegExp(r'(idea|concetto|forse|potrebbe|innovativo|nuovo|migliorare)', caseSensitive: false),
        RegExp(r'(invenzione|sviluppo|ottimizzare|creativo|pensare a|considerare)', caseSensitive: false),
      ],
    },
  };

  /// Language-specific replacements to correct common errors
  final Map<String, List<List<String>>> _localizedReplacements = {
    'de': [
      ['ne', 'eine'], ['nich', 'nicht'], ['is', 'ist'], ['ham', 'haben'],
      ['habs', 'habe es'], [' nen ', ' einen '], ['son', 'so ein'],
      ['sowas', 'so etwas'], ['aufjedenfall', 'auf jeden Fall'], ['aufm', 'auf dem'],
    ],
    'en': [
      ['dont', 'don\'t'], ['cant', 'can\'t'], ['wont', 'won\'t'], ['im', 'I\'m'],
      ['youre', 'you\'re'], ['theyre', 'they\'re'], ['couldnt', 'couldn\'t'],
      ['wouldnt', 'wouldn\'t'], ['shouldnt', 'shouldn\'t'], ['isnt', 'isn\'t'],
    ],
    'tr': [
      ['diil', 'değil'], ['naber', 'ne haber'], ['iyiyim', 'iyi yim'],
      ['nasilsin', 'nasılsın'], ['geliyom', 'geliyorum'], ['yapiyom', 'yapıyorum'],
    ],
    'ru': [
      ['шо', 'что'], ['чё', 'что'], ['нету', 'нет'], ['щас', 'сейчас'],
      ['тя', 'тебя'], ['че', 'что'], ['ваще', 'вообще'], ['тока', 'только'],
    ],
    'ar': [
      ['ازاي', 'كيف'], ['مش', 'ليس'], ['ليه', 'لماذا'], ['عشان', 'لأن'],
      ['بردو', 'أيضا'], ['متجيش', 'لا تأتي'], ['مفيش', 'لا يوجد'],
    ],
    'es': [
      ['pa', 'para'], ['q', 'que'], ['pq', 'porque'], ['k', 'que'],
      ['xq', 'porque'], ['tb', 'también'], ['na', 'nada'], ['pa\'', 'para'],
    ],
    'fr': [
      ['chui', 'je suis'], ['psk', 'parce que'], ['c', 'c\'est'], ['t', 'tu'],
      ['jsuis', 'je suis'], ['qq', 'quelque'], ['dsl', 'désolé'], ['mnt', 'maintenant'],
    ],
    'it': [
      ['cmq', 'comunque'], ['xké', 'perché'], ['nn', 'non'], ['ke', 'che'],
      ['qst', 'questo'], ['dp', 'dopo'], ['sn', 'sono'], ['qlc', 'qualcosa'],
    ],
  };

  /// Optimize and categorize transcription based on language
  TranscriptionResult optimizeAndClassify(String transcription, String languageCode) {
    _logger.i('Optimizing transcription: "$transcription" in language: $languageCode');

    // Default to English if language not supported
    if (!_localizedCategoryPatterns.containsKey(languageCode)) {
      _logger.w('Language $languageCode not supported, falling back to English');
      languageCode = 'en';
    }

    final optimizedContent = _optimizeText(transcription, languageCode);
    final category = _categorizeText(optimizedContent, languageCode);
    const confidence = 0.7;

    _logger.i('Optimization done. Category: $category, Confidence: $confidence');

    return TranscriptionResult(
      originalTranscription: transcription,
      optimizedContent: optimizedContent,
      category: category,
      confidence: confidence,
    );
  }

  /// Language-specific text optimization
  String _optimizeText(String text, String languageCode) {
    String optimized = text.trim().replaceAll(RegExp(r'\s+'), ' ');

    final replacements = _localizedReplacements[languageCode] ?? [];
    for (final replacement in replacements) {
      optimized = optimized.replaceAll(
        RegExp('\\b${replacement[0]}\\b', caseSensitive: false),
        replacement[1],
      );
    }

    if (optimized.isNotEmpty) {
      optimized = optimized[0].toUpperCase() + optimized.substring(1);
      // Fix: Use String.contains instead of String.endsWith with RegExp
      if (!_endsWithPunctuation(optimized)) {
        optimized += '.';
      }
    }

    return optimized;
  }

  /// Check if text ends with punctuation
  bool _endsWithPunctuation(String text) {
    return text.endsWith('.') || text.endsWith('!') || text.endsWith('?');
  }

  /// Categorize text based on patterns
  ContentCategory _categorizeText(String text, String languageCode) {
    final categoryPatterns = _localizedCategoryPatterns[languageCode];

    if (categoryPatterns != null) {
      for (final entry in categoryPatterns.entries) {
        for (final pattern in entry.value) {
          if (pattern.hasMatch(text)) return entry.key;
        }
      }
    }

    return ContentCategory.note;
  }
  
  /// Detect probable language from text
  /// Returns ISO 639-1 language code
  String detectLanguage(String text) {
    if (text.isEmpty) return 'en'; // Default to English for empty text
    
    final Map<String, int> langScores = {
      'de': 0, 'en': 0, 'tr': 0, 'ru': 0, 'ar': 0, 'es': 0, 'fr': 0, 'it': 0,
    };
    
    // Simple language detection based on common words
    final Map<String, List<String>> langMarkers = {
      'de': ['und', 'ist', 'der', 'die', 'das', 'nicht', 'ein', 'für', 'mit', 'auf'],
      'en': ['the', 'and', 'is', 'in', 'to', 'a', 'for', 'with', 'of', 'that'],
      'tr': ['ve', 'bir', 'bu', 'için', 'ile', 'çok', 'olarak', 'daha', 'var', 'ben'],
      'ru': ['и', 'в', 'не', 'на', 'что', 'это', 'с', 'я', 'как', 'по'],
      'ar': ['و', 'في', 'من', 'على', 'إلى', 'هذا', 'أن', 'مع', 'لا', 'هو'],
      'es': ['y', 'el', 'la', 'en', 'de', 'que', 'por', 'un', 'con', 'para'],
      'fr': ['le', 'la', 'et', 'en', 'de', 'un', 'une', 'pour', 'avec', 'que'],
      'it': ['il', 'la', 'e', 'in', 'di', 'un', 'una', 'per', 'con', 'che'],
    };
    
    final String lowerText = text.toLowerCase();
    final List<String> words = lowerText.split(RegExp(r'\s+'));
    
    // Check for language markers
    for (final entry in langMarkers.entries) {
      final String lang = entry.key;
      final List<String> markers = entry.value;
      
      for (final word in words) {
        if (markers.contains(word)) {
          langScores[lang] = langScores[lang]! + 1;
        }
      }
    }
    
    // Find language with highest score
    String detectedLang = 'en';
    int maxScore = 0;
    
    for (final entry in langScores.entries) {
      if (entry.value > maxScore) {
        maxScore = entry.value;
        detectedLang = entry.key;
      }
    }
    
    return detectedLang;
  }
}
