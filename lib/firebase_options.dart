// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBHqJfiezw1BpmT1ea0vJrIhTPOw_1Cb9Q',
    appId: '1:92846616463:web:3225e424804fea0db8e5fa',
    messagingSenderId: '92846616463',
    projectId: 'projectpilot-19c73',
    authDomain: 'projectpilot-19c73.firebaseapp.com',
    storageBucket: 'projectpilot-19c73.firebasestorage.app',
    measurementId: 'G-STYQTXEE1W',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD4pS0PZuXp6BtLdXy61q_ODBuTWpsy0Fs',
    appId: '1:92846616463:android:6b563dfdef285161b8e5fa',
    messagingSenderId: '92846616463',
    projectId: 'projectpilot-19c73',
    storageBucket: 'projectpilot-19c73.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCwm8NCh8fcU5cI4YdljJFhWcjl_BKdV2M',
    appId: '1:92846616463:ios:2ac7a0eea12d2f79b8e5fa',
    messagingSenderId: '92846616463',
    projectId: 'projectpilot-19c73',
    storageBucket: 'projectpilot-19c73.firebasestorage.app',
    iosClientId: '92846616463-c7mg7vmrgtivdnub0v60t873n99steml.apps.googleusercontent.com',
    iosBundleId: 'com.innovatio.projectpilot',
  );

}