import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:projectpilot/data/local/models/api_key_model.dart';
import 'package:projectpilot/domain/entities/api_key.dart';

/// Secure storage for API keys using flutter_secure_storage
class ApiKeySecureStorage {
  static const String _keyPrefix = 'api_key_';
  final FlutterSecureStorage _secureStorage;

  /// Constructor for ApiKeySecureStorage
  ApiKeySecureStorage({FlutterSecureStorage? secureStorage})
    : _secureStorage = secureStorage ?? const FlutterSecureStorage();

  /// Save an API key securely
  Future<void> saveApiKey(ApiKey apiKey) async {
    final String key = _keyPrefix + apiKey.keyType;
    final model = ApiKeyModel.fromEntity(apiKey);
    await _secureStorage.write(key: key, value: json.encode(model.toJson()));
  }

  /// Get an API key by type
  Future<ApiKey?> getApiKey(String keyType) async {
    final String key = _keyPrefix + keyType;
    final String? apiKeyString = await _secureStorage.read(key: key);

    if (apiKeyString == null) {
      return null;
    }

    try {
      final Map<String, dynamic> apiKeyMap = json.decode(apiKeyString);
      final model = ApiKeyModel.fromJson(apiKeyMap);
      return model.toEntity();
    } catch (e) {
      return null;
    }
  }

  /// Delete an API key by type
  Future<void> deleteApiKey(String keyType) async {
    final String key = _keyPrefix + keyType;
    await _secureStorage.delete(key: key);
  }

  /// Check if an API key exists
  Future<bool> hasApiKey(String keyType) async {
    final String key = _keyPrefix + keyType;
    final String? value = await _secureStorage.read(key: key);
    return value != null;
  }
}
