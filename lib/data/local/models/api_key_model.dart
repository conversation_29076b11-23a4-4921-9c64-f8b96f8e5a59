import 'package:projectpilot/domain/entities/api_key.dart';

/// Model class for API keys in local storage
class ApiKeyModel {
  final String keyType;
  final String keyValue;
  final String? description;
  final String lastUpdated;

  /// Constructor for ApiKeyModel
  ApiKeyModel({
    required this.keyType,
    required this.keyValue,
    this.description,
    required this.lastUpdated,
  });

  /// Create a model from an entity
  factory ApiKeyModel.fromEntity(ApiKey apiKey) {
    return ApiKeyModel(
      keyType: apiKey.keyType,
      keyValue: apiKey.keyValue,
      description: apiKey.description,
      lastUpdated: apiKey.lastUpdated.toIso8601String(),
    );
  }

  /// Convert model to entity
  ApiKey toEntity() {
    return ApiKey(
      keyType: keyType,
      keyValue: keyValue,
      description: description,
      lastUpdated: DateTime.parse(lastUpdated),
    );
  }

  /// Create model from JSON
  factory ApiKeyModel.fromJson(Map<String, dynamic> json) {
    return ApiKeyModel(
      keyType: json['keyType'],
      keyValue: json['keyValue'],
      description: json['description'],
      lastUpdated: json['lastUpdated'],
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'keyType': keyType,
      'keyValue': keyValue,
      'description': description,
      'lastUpdated': lastUpdated,
    };
  }
}
