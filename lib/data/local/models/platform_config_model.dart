import 'package:equatable/equatable.dart';

/// Model for storing platform configuration and API keys
class PlatformConfigModel extends Equatable {
  final String id;
  final String platformId; // 'clickup', 'notion', 'jira', etc.
  final String platformName;
  final String apiKey;
  final String? userId;
  final bool isEnabled;
  final Map<String, dynamic> settings;
  final List<String> connectedProjects;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PlatformConfigModel({
    required this.id,
    required this.platformId,
    required this.platformName,
    required this.apiKey,
    this.userId,
    this.isEnabled = true,
    this.settings = const {},
    this.connectedProjects = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  PlatformConfigModel copyWith({
    String? id,
    String? platformId,
    String? platformName,
    String? apiKey,
    String? userId,
    bool? isEnabled,
    Map<String, dynamic>? settings,
    List<String>? connectedProjects,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PlatformConfigModel(
      id: id ?? this.id,
      platformId: platformId ?? this.platformId,
      platformName: platformName ?? this.platformName,
      apiKey: apiKey ?? this.apiKey,
      userId: userId ?? this.userId,
      isEnabled: isEnabled ?? this.isEnabled,
      settings: settings ?? this.settings,
      connectedProjects: connectedProjects ?? this.connectedProjects,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'platformId': platformId,
      'platformName': platformName,
      'apiKey': apiKey,
      'userId': userId,
      'isEnabled': isEnabled,
      'settings': settings,
      'connectedProjects': connectedProjects,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory PlatformConfigModel.fromJson(Map<String, dynamic> json) {
    return PlatformConfigModel(
      id: json['id'] as String,
      platformId: json['platformId'] as String,
      platformName: json['platformName'] as String,
      apiKey: json['apiKey'] as String,
      userId: json['userId'] as String?,
      isEnabled: json['isEnabled'] as bool? ?? true,
      settings: Map<String, dynamic>.from(json['settings'] as Map? ?? {}),
      connectedProjects: List<String>.from(
        json['connectedProjects'] as List? ?? [],
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  List<Object?> get props => [
    id,
    platformId,
    platformName,
    apiKey,
    userId,
    isEnabled,
    settings,
    connectedProjects,
    createdAt,
    updatedAt,
  ];

  /// Check if this config is for a specific project
  bool hasProject(String projectId) {
    return connectedProjects.contains(projectId);
  }

  /// Add a project to this configuration
  PlatformConfigModel addProject(String projectId) {
    if (connectedProjects.contains(projectId)) {
      return this;
    }

    return copyWith(
      connectedProjects: [...connectedProjects, projectId],
      updatedAt: DateTime.now(),
    );
  }

  /// Remove a project from this configuration
  PlatformConfigModel removeProject(String projectId) {
    if (!connectedProjects.contains(projectId)) {
      return this;
    }

    final updatedProjects =
        connectedProjects.where((id) => id != projectId).toList();
    return copyWith(
      connectedProjects: updatedProjects,
      updatedAt: DateTime.now(),
    );
  }

  /// Check if the configuration is valid
  bool get isValid {
    return platformId.isNotEmpty &&
        platformName.isNotEmpty &&
        apiKey.isNotEmpty;
  }

  /// Get display name for the platform
  String get displayName {
    return '$platformName${userId != null ? ' ($userId)' : ''}';
  }
}
