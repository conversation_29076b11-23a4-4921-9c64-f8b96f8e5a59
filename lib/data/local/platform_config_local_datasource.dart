import 'package:logger/logger.dart';
import 'models/platform_config_model.dart';

/// Local data source for platform configurations
/// This would typically use Hive, SQLite, or another local storage solution
class PlatformConfigLocalDataSource {
  final Logger _logger = Logger();

  // In a real implementation, this would be backed by persistent storage
  final Map<String, PlatformConfigModel> _configs = {};

  /// Get all platform configurations
  Future<List<PlatformConfigModel>> getAllConfigs() async {
    try {
      _logger.d('Getting all platform configurations');

      // In a real implementation, this would load from persistent storage
      return _configs.values.toList();
    } catch (e) {
      _logger.e('Error getting all platform configs: $e');
      rethrow;
    }
  }

  /// Get configuration for a specific platform
  Future<PlatformConfigModel?> getConfigByPlatform(String platformId) async {
    try {
      _logger.d('Getting config for platform: $platformId');

      // Find the first config for the specified platform
      return _configs.values
          .where((config) => config.platformId == platformId)
          .firstOrNull;
    } catch (e) {
      _logger.e('Error getting config for platform $platformId: $e');
      rethrow;
    }
  }

  /// Get configuration that contains a specific project
  Future<PlatformConfigModel?> getConfigByProjectId(String projectId) async {
    try {
      _logger.d('Getting config for project: $projectId');

      // Find the config that contains this project
      return _configs.values
          .where((config) => config.hasProject(projectId))
          .firstOrNull;
    } catch (e) {
      _logger.e('Error getting config for project $projectId: $e');
      rethrow;
    }
  }

  /// Get configuration by ID
  Future<PlatformConfigModel?> getConfigById(String id) async {
    try {
      _logger.d('Getting config by ID: $id');

      return _configs[id];
    } catch (e) {
      _logger.e('Error getting config by ID $id: $e');
      rethrow;
    }
  }

  /// Save or update a platform configuration
  Future<void> saveConfig(PlatformConfigModel config) async {
    try {
      _logger.d('Saving platform config: ${config.platformId}');

      // In a real implementation, this would save to persistent storage
      _configs[config.id] = config;

      _logger.d('Successfully saved platform config: ${config.id}');
    } catch (e) {
      _logger.e('Error saving platform config: $e');
      rethrow;
    }
  }

  /// Delete a platform configuration
  Future<void> deleteConfig(String id) async {
    try {
      _logger.d('Deleting platform config: $id');

      // In a real implementation, this would delete from persistent storage
      _configs.remove(id);

      _logger.d('Successfully deleted platform config: $id');
    } catch (e) {
      _logger.e('Error deleting platform config $id: $e');
      rethrow;
    }
  }

  /// Update API key for a platform
  Future<void> updateApiKey(String id, String newApiKey) async {
    try {
      _logger.d('Updating API key for config: $id');

      final config = _configs[id];
      if (config == null) {
        throw Exception('Platform config not found: $id');
      }

      final updatedConfig = config.copyWith(
        apiKey: newApiKey,
        updatedAt: DateTime.now(),
      );

      await saveConfig(updatedConfig);

      _logger.d('Successfully updated API key for config: $id');
    } catch (e) {
      _logger.e('Error updating API key for config $id: $e');
      rethrow;
    }
  }

  /// Enable or disable a platform configuration
  Future<void> toggleConfigEnabled(String id, bool isEnabled) async {
    try {
      _logger.d('Toggling config enabled status: $id -> $isEnabled');

      final config = _configs[id];
      if (config == null) {
        throw Exception('Platform config not found: $id');
      }

      final updatedConfig = config.copyWith(
        isEnabled: isEnabled,
        updatedAt: DateTime.now(),
      );

      await saveConfig(updatedConfig);

      _logger.d('Successfully toggled config enabled status: $id');
    } catch (e) {
      _logger.e('Error toggling config enabled status $id: $e');
      rethrow;
    }
  }

  /// Add a project to a platform configuration
  Future<void> addProjectToConfig(String configId, String projectId) async {
    try {
      _logger.d('Adding project $projectId to config $configId');

      final config = _configs[configId];
      if (config == null) {
        throw Exception('Platform config not found: $configId');
      }

      final updatedConfig = config.addProject(projectId);
      await saveConfig(updatedConfig);

      _logger.d('Successfully added project to config: $configId');
    } catch (e) {
      _logger.e('Error adding project to config $configId: $e');
      rethrow;
    }
  }

  /// Remove a project from a platform configuration
  Future<void> removeProjectFromConfig(
    String configId,
    String projectId,
  ) async {
    try {
      _logger.d('Removing project $projectId from config $configId');

      final config = _configs[configId];
      if (config == null) {
        throw Exception('Platform config not found: $configId');
      }

      final updatedConfig = config.removeProject(projectId);
      await saveConfig(updatedConfig);

      _logger.d('Successfully removed project from config: $configId');
    } catch (e) {
      _logger.e('Error removing project from config $configId: $e');
      rethrow;
    }
  }

  /// Get enabled configurations only
  Future<List<PlatformConfigModel>> getEnabledConfigs() async {
    try {
      _logger.d('Getting enabled platform configurations');

      final allConfigs = await getAllConfigs();
      return allConfigs.where((config) => config.isEnabled).toList();
    } catch (e) {
      _logger.e('Error getting enabled platform configs: $e');
      rethrow;
    }
  }

  /// Check if a platform is already configured
  Future<bool> isPlatformConfigured(String platformId) async {
    try {
      final config = await getConfigByPlatform(platformId);
      return config != null;
    } catch (e) {
      _logger.e('Error checking if platform is configured $platformId: $e');
      return false;
    }
  }

  /// Clear all configurations (for testing or reset)
  Future<void> clearAllConfigs() async {
    try {
      _logger.d('Clearing all platform configurations');

      // In a real implementation, this would clear persistent storage
      _configs.clear();

      _logger.d('Successfully cleared all platform configurations');
    } catch (e) {
      _logger.e('Error clearing all platform configs: $e');
      rethrow;
    }
  }
}
