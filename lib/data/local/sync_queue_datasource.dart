import 'package:uuid/uuid.dart';
import 'package:logger/logger.dart';

/// Represents the type of sync operation
enum SyncOperationType { create, update, delete }

/// Simple sync operation data structure
class SyncOperation {
  final String id;
  final String entityType;
  final String entityId;
  final SyncOperationType operationType;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final int retryCount;
  final String? lastError;

  const SyncOperation({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.operationType,
    required this.data,
    required this.timestamp,
    this.retryCount = 0,
    this.lastError,
  });

  SyncOperation copyWith({
    String? id,
    String? entityType,
    String? entityId,
    SyncOperationType? operationType,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    int? retryCount,
    String? lastError,
  }) {
    return SyncOperation(
      id: id ?? this.id,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      operationType: operationType ?? this.operationType,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      retryCount: retryCount ?? this.retryCount,
      lastError: lastError ?? this.lastError,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'entityType': entityType,
      'entityId': entityId,
      'operationType': operationType.name,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'retryCount': retryCount,
      'lastError': lastError,
    };
  }

  /// Create from JSON
  factory SyncOperation.fromJson(Map<String, dynamic> json) {
    return SyncOperation(
      id: json['id'] as String,
      entityType: json['entityType'] as String,
      entityId: json['entityId'] as String,
      operationType: SyncOperationType.values.firstWhere(
        (e) => e.name == json['operationType'],
      ),
      data: Map<String, dynamic>.from(json['data'] as Map),
      timestamp: DateTime.parse(json['timestamp'] as String),
      retryCount: json['retryCount'] as int? ?? 0,
      lastError: json['lastError'] as String?,
    );
  }
}

/// Data source for managing sync queue operations using in-memory storage
class SyncQueueDataSource {
  final Uuid _uuid = const Uuid();
  final Logger _logger = Logger();
  final List<SyncOperation> _operations = [];

  /// Initialize the data source
  Future<void> init() async {
    _logger.i('SyncQueueDataSource initialized with in-memory storage');
  }

  /// Get all pending sync operations
  Future<List<SyncOperation>> getAllOperations() async {
    return List.from(_operations);
  }

  /// Get pending operations for a specific entity type
  Future<List<SyncOperation>> getOperationsByEntityType(
    String entityType,
  ) async {
    return _operations
        .where((operation) => operation.entityType == entityType)
        .toList();
  }

  /// Get pending operations for a specific entity
  Future<List<SyncOperation>> getOperationsForEntity(
    String entityType,
    String entityId,
  ) async {
    return _operations
        .where(
          (operation) =>
              operation.entityType == entityType &&
              operation.entityId == entityId,
        )
        .toList();
  }

  /// Add operation to sync queue
  Future<SyncOperation> addOperation({
    required String entityType,
    required String entityId,
    required SyncOperationType operationType,
    Map<String, dynamic>? data,
  }) async {
    // Create a new sync operation
    final operation = SyncOperation(
      id: _uuid.v4(),
      entityType: entityType,
      entityId: entityId,
      operationType: operationType,
      data: data ?? {},
      timestamp: DateTime.now(),
    );

    // Add to queue
    _operations.add(operation);

    _logger.i(
      'Added sync operation: ${operation.operationType.name} for $entityType:$entityId',
    );
    return operation;
  }

  /// Update an existing operation
  Future<SyncOperation> updateOperation(SyncOperation operation) async {
    final index = _operations.indexWhere((op) => op.id == operation.id);
    if (index != -1) {
      _operations[index] = operation;
    }
    return operation;
  }

  /// Remove an operation from the queue
  Future<void> removeOperation(String operationId) async {
    _operations.removeWhere((operation) => operation.id == operationId);
    _logger.i('Removed sync operation: $operationId');
  }

  /// Remove all operations for an entity
  Future<void> removeOperationsForEntity(
    String entityType,
    String entityId,
  ) async {
    final operationsToRemove = await getOperationsForEntity(
      entityType,
      entityId,
    );

    for (final operation in operationsToRemove) {
      _operations.removeWhere((op) => op.id == operation.id);
    }

    _logger.i(
      'Removed ${operationsToRemove.length} sync operations for $entityType:$entityId',
    );
  }

  /// Clear all operations
  Future<void> clearQueue() async {
    _operations.clear();
    _logger.i('Cleared sync queue');
  }
}
