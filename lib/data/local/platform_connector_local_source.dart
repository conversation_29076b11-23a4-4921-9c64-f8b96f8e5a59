import 'dart:convert';
import 'package:hive/hive.dart';
import 'package:logger/logger.dart';
import '../../domain/entities/platform_connector.dart';

/// Local data source for platform connectors.
class PlatformConnectorLocalSource {
  static const String _boxName = 'platform_connectors';
  final Logger _logger = Logger();
  late Box<String> _box;

  /// Initialize the local data source.
  Future<void> init() async {
    try {
      _box = await Hive.openBox<String>(_boxName);
      _logger.i('PlatformConnectorLocalSource initialized with box: $_boxName');
    } catch (e) {
      _logger.e('Error initializing PlatformConnectorLocalSource: $e');
      rethrow;
    }
  }

  /// Get all platform connectors.
  Future<List<PlatformConnector>> getPlatformConnectors() async {
    try {
      final List<PlatformConnector> connectors = [];

      for (final key in _box.keys) {
        final String? jsonStr = _box.get(key);
        if (jsonStr != null) {
          try {
            final Map<String, dynamic> json = jsonDecode(jsonStr);
            connectors.add(PlatformConnector.fromJson(json));
          } catch (e) {
            _logger.e('Error parsing connector with key $key: $e');
            // Skip invalid entries and continue
          }
        }
      }

      return connectors;
    } catch (e) {
      _logger.e('Error getting platform connectors: $e');
      rethrow;
    }
  }

  /// Save a platform connector.
  Future<void> savePlatformConnector(PlatformConnector connector) async {
    try {
      final String jsonStr = jsonEncode(connector.toJson());
      await _box.put(connector.id, jsonStr);
      _logger.i(
        'Saved platform connector: ${connector.id} (${connector.name})',
      );
    } catch (e) {
      _logger.e('Error saving platform connector: $e');
      rethrow;
    }
  }

  /// Update an existing platform connector.
  Future<void> updatePlatformConnector(PlatformConnector connector) async {
    try {
      if (!_box.containsKey(connector.id)) {
        throw Exception('Connector with ID ${connector.id} does not exist');
      }

      final String jsonStr = jsonEncode(connector.toJson());
      await _box.put(connector.id, jsonStr);
      _logger.i(
        'Updated platform connector: ${connector.id} (${connector.name})',
      );
    } catch (e) {
      _logger.e('Error updating platform connector: $e');
      rethrow;
    }
  }

  /// Delete a platform connector.
  Future<void> deletePlatformConnector(String connectorId) async {
    try {
      await _box.delete(connectorId);
      _logger.i('Deleted platform connector: $connectorId');
    } catch (e) {
      _logger.e('Error deleting platform connector: $e');
      rethrow;
    }
  }

  /// Clear all platform connectors.
  Future<void> clearAll() async {
    try {
      await _box.clear();
      _logger.i('Cleared all platform connectors');
    } catch (e) {
      _logger.e('Error clearing platform connectors: $e');
      rethrow;
    }
  }

  /// Close the box when done.
  Future<void> close() async {
    await _box.close();
  }
}
