import 'package:dartz/dartz.dart';
import '../../../domain/entities/project_task.dart';
import '../../../domain/failure/failures.dart';

/// Base class for platform adapters
/// This is a simplified interface for project task management across platforms
abstract class PlatformAdapter {
  /// Create a task in the external platform
  Future<Either<Failure, String>> createTask(ProjectTask task);

  /// Update a task in the external platform  
  Future<Either<Failure, void>> updateTask(
    String taskId,
    ProjectTask updatedTask,
  );

  /// Delete a task from the external platform
  Future<Either<Failure, void>> deleteTask(String taskId);

  /// Get the platform name
  String get platformName;

  /// Check if the adapter is properly configured
  Future<bool> isConfigured();

  /// Convert a task to the platform-specific format
  Map<String, dynamic> convertTask(ProjectTask task);
}
