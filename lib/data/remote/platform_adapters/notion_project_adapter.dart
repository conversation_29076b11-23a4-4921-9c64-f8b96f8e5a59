import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../../../domain/entities/project.dart';
import '../../../domain/entities/project_task.dart';
import '../../../domain/entities/project_data.dart';
import '../../../domain/failure/failures.dart';

/// Notion API adapter for fetching project and task data
class NotionProjectAdapter {
  final Dio _dio;
  final Logger _logger = Logger();
  final String _baseUrl = 'https://api.notion.com/v1';

  NotionProjectAdapter(this._dio);

  /// Fetch project details from Notion
  Future<Project> fetchProject(String projectId, String apiKey) async {
    try {
      _logger.d('Fetching Notion project: $projectId');

      final response = await _dio.get(
        '$_baseUrl/databases/$projectId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $apiKey',
            'Notion-Version': '2022-06-28',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return _mapNotionDatabaseToProject(response.data);
      } else {
        throw ServerFailure(
          message: 'Failed to fetch Notion project: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      _logger.e('Notion API error: ${e.message}');
      throw ServerFailure(message: 'Notion API error: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching Notion project: $e');
      throw ServerFailure(message: 'Unexpected error: $e');
    }
  }

  /// Fetch tasks for a project from Notion
  Future<List<ProjectTask>> fetchTasks(String projectId, String apiKey) async {
    try {
      _logger.d('Fetching Notion tasks for project: $projectId');

      final response = await _dio.post(
        '$_baseUrl/databases/$projectId/query',
        options: Options(
          headers: {
            'Authorization': 'Bearer $apiKey',
            'Notion-Version': '2022-06-28',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'page_size': 100,
          'sorts': [
            {'property': 'Created', 'direction': 'descending'},
          ],
        },
      );

      if (response.statusCode == 200) {
        final results = response.data['results'] as List;
        return results
            .map((task) => _mapNotionPageToProjectTask(task, projectId))
            .toList();
      } else {
        throw ServerFailure(
          message: 'Failed to fetch Notion tasks: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      _logger.e('Notion API error fetching tasks: ${e.message}');
      throw ServerFailure(message: 'Notion API error: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching Notion tasks: $e');
      throw ServerFailure(message: 'Unexpected error: $e');
    }
  }

  /// Fetch complete project data from Notion
  Future<ProjectData> fetchProjectData(String projectId, String apiKey) async {
    try {
      _logger.d('Fetching complete Notion project data: $projectId');

      // Fetch project and tasks data
      final results = await Future.wait([
        fetchProject(projectId, apiKey),
        fetchTasks(projectId, apiKey),
      ]);

      final project = results[0] as Project;
      final tasks = results[1] as List<ProjectTask>;

      return ProjectData(
        project: project,
        tasks: tasks,
        teamMembers: _extractTeamMembersFromTasks(tasks),
        budget: null, // Notion doesn't have built-in budget tracking
        recentActivities: [], // Would need separate processing
        lastSync: DateTime.now(),
      );
    } catch (e) {
      _logger.e('Error fetching complete Notion project data: $e');
      rethrow;
    }
  }

  /// Map Notion database to Project entity
  Project _mapNotionDatabaseToProject(Map<String, dynamic> databaseData) {
    final title = databaseData['title'] as List? ?? [];
    final description = databaseData['description'] as List? ?? [];

    String projectName = 'Unnamed Project';
    if (title.isNotEmpty) {
      projectName = title.first['plain_text']?.toString() ?? 'Unnamed Project';
    }

    String projectDescription = '';
    if (description.isNotEmpty) {
      projectDescription = description.first['plain_text']?.toString() ?? '';
    }

    return Project(
      id: databaseData['id']?.toString() ?? '',
      name: projectName,
      description: projectDescription,
      platformId: 'notion',
      externalId: databaseData['id']?.toString() ?? '',
      progress: 0.0, // Would need to calculate from tasks
      status: ProjectStatus.active, // Default status
      deadline: null, // Would need to extract from properties
      teamMembers: [], // Will be populated separately
      metadata: {
        'notion_database_id': databaseData['id'],
        'object': databaseData['object'],
        'url': databaseData['url'],
        'archived': databaseData['archived'] ?? false,
        'is_inline': databaseData['is_inline'] ?? false,
        'public_url': databaseData['public_url'],
        'properties': databaseData['properties'] ?? {},
      },
      createdAt:
          databaseData['created_time'] != null
              ? DateTime.parse(databaseData['created_time'])
              : DateTime.now(),
      updatedAt:
          databaseData['last_edited_time'] != null
              ? DateTime.parse(databaseData['last_edited_time'])
              : DateTime.now(),
    );
  }

  /// Map Notion page to ProjectTask entity
  ProjectTask _mapNotionPageToProjectTask(
    Map<String, dynamic> pageData,
    String projectId,
  ) {
    final properties = pageData['properties'] as Map<String, dynamic>? ?? {};

    // Extract task name from title property
    String taskName = 'Unnamed Task';
    final nameProperty = _findTitleProperty(properties);
    if (nameProperty != null) {
      final titleList = nameProperty['title'] as List? ?? [];
      if (titleList.isNotEmpty) {
        taskName = titleList.first['plain_text']?.toString() ?? 'Unnamed Task';
      }
    }

    // Extract status
    final statusProperty = _findSelectProperty(properties, 'Status');
    final status = _mapNotionStatusToProjectTaskStatus(statusProperty);

    // Extract priority
    final priorityProperty = _findSelectProperty(properties, 'Priority');
    final priority = _mapNotionPriorityToProjectTaskPriority(priorityProperty);

    // Extract assignee
    final assigneeProperty = _findPeopleProperty(properties, 'Assignee');
    String? assignee;
    if (assigneeProperty != null) {
      final people = assigneeProperty['people'] as List? ?? [];
      if (people.isNotEmpty) {
        assignee = people.first['name']?.toString();
      }
    }

    // Extract deadline
    DateTime? deadline;
    final dueDateProperty = _findDateProperty(properties, 'Due Date');
    if (dueDateProperty != null) {
      final dateValue = dueDateProperty['date']?['start'];
      if (dateValue != null) {
        deadline = DateTime.parse(dateValue);
      }
    }

    return ProjectTask(
      id: pageData['id']?.toString() ?? '',
      projectId: projectId,
      name: taskName,
      description: null, // Would need to fetch page content for description
      status: status,
      assignee: assignee,
      priority: priority,
      deadline: deadline,
      blockers: [], // Notion doesn't have explicit blockers
      blockerReason: null,
      metadata: {
        'notion_page_id': pageData['id'],
        'object': pageData['object'],
        'url': pageData['url'],
        'archived': pageData['archived'] ?? false,
        'properties': properties,
        'parent': pageData['parent'],
        'icon': pageData['icon'],
        'cover': pageData['cover'],
      },
      createdAt:
          pageData['created_time'] != null
              ? DateTime.parse(pageData['created_time'])
              : DateTime.now(),
      updatedAt:
          pageData['last_edited_time'] != null
              ? DateTime.parse(pageData['last_edited_time'])
              : DateTime.now(),
    );
  }

  /// Extract team members from task assignments
  List<TeamMember> _extractTeamMembersFromTasks(List<ProjectTask> tasks) {
    final Map<String, TeamMember> memberMap = {};

    for (final task in tasks) {
      final assignee = task.assignee;
      if (assignee != null && !memberMap.containsKey(assignee)) {
        memberMap[assignee] = TeamMember(
          id: assignee.toLowerCase().replaceAll(' ', '_'),
          name: assignee,
          email: '', // Notion API doesn't provide email directly
          role: 'Member',
          workload: _calculateWorkload(assignee, tasks),
          isActive: true,
        );
      }
    }

    return memberMap.values.toList();
  }

  /// Calculate workload for a team member
  double _calculateWorkload(String assignee, List<ProjectTask> tasks) {
    final assignedTasks =
        tasks.where((task) => task.assignee == assignee).toList();
    final incompleteTasks =
        assignedTasks
            .where((task) => task.status != ProjectTaskStatus.done)
            .length;

    // Simple workload calculation: 0.1 per incomplete task, capped at 1.0
    return (incompleteTasks * 0.1).clamp(0.0, 1.0);
  }

  /// Find a title property in Notion properties
  Map<String, dynamic>? _findTitleProperty(Map<String, dynamic> properties) {
    for (final property in properties.values) {
      if (property is Map<String, dynamic> && property['type'] == 'title') {
        return property;
      }
    }
    return null;
  }

  /// Find a select property by name
  Map<String, dynamic>? _findSelectProperty(
    Map<String, dynamic> properties,
    String propertyName,
  ) {
    final property = properties[propertyName];
    if (property is Map<String, dynamic> && property['type'] == 'select') {
      return property;
    }
    return null;
  }

  /// Find a people property by name
  Map<String, dynamic>? _findPeopleProperty(
    Map<String, dynamic> properties,
    String propertyName,
  ) {
    final property = properties[propertyName];
    if (property is Map<String, dynamic> && property['type'] == 'people') {
      return property;
    }
    return null;
  }

  /// Find a date property by name
  Map<String, dynamic>? _findDateProperty(
    Map<String, dynamic> properties,
    String propertyName,
  ) {
    final property = properties[propertyName];
    if (property is Map<String, dynamic> && property['type'] == 'date') {
      return property;
    }
    return null;
  }

  /// Map Notion status to ProjectTaskStatus enum
  ProjectTaskStatus _mapNotionStatusToProjectTaskStatus(
    Map<String, dynamic>? statusProperty,
  ) {
    if (statusProperty == null) return ProjectTaskStatus.toDo;

    final selectValue = statusProperty['select'];
    if (selectValue == null) return ProjectTaskStatus.toDo;

    final statusName = selectValue['name']?.toString().toLowerCase() ?? '';

    if (statusName.contains('done') || statusName.contains('complete')) {
      return ProjectTaskStatus.done;
    } else if (statusName.contains('progress') ||
        statusName.contains('doing')) {
      return ProjectTaskStatus.inProgress;
    } else if (statusName.contains('blocked') ||
        statusName.contains('waiting')) {
      return ProjectTaskStatus.blocked;
    } else if (statusName.contains('review')) {
      return ProjectTaskStatus.review;
    } else {
      return ProjectTaskStatus.toDo;
    }
  }

  /// Map Notion priority to ProjectTaskPriority enum
  ProjectTaskPriority _mapNotionPriorityToProjectTaskPriority(
    Map<String, dynamic>? priorityProperty,
  ) {
    if (priorityProperty == null) return ProjectTaskPriority.medium;

    final selectValue = priorityProperty['select'];
    if (selectValue == null) return ProjectTaskPriority.medium;

    final priorityName = selectValue['name']?.toString().toLowerCase() ?? '';

    switch (priorityName) {
      case 'urgent':
      case 'critical':
        return ProjectTaskPriority.urgent;
      case 'high':
        return ProjectTaskPriority.high;
      case 'medium':
      case 'normal':
        return ProjectTaskPriority.medium;
      case 'low':
        return ProjectTaskPriority.low;
      default:
        return ProjectTaskPriority.medium;
    }
  }
}
