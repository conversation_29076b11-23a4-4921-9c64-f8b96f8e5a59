import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../../../domain/entities/project.dart';
import '../../../domain/entities/project_task.dart';
import '../../../domain/entities/project_data.dart';
import '../../../domain/failure/failures.dart';
import 'package:dartz/dartz.dart';

/// ClickUp API adapter for fetching project and task data
class ClickUpProjectAdapter {
  final Dio _dio;
  final Logger _logger = Logger();
  final String _baseUrl = 'https://api.clickup.com/api/v2';

  ClickUpProjectAdapter(this._dio);

  /// Fetch project details from ClickUp
  Future<Project> fetchProject(String projectId, String apiKey) async {
    try {
      _logger.d('Fetching ClickUp project: $projectId');

      final response = await _dio.get(
        '$_baseUrl/space/$projectId',
        options: Options(headers: {'Authorization': api<PERSON>ey}),
      );

      if (response.statusCode == 200) {
        return _mapClickUpSpaceToProject(response.data['space']);
      } else {
        throw ServerFailure(
          message: 'Failed to fetch ClickUp project: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      _logger.e('ClickUp API error: ${e.message}');
      throw ServerFailure(message: 'ClickUp API error: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching ClickUp project: $e');
      throw ServerFailure(message: 'Unexpected error: $e');
    }
  }

  /// Fetch tasks for a project from ClickUp
  Future<List<ProjectTask>> fetchTasks(String projectId, String apiKey) async {
    try {
      _logger.d('Fetching ClickUp tasks for project: $projectId');

      final response = await _dio.get(
        '$_baseUrl/space/$projectId/task',
        options: Options(headers: {'Authorization': apiKey}),
        queryParameters: {
          'include_closed': true,
          'subtasks': true,
          'include_markdown_description': true,
        },
      );

      if (response.statusCode == 200) {
        final tasks = response.data['tasks'] as List;
        return tasks
            .map((task) => _mapClickUpTaskToProjectTask(task, projectId))
            .toList();
      } else {
        throw ServerFailure(
          message: 'Failed to fetch ClickUp tasks: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      _logger.e('ClickUp API error fetching tasks: ${e.message}');
      throw ServerFailure(message: 'ClickUp API error: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching ClickUp tasks: $e');
      throw ServerFailure(message: 'Unexpected error: $e');
    }
  }

  /// Fetch team members for a project from ClickUp
  Future<List<TeamMember>> fetchTeamMembers(
    String projectId,
    String apiKey,
  ) async {
    try {
      _logger.d('Fetching ClickUp team members for project: $projectId');

      final response = await _dio.get(
        '$_baseUrl/space/$projectId/member',
        options: Options(headers: {'Authorization': apiKey}),
      );

      if (response.statusCode == 200) {
        final members = response.data['members'] as List;
        return members
            .map((member) => _mapClickUpMemberToTeamMember(member))
            .toList();
      } else {
        throw ServerFailure(
          message:
              'Failed to fetch ClickUp team members: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      _logger.e('ClickUp API error fetching team members: ${e.message}');
      throw ServerFailure(message: 'ClickUp API error: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching ClickUp team members: $e');
      throw ServerFailure(message: 'Unexpected error: $e');
    }
  }

  /// Fetch complete project data from ClickUp
  Future<ProjectData> fetchProjectData(String projectId, String apiKey) async {
    try {
      _logger.d('Fetching complete ClickUp project data: $projectId');

      // Fetch all data in parallel for better performance
      final results = await Future.wait([
        fetchProject(projectId, apiKey),
        fetchTasks(projectId, apiKey),
        fetchTeamMembers(projectId, apiKey),
      ]);

      final project = results[0] as Project;
      final tasks = results[1] as List<ProjectTask>;
      final teamMembers = results[2] as List<TeamMember>;

      return ProjectData(
        project: project,
        tasks: tasks,
        teamMembers: teamMembers,
        budget: null, // ClickUp doesn't have built-in budget tracking
        recentActivities: [], // Would need separate API call for activity
        lastSync: DateTime.now(),
      );
    } catch (e) {
      _logger.e('Error fetching complete ClickUp project data: $e');
      rethrow;
    }
  }

  /// Map ClickUp space data to Project entity
  Project _mapClickUpSpaceToProject(Map<String, dynamic> spaceData) {
    final features = spaceData['features'] as Map<String, dynamic>? ?? {};
    final multipleAssignees =
        features['multiple_assignees'] as Map<String, dynamic>? ?? {};

    return Project(
      id: spaceData['id']?.toString() ?? '',
      name: spaceData['name']?.toString() ?? 'Unnamed Project',
      description: spaceData['description']?.toString() ?? '',
      platformId: 'clickup',
      externalId: spaceData['id']?.toString() ?? '',
      progress: 0.0, // ClickUp doesn't provide overall progress
      status: ProjectStatus.active, // Default status
      deadline: null, // ClickUp spaces don't have deadlines
      teamMembers: [], // Will be populated separately
      metadata: {
        'clickup_space_id': spaceData['id'],
        'color': spaceData['color'],
        'private': spaceData['private'] ?? false,
        'avatar': spaceData['avatar'],
        'admin_can_manage': spaceData['admin_can_manage'] ?? false,
        'multiple_assignees_enabled': multipleAssignees['enabled'] ?? false,
      },
      createdAt:
          DateTime.now(), // ClickUp API doesn't provide creation date for spaces
      updatedAt: DateTime.now(),
    );
  }

  /// Map ClickUp task data to ProjectTask entity
  ProjectTask _mapClickUpTaskToProjectTask(
    Map<String, dynamic> taskData,
    String projectId,
  ) {
    final status = taskData['status'] as Map<String, dynamic>? ?? {};
    final assignees = taskData['assignees'] as List? ?? [];
    final priority = taskData['priority'] as Map<String, dynamic>? ?? {};

    return ProjectTask(
      id: taskData['id']?.toString() ?? '',
      projectId: projectId,
      name: taskData['name']?.toString() ?? 'Unnamed Task',
      description: taskData['description']?.toString(),
      status: _mapClickUpStatusToProjectTaskStatus(status),
      assignee:
          assignees.isNotEmpty ? assignees.first['username']?.toString() : null,
      priority: _mapClickUpPriorityToProjectTaskPriority(priority),
      deadline:
          taskData['due_date'] != null
              ? DateTime.fromMillisecondsSinceEpoch(
                int.parse(taskData['due_date'].toString()),
              )
              : null,
      blockers: [], // ClickUp doesn't have explicit blocker fields
      blockerReason: null,
      estimatedHours:
          taskData['time_estimate'] != null
              ? (int.parse(taskData['time_estimate'].toString()) / 3600000)
                  .toDouble() // Convert ms to hours
              : null,
      actualHours:
          taskData['time_spent'] != null
              ? (int.parse(taskData['time_spent'].toString()) / 3600000)
                  .toDouble() // Convert ms to hours
              : null,
      progress: _calculateTaskProgress(taskData),
      metadata: {
        'clickup_task_id': taskData['id'],
        'url': taskData['url'],
        'creator': taskData['creator']?['username'],
        'date_created': taskData['date_created'],
        'date_updated': taskData['date_updated'],
        'tags': taskData['tags'] ?? [],
        'parent': taskData['parent'],
        'custom_fields': taskData['custom_fields'] ?? [],
      },
      createdAt:
          taskData['date_created'] != null
              ? DateTime.fromMillisecondsSinceEpoch(
                int.parse(taskData['date_created'].toString()),
              )
              : DateTime.now(),
      updatedAt:
          taskData['date_updated'] != null
              ? DateTime.fromMillisecondsSinceEpoch(
                int.parse(taskData['date_updated'].toString()),
              )
              : DateTime.now(),
    );
  }

  /// Map ClickUp member data to TeamMember entity
  TeamMember _mapClickUpMemberToTeamMember(Map<String, dynamic> memberData) {
    final user = memberData['user'] as Map<String, dynamic>? ?? {};

    return TeamMember(
      id: user['id']?.toString() ?? '',
      name: user['username']?.toString() ?? 'Unknown',
      email: user['email']?.toString() ?? '',
      role: _determineRole(memberData),
      isActive: true, // ClickUp API doesn't provide activity status
      workload: 0.0, // Would need separate calculation
    );
  }

  /// Map ClickUp status to ProjectTaskStatus enum
  ProjectTaskStatus _mapClickUpStatusToProjectTaskStatus(
    Map<String, dynamic> status,
  ) {
    final statusType = status['type']?.toString().toLowerCase() ?? '';
    final statusName = status['status']?.toString().toLowerCase() ?? '';

    if (statusType == 'done' || statusName.contains('complete')) {
      return ProjectTaskStatus.done;
    } else if (statusType == 'in_progress' || statusName.contains('progress')) {
      return ProjectTaskStatus.inProgress;
    } else if (statusName.contains('blocked') ||
        statusName.contains('waiting')) {
      return ProjectTaskStatus.blocked;
    } else {
      return ProjectTaskStatus.toDo;
    }
  }

  /// Map ClickUp priority to ProjectTaskPriority enum
  ProjectTaskPriority _mapClickUpPriorityToProjectTaskPriority(
    Map<String, dynamic>? priority,
  ) {
    if (priority == null) return ProjectTaskPriority.medium;

    final priorityValue = priority['priority']?.toString().toLowerCase() ?? '';

    switch (priorityValue) {
      case 'urgent':
        return ProjectTaskPriority.urgent;
      case 'high':
        return ProjectTaskPriority.high;
      case 'normal':
        return ProjectTaskPriority.medium;
      case 'low':
        return ProjectTaskPriority.low;
      default:
        return ProjectTaskPriority.medium;
    }
  }

  /// Calculate task progress based on checklist completion
  double _calculateTaskProgress(Map<String, dynamic> taskData) {
    final checklists = taskData['checklists'] as List? ?? [];
    if (checklists.isEmpty) {
      // If no checklists, consider completion based on status
      final status = taskData['status'] as Map<String, dynamic>? ?? {};
      final statusType = status['type']?.toString().toLowerCase() ?? '';
      return statusType == 'done' ? 1.0 : 0.0;
    }

    int totalItems = 0;
    int completedItems = 0;

    for (final checklist in checklists) {
      final items = checklist['items'] as List? ?? [];
      totalItems += items.length;
      completedItems += items.where((item) => item['resolved'] == true).length;
    }

    return totalItems > 0 ? (completedItems / totalItems) : 0.0;
  }

  /// Determine team member role from ClickUp member data
  String _determineRole(Map<String, dynamic> memberData) {
    // ClickUp doesn't provide explicit role information in the member endpoint
    // This would typically come from workspace/team permissions
    return 'Member'; // Default role
  }
}
