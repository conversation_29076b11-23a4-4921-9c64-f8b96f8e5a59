import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';

/// Generic API client for making HTTP requests to different integration platforms.
class ApiClient {
  final Logger _logger = Logger();
  final http.Client _httpClient;

  ApiClient({http.Client? httpClient})
    : _httpClient = httpClient ?? http.Client();

  /// Make a GET request to the specified URL with optional headers and queryParameters.
  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
  }) async {
    final uri = Uri.parse(url).replace(queryParameters: queryParameters);
    _logger.d('GET Request: $uri');

    try {
      final response = await _httpClient.get(uri, headers: headers);
      _logResponse(response);
      return response;
    } catch (e) {
      _logger.e('Error making GET request: $e');
      rethrow;
    }
  }

  /// Make a POST request to the specified URL with optional headers, body, and queryParameters.
  Future<http.Response> post(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    String? rawBody,
    Map<String, dynamic>? queryParameters,
  }) async {
    final uri = Uri.parse(url).replace(queryParameters: queryParameters);
    final requestHeaders = {'Content-Type': 'application/json', ...?headers};

    _logger.d('POST Request: $uri');
    _logger.d('Headers: $requestHeaders');
    _logger.d('Body: ${body ?? rawBody}');

    try {
      final response = await _httpClient.post(
        uri,
        headers: requestHeaders,
        body: rawBody ?? (body != null ? json.encode(body) : null),
      );
      _logResponse(response);
      return response;
    } catch (e) {
      _logger.e('Error making POST request: $e');
      rethrow;
    }
  }

  /// Make a PUT request to the specified URL with optional headers, body, and queryParameters.
  Future<http.Response> put(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    String? rawBody,
    Map<String, dynamic>? queryParameters,
  }) async {
    final uri = Uri.parse(url).replace(queryParameters: queryParameters);
    final requestHeaders = {'Content-Type': 'application/json', ...?headers};

    _logger.d('PUT Request: $uri');

    try {
      final response = await _httpClient.put(
        uri,
        headers: requestHeaders,
        body: rawBody ?? (body != null ? json.encode(body) : null),
      );
      _logResponse(response);
      return response;
    } catch (e) {
      _logger.e('Error making PUT request: $e');
      rethrow;
    }
  }

  /// Make a DELETE request to the specified URL with optional headers and queryParameters.
  Future<http.Response> delete(
    String url, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
  }) async {
    final uri = Uri.parse(url).replace(queryParameters: queryParameters);
    _logger.d('DELETE Request: $uri');

    try {
      final response = await _httpClient.delete(uri, headers: headers);
      _logResponse(response);
      return response;
    } catch (e) {
      _logger.e('Error making DELETE request: $e');
      rethrow;
    }
  }

  /// Log the response for debugging purposes.
  void _logResponse(http.Response response) {
    _logger.d('Response status: ${response.statusCode}');
    _logger.d('Response body: ${response.body}');
  }

  /// Close the HTTP client when done.
  void dispose() {
    _httpClient.close();
  }
}
