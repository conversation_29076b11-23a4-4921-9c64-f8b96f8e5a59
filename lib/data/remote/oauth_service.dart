import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service für die Kommunikation mit dem OAuth-Proxy in Supabase
class OAuthService {
  final SupabaseClient _supabaseClient;
  final Logger _logger = Logger();

  // Edge-Funktion URL
  static const String _edgeFunctionName = 'oauth-proxy';

  OAuthService({SupabaseClient? supabaseClient})
    : _supabaseClient = supabaseClient ?? Supabase.instance.client;

  /// Holt die Client-ID für eine bestimmte Plattform aus der Datenbank
  Future<String?> getClientId(String platformId) async {
    try {
      final response = await _callEdgeFunction(
        action: 'getClientId',
        payload: {'platformId': platformId},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['clientId'] as String?;
      } else {
        _logger.e('Error getting client ID: ${response.body}');
        return null;
      }
    } catch (e) {
      _logger.e('Failed to get client ID: $e');
      return null;
    }
  }

  /// Tauscht einen Authorization Code gegen ein Access Token aus
  /// Dies geschieht serverseitig, um das Client Secret nicht auf dem Client zu exponieren
  Future<Map<String, dynamic>?> exchangeToken({
    required String platformId,
    required String code,
    required String redirectUri,
  }) async {
    try {
      final response = await _callEdgeFunction(
        action: 'exchangeToken',
        payload: {
          'platformId': platformId,
          'code': code,
          'redirectUri': redirectUri,
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        _logger.e('Error exchanging token: ${response.body}');
        return null;
      }
    } catch (e) {
      _logger.e('Failed to exchange token: $e');
      return null;
    }
  }

  /// Fetches additional platform information using the access token
  /// This is used to get user details, workspace information, etc.
  Future<Map<String, dynamic>?> fetchPlatformInfo({
    required String platformId,
    required String accessToken,
  }) async {
    try {
      final response = await _callEdgeFunction(
        action: 'fetchPlatformInfo',
        payload: {'platformId': platformId, 'accessToken': accessToken},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        _logger.e('Error fetching platform info: ${response.body}');
        return null;
      }
    } catch (e) {
      _logger.e('Failed to fetch platform info: $e');
      return null;
    }
  }

  /// Hilfsmethode, um Edge-Funktionen aufzurufen
  Future<http.Response> _callEdgeFunction({
    required String action,
    required Map<String, dynamic> payload,
  }) async {
    // Die URL wird aus der Umgebungsvariable geholt, da der direkte Zugriff nicht verfügbar ist
    final supabaseUrl = const String.fromEnvironment(
      'SUPABASE_URL',
      defaultValue: 'https://oivqmtsotvaypcucnrgm.supabase.co',
    );

    final url = '$supabaseUrl/functions/v1/$_edgeFunctionName';
    final token = _supabaseClient.auth.currentSession?.accessToken;

    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };

    final fullPayload = {'action': action, ...payload};

    return await http.post(
      Uri.parse(url),
      headers: headers,
      body: jsonEncode(fullPayload),
    );
  }
}
