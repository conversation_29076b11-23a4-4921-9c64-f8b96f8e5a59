import 'package:dartz/dartz.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:projectpilot/domain/failure/failures.dart';
import 'package:projectpilot/domain/repositories/token_repository.dart';

/// Implementation of TokenRepository using Supabase
class TokenRepositoryImpl implements TokenRepository {
  final SupabaseClient _supabaseClient;

  const TokenRepositoryImpl({required SupabaseClient supabaseClient})
    : _supabaseClient = supabaseClient;

  @override
  Future<Either<Failure, Map<String, dynamic>?>> getTokenBalance(
    String userId,
  ) async {
    try {
      final response =
          await _supabaseClient
              .from('user_tokens')
              .select('available_tokens, used_tokens, total_tokens')
              .eq('user_id', userId)
              .maybeSingle();

      if (response == null) {
        // Return default token balance for new users
        return Right({
          'availableTokens': 1000,
          'usedTokens': 0,
          'totalTokens': 1000,
        });
      }

      return Right({
        'availableTokens': response['available_tokens'] ?? 0,
        'usedTokens': response['used_tokens'] ?? 0,
        'totalTokens': response['total_tokens'] ?? 0,
      });
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get token balance: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateTokenBalance({
    required String userId,
    required int tokensUsed,
  }) async {
    try {
      // First get current balance
      final currentBalanceResult = await getTokenBalance(userId);
      
      return currentBalanceResult.fold((failure) => Left(failure), (
        currentBalance,
      ) async {
        if (currentBalance == null) {
          return Left(ServerFailure(message: 'Failed to get current balance'));
        }

        final int currentUsed = currentBalance['usedTokens'] ?? 0;
        final int totalTokens = currentBalance['totalTokens'] ?? 1000;
        final int newUsedTokens = currentUsed + tokensUsed;
        final int newAvailableTokens = totalTokens - newUsedTokens;

        // Update the token balance
        await _supabaseClient.from('user_tokens').upsert({
          'user_id': userId,
          'available_tokens': newAvailableTokens,
          'used_tokens': newUsedTokens,
          'total_tokens': totalTokens,
          'updated_at': DateTime.now().toIso8601String(),
        });

        return const Right(null);
      });
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update token balance: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> addTokens({
    required String userId,
    required int tokensToAdd,
  }) async {
    try {
      // Get current balance
      final currentBalanceResult = await getTokenBalance(userId);
      
      return currentBalanceResult.fold((failure) => Left(failure), (
        currentBalance,
      ) async {
        if (currentBalance == null) {
          return Left(ServerFailure(message: 'Failed to get current balance'));
        }

        final int currentTotal = currentBalance['totalTokens'] ?? 1000;
        final int currentUsed = currentBalance['usedTokens'] ?? 0;
        final int newTotal = currentTotal + tokensToAdd;
        final int newAvailable = newTotal - currentUsed;

        // Update the token balance
        await _supabaseClient.from('user_tokens').upsert({
          'user_id': userId,
          'available_tokens': newAvailable,
          'used_tokens': currentUsed,
          'total_tokens': newTotal,
          'updated_at': DateTime.now().toIso8601String(),
        });

        return const Right(null);
      });
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to add tokens: $e'));
    }
  }
}
