import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';

class StorageRepository {
  final SupabaseClient _supabaseClient;
  final Logger _logger = Logger();

  StorageRepository({SupabaseClient? supabaseClient})
    : _supabaseClient = supabaseClient ?? Supabase.instance.client;

  Future<String?> uploadAudioFile(String filePath, String supabasePath) async {
    try {
      final File file = File(filePath);
      if (!await file.exists()) {
        _logger.w('File does not exist at path: $filePath');
        return null;
      }

      final String fileName = filePath.split('/').last;
      final String storagePath = '$supabasePath/$fileName';

      _logger.i('Uploading file $fileName to Supabase Storage at $storagePath');

      final response = await _supabaseClient.storage
          .from(
            'user_uploads',
          ) // Changed from 'voice_recordings' to 'user_uploads'
          .upload(
            storagePath,
            file,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: false),
          );

      _logger.i('Upload response: $response');
      // Supabase upload gibt den Pfad nicht direkt in `response` zurück, sondern er ist `storagePath`
      // Um die öffentliche URL zu erhalten:
      // final String publicUrl = _supabaseClient.storage.from('user_uploads').getPublicUrl(storagePath);
      // return publicUrl;

      // Für den Moment geben wir den storagePath zurück, da der Upload erfolgreich war.
      // Die response Variable in supabase_flutter ^2.0.0 für .upload ist ein String, der den Key des Objekts im Bucket darstellt.
      // In älteren Versionen oder anderen SDKs könnte es anders sein.
      // Wenn response ein Fehler ist oder null, dann ist der Upload fehlgeschlagen.
      // Da .upload() eine Exception wirft bei Fehlern, ist ein erfolgreicher Abschluss hier ausreichend.
      return storagePath; // Oder response, je nachdem, was die .upload Methode genau zurückgibt und was du brauchst.
    } catch (e) {
      _logger.e('Error uploading file to Supabase Storage: $e');
      // Du könntest hier einen spezifischeren Fehler werfen oder null zurückgeben
      return null;
    }
  }

  // Optional: Methode zum Löschen einer Datei aus Supabase, falls benötigt
  Future<bool> deleteAudioFile(String supabasePath) async {
    try {
      _logger.i('Deleting file from Supabase Storage at $supabasePath');
      await _supabaseClient.storage
          .from(
            'user_uploads',
          ) // Changed from 'voice_recordings' to 'user_uploads'
          .remove([supabasePath]);
      _logger.i('File $supabasePath deleted successfully from Supabase.');
      return true;
    } catch (e) {
      _logger.e('Error deleting file from Supabase Storage: $e');
      return false;
    }
  }
}
