import 'package:dartz/dartz.dart';
import 'package:logger/logger.dart';
import '../../domain/entities/project.dart';
import '../../domain/entities/project_task.dart';
import '../../domain/entities/project_data.dart';
import '../../domain/failure/failures.dart';
import '../../domain/repositories/project_repository.dart';
import '../remote/platform_adapters/clickup_project_adapter.dart';
import '../remote/platform_adapters/notion_project_adapter.dart';
import '../local/models/platform_config_model.dart';
import '../local/platform_config_local_datasource.dart';

/// Implementation of ProjectRepository using multiple platform adapters
class ProjectRepositoryImpl implements ProjectRepository {
  final ClickUpProjectAdapter _clickUpAdapter;
  final NotionProjectAdapter _notionAdapter;
  final PlatformConfigLocalDataSource _platformConfigDataSource;
  final Logger _logger = Logger();

  ProjectRepositoryImpl({
    required ClickUpProjectAdapter clickUpAdapter,
    required NotionProjectAdapter notionAdapter,
    required PlatformConfigLocalDataSource platformConfigDataSource,
  }) : _clickUpAdapter = clickUpAdapter,
       _notionAdapter = notionAdapter,
       _platformConfigDataSource = platformConfigDataSource;

  @override
  Future<Either<Failure, List<Project>>> getProjects() async {
    try {
      _logger.d('Fetching projects from all connected platforms');

      final configs = await _platformConfigDataSource.getAllConfigs();
      final List<Project> allProjects = [];

      for (final config in configs) {
        if (!config.isEnabled) continue;

        try {
          final projects = await _fetchProjectsFromPlatform(config);
          allProjects.addAll(projects);
        } catch (e) {
          _logger.w('Failed to fetch projects from ${config.platformId}: $e');
          // Continue with other platforms instead of failing completely
        }
      }

      _logger.d('Successfully fetched ${allProjects.length} projects');
      return Right(allProjects);
    } catch (e) {
      _logger.e('Error fetching projects: $e');
      return Left(ServerFailure(message: 'Failed to fetch projects: $e'));
    }
  }

  @override
  Future<Either<Failure, Project>> getProject(String id) async {
    try {
      _logger.d('Fetching project: $id');

      // First try to find the project config to determine platform
      final config = await _platformConfigDataSource.getConfigByProjectId(id);

      if (config == null) {
        return Left(
          NotFoundFailure(message: 'Project configuration not found'),
        );
      }

      final project = await _fetchProjectFromPlatform(id, config);
      return Right(project);
    } catch (e) {
      _logger.e('Error fetching project $id: $e');
      return Left(ServerFailure(message: 'Failed to fetch project: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ProjectTask>>> getProjectTasks(
    String projectId,
  ) async {
    try {
      _logger.d('Fetching tasks for project: $projectId');

      final config = await _platformConfigDataSource.getConfigByProjectId(
        projectId,
      );

      if (config == null) {
        return Left(
          NotFoundFailure(message: 'Project configuration not found'),
        );
      }

      final tasks = await _fetchTasksFromPlatform(projectId, config);
      return Right(tasks);
    } catch (e) {
      _logger.e('Error fetching tasks for project $projectId: $e');
      return Left(ServerFailure(message: 'Failed to fetch tasks: $e'));
    }
  }

  @override
  Future<Either<Failure, ProjectData>> fetchProjectDataFromPlatform(
    String projectId,
  ) async {
    try {
      _logger.d('Fetching complete project data: $projectId');

      final config = await _platformConfigDataSource.getConfigByProjectId(
        projectId,
      );

      if (config == null) {
        return Left(
          NotFoundFailure(message: 'Project configuration not found'),
        );
      }

      final projectData = await _fetchProjectDataFromPlatform(
        projectId,
        config,
      );

      // Cache the fetched data locally
      await _cacheProjectData(projectData);

      return Right(projectData);
    } catch (e) {
      _logger.e('Error fetching project data for $projectId: $e');
      return Left(ServerFailure(message: 'Failed to fetch project data: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> syncProjectData(String projectId) async {
    try {
      _logger.d('Syncing project data: $projectId');

      // Fetch fresh data from platform
      final result = await fetchProjectDataFromPlatform(projectId);

      return result.fold((failure) => Left(failure), (projectData) async {
        // Update local cache with fresh data
        await _cacheProjectData(projectData);
        _logger.d('Successfully synced project data: $projectId');
        return const Right(null);
      });
    } catch (e) {
      _logger.e('Error syncing project data for $projectId: $e');
      return Left(ServerFailure(message: 'Failed to sync project data: $e'));
    }
  }

  @override
  Future<Either<Failure, ProjectData?>> getCachedProjectData(
    String projectId,
  ) async {
    try {
      _logger.d('Getting cached project data: $projectId');
      // Implementation would depend on your local storage solution
      // For now, return null (not found)
      return const Right(null);
    } catch (e) {
      _logger.e('Error getting cached project data $projectId: $e');
      return Left(
        CacheFailure(message: 'Failed to get cached project data: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> cacheProjectData(
    ProjectData projectData,
  ) async {
    try {
      _logger.d('Caching project data: ${projectData.project.id}');
      await _cacheProjectData(projectData);
      return const Right(null);
    } catch (e) {
      _logger.e('Error caching project data ${projectData.project.id}: $e');
      return Left(CacheFailure(message: 'Failed to cache project data: $e'));
    }
  }

  @override
  Future<Either<Failure, Project>> createProject(Project project) async {
    try {
      _logger.d('Creating project: ${project.name}');
      // Implementation would depend on platform capabilities
      // For now, return the project as if it was created
      return Right(project);
    } catch (e) {
      _logger.e('Error creating project: $e');
      return Left(ServerFailure(message: 'Failed to create project: $e'));
    }
  }

  @override
  Future<Either<Failure, Project>> updateProject(Project project) async {
    try {
      _logger.d('Updating project: ${project.id}');
      // Implementation would depend on platform capabilities
      // For now, return the project as if it was updated
      return Right(project);
    } catch (e) {
      _logger.e('Error updating project ${project.id}: $e');
      return Left(ServerFailure(message: 'Failed to update project: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteProject(String projectId) async {
    try {
      _logger.d('Deleting project: $projectId');
      // Implementation would depend on platform capabilities
      // For now, just return success
      return const Right(null);
    } catch (e) {
      _logger.e('Error deleting project $projectId: $e');
      return Left(ServerFailure(message: 'Failed to delete project: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Project>>> getProjectsByPlatform(
    String platformId,
  ) async {
    try {
      _logger.d('Fetching projects for platform: $platformId');

      final configs = await _platformConfigDataSource.getAllConfigs();
      final platformConfigs =
          configs
              .where(
                (config) =>
                    config.platformId.toLowerCase() == platformId.toLowerCase(),
              )
              .toList();

      final List<Project> projects = [];

      for (final config in platformConfigs) {
        try {
          final platformProjects = await _fetchProjectsFromPlatform(config);
          projects.addAll(platformProjects);
        } catch (e) {
          _logger.w('Failed to fetch projects from ${config.platformId}: $e');
        }
      }

      return Right(projects);
    } catch (e) {
      _logger.e('Error fetching projects for platform $platformId: $e');
      return Left(ServerFailure(message: 'Failed to fetch projects: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Project>>> searchProjects(String query) async {
    try {
      _logger.d('Searching projects with query: $query');

      final allProjectsResult = await getProjects();

      return allProjectsResult.fold((failure) => Left(failure), (projects) {
        final filteredProjects =
            projects.where((project) {
              final nameMatch = project.name.toLowerCase().contains(
                query.toLowerCase(),
              );
              final descriptionMatch = project.description
                  .toLowerCase()
                  .contains(query.toLowerCase());
              return nameMatch || descriptionMatch;
            }).toList();

        return Right(filteredProjects);
      });
    } catch (e) {
      _logger.e('Error searching projects: $e');
      return Left(ServerFailure(message: 'Failed to search projects: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getProjectStatistics(
    String projectId,
  ) async {
    try {
      _logger.d('Getting project statistics: $projectId');

      final tasksResult = await getProjectTasks(projectId);

      return tasksResult.fold((failure) => Left(failure), (tasks) {
        final totalTasks = tasks.length;
        final completedTasks =
            tasks.where((task) => task.status == ProjectTaskStatus.done).length;
        final inProgressTasks =
            tasks
                .where((task) => task.status == ProjectTaskStatus.inProgress)
                .length;
        final blockedTasks =
            tasks.where((task) => task.blockers.isNotEmpty).length;

        final statistics = {
          'totalTasks': totalTasks,
          'completedTasks': completedTasks,
          'inProgressTasks': inProgressTasks,
          'blockedTasks': blockedTasks,
          'completionPercentage':
              totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0.0,
          'lastUpdated': DateTime.now().toIso8601String(),
        };

        return Right(statistics);
      });
    } catch (e) {
      _logger.e('Error getting project statistics for $projectId: $e');
      return Left(
        ServerFailure(message: 'Failed to get project statistics: $e'),
      );
    }
  }

  /// Fetch projects from a specific platform
  Future<List<Project>> _fetchProjectsFromPlatform(
    PlatformConfigModel config,
  ) async {
    switch (config.platformId.toLowerCase()) {
      case 'clickup':
        // For ClickUp, we'd need to list all spaces/projects
        // This would require additional API calls to list spaces
        return []; // Placeholder - would need ClickUp workspace API
      case 'notion':
        // For Notion, we'd need to search for databases
        // This would require additional API calls to search for databases
        return []; // Placeholder - would need Notion search API
      default:
        throw UnsupportedError('Platform ${config.platformId} not supported');
    }
  }

  /// Fetch a single project from a platform
  Future<Project> _fetchProjectFromPlatform(
    String projectId,
    PlatformConfigModel config,
  ) async {
    switch (config.platformId.toLowerCase()) {
      case 'clickup':
        return await _clickUpAdapter.fetchProject(projectId, config.apiKey);
      case 'notion':
        return await _notionAdapter.fetchProject(projectId, config.apiKey);
      default:
        throw UnsupportedError('Platform ${config.platformId} not supported');
    }
  }

  /// Fetch tasks from a platform
  Future<List<ProjectTask>> _fetchTasksFromPlatform(
    String projectId,
    PlatformConfigModel config,
  ) async {
    switch (config.platformId.toLowerCase()) {
      case 'clickup':
        return await _clickUpAdapter.fetchTasks(projectId, config.apiKey);
      case 'notion':
        return await _notionAdapter.fetchTasks(projectId, config.apiKey);
      default:
        throw UnsupportedError('Platform ${config.platformId} not supported');
    }
  }

  /// Fetch complete project data from a platform
  Future<ProjectData> _fetchProjectDataFromPlatform(
    String projectId,
    PlatformConfigModel config,
  ) async {
    switch (config.platformId.toLowerCase()) {
      case 'clickup':
        return await _clickUpAdapter.fetchProjectData(projectId, config.apiKey);
      case 'notion':
        return await _notionAdapter.fetchProjectData(projectId, config.apiKey);
      default:
        throw UnsupportedError('Platform ${config.platformId} not supported');
    }
  }

  /// Cache project data locally
  Future<void> _cacheProjectData(ProjectData projectData) async {
    try {
      _logger.d('Caching project data: ${projectData.project.id}');
      // Implementation would depend on your local storage solution
      // This could use Hive, SQLite, or other local storage
      // For now, this is a placeholder
    } catch (e) {
      _logger.w('Failed to cache project data: $e');
      // Don't throw here as caching failure shouldn't fail the main operation
    }
  }
}
