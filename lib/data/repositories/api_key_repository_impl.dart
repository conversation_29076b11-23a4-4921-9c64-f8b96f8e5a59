import 'package:dartz/dartz.dart';
import 'package:projectpilot/data/local/api_key_secure_storage.dart';
import 'package:projectpilot/domain/entities/api_key.dart';
import 'package:projectpilot/domain/failure/failures.dart';
import 'package:projectpilot/domain/repositories/api_key_repository.dart';

/// Implementation of ApiKeyRepository using secure storage
class ApiKeyRepositoryImpl implements ApiKeyRepository {
  final ApiKeySecureStorage _apiKeySecureStorage;

  /// Constructor for ApiKeyRepositoryImpl
  ApiKeyRepositoryImpl(this._apiKeySecureStorage);

  @override
  Future<Either<Failure, bool>> saveApiKey(ApiKey apiKey) async {
    try {
      await _apiKeySecureStorage.saveApiKey(apiKey);
      return const Right(true);
    } catch (e) {
      return Left(
        ApiKeyFailure(
          message: 'Failed to save API key: ${e.toString()}',
          keyType: apiKey.keyType,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, ApiKey?>> getApiKey(String keyType) async {
    try {
      final apiKey = await _apiKeySecureStorage.getApiKey(keyType);
      return Right(apiKey);
    } catch (e) {
      return Left(
        ApiKeyFailure(
          message: 'Failed to get API key: ${e.toString()}',
          keyType: keyType,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> deleteApiKey(String keyType) async {
    try {
      await _apiKeySecureStorage.deleteApiKey(keyType);
      return const Right(true);
    } catch (e) {
      return Left(
        ApiKeyFailure(
          message: 'Failed to delete API key: ${e.toString()}',
          keyType: keyType,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> hasApiKey(String keyType) async {
    try {
      final hasKey = await _apiKeySecureStorage.hasApiKey(keyType);
      return Right(hasKey);
    } catch (e) {
      return Left(
        ApiKeyFailure(
          message: 'Failed to check API key: ${e.toString()}',
          keyType: keyType,
        ),
      );
    }
  }
}
