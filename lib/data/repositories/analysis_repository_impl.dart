import 'package:dartz/dartz.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/project_data.dart';
import '../../domain/entities/project_analysis.dart';
import '../../domain/entities/project_task.dart';
import '../../domain/failure/failures.dart';
import '../../domain/repositories/analysis_repository.dart';
import '../../domain/repositories/project_repository.dart';

/// Implementation of AnalysisRepository using Supabase Edge Functions
class AnalysisRepositoryImpl implements AnalysisRepository {
  final SupabaseClient _supabaseClient;
  final ProjectRepository _projectRepository;
  final Logger _logger = Logger();

  AnalysisRepositoryImpl({
    required SupabaseClient supabaseClient,
    required ProjectRepository projectRepository,
  }) : _supabaseClient = supabaseClient,
       _projectRepository = projectRepository;

  @override
  Future<Either<Failure, ProjectAnalysis>> analyzeProject(
    ProjectData projectData,
  ) async {
    try {
      _logger.d('Analyzing project: ${projectData.project.id}');

      // Prepare request data
      final requestData = {
        'projectData': {
          'project': _projectToMap(projectData.project),
          'tasks': projectData.tasks.map(_taskToMap).toList(),
          'teamMembers': projectData.teamMembers.map(_teamMemberToMap).toList(),
          'budget':
              projectData.budget != null
                  ? _budgetToMap(projectData.budget!)
                  : null,
          'recentActivities':
              projectData.recentActivities.map(_activityToMap).toList(),
        },
        'analysisType': 'full',
        'includeRecommendations': true,
      };

      // Call the Supabase Edge Function
      final response = await _supabaseClient.functions.invoke(
        'project-analysis',
        body: requestData,
      );

      if (response.status != 200) {
        throw ServerFailure(
          message: 'Analysis service error: ${response.status}',
          statusCode: response.status,
        );
      }

      final responseData = response.data as Map<String, dynamic>;
      final analysis = _parseAnalysisResponse(
        responseData,
        projectData.project.id,
      );

      _logger.d('Successfully analyzed project: ${projectData.project.id}');
      return Right(analysis);
    } catch (e) {
      _logger.e('Error analyzing project: $e');
      if (e is Failure) {
        return Left(e);
      }
      return Left(ServerFailure(message: 'Failed to analyze project: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> processNaturalLanguageQuery(
    String query,
    ProjectData projectContext,
  ) async {
    try {
      _logger.d('Processing natural language query: $query');

      // Prepare request data
      final requestData = {
        'query': query,
        'projectData': {
          'project': _projectToMap(projectContext.project),
          'tasks': projectContext.tasks.map(_taskToMap).toList(),
          'teamMembers':
              projectContext.teamMembers.map(_teamMemberToMap).toList(),
          'budget':
              projectContext.budget != null
                  ? _budgetToMap(projectContext.budget!)
                  : null,
          'recentActivities':
              projectContext.recentActivities.map(_activityToMap).toList(),
        },
        'conversationHistory': <Map<String, dynamic>>[],
        'includeContext': true,
      };

      // Call the Supabase Edge Function
      final response = await _supabaseClient.functions.invoke(
        'project-query',
        body: requestData,
      );

      if (response.status != 200) {
        throw ServerFailure(
          message: 'Query service error: ${response.status}',
          statusCode: response.status,
        );
      }

      final responseData = response.data as Map<String, dynamic>;
      final queryResponse = responseData['response'] as String;

      _logger.d('Successfully processed query: $query');
      return Right(queryResponse);
    } catch (e) {
      _logger.e('Error processing query: $e');
      if (e is Failure) {
        return Left(e);
      }
      return Left(ServerFailure(message: 'Failed to process query: $e'));
    }
  }

  @override
  Future<Either<Failure, ProjectAnalysis?>> getCachedAnalysis(
    String projectId,
  ) async {
    try {
      _logger.d('Getting cached analysis for project: $projectId');
      // Implementation would depend on your local storage solution
      // For now, return null (not found)
      return const Right(null);
    } catch (e) {
      _logger.e('Error getting cached analysis for $projectId: $e');
      return Left(CacheFailure(message: 'Failed to get cached analysis: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cacheAnalysis(ProjectAnalysis analysis) async {
    try {
      _logger.d('Caching analysis: ${analysis.id}');
      // Implementation would depend on your local storage solution
      // For now, just return success
      return const Right(null);
    } catch (e) {
      _logger.e('Error caching analysis ${analysis.id}: $e');
      return Left(CacheFailure(message: 'Failed to cache analysis: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ProjectAnalysis>>> getAnalysisHistory(
    String projectId,
  ) async {
    try {
      _logger.d('Getting analysis history for project: $projectId');
      // Implementation would depend on your local storage solution
      // For now, return empty list
      return const Right([]);
    } catch (e) {
      _logger.e('Error getting analysis history for $projectId: $e');
      return Left(ServerFailure(message: 'Failed to get analysis history: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> generatePortfolioInsights(
    List<ProjectData> projects,
  ) async {
    try {
      _logger.d(
        'Generating portfolio insights for ${projects.length} projects',
      );

      if (projects.isEmpty) {
        return Left(
          ValidationFailure(
            message: 'No projects provided for portfolio analysis',
          ),
        );
      }

      // Analyze each project individually first
      final List<ProjectAnalysis> individualAnalyses = [];

      for (final project in projects) {
        final analysisResult = await analyzeProject(project);

        analysisResult.fold(
          (failure) => _logger.w(
            'Failed to analyze project ${project.project.id}: $failure',
          ),
          (analysis) => individualAnalyses.add(analysis),
        );
      }

      if (individualAnalyses.isEmpty) {
        return Left(
          ServerFailure(message: 'Failed to analyze any projects in portfolio'),
        );
      }

      // Aggregate insights from individual analyses
      final portfolioInsights = _aggregatePortfolioInsights(
        individualAnalyses,
        projects,
      );

      _logger.d('Successfully generated portfolio insights');
      return Right(portfolioInsights);
    } catch (e) {
      _logger.e('Error generating portfolio insights: $e');
      return Left(
        ServerFailure(message: 'Failed to generate portfolio insights: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, List<ActionRecommendation>>>
  getManagementRecommendations(ProjectData projectData) async {
    try {
      _logger.d(
        'Getting management recommendations for project: ${projectData.project.id}',
      );

      // Use the analysis service to get recommendations
      final analysisResult = await analyzeProject(projectData);

      return analysisResult.fold(
        (failure) => Left(failure),
        (analysis) => Right(analysis.recommendations),
      );
    } catch (e) {
      _logger.e('Error getting management recommendations: $e');
      return Left(
        ServerFailure(message: 'Failed to get management recommendations: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> predictProjectRisks(
    ProjectData projectData,
  ) async {
    try {
      _logger.d('Predicting risks for project: ${projectData.project.id}');

      // Use the analysis service with focus on risk assessment
      final analysisResult = await analyzeProject(projectData);

      return analysisResult.fold((failure) => Left(failure), (analysis) {
        final riskPrediction = {
          'overallRiskScore': analysis.riskScore,
          'riskLevel': _getRiskLevel(analysis.riskScore),
          'criticalBlockers':
              analysis.blockers
                  .where((b) => b.severity == BlockerSeverity.critical)
                  .map(
                    (b) => {
                      'id': b.id,
                      'description': b.description,
                      'taskName': b.taskName,
                    },
                  )
                  .toList(),
          'highRiskTasks':
              analysis.blockers
                  .where((b) => b.severity == BlockerSeverity.major)
                  .map((b) => b.taskName)
                  .toSet()
                  .toList(),
          'keyInsights': analysis.keyInsights,
          'recommendedActions':
              analysis.recommendations
                  .where((r) => r.priority == RecommendationPriority.urgent)
                  .map((r) => r.title)
                  .toList(),
        };

        return Right(riskPrediction);
      });
    } catch (e) {
      _logger.e('Error predicting project risks: $e');
      return Left(
        ServerFailure(message: 'Failed to predict project risks: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, String>> generateExecutiveSummary(
    ProjectData projectData,
  ) async {
    try {
      _logger.d(
        'Generating executive summary for project: ${projectData.project.id}',
      );

      // Use the analysis service to generate summary
      final analysisResult = await analyzeProject(projectData);

      return analysisResult.fold((failure) => Left(failure), (analysis) {
        final summary = _buildExecutiveSummary(analysis, projectData);
        return Right(summary);
      });
    } catch (e) {
      _logger.e('Error generating executive summary: $e');
      return Left(
        ServerFailure(message: 'Failed to generate executive summary: $e'),
      );
    }
  }

  // Helper methods to convert domain entities to Maps for API calls

  Map<String, dynamic> _projectToMap(dynamic project) {
    return {
      'id': project.id,
      'name': project.name,
      'description': project.description,
      'status': project.status.toString(),
      'progress': project.progress,
      'platformId': project.platformId,
      'deadline': project.deadline?.toIso8601String(),
      'createdAt': project.createdAt.toIso8601String(),
      'updatedAt': project.updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> _taskToMap(dynamic task) {
    return {
      'id': task.id,
      'name': task.name,
      'description': task.description,
      'status': task.status.toString(),
      'priority': task.priority.toString(),
      'assignee': task.assignee,
      'deadline': task.deadline?.toIso8601String(),
      'progress': task.progress,
      'estimatedHours': task.estimatedHours,
      'actualHours': task.actualHours,
      'blockers': task.blockers,
    };
  }

  Map<String, dynamic> _teamMemberToMap(dynamic member) {
    return {
      'id': member.id,
      'name': member.name,
      'email': member.email,
      'role': member.role,
      'isActive': member.isActive,
      'workload': member.workload,
    };
  }

  Map<String, dynamic> _budgetToMap(dynamic budget) {
    return {
      'total': budget.total,
      'spent': budget.spent,
      'remaining': budget.remaining,
      'isOverBudget': budget.isOverBudget,
    };
  }

  Map<String, dynamic> _activityToMap(dynamic activity) {
    return {
      'id': activity.id,
      'action': activity.action,
      'description': activity.description,
      'userName': activity.userName,
      'timestamp': activity.timestamp.toIso8601String(),
    };
  }

  // Helper method to parse analysis response from Edge Function
  ProjectAnalysis _parseAnalysisResponse(
    Map<String, dynamic> response,
    String projectId,
  ) {
    final analysisData = response['analysis'] as Map<String, dynamic>;

    // Parse blockers
    final blockersData = analysisData['blockers'] as List<dynamic>? ?? [];
    final blockers =
        blockersData.map<ProjectBlocker>((blocker) {
          return ProjectBlocker(
            id: blocker['id'] as String? ?? const Uuid().v4(),
            taskId: blocker['taskId'] as String? ?? '',
            taskName:
                blocker['taskName'] as String? ??
                blocker['name'] as String? ??
                'Unknown Task',
            description: blocker['description'] as String,
            severity: _parseBlockerSeverity(
              blocker['severity'] as String? ?? 'medium',
            ),
            identifiedAt: DateTime.now(),
            suggestedActions: List<String>.from(
              blocker['suggestedActions'] ?? [],
            ),
          );
        }).toList();

    // Parse recommendations
    final recommendationsData =
        analysisData['recommendations'] as List<dynamic>? ?? [];
    final recommendations =
        recommendationsData.map<ActionRecommendation>((rec) {
          return ActionRecommendation(
            id: rec['id'] as String? ?? const Uuid().v4(),
            title: rec['title'] as String,
            description: rec['description'] as String,
            priority: _parseRecommendationPriority(
              rec['priority'] as String? ?? 'medium',
            ),
            category: rec['category'] as String? ?? 'general',
            generatedAt: DateTime.now(),
          );
        }).toList();

    // Parse critical paths
    final criticalPathData =
        analysisData['criticalPath'] as List<dynamic>? ?? [];
    final criticalPaths =
        criticalPathData.map<CriticalPath>((path) {
          return CriticalPath(
            id: path['id'] as String? ?? const Uuid().v4(),
            taskIds: List<String>.from(
              path['taskIds'] ?? [path['taskId']] ?? [],
            ),
            totalDuration:
                (path['totalDuration'] as num?)?.toInt() ??
                (path['estimatedDuration'] as num?)?.toInt() ??
                1,
            riskLevel: path['riskLevel'] as String? ?? 'medium',
            description:
                path['description'] as String? ??
                'Critical path for ${path['taskName'] ?? 'project tasks'}',
          );
        }).toList();

    return ProjectAnalysis(
      id: const Uuid().v4(),
      projectId: projectId,
      progressPercentage:
          (response['progressPercentage'] as num?)?.toDouble() ?? 0.0,
      summary: analysisData['summary'] as String? ?? '',
      blockers: blockers,
      recommendations: recommendations,
      criticalPaths: criticalPaths,
      keyInsights: List<String>.from(analysisData['keyInsights'] ?? []),
      riskScore: (response['riskScore'] as num?)?.toDouble() ?? 0.0,
      confidenceLevel: (response['confidence'] as num?)?.toDouble() ?? 0.0,
      analyzedAt: DateTime.now(),
    );
  }

  // Helper methods to parse enums
  BlockerSeverity _parseBlockerSeverity(String severity) {
    switch (severity.toLowerCase()) {
      case 'minor':
        return BlockerSeverity.minor;
      case 'moderate':
        return BlockerSeverity.moderate;
      case 'major':
        return BlockerSeverity.major;
      case 'critical':
        return BlockerSeverity.critical;
      default:
        return BlockerSeverity.moderate;
    }
  }

  RecommendationPriority _parseRecommendationPriority(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
        return RecommendationPriority.low;
      case 'medium':
        return RecommendationPriority.medium;
      case 'high':
        return RecommendationPriority.high;
      case 'urgent':
        return RecommendationPriority.urgent;
      case 'critical':
        return RecommendationPriority.critical;
      default:
        return RecommendationPriority.medium;
    }
  }

  // Helper method to aggregate portfolio insights
  Map<String, dynamic> _aggregatePortfolioInsights(
    List<ProjectAnalysis> analyses,
    List<ProjectData> projects,
  ) {
    // Aggregate all blockers and recommendations
    final allBlockers = <ProjectBlocker>[];
    final allRecommendations = <ActionRecommendation>[];
    final allKeyInsights = <String>[];

    double totalRiskScore = 0.0;
    double totalProgress = 0.0;
    int criticalRiskCount = 0;
    int highRiskCount = 0;

    for (final analysis in analyses) {
      allBlockers.addAll(analysis.blockers);
      allRecommendations.addAll(analysis.recommendations);
      allKeyInsights.addAll(analysis.keyInsights);
      totalRiskScore += analysis.riskScore;
      totalProgress += analysis.progressPercentage;

      if (analysis.riskScore > 0.8) {
        criticalRiskCount++;
      } else if (analysis.riskScore > 0.6) {
        highRiskCount++;
      }
    }

    final avgRiskScore =
        analyses.isNotEmpty ? totalRiskScore / analyses.length : 0.0;
    final avgProgress =
        analyses.isNotEmpty ? totalProgress / analyses.length : 0.0;

    return {
      'portfolioSize': projects.length,
      'analyzedProjects': analyses.length,
      'averageProgress': avgProgress,
      'averageRiskScore': avgRiskScore,
      'totalBlockers': allBlockers.length,
      'criticalRiskProjects': criticalRiskCount,
      'highRiskProjects': highRiskCount,
      'topRecommendations':
          allRecommendations
              .where((r) => r.priority == RecommendationPriority.urgent)
              .map((r) => r.title)
              .take(5)
              .toList(),
      'keyInsights': allKeyInsights.take(10).toList(),
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  String _getRiskLevel(double riskScore) {
    if (riskScore >= 0.8) return 'Critical';
    if (riskScore >= 0.6) return 'High';
    if (riskScore >= 0.4) return 'Medium';
    return 'Low';
  }

  String _buildExecutiveSummary(
    ProjectAnalysis analysis,
    ProjectData projectData,
  ) {
    final project = projectData.project;
    final tasksCount = projectData.tasks.length;
    final completedTasks =
        projectData.tasks.where((t) => t.progress >= 1.0).length;
    final criticalBlockers =
        analysis.blockers
            .where((b) => b.severity == BlockerSeverity.critical)
            .length;

    return '''
Executive Summary for ${project.name}

Progress: ${analysis.progressPercentage.toStringAsFixed(1)}% complete
Tasks: $completedTasks of $tasksCount completed
Risk Level: ${_getRiskLevel(analysis.riskScore)}

${analysis.summary}

Key Concerns:
${criticalBlockers > 0 ? '• $criticalBlockers critical blockers requiring immediate attention' : '• No critical blockers identified'}
${analysis.recommendations.where((r) => r.priority == RecommendationPriority.urgent).isNotEmpty ? '• Urgent recommendations available for review' : '• No urgent actions required'}

Next Steps:
${analysis.recommendations.take(3).map((r) => '• ${r.title}').join('\n')}

Generated on ${DateTime.now().toString().substring(0, 16)}
''';
  }
}
