import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:projectpilot/domain/entities/user_profile.dart';
import 'package:projectpilot/domain/failure/failures.dart';
import 'package:projectpilot/domain/repositories/user_repository.dart';
import 'package:projectpilot/domain/repositories/token_repository.dart';

import 'package:uuid/uuid.dart';

/// Implementation of UserRepository
class UserRepositoryImpl implements UserRepository {
  final SupabaseClient _supabaseClient;
  final TokenRepository _tokenRepository;

  /// Constructor for UserRepositoryImpl
  UserRepositoryImpl({
    required SupabaseClient supabaseClient,
    required TokenRepository tokenRepository,
  }) : _supabaseClient = supabaseClient,
       _tokenRepository = tokenRepository;

  @override
  Future<Either<Failure, UserProfile?>> getCurrentUser() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        return const Right(null);
      }

      // Try to get user from the database first
      try {
        final response =
            await _supabaseClient
                .from('users')
                .select()
                .eq('id', user.id)
                .single();

        // Build user profile from database record
        return Right(
          UserProfile(
            id: user.id,
            email: response['email'] as String?,
            username: response['username'] as String?,
            profileImageUrl: response['profile_image_url'] as String?,
            birthYear: response['birth_year'] as int?,
            role: response['role'] as String?,
            goals:
                response['goals'] != null
                    ? List<String>.from(response['goals'] as List)
                    : const [],
            completedOnboarding:
                response['completed_onboarding'] as bool? ?? false,
          ),
        );
      } catch (e) {
        // If not found in database, create from auth user
        final userProfile = UserProfile(
          id: user.id,
          email: user.email,
          username: user.userMetadata?['username'] as String?,
          profileImageUrl: user.userMetadata?['profileImageUrl'] as String?,
          completedOnboarding:
              user.userMetadata?['completedOnboarding'] as bool? ?? false,
        );

        return Right(userProfile);
      }
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get current user: $e'));
    }
  }

  @override
  Future<Either<Failure, UserProfile>> updateUserProfile(
    UserProfile profile,
  ) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      // Check if user exists in the 'users' table
      try {
        await _supabaseClient.from('users').select().eq('id', user.id).single();

        // User exists, update the record
        await _supabaseClient
            .from('users')
            .update({
              'email': profile.email,
              'username': profile.username,
              'profile_image_url': profile.profileImageUrl,
              'birth_year': profile.birthYear,
              'role': profile.role,
              'goals': profile.goals,
              'completed_onboarding': profile.completedOnboarding,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', user.id);
      } catch (e) {
        // User doesn't exist in the table, insert a new record
        await _supabaseClient.from('users').insert({
          'id': user.id, // Use the auth user ID as the primary key
          'email': profile.email,
          'username': profile.username,
          'profile_image_url': profile.profileImageUrl,
          'birth_year': profile.birthYear,
          'role': profile.role,
          'goals': profile.goals,
          'completed_onboarding': profile.completedOnboarding,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      // Update user metadata in Supabase auth
      await _supabaseClient.auth.updateUser(
        UserAttributes(
          data: {
            'username': profile.username,
            'profileImageUrl': profile.profileImageUrl,
            'role': profile.role,
            'goals': profile.goals,
            'completedOnboarding': profile.completedOnboarding,
          },
        ),
      );

      return Right(profile);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update user profile: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteUserAccount(String userId) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null || user.id != userId) {
        return Left(
          AuthFailure(message: 'User not authenticated or unauthorized'),
        );
      }

      // Delete user from the database
      await _supabaseClient.from('users').delete().eq('id', userId);

      // Delete auth user (this would require admin privileges in practice)
      // await _supabaseClient.auth.admin.deleteUser(userId);

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to delete user account: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> userExists(String userId) async {
    try {
      final response =
          await _supabaseClient
              .from('users')
              .select('id')
              .eq('id', userId)
              .maybeSingle();

      return Right(response != null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to check if user exists: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getUserPreferences(
    String userId,
  ) async {
    try {
      final response =
          await _supabaseClient
              .from('user_preferences')
              .select('preferences')
              .eq('user_id', userId)
              .maybeSingle();

      if (response == null) {
        // Return default preferences if none exist
        return const Right({
          'theme': 'system',
          'language': 'en',
          'notifications': true,
          'autoAssign': false,
        });
      }

      return Right(response['preferences'] as Map<String, dynamic>);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get user preferences: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateUserPreferences(
    String userId,
    Map<String, dynamic> preferences,
  ) async {
    try {
      // Check if preferences exist
      final existingResponse =
          await _supabaseClient
              .from('user_preferences')
              .select('id')
              .eq('user_id', userId)
              .maybeSingle();

      if (existingResponse != null) {
        // Update existing preferences
        await _supabaseClient
            .from('user_preferences')
            .update({
              'preferences': preferences,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('user_id', userId);
      } else {
        // Insert new preferences
        await _supabaseClient.from('user_preferences').insert({
          'user_id': userId,
          'preferences': preferences,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      return const Right(null);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to update user preferences: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, UserProfile>> getCurrentUserProfile() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      // Try to get user from the database first
      try {
        final response =
            await _supabaseClient
                .from('users')
                .select()
                .eq('id', user.id)
                .single();

        // Build user profile from database record
        return Right(
          UserProfile(
            id: user.id,
            email: response['email'] as String?,
            username: response['username'] as String?,
            profileImageUrl: response['profile_image_url'] as String?,
            birthYear: response['birth_year'] as int?,
            completedOnboarding:
                response['completed_onboarding'] as bool? ?? false,
          ),
        );
      } catch (e) {
        // If not found in database, create from auth user
        final userProfile = UserProfile(
          id: user.id,
          email: user.email,
          username: user.userMetadata?['username'] as String?,
          profileImageUrl: user.userMetadata?['profileImageUrl'] as String?,
          completedOnboarding:
              user.userMetadata?['completedOnboarding'] as bool? ?? false,
        );

        return Right(userProfile);
      }
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to get current user profile: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, int>> getTokenBalance() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      final tokenBalanceResult = await _tokenRepository.getTokenBalance(
        user.id,
      );
      return tokenBalanceResult.fold(
        (failure) => Left(failure),
        (tokenBalance) => Right(tokenBalance?['availableTokens'] as int? ?? 0),
      );
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get token balance: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getMinutesBalance() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      // Get user's minutes balance from database
      final response =
          await _supabaseClient
              .from('user_minutes')
              .select('minutes_balance')
              .eq('user_id', user.id)
              .maybeSingle();

      return Right(response?['minutes_balance'] as int? ?? 0);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get minutes balance: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> uploadProfileImage(File imageFile) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      // Generate a unique filename
      final uuid = const Uuid();
      final fileExtension = imageFile.path.split('.').last;
      final fileName = '${user.id}/${uuid.v4()}.$fileExtension';

      // Upload the file to Supabase Storage
      final bytes = await imageFile.readAsBytes();
      await _supabaseClient.storage
          .from('profile-images')
          .uploadBinary(fileName, bytes);

      // Get the public URL
      final imageUrl = _supabaseClient.storage
          .from('profile-images')
          .getPublicUrl(fileName);

      return Right(imageUrl);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to upload profile image: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      await _supabaseClient.auth.signOut();
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to logout: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> upgradePackage(String packageId) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        return Left(AuthFailure(message: 'User not authenticated'));
      }

      // Update user's package in database
      await _supabaseClient
          .from('users')
          .update({
            'package_id': packageId,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', user.id);

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to upgrade package: $e'));
    }
  }
}
