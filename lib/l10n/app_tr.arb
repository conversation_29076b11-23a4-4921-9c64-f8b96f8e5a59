{"@@locale": "tr", "appTitle": "ProjectPilot", "homeTitle": "<PERSON>", "settingsTitle": "<PERSON><PERSON><PERSON>", "welcome": "<PERSON><PERSON> Geldiniz", "@welcome": {"description": "Welcome text on splash screen"}, "welcomeSubtitle": "ProjectPilot – proje yönetiminin gel<PERSON>", "@welcomeSubtitle": {"description": "Subtitle on splash screen"}, "languageTitle": "Dil", "englishLanguage": "İngilizce", "germanLanguage": "Almanca", "russianLanguage": "Rus<PERSON>", "turkishLanguage": "Türkçe", "arabicLanguage": "<PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON>", "cancelButton": "İptal", "errorOccurred": "<PERSON>ir hata o<PERSON>", "tryAgain": "<PERSON><PERSON><PERSON>", "selectLanguage": "<PERSON><PERSON>", "tokenBalance": "Token Bakiyesi: {count}", "transcriptionTitle": "Transkripsiyon", "startRecording": "<PERSON><PERSON><PERSON>", "stopRecording": "Kaydı Durdur", "noTranscriptionYet": "Henüz transkripsiyon yok", "shareTranscription": "Transkripsiyonu <PERSON>", "themeTitle": "<PERSON><PERSON>", "selectTheme": "<PERSON><PERSON>", "systemTheme": "Sistem Varsayılanı", "lightTheme": "Açık", "darkTheme": "<PERSON><PERSON>", "toggleTheme": "Temayı Değiştir", "apiKeysSection": "API Anahtarları", "openaiApiKey": "OpenAI API Anahtarı", "whisperApiKey": "Whisper API Anahtarı", "apiKeyHint": "API anahtarınızı girin", "tokenSection": "Tokenler ve Kullanım", "remainingMinutes": "<PERSON><PERSON>: {count}", "purchasePackages": "Paket Satın Al", "buyTokens": "Token Satın Al", "starterPackage": "Başlangıç", "proPackage": "Pro", "businessPackage": "İş", "ultimatePackage": "Ultimate", "tokens": "{count} Token", "price": "€{price}", "profileSection": "Profil", "username": "Kullanıcı Adı", "email": "E-posta", "notAvailable": "<PERSON><PERSON><PERSON>", "logoutButton": "Çıkış Yap", "apiKeySaved": "API anahtarı başarıyla kaydedildi!", "introScreen1Title": "AI-Destekli Pro<PERSON>", "@introScreen1Title": {"description": "Title for first intro screen"}, "introScreen1Description": "Projelerinizde kırmızı çizgiyi koruyun. Sorunları erken tespit edin ve akıllı içgörülerle yolda kalın.", "@introScreen1Description": {"description": "Description for first intro screen"}, "introScreen2Title": "Çoklu-Platform Proje Genel Bakış", "@introScreen2Title": {"description": "Title for second intro screen"}, "introScreen2Description": "Tüm proje araçlarınızı tek bir panelde birleştirin. ClickUp, Notion, Jira ve daha fazlası - hepsi senkronize.", "@introScreen2Description": {"description": "Description for second intro screen"}, "introScreen3Title": "Akıllı Problem Tespiti", "@introScreen3Title": {"description": "Title for third intro screen"}, "introScreen3Description": "Engelleri otomatik olarak tespit edin, riskleri değerlendirin ve projelerinizi hareket halinde tutmak için eylem odaklı öneriler alın.", "@introScreen3Description": {"description": "Description for third intro screen"}, "introMainTitle": "ProjectPilot", "@introMainTitle": {"description": "Main title for intro screens"}, "introMainSubtitle": "Projelerinizin izini bir daha asla ka<PERSON>n", "@introMainSubtitle": {"description": "Main subtitle for intro screens"}, "getStarted": "Başlayın", "@getStarted": {"description": "Text for get started button"}, "skip": "Atla", "@skip": {"description": "Text for skip button on intro screens"}, "next": "İleri", "@next": {"description": "Text for next button on intro screens"}, "loginTitle": "ProjectPilot'a Ho<PERSON> Geldiniz", "loginSubtitle": "<PERSON><PERSON> etmek i<PERSON>in giriş ya<PERSON>ın", "continueWithGoogle": "Google ile devam et", "continueWithApple": "Apple ile devam et", "continueWithEmail": "E-posta ile devam et", "moreOptions": "<PERSON><PERSON>", "continueWithLinkedIn": "LinkedIn ile devam et", "continueWithPhone": "Telefon ile devam et", "continueWithAzure": "Azure ile devam et", "continueWithNotion": "Notion ile devam et", "password": "Şifre", "forgotPassword": "Şifrenizi mi unuttunuz?", "signIn": "<PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON>", "dontHaveAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "alreadyHaveAccount": "Zaten hesabınız var mı?", "phoneNumber": "Telefon Numarası", "or": "veya", "createAccountText": "ProjectPilot hesabınızı oluşturun", "@createAccountText": {"description": "Subtitle text for sign-up screen"}, "usernameRequired": "Kullanıcı adı gereklidir", "@usernameRequired": {"description": "Error message when username is empty"}, "usernameMinLength": "Kullanıcı adı en az 3 karakter olmalıdır", "@usernameMinLength": {"description": "Error message when username is too short"}, "emailRequired": "E-posta gereklidir", "@emailRequired": {"description": "Error message when email is empty"}, "invalidEmail": "Geçerli bir e-posta girin", "@invalidEmail": {"description": "Error message when email format is invalid"}, "passwordRequired": "<PERSON><PERSON><PERSON>", "@passwordRequired": {"description": "Error message when password is empty"}, "passwordMinLength": "Şifre en az 6 karakter olmalıdır", "@passwordMinLength": {"description": "Error message when password is too short"}, "signUpSuccessful": "<PERSON><PERSON>t başarılı!", "@signUpSuccessful": {"description": "Success message after signing up"}, "onboarding": "Profili<PERSON><PERSON>", "@onboarding": {"description": "Title for the onboarding screen"}, "camera": "<PERSON><PERSON><PERSON>", "@camera": {"description": "Option for taking a photo with camera"}, "gallery": "<PERSON><PERSON>", "@gallery": {"description": "Option for selecting a photo from gallery"}, "birthYear": "Doğum Yılı", "@birthYear": {"description": "Label for birth year selection"}, "selectBirthYear": "Doğum Yılı Seçin", "@selectBirthYear": {"description": "Title for birth year picker"}, "userRole": "Kullanıcı Rolü", "@userRole": {"description": "Label for user role selection"}, "developer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@developer": {"description": "Developer role option"}, "projectManager": "<PERSON><PERSON>", "@projectManager": {"description": "Project Manager role option"}, "private": "<PERSON><PERSON>", "@private": {"description": "Private role option"}, "other": "<PERSON><PERSON><PERSON>", "@other": {"description": "Other option for selections"}, "usagePurpose": "Ku<PERSON><PERSON>m Amacı", "@usagePurpose": {"description": "Label for usage purpose selection"}, "tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@tasks": {"description": "Tasks purpose option"}, "ideas": "Fikirler", "@ideas": {"description": "Ideas purpose option"}, "notes": "Notlar", "@notes": {"description": "Notes purpose option"}, "connectPlatforms": "Platformları Bağla", "@connectPlatforms": {"description": "Title for the platform connection screen"}, "platformsDescription": "Favori platformlarınıza bağlanın", "@platformsDescription": {"description": "Description text for platform connection screen"}, "connectMore": "<PERSON>ha fazla platformu daha sonra ba<PERSON>la", "@connectMore": {"description": "Text for connecting more platforms in the future"}, "finish": "Bitir", "@finish": {"description": "Text for finish button"}, "platformConnections": "Platform Bağlantıları", "@platformConnections": {"description": "Title for the platform connections screen"}, "connectToPlatform": "{platform}'a bağlan", "@connectToPlatform": {"description": "Button text to connect to a specific platform", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "connected": "Bağlandı", "@connected": {"description": "Status text when a platform is connected"}, "disconnected": "Bağlı Değil", "@disconnected": {"description": "Status text when a platform is not connected"}, "disconnect": "Bağlantıyı Kes", "@disconnect": {"description": "Button text to disconnect from a platform"}, "connecting": "Bağlanıyor...", "@connecting": {"description": "Status text when connecting to a platform"}, "authorizationFailed": "Yetkilendirme Başarısız", "@authorizationFailed": {"description": "Error message when platform authorization fails"}, "connectionSuccessful": "Bağlantı Başarılı", "@connectionSuccessful": {"description": "Success message when platform connection is established"}, "connectionFailed": "Bağlantı Başarısız", "@connectionFailed": {"description": "Error message when platform connection fails"}, "integrationAvailableFor": "{platform} i<PERSON>in entegrasyon mevcut", "@integrationAvailableFor": {"description": "Text indicating a platform integration is available", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "allPlatforms": "<PERSON><PERSON><PERSON>", "@allPlatforms": {"description": "Text for showing all available platforms"}, "managePlatformConnections": "Platform bağlantılarını yönet", "@managePlatformConnections": {"description": "Description for the platform connections screen"}, "connectPlatformToSendTasks": "Görevleri doğrudan göndermek için favori platformlarınızı bağlayın", "@connectPlatformToSendTasks": {"description": "Explanation text for platform connections"}, "cancel": "İptal", "@cancel": {"description": "Text for cancel button"}, "continueButton": "<PERSON><PERSON>", "@continueButton": {"description": "Text for continue button"}, "whatWouldYouLikeToDo": "Ne yapmak is<PERSON><PERSON>z?", "@whatWouldYouLikeToDo": {"description": "Prompt text for the recording screen"}, "recordingInProgress": "Kay<PERSON>t devam ediyor...", "@recordingInProgress": {"description": "Text shown while recording is in progress"}, "processingYourVoice": "<PERSON><PERSON><PERSON>leniyor...", "@processingYourVoice": {"description": "Text shown while transcribing audio"}, "whereToSave": "<PERSON>unu nereye kaydetmek istersiniz?", "@whereToSave": {"description": "Prompt for selecting a platform to save to"}, "noPlatformsConnected": "Henüz bağlı platform yok", "@noPlatformsConnected": {"description": "Text shown when no platforms are connected"}, "newRecording": "<PERSON><PERSON>", "@newRecording": {"description": "<PERSON><PERSON> text to start a new recording"}, "task": "<PERSON><PERSON><PERSON><PERSON>", "@task": {"description": "Label for task category"}, "note": "Not", "@note": {"description": "Label for note category"}, "idea": "Fikir", "@idea": {"description": "Label for idea category"}, "uncategorized": "Kategor<PERSON>z", "@uncategorized": {"description": "Label for uncategorized items"}, "edit": "<PERSON><PERSON><PERSON><PERSON>", "@edit": {"description": "Text for edit button"}, "share": "Paylaş", "@share": {"description": "Text for share button"}, "transcriptionResult": "Transkripsiyon <PERSON>ucu", "@transcriptionResult": {"description": "Title for transcription result"}, "detectedIntent": "Algılanan Niyet", "@detectedIntent": {"description": "Label for detected intent"}, "detectedEntities": "Algılanan Varlıklar", "@detectedEntities": {"description": "Label for detected entities"}, "originalTranscription": "Orijinal Transkripsiyon", "@originalTranscription": {"description": "Label for original transcription"}, "entries": "<PERSON><PERSON><PERSON><PERSON>", "@entries": {"description": "Title for entries screen"}, "entryDetails": "<PERSON><PERSON><PERSON>", "@entryDetails": {"description": "Title for entry details screen"}, "addUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@addUpdate": {"description": "Button text to add an update to an entry"}, "updateDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@updateDetails": {"description": "Title for update details dialog"}, "deleteUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@deleteUpdate": {"description": "Title for delete update confirmation dialog"}, "deleteUpdateConfirmation": "<PERSON>u güncellemeyi silmek istediğinizden emin misiniz?", "@deleteUpdateConfirmation": {"description": "Confirmation message for deleting an update"}, "deleteEntry": "<PERSON><PERSON><PERSON><PERSON>", "@deleteEntry": {"description": "Title for delete entry confirmation dialog"}, "deleteEntryConfirmation": "Bu girdiyi ve tüm güncellemelerini silmek istediğinizden emin misiniz?", "@deleteEntryConfirmation": {"description": "Confirmation message for deleting an entry"}, "entryCreated": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> oluşturuldu", "@entryCreated": {"description": "Success message when an entry is created"}, "entryDeleted": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "@entryDeleted": {"description": "Success message when an entry is deleted"}, "entryUpdateAdded": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarı<PERSON> e<PERSON>ndi", "@entryUpdateAdded": {"description": "Success message when an update is added to an entry"}, "noEntriesFound": "<PERSON><PERSON><PERSON> b<PERSON>", "@noEntriesFound": {"description": "Message when no entries are found"}, "createEntryPrompt": "+ dü<PERSON><PERSON><PERSON> dokunarak yeni bir girdi o<PERSON>", "@createEntryPrompt": {"description": "Prompt to create a new entry"}, "createEntry": "<PERSON><PERSON><PERSON>", "@createEntry": {"description": "Button text to create a new entry"}, "content": "İçerik", "@content": {"description": "Label for content input field"}, "type": "<PERSON><PERSON><PERSON>", "@type": {"description": "Label for type selection"}, "create": "Oluştur", "@create": {"description": "Button text to create something"}, "search": "Ara", "@search": {"description": "Button text for search"}, "close": "Ka<PERSON><PERSON>", "@close": {"description": "Button text to close a dialog or screen"}, "save": "<PERSON><PERSON>", "@save": {"description": "Button text to save changes"}, "editEntry": "<PERSON><PERSON><PERSON><PERSON>", "@editEntry": {"description": "Title for edit entry dialog"}, "sharingNotImplemented": "Paylaşma özelliği henüz uygulanmadı", "@sharingNotImplemented": {"description": "Message when sharing functionality is not implemented"}, "updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@updates": {"description": "Label for updates section"}, "voice": "Ses", "@voice": {"description": "Label for voice source type"}, "manual": "<PERSON>", "@manual": {"description": "Label for manual source type"}, "addedAt": "Eklenme zamanı", "@addedAt": {"description": "Label for when an update was added"}, "source": "<PERSON><PERSON><PERSON>", "@source": {"description": "Label for source of an update"}, "created": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "@created": {"description": "Label for creation date"}, "selectEntry": "<PERSON><PERSON><PERSON>", "@selectEntry": {"description": "Title for entry selection dialog"}, "entryReferenceDetected": "Mevcut bir girdiye referans tespit ettim", "@entryReferenceDetected": {"description": "Message when a reference to an existing entry is detected"}, "selectEntryToUpdate": "Güncellemek istediğiniz girdiyi seçin", "@selectEntryToUpdate": {"description": "Prompt to select an entry to update"}, "createNewInstead": "<PERSON><PERSON><PERSON> yerine yeni <PERSON>", "@createNewInstead": {"description": "Button text to create a new entry instead of updating an existing one"}, "creatingNewEntry": "<PERSON><PERSON><PERSON> yerine yeni bir girdi o<PERSON>r", "@creatingNewEntry": {"description": "Message when creating a new entry instead of updating an existing one"}, "all": "Tümü", "@all": {"description": "Label for showing all items"}, "chatHistory": "So<PERSON>bet Geçmişi", "@chatHistory": {"description": "Title for the chat history section in recording view"}, "authenticating": "Kimlik doğrulanıyor...", "@authenticating": {"description": "Text shown during authentication process"}, "createAccount": "<PERSON><PERSON><PERSON>", "@createAccount": {"description": "Button text for creating a new account"}, "login": "<PERSON><PERSON><PERSON>", "@login": {"description": "Login button text"}, "confirmPassword": "<PERSON><PERSON><PERSON><PERSON>", "@confirmPassword": {"description": "Label for confirm password field"}, "confirmPasswordHint": "Şifrenizi onaylayın", "@confirmPasswordHint": {"description": "Hint text for confirm password field"}, "confirmPasswordRequired": "Lütfen şifrenizi onaylayın", "@confirmPasswordRequired": {"description": "Error message when confirm password is empty"}, "passwordsDoNotMatch": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor", "@passwordsDoNotMatch": {"description": "Error message when passwords do not match"}, "nameFieldHint": "Tam adınızı girin", "@nameFieldHint": {"description": "Hint text for name input field"}, "connectedSuccessfully": "Başarıyla bağlandı!", "@connectedSuccessfully": {"description": "Success message when connection is established"}, "riskAssessment": "<PERSON>", "@riskAssessment": {"description": "Title for risk assessment section"}, "budgetOverview": "Bütçe Genel Bakış", "@budgetOverview": {"description": "Title for budget overview section"}, "keyInsights": "<PERSON><PERSON>ö<PERSON>", "@keyInsights": {"description": "Title for key insights section"}, "errorLoadingAnalysis": "<PERSON><PERSON><PERSON>", "@errorLoadingAnalysis": {"description": "Error message when analysis fails to load"}, "retry": "<PERSON><PERSON><PERSON>", "@retry": {"description": "Button text to retry an operation"}, "tapAnalyzeToGetInsights": "AI görüşleri almak için analiz et'e dokunun", "@tapAnalyzeToGetInsights": {"description": "Instruction text for analyzing project"}, "analyzeProject": "<PERSON><PERSON><PERSON>", "@analyzeProject": {"description": "Button text to analyze project"}, "aiAnalysisSummary": "AI Analiz <PERSON>", "@aiAnalysisSummary": {"description": "Title for AI analysis summary section"}, "active": "Aktif", "@active": {"description": "Status text for active items"}, "inactive": "<PERSON><PERSON><PERSON>", "@inactive": {"description": "Status text for inactive items"}, "overBudget": "Bütçe Aşımı", "@overBudget": {"description": "Label when project is over budget"}, "totalBudget": "Toplam Bütçe", "@totalBudget": {"description": "Label for total budget amount"}, "spent": "<PERSON><PERSON><PERSON>", "@spent": {"description": "Label for spent amount"}, "remaining": "<PERSON><PERSON>", "@remaining": {"description": "Label for remaining amount"}, "analysisDetails": "<PERSON><PERSON><PERSON>", "@analysisDetails": {"description": "Title for analysis details section"}, "confidenceLevelLabel": "<PERSON><PERSON><PERSON>", "@confidenceLevelLabel": {"description": "Label for confidence level"}, "analysisDate": "<PERSON><PERSON><PERSON>", "@analysisDate": {"description": "Label for analysis date"}, "predictedCompletion": "<PERSON><PERSON><PERSON>", "@predictedCompletion": {"description": "Label for predicted completion date"}, "critical": "<PERSON><PERSON><PERSON>", "@critical": {"description": "Critical priority/severity level"}, "high": "<PERSON><PERSON><PERSON><PERSON>", "@high": {"description": "High priority/severity level"}, "medium": "Orta", "@medium": {"description": "Medium priority/severity level"}, "low": "Düşük", "@low": {"description": "Low priority/severity level"}, "justNow": "<PERSON><PERSON><PERSON>", "@justNow": {"description": "Time indicator for very recent events"}, "blockers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@blockers": {"description": "Title for blockers section"}, "criticalPaths": "<PERSON><PERSON><PERSON>", "@criticalPaths": {"description": "Title for critical paths section"}, "error": "<PERSON><PERSON>", "@error": {"description": "Generic error title"}, "noProjectsYet": "Henüz <PERSON>je <PERSON>", "@noProjectsYet": {"description": "Message when no projects exist"}, "connectProjectManagementPlatforms": "Başlamak için proje yönetimi platformlarınızı bağlayın", "@connectProjectManagementPlatforms": {"description": "Instructions for connecting platforms"}, "projectPilotDashboard": "ProjectPilot Kontrol Paneli", "@projectPilotDashboard": {"description": "Title for ProjectPilot dashboard"}, "totalProjects": "<PERSON><PERSON>", "@totalProjects": {"description": "Label for total projects count"}, "recentProjects": "<PERSON>", "@recentProjects": {"description": "Title for recent projects section"}, "viewAll": "<PERSON>ümü<PERSON><PERSON>", "@viewAll": {"description": "Button text to view all items"}, "tasksOverview": "Görevlere Genel Bakış", "@tasksOverview": {"description": "Title for tasks overview section"}, "recentTasks": "<PERSON>", "@recentTasks": {"description": "Title for recent tasks section"}, "noTasksFound": "Görev bulunamadı", "@noTasksFound": {"description": "Message when no tasks are found"}, "inProgress": "<PERSON><PERSON>", "@inProgress": {"description": "Status for tasks in progress"}, "blocked": "<PERSON>gel<PERSON>di", "@blocked": {"description": "Status for blocked tasks"}, "overdue": "Vadesi Geçmiş", "@overdue": {"description": "Status for overdue tasks"}, "toDo": "Yapılacak", "@toDo": {"description": "Status for tasks to do"}, "review": "İnceleme", "@review": {"description": "Status for tasks in review"}, "done": "Tamamlandı", "@done": {"description": "Status for completed tasks"}, "cancelled": "İptal Edildi", "@cancelled": {"description": "Status for cancelled tasks"}, "progress": "<PERSON><PERSON><PERSON><PERSON>", "@progress": {"description": "Label for progress information"}, "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@createdAt": {"description": "Label for creation date"}, "deadline": "<PERSON>", "@deadline": {"description": "Label for deadline"}, "platform": "Platform", "@platform": {"description": "Label for platform information"}, "planning": "Planlama", "@planning": {"description": "Project status - planning"}, "onHold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@onHold": {"description": "Project status - on hold"}, "aiProjectAssistant": "AI Proje Asistanı", "@aiProjectAssistant": {"description": "Title for AI project assistant"}, "showMeProjectRisks": "<PERSON><PERSON>", "@showMeProjectRisks": {"description": "Sample query for project assistant"}, "projectProgressSummary": "<PERSON>je ilerleme özeti", "@projectProgressSummary": {"description": "Sample query for project assistant"}, "teamWorkloadAnalysis": "Takım iş yükü analizi", "@teamWorkloadAnalysis": {"description": "Sample query for project assistant"}, "dismiss": "Ka<PERSON><PERSON>", "@dismiss": {"description": "Button text to dismiss a notification"}, "recommendations": "<PERSON><PERSON><PERSON>", "@recommendations": {"description": "Title for recommendations section"}, "projectAnalysis": "<PERSON><PERSON>", "@projectAnalysis": {"description": "Title for project analysis"}, "analyzingProject": "Proje analiz ediliyor...", "@analyzingProject": {"description": "Loading message while analyzing project"}, "suggestedActions": "<PERSON><PERSON><PERSON><PERSON>", "@suggestedActions": {"description": "Title for suggested actions"}, "examplesForRole": "{role} <PERSON><PERSON><PERSON>", "@examplesForRole": {"description": "Title for role-based example prompts", "placeholders": {"role": {"type": "String", "example": "Developer"}}}, "tapToUse": "Kullanmak için dokun", "@tapToUse": {"description": "Text to indicate user can tap prompt to use it"}, "roleDeveloper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@roleDeveloper": {"description": "Developer role display name"}, "roleProjectManager": "<PERSON><PERSON>", "@roleProjectManager": {"description": "Project Manager role display name"}, "roleProductOwner": "<PERSON><PERSON><PERSON><PERSON>", "@roleProductOwner": {"description": "Product Owner role display name"}, "roleQaTester": "QA Test Uzmanı", "@roleQaTester": {"description": "QA Tester role display name"}, "roleCtoCeo": "CTO/CEO", "@roleCtoCeo": {"description": "CTO/CEO role display name"}, "roleAllRoles": "tüm roller", "@roleAllRoles": {"description": "Default text for all roles"}, "developerPrompt1": "<PERSON>u anda bana hangi görevler atanmış?", "@developerPrompt1": {"description": "Developer role prompt 1"}, "developerPrompt2": "Kod incelememi ne engelliyor?", "@developerPrompt2": {"description": "Developer role prompt 2"}, "developerPrompt3": "Sonraki sprintte neler var?", "@developerPrompt3": {"description": "Developer role prompt 3"}, "developerPrompt4": "Hangi hatalar en yüksek önceliğe sahip?", "@developerPrompt4": {"description": "Developer role prompt 4"}, "projectManagerPrompt1": "Mevcut sprinti ne geciktiriyor?", "@projectManagerPrompt1": {"description": "Project Manager role prompt 1"}, "projectManagerPrompt2": "Hangi görevler gecikmiş?", "@projectManagerPrompt2": {"description": "Project Manager role prompt 2"}, "projectManagerPrompt3": "Projede en son kim <PERSON>?", "@projectManagerPrompt3": {"description": "Project Manager role prompt 3"}, "projectManagerPrompt4": "Mevcut proje ilerlemesi nasıl?", "@projectManagerPrompt4": {"description": "Project Manager role prompt 4"}, "productOwnerPrompt1": "<PERSON>rak<PERSON> sürüm için hangi özellikler planlandı?", "@productOwnerPrompt1": {"description": "Product Owner role prompt 1"}, "productOwnerPrompt2": "Son gü<PERSON><PERSON><PERSON><PERSON>z hakkında geri bildiri<PERSON> neler?", "@productOwnerPrompt2": {"description": "Product Owner role prompt 2"}, "productOwnerPrompt3": "Hangi kullanıcı hikayeleri en yüksek önceliğe sahip?", "@productOwnerPrompt3": {"description": "Product Owner role prompt 3"}, "productOwnerPrompt4": "<PERSON><PERSON>ün birikimleri nasıl gelişiyor?", "@productOwnerPrompt4": {"description": "Product Owner role prompt 4"}, "qaTesterPrompt1": "Hangi testlerin yapılması gerekiyor?", "@qaTesterPrompt1": {"description": "QA Tester role prompt 1"}, "qaTesterPrompt2": "Açık kritik hatalar var mı?", "@qaTesterPrompt2": {"description": "QA Tester role prompt 2"}, "qaTesterPrompt3": "Mevcut test kapsamı nedir?", "@qaTesterPrompt3": {"description": "QA Tester role prompt 3"}, "qaTesterPrompt4": "<PERSON>i özellikler test i<PERSON><PERSON> hazır?", "@qaTesterPrompt4": {"description": "QA Tester role prompt 4"}, "ctoCeoPrompt1": "Proje genel olarak nasıl gidiyor?", "@ctoCeoPrompt1": {"description": "CTO/CEO role prompt 1"}, "ctoCeoPrompt2": "<PERSON>u anda bize en çok zaman ka<PERSON>bettiren şey nedir?", "@ctoCeoPrompt2": {"description": "CTO/CEO role prompt 2"}, "ctoCeoPrompt3": "Hangi takımlar desteğe ihtiyaç duyuyor?", "@ctoCeoPrompt3": {"description": "CTO/CEO role prompt 3"}, "ctoCeoPrompt4": "Bütçe ve zaman çizelgesi durumu nasıl?", "@ctoCeoPrompt4": {"description": "CTO/CEO role prompt 4"}, "defaultPrompt1": "<PERSON><PERSON>ün hangi görevler gündemde?", "@defaultPrompt1": {"description": "Default role prompt 1"}, "defaultPrompt2": "Mevcut proje durumu nedir?", "@defaultPrompt2": {"description": "Default role prompt 2"}, "defaultPrompt3": "<PERSON><PERSON><PERSON> önemli adımlar nelerdir?", "@defaultPrompt3": {"description": "Default role prompt 3"}, "defaultPrompt4": "Çözülmesi gereken engeller var mı?", "@defaultPrompt4": {"description": "Default role prompt 4"}}