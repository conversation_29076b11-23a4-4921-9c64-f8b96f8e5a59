import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_ru.dart';
import 'app_localizations_tr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('de'),
    Locale('en'),
    Locale('ru'),
    Locale('tr'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'ProjectPilot'**
  String get appTitle;

  /// Title for the home screen
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get homeTitle;

  /// Title for the settings screen
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingsTitle;

  /// Welcome text on splash screen
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// Subtitle on splash screen
  ///
  /// In en, this message translates to:
  /// **'ProjectPilot – the future of project management'**
  String get welcomeSubtitle;

  /// Title for the language selection
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get languageTitle;

  /// Name of English language
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get englishLanguage;

  /// Name of German language
  ///
  /// In en, this message translates to:
  /// **'German'**
  String get germanLanguage;

  /// Name of Russian language
  ///
  /// In en, this message translates to:
  /// **'Russian'**
  String get russianLanguage;

  /// Name of Turkish language
  ///
  /// In en, this message translates to:
  /// **'Turkish'**
  String get turkishLanguage;

  /// Name of Arabic language
  ///
  /// In en, this message translates to:
  /// **'Arabic'**
  String get arabicLanguage;

  /// Text for save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get saveButton;

  /// Text for cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancelButton;

  /// Generic error message
  ///
  /// In en, this message translates to:
  /// **'An error occurred'**
  String get errorOccurred;

  /// Button text to try again
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// Title for language selection dialog
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// Token balance display
  ///
  /// In en, this message translates to:
  /// **'Token Balance: {count}'**
  String tokenBalance(int count);

  /// Description of token usage for AI text generation
  ///
  /// In en, this message translates to:
  /// **'Tokens for AI text generation with GPT'**
  String get tokenBalanceDescription;

  /// Title for transcription feature
  ///
  /// In en, this message translates to:
  /// **'Transcription'**
  String get transcriptionTitle;

  /// Button text to start recording
  ///
  /// In en, this message translates to:
  /// **'Start Recording'**
  String get startRecording;

  /// Button text to stop recording
  ///
  /// In en, this message translates to:
  /// **'Stop Recording'**
  String get stopRecording;

  /// Message when no transcription is available
  ///
  /// In en, this message translates to:
  /// **'No transcription yet'**
  String get noTranscriptionYet;

  /// Button text to share transcription
  ///
  /// In en, this message translates to:
  /// **'Share Transcription'**
  String get shareTranscription;

  /// Title for the theme selection
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get themeTitle;

  /// Title for theme selection dialog
  ///
  /// In en, this message translates to:
  /// **'Select Theme'**
  String get selectTheme;

  /// Name of system default theme
  ///
  /// In en, this message translates to:
  /// **'System Default'**
  String get systemTheme;

  /// Name of light theme
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get lightTheme;

  /// Name of dark theme
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get darkTheme;

  /// Button text to toggle between light and dark theme
  ///
  /// In en, this message translates to:
  /// **'Toggle Theme'**
  String get toggleTheme;

  /// Section title for API keys in settings
  ///
  /// In en, this message translates to:
  /// **'API Keys'**
  String get apiKeysSection;

  /// Label for OpenAI API key input
  ///
  /// In en, this message translates to:
  /// **'OpenAI API Key'**
  String get openaiApiKey;

  /// Label for Whisper API key input
  ///
  /// In en, this message translates to:
  /// **'Whisper API Key'**
  String get whisperApiKey;

  /// Hint text for API key input fields
  ///
  /// In en, this message translates to:
  /// **'Enter your API key here'**
  String get apiKeyHint;

  /// Section title for tokens and usage in settings
  ///
  /// In en, this message translates to:
  /// **'Tokens & Usage'**
  String get tokenSection;

  /// Remaining recording minutes display
  ///
  /// In en, this message translates to:
  /// **'Remaining Minutes: {count}'**
  String remainingMinutes(int count);

  /// Description of remaining recording minutes
  ///
  /// In en, this message translates to:
  /// **'Minutes available for audio transcription with Whisper'**
  String get minutesBalanceDescription;

  /// Title for purchase packages section
  ///
  /// In en, this message translates to:
  /// **'Purchase Packages'**
  String get purchasePackages;

  /// Button text to buy tokens
  ///
  /// In en, this message translates to:
  /// **'Buy Tokens'**
  String get buyTokens;

  /// Name of starter package
  ///
  /// In en, this message translates to:
  /// **'Starter'**
  String get starterPackage;

  /// Name of pro package
  ///
  /// In en, this message translates to:
  /// **'Pro'**
  String get proPackage;

  /// Name of business package
  ///
  /// In en, this message translates to:
  /// **'Business'**
  String get businessPackage;

  /// Name of ultimate package
  ///
  /// In en, this message translates to:
  /// **'Ultimate'**
  String get ultimatePackage;

  /// Number of tokens in a package
  ///
  /// In en, this message translates to:
  /// **'{count} Tokens'**
  String tokens(int count);

  /// Price display
  ///
  /// In en, this message translates to:
  /// **'€{price}'**
  String price(double price);

  /// Section title for user profile in settings
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profileSection;

  /// Label for username
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// Label for email
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Text displayed when a value is not available
  ///
  /// In en, this message translates to:
  /// **'Not available'**
  String get notAvailable;

  /// Text for logout button
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logoutButton;

  /// Confirmation message when API key is saved
  ///
  /// In en, this message translates to:
  /// **'API key saved successfully!'**
  String get apiKeySaved;

  /// Title for first intro screen
  ///
  /// In en, this message translates to:
  /// **'AI-Powered Project Intelligence'**
  String get introScreen1Title;

  /// Description for first intro screen
  ///
  /// In en, this message translates to:
  /// **'Keep the red thread in your projects. Identify problems early and stay on track with intelligent insights.'**
  String get introScreen1Description;

  /// Title for second intro screen
  ///
  /// In en, this message translates to:
  /// **'Multi-Platform Project Overview'**
  String get introScreen2Title;

  /// Description for second intro screen
  ///
  /// In en, this message translates to:
  /// **'Connect all your project tools in one dashboard. ClickUp, Notion, Jira, and more - all synchronized.'**
  String get introScreen2Description;

  /// Title for third intro screen
  ///
  /// In en, this message translates to:
  /// **'Smart Problem Detection'**
  String get introScreen3Title;

  /// Description for third intro screen
  ///
  /// In en, this message translates to:
  /// **'Automatically detect blockers, assess risks, and get actionable recommendations to keep your projects moving.'**
  String get introScreen3Description;

  /// Main title for intro screens
  ///
  /// In en, this message translates to:
  /// **'ProjectPilot'**
  String get introMainTitle;

  /// Main subtitle for intro screens
  ///
  /// In en, this message translates to:
  /// **'Never lose track of your projects again'**
  String get introMainSubtitle;

  /// Text for get started button
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get getStarted;

  /// Text for skip button on intro screens
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// Text for next button on intro screens
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Title for the login screen
  ///
  /// In en, this message translates to:
  /// **'Welcome to ProjectPilot'**
  String get loginTitle;

  /// Subtitle for the login screen
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue'**
  String get loginSubtitle;

  /// Button text for Google login
  ///
  /// In en, this message translates to:
  /// **'Continue with Google'**
  String get continueWithGoogle;

  /// Button text for Apple login
  ///
  /// In en, this message translates to:
  /// **'Continue with Apple'**
  String get continueWithApple;

  /// Button text for Email login
  ///
  /// In en, this message translates to:
  /// **'Continue with Email'**
  String get continueWithEmail;

  /// Button text to show more login options
  ///
  /// In en, this message translates to:
  /// **'More Options'**
  String get moreOptions;

  /// Button text for LinkedIn login
  ///
  /// In en, this message translates to:
  /// **'Continue with LinkedIn'**
  String get continueWithLinkedIn;

  /// Button text for Phone login
  ///
  /// In en, this message translates to:
  /// **'Continue with Phone'**
  String get continueWithPhone;

  /// Button text for Azure login
  ///
  /// In en, this message translates to:
  /// **'Continue with Azure'**
  String get continueWithAzure;

  /// Button text for Notion login
  ///
  /// In en, this message translates to:
  /// **'Continue with Notion'**
  String get continueWithNotion;

  /// Label for password
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Text for forgot password button
  ///
  /// In en, this message translates to:
  /// **'Forgot password?'**
  String get forgotPassword;

  /// Button text for sign in
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// Button text for sign up
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// Text for users without an account
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccount;

  /// Text for users with an account
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAccount;

  /// Label for phone number
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// Text for 'or' separator
  ///
  /// In en, this message translates to:
  /// **'or'**
  String get or;

  /// Subtitle text for sign-up screen
  ///
  /// In en, this message translates to:
  /// **'Create your ProjectPilot account'**
  String get createAccountText;

  /// Error message when username is empty
  ///
  /// In en, this message translates to:
  /// **'Username is required'**
  String get usernameRequired;

  /// Error message when username is too short
  ///
  /// In en, this message translates to:
  /// **'Username must be at least 3 characters'**
  String get usernameMinLength;

  /// Validation message when email is empty
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get emailRequired;

  /// Error message when email format is invalid
  ///
  /// In en, this message translates to:
  /// **'Enter a valid email'**
  String get invalidEmail;

  /// Error message when password is empty
  ///
  /// In en, this message translates to:
  /// **'Password is required'**
  String get passwordRequired;

  /// Error message when password is too short
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordMinLength;

  /// Success message after signing up
  ///
  /// In en, this message translates to:
  /// **'Signup successful!'**
  String get signUpSuccessful;

  /// Title for the onboarding screen
  ///
  /// In en, this message translates to:
  /// **'Complete Your Profile'**
  String get onboarding;

  /// Option for taking a photo with camera
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// Option for selecting a photo from gallery
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// Label for birth year selection
  ///
  /// In en, this message translates to:
  /// **'Birth Year'**
  String get birthYear;

  /// Title for birth year picker
  ///
  /// In en, this message translates to:
  /// **'Select Birth Year'**
  String get selectBirthYear;

  /// Label for user role selection
  ///
  /// In en, this message translates to:
  /// **'User Role'**
  String get userRole;

  /// Developer role option
  ///
  /// In en, this message translates to:
  /// **'Developer'**
  String get developer;

  /// Project Manager role option
  ///
  /// In en, this message translates to:
  /// **'Project Manager'**
  String get projectManager;

  /// Private role option
  ///
  /// In en, this message translates to:
  /// **'Private'**
  String get private;

  /// Other option for selections
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// Label for usage purpose selection
  ///
  /// In en, this message translates to:
  /// **'Usage Purpose'**
  String get usagePurpose;

  /// Label for tasks content type
  ///
  /// In en, this message translates to:
  /// **'Tasks'**
  String get tasks;

  /// Label for notes content type
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// Label for ideas content type
  ///
  /// In en, this message translates to:
  /// **'Ideas'**
  String get ideas;

  /// Button text to connect platforms
  ///
  /// In en, this message translates to:
  /// **'Connect Platforms'**
  String get connectPlatforms;

  /// Description text for platform connection screen
  ///
  /// In en, this message translates to:
  /// **'Connect to your favorite platforms'**
  String get platformsDescription;

  /// Text for connecting more platforms in the future
  ///
  /// In en, this message translates to:
  /// **'Connect more platforms later'**
  String get connectMore;

  /// Text for finish button
  ///
  /// In en, this message translates to:
  /// **'Finish'**
  String get finish;

  /// Title for the platform connections screen
  ///
  /// In en, this message translates to:
  /// **'Platform Connections'**
  String get platformConnections;

  /// Button text to connect to a specific platform
  ///
  /// In en, this message translates to:
  /// **'Connect to {platform}'**
  String connectToPlatform(String platform);

  /// Status text when a platform is connected
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get connected;

  /// Status text when a platform is not connected
  ///
  /// In en, this message translates to:
  /// **'Not Connected'**
  String get disconnected;

  /// Button text to disconnect from a platform
  ///
  /// In en, this message translates to:
  /// **'Disconnect'**
  String get disconnect;

  /// Status text when connecting to a platform
  ///
  /// In en, this message translates to:
  /// **'Connecting...'**
  String get connecting;

  /// Error message when platform authorization fails
  ///
  /// In en, this message translates to:
  /// **'Authorization Failed'**
  String get authorizationFailed;

  /// Success message when platform connection is established
  ///
  /// In en, this message translates to:
  /// **'Connection Successful'**
  String get connectionSuccessful;

  /// Error message when platform connection fails
  ///
  /// In en, this message translates to:
  /// **'Connection Failed'**
  String get connectionFailed;

  /// Text indicating a platform integration is available
  ///
  /// In en, this message translates to:
  /// **'Integration available for {platform}'**
  String integrationAvailableFor(String platform);

  /// Text for showing all available platforms
  ///
  /// In en, this message translates to:
  /// **'All Platforms'**
  String get allPlatforms;

  /// Description for the platform connections screen
  ///
  /// In en, this message translates to:
  /// **'Manage platform connections'**
  String get managePlatformConnections;

  /// Explanation text for platform connections
  ///
  /// In en, this message translates to:
  /// **'Connect your favorite platforms to send tasks directly'**
  String get connectPlatformToSendTasks;

  /// Label for cancel action
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Hint text for the title input field in the add entry bottom sheet
  ///
  /// In en, this message translates to:
  /// **'What would you like to do?'**
  String get whatWouldYouLikeToDo;

  /// Text shown while recording is in progress
  ///
  /// In en, this message translates to:
  /// **'Recording in progress...'**
  String get recordingInProgress;

  /// Text shown while transcribing audio
  ///
  /// In en, this message translates to:
  /// **'Processing your voice...'**
  String get processingYourVoice;

  /// Prompt for selecting a platform to save to
  ///
  /// In en, this message translates to:
  /// **'Where would you like to save this?'**
  String get whereToSave;

  /// Text shown when no platforms are connected
  ///
  /// In en, this message translates to:
  /// **'No platforms connected. Connect a platform to continue.'**
  String get noPlatformsConnected;

  /// Button text to start a new recording
  ///
  /// In en, this message translates to:
  /// **'New Recording'**
  String get newRecording;

  /// Label for uncategorized items
  ///
  /// In en, this message translates to:
  /// **'Uncategorized'**
  String get uncategorized;

  /// Label for preferences
  ///
  /// In en, this message translates to:
  /// **'Preferences'**
  String get preferences;

  /// Button text to upgrade package
  ///
  /// In en, this message translates to:
  /// **'Upgrade Package'**
  String get upgradePackage;

  /// Title for info and support section
  ///
  /// In en, this message translates to:
  /// **'Info & Support'**
  String get infoAndSupport;

  /// Label for light mode option
  ///
  /// In en, this message translates to:
  /// **'Light Mode'**
  String get lightMode;

  /// Label for connected platform
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get platformConnected;

  /// Label for not connected platform
  ///
  /// In en, this message translates to:
  /// **'Not connected'**
  String get platformNotConnected;

  /// Button text to connect to a platform
  ///
  /// In en, this message translates to:
  /// **'Connect'**
  String get connect;

  /// Message encouraging users to upgrade their package
  ///
  /// In en, this message translates to:
  /// **'Get more tokens and recording minutes by upgrading your package'**
  String get upgradeMessage;

  /// Link text for privacy policy
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Link text for terms of service
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// Link text for FAQ
  ///
  /// In en, this message translates to:
  /// **'FAQ'**
  String get faq;

  /// Link text for contact support
  ///
  /// In en, this message translates to:
  /// **'Contact Support'**
  String get contactSupport;

  /// Link text for about app
  ///
  /// In en, this message translates to:
  /// **'About ProjectPilot'**
  String get aboutApp;

  /// Title for the language section in settings
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get settingsLanguageSection;

  /// Title for the appearance and behavior section in settings
  ///
  /// In en, this message translates to:
  /// **'Appearance & Behavior'**
  String get appearanceAndBehavior;

  /// Title for the information and support section in settings
  ///
  /// In en, this message translates to:
  /// **'Information & Support'**
  String get settingsInfoSection;

  /// Title for help and support option in settings
  ///
  /// In en, this message translates to:
  /// **'Help & Support'**
  String get helpAndSupport;

  /// Subtitle for help and support option in settings
  ///
  /// In en, this message translates to:
  /// **'Privacy, Terms, FAQ'**
  String get privacyTermsFaq;

  /// Title for about option in settings
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get settingsAbout;

  /// Title for storage management section in settings
  ///
  /// In en, this message translates to:
  /// **'Storage Management'**
  String get storageManagement;

  /// Title for voice recordings option in storage management
  ///
  /// In en, this message translates to:
  /// **'Voice Recordings'**
  String get voiceRecordings;

  /// Text showing storage usage
  ///
  /// In en, this message translates to:
  /// **'Using {size} of storage'**
  String usingStorage(String size);

  /// Title for dark mode option in settings
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// Title for automatic assignment option in settings
  ///
  /// In en, this message translates to:
  /// **'Automatic Assignment'**
  String get automaticAssignment;

  /// Title for the task destination selector
  ///
  /// In en, this message translates to:
  /// **'Where should I send this?'**
  String get whereToSendTask;

  /// Label for platform selection
  ///
  /// In en, this message translates to:
  /// **'Select Platform'**
  String get selectPlatform;

  /// Label for project selection
  ///
  /// In en, this message translates to:
  /// **'Select Project / Notebook / Board'**
  String get selectProject;

  /// Button text to send the task
  ///
  /// In en, this message translates to:
  /// **'Send Now'**
  String get sendNow;

  /// Message when no projects are available
  ///
  /// In en, this message translates to:
  /// **'No projects found'**
  String get noProjectsFound;

  /// Success message when task is sent
  ///
  /// In en, this message translates to:
  /// **'Task sent successfully'**
  String get taskSentSuccessfully;

  /// Message shown when platform is detected from voice
  ///
  /// In en, this message translates to:
  /// **'Platform detected from your voice'**
  String get platformDetected;

  /// Message shown when project is detected from voice
  ///
  /// In en, this message translates to:
  /// **'Project detected from your voice'**
  String get projectDetected;

  /// Message shown when intent is detected from voice
  ///
  /// In en, this message translates to:
  /// **'Intent detected from your voice'**
  String get intentDetected;

  /// Tooltip for edit button
  ///
  /// In en, this message translates to:
  /// **'Edit content'**
  String get editContent;

  /// Message when no platforms are connected
  ///
  /// In en, this message translates to:
  /// **'No connected platforms'**
  String get noConnectedPlatforms;

  /// Message instructing user to connect platforms
  ///
  /// In en, this message translates to:
  /// **'Connect platforms in settings to send tasks'**
  String get connectPlatformsInSettings;

  /// Title for new task screen
  ///
  /// In en, this message translates to:
  /// **'New Task'**
  String get newTask;

  /// Title for new note screen
  ///
  /// In en, this message translates to:
  /// **'New Note'**
  String get newNote;

  /// Title for new idea screen
  ///
  /// In en, this message translates to:
  /// **'New Idea'**
  String get newIdea;

  /// Label for edit action
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Text for share button
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// Title for transcription result
  ///
  /// In en, this message translates to:
  /// **'Transcription Result'**
  String get transcriptionResult;

  /// Label for detected intent
  ///
  /// In en, this message translates to:
  /// **'Detected Intent'**
  String get detectedIntent;

  /// Label for detected entities
  ///
  /// In en, this message translates to:
  /// **'Detected Entities'**
  String get detectedEntities;

  /// Label for original transcription
  ///
  /// In en, this message translates to:
  /// **'Original Transcription'**
  String get originalTranscription;

  /// Text for add connection button
  ///
  /// In en, this message translates to:
  /// **'Add Connection'**
  String get addConnection;

  /// Text shown when no connections are available
  ///
  /// In en, this message translates to:
  /// **'No Connections'**
  String get noConnections;

  /// Description for adding connections
  ///
  /// In en, this message translates to:
  /// **'Connect your favorite platforms to send tasks directly'**
  String get addConnectionsDescription;

  /// Title for delete confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get confirmDelete;

  /// Message for delete connection confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete the connection to {platform}?'**
  String confirmDeleteConnectionMessage(String platform);

  /// Label for delete action
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Message shown when platform is connected
  ///
  /// In en, this message translates to:
  /// **'Connected to {platform}'**
  String platformConnectedMessage(String platform);

  /// Title for the speech history screen
  ///
  /// In en, this message translates to:
  /// **'Speech History'**
  String get speechHistoryTitle;

  /// Button text to clear conversation history
  ///
  /// In en, this message translates to:
  /// **'Clear History'**
  String get clearHistory;

  /// Instruction text for starting recording
  ///
  /// In en, this message translates to:
  /// **'Tap the microphone to start recording'**
  String get tapMicToStart;

  /// Label for suggested platform
  ///
  /// In en, this message translates to:
  /// **'Suggested platform based on your speech:'**
  String get suggestedPlatform;

  /// Text for confirm button
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// Text for back button
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Message shown when task is sent to a platform
  ///
  /// In en, this message translates to:
  /// **'Task sent to {platform}'**
  String taskSent(String platform);

  /// Text for AI recognition message
  ///
  /// In en, this message translates to:
  /// **'I recognized:'**
  String get iRecognized;

  /// Label for target platform
  ///
  /// In en, this message translates to:
  /// **'Target:'**
  String get targetPlatform;

  /// Button text to edit content before sending
  ///
  /// In en, this message translates to:
  /// **'Edit before sending'**
  String get editBeforeSending;

  /// Title for command type selection prompt
  ///
  /// In en, this message translates to:
  /// **'What would you like to create?'**
  String get selectCommandType;

  /// Button text to create a task
  ///
  /// In en, this message translates to:
  /// **'Create Task'**
  String get createTaskCommand;

  /// Button text to add a note
  ///
  /// In en, this message translates to:
  /// **'Add Note'**
  String get addNoteCommand;

  /// Button text to save an idea
  ///
  /// In en, this message translates to:
  /// **'Save Idea'**
  String get saveIdeaCommand;

  /// Hint text for project name input
  ///
  /// In en, this message translates to:
  /// **'Enter project name'**
  String get enterProjectName;

  /// Title for project selection prompt
  ///
  /// In en, this message translates to:
  /// **'Select Project'**
  String get selectProjectPrompt;

  /// Title for entries screen
  ///
  /// In en, this message translates to:
  /// **'Entries'**
  String get entries;

  /// Title for entry details screen
  ///
  /// In en, this message translates to:
  /// **'Entry Details'**
  String get entryDetails;

  /// Button text to add an update to an entry
  ///
  /// In en, this message translates to:
  /// **'Add Update'**
  String get addUpdate;

  /// Title for update details dialog
  ///
  /// In en, this message translates to:
  /// **'Update Details'**
  String get updateDetails;

  /// Title for delete update confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Delete Update'**
  String get deleteUpdate;

  /// Confirmation message for deleting an update
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this update?'**
  String get deleteUpdateConfirmation;

  /// Title for delete entry dialog
  ///
  /// In en, this message translates to:
  /// **'Delete Entry'**
  String get deleteEntry;

  /// Confirmation message for deleting an entry
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this entry? This action cannot be undone.'**
  String get deleteEntryConfirmation;

  /// Success message when an entry is created
  ///
  /// In en, this message translates to:
  /// **'Entry created successfully'**
  String get entryCreated;

  /// Message when an entry is deleted
  ///
  /// In en, this message translates to:
  /// **'Entry deleted'**
  String get entryDeleted;

  /// Success message when an update is added to an entry
  ///
  /// In en, this message translates to:
  /// **'Update added successfully'**
  String get entryUpdateAdded;

  /// Message when no entries are found
  ///
  /// In en, this message translates to:
  /// **'No entries found'**
  String get noEntriesFound;

  /// Prompt to create a new entry
  ///
  /// In en, this message translates to:
  /// **'Create a new entry by tapping the + button'**
  String get createEntryPrompt;

  /// Title for adding a new entry
  ///
  /// In en, this message translates to:
  /// **'Create New Entry'**
  String get createNewEntry;

  /// Label for title field
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// Hint for title input field
  ///
  /// In en, this message translates to:
  /// **'Enter title here'**
  String get titleHint;

  /// Label for description field
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Hint for description input field
  ///
  /// In en, this message translates to:
  /// **'Enter description here'**
  String get descriptionHint;

  /// Label for time field
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// Label for date field
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// Format for displaying date
  ///
  /// In en, this message translates to:
  /// **'{month}/{day}/{year}'**
  String dateFormat(int month, int day, int year);

  /// Label for type field
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// Label for priority field
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// Low priority level
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get priorityLow;

  /// Medium priority level
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get priorityMedium;

  /// High priority level
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get priorityHigh;

  /// Urgent priority level
  ///
  /// In en, this message translates to:
  /// **'Urgent'**
  String get priorityUrgent;

  /// Label for destination field
  ///
  /// In en, this message translates to:
  /// **'Destination'**
  String get destination;

  /// Name of ClickUp platform
  ///
  /// In en, this message translates to:
  /// **'ClickUp'**
  String get clickupPlatform;

  /// Name of Notion platform
  ///
  /// In en, this message translates to:
  /// **'Notion'**
  String get notionPlatform;

  /// Asana platform name
  ///
  /// In en, this message translates to:
  /// **'Asana'**
  String get asanaPlatform;

  /// Monday platform name
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get mondayPlatform;

  /// Jira platform name
  ///
  /// In en, this message translates to:
  /// **'Jira'**
  String get jiraPlatform;

  /// Trello platform name
  ///
  /// In en, this message translates to:
  /// **'Trello'**
  String get trelloPlatform;

  /// Label for create button
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// Error message when title is empty
  ///
  /// In en, this message translates to:
  /// **'Please enter a title'**
  String get pleaseEnterTitle;

  /// Label for all items filter
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Hint text for search input
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get searchHint;

  /// Title for the chats section in the drawer
  ///
  /// In en, this message translates to:
  /// **'Chats'**
  String get chats;

  /// Button text to create a new chat
  ///
  /// In en, this message translates to:
  /// **'New Chat'**
  String get newChat;

  /// Title for search results section
  ///
  /// In en, this message translates to:
  /// **'Search Results'**
  String get searchResults;

  /// Message shown when no search results are found
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get noResultsFound;

  /// General term for projects
  ///
  /// In en, this message translates to:
  /// **'Projects'**
  String get projects;

  /// Button text to create a new folder
  ///
  /// In en, this message translates to:
  /// **'New Folder'**
  String get newFolder;

  /// Title for the create folder dialog
  ///
  /// In en, this message translates to:
  /// **'Create New Folder'**
  String get createNewFolder;

  /// Hint text for folder name input
  ///
  /// In en, this message translates to:
  /// **'Enter folder name'**
  String get enterFolderName;

  /// Action to rename an item
  ///
  /// In en, this message translates to:
  /// **'Rename'**
  String get rename;

  /// Title for the rename chat dialog
  ///
  /// In en, this message translates to:
  /// **'Rename Chat'**
  String get renameChat;

  /// Title for the rename folder dialog
  ///
  /// In en, this message translates to:
  /// **'Rename Folder'**
  String get renameFolder;

  /// Hint text for new name input
  ///
  /// In en, this message translates to:
  /// **'Enter new name'**
  String get enterNewName;

  /// Action to move an item to a folder
  ///
  /// In en, this message translates to:
  /// **'Move to folder'**
  String get moveToFolder;

  /// Title for the delete chat confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Delete Chat'**
  String get deleteChat;

  /// Title for the delete folder confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Delete Folder'**
  String get deleteFolder;

  /// Confirmation message for deleting a chat
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this chat? This action cannot be undone.'**
  String get deleteChatConfirmation;

  /// Confirmation message for deleting a folder
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this folder? This action cannot be undone.'**
  String get deleteFolderConfirmation;

  /// Text for items created today
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// Text for items created yesterday
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// Text for items created days ago
  ///
  /// In en, this message translates to:
  /// **'{count} days ago'**
  String daysAgo(int count);

  /// Title for the chat history section in recording view
  ///
  /// In en, this message translates to:
  /// **'Chat History'**
  String get chatHistory;

  /// Button text to add a note to a message
  ///
  /// In en, this message translates to:
  /// **'Add Note'**
  String get addNote;

  /// Button text to pin a message
  ///
  /// In en, this message translates to:
  /// **'Pin Message'**
  String get pinMessage;

  /// Button text to bookmark a message
  ///
  /// In en, this message translates to:
  /// **'Bookmark'**
  String get bookmark;

  /// Title for message options menu
  ///
  /// In en, this message translates to:
  /// **'Message Options'**
  String get messageOptions;

  /// Confirmation message when a message is deleted
  ///
  /// In en, this message translates to:
  /// **'Message deleted'**
  String get messageDeleted;

  /// Confirmation message when a message is pinned
  ///
  /// In en, this message translates to:
  /// **'Message pinned'**
  String get messagePinned;

  /// Confirmation message when a message is bookmarked
  ///
  /// In en, this message translates to:
  /// **'Message bookmarked'**
  String get messageBookmarked;

  /// Confirmation message when a note is added to a message
  ///
  /// In en, this message translates to:
  /// **'Note added'**
  String get noteAdded;

  /// Hint text for note input field
  ///
  /// In en, this message translates to:
  /// **'Enter your note'**
  String get enterNote;

  /// Title for add note dialog
  ///
  /// In en, this message translates to:
  /// **'Add Note to Message'**
  String get addNoteToMessage;

  /// Title for edit message dialog
  ///
  /// In en, this message translates to:
  /// **'Edit Message'**
  String get editMessage;

  /// Hint text for message input field
  ///
  /// In en, this message translates to:
  /// **'Enter your message'**
  String get enterMessage;

  /// Confirmation message when a message is moved to a folder
  ///
  /// In en, this message translates to:
  /// **'Message moved to folder'**
  String get messageMoved;

  /// Title for folder selection dialog
  ///
  /// In en, this message translates to:
  /// **'Select Folder'**
  String get selectFolder;

  /// Confirmation message when a folder is created
  ///
  /// In en, this message translates to:
  /// **'Folder created'**
  String get folderCreated;

  /// Message shown when no folders exist
  ///
  /// In en, this message translates to:
  /// **'No folders yet'**
  String get noFolders;

  /// Prompt to create a new folder
  ///
  /// In en, this message translates to:
  /// **'Create a folder to organize your chats'**
  String get createFolderPrompt;

  /// Message shown when a folder has no chats
  ///
  /// In en, this message translates to:
  /// **'No chats in this folder'**
  String get noChatsInFolder;

  /// Prompt to add chats to a folder
  ///
  /// In en, this message translates to:
  /// **'Add chats by dragging them here'**
  String get addChatsToFolder;

  /// Message shown when there are no chats
  ///
  /// In en, this message translates to:
  /// **'No conversations yet'**
  String get noChatsYet;

  /// Prompt to start a new chat
  ///
  /// In en, this message translates to:
  /// **'Start a new chat to begin'**
  String get startNewChatPrompt;

  /// Message shown when there are no recent items
  ///
  /// In en, this message translates to:
  /// **'No recent items'**
  String get noRecentItems;

  /// Title for recent items section
  ///
  /// In en, this message translates to:
  /// **'Recent Items'**
  String get recentItems;

  /// Title for today's tasks section
  ///
  /// In en, this message translates to:
  /// **'Today\'s Tasks'**
  String get todaysTasks;

  /// Prompt to view tasks
  ///
  /// In en, this message translates to:
  /// **'Tap to view your tasks'**
  String get tapToViewTasks;

  /// Short description of the app
  ///
  /// In en, this message translates to:
  /// **'Your smart voice-to-task assistant'**
  String get appDescription;

  /// Text shown when the app is in offline mode
  ///
  /// In en, this message translates to:
  /// **'Offline Mode'**
  String get offlineMode;

  /// Description of offline mode limitations
  ///
  /// In en, this message translates to:
  /// **'You\'re currently offline. Some features may be limited.'**
  String get offlineModeDescription;

  /// Status text when sync is pending
  ///
  /// In en, this message translates to:
  /// **'Sync Pending'**
  String get syncPending;

  /// Status text when sync is complete
  ///
  /// In en, this message translates to:
  /// **'Sync Complete'**
  String get syncComplete;

  /// Status text when sync fails
  ///
  /// In en, this message translates to:
  /// **'Sync Failed'**
  String get syncFailed;

  /// Button text to retry synchronization
  ///
  /// In en, this message translates to:
  /// **'Retry Sync'**
  String get retrySync;

  /// Button text to continue in offline mode
  ///
  /// In en, this message translates to:
  /// **'Continue in Offline Mode'**
  String get continueInOfflineMode;

  /// Error message for network issues
  ///
  /// In en, this message translates to:
  /// **'Network Error'**
  String get networkError;

  /// Prompt to check internet connection
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection and try again.'**
  String get checkConnection;

  /// Button text to pause recording
  ///
  /// In en, this message translates to:
  /// **'Pause'**
  String get pause;

  /// Button text to resume recording
  ///
  /// In en, this message translates to:
  /// **'Resume'**
  String get resume;

  /// Text for reset button
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// Message shown when transcription is complete
  ///
  /// In en, this message translates to:
  /// **'Transcription Complete'**
  String get transcriptionComplete;

  /// Button text to view details of a transcription or entry
  ///
  /// In en, this message translates to:
  /// **'View Details'**
  String get viewDetails;

  /// Button text to continue to next step
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueButton;

  /// Title for the collection screen
  ///
  /// In en, this message translates to:
  /// **'Collection'**
  String get collection;

  /// Label for sorting by newest first
  ///
  /// In en, this message translates to:
  /// **'Newest'**
  String get newest;

  /// Label for sorting by oldest first
  ///
  /// In en, this message translates to:
  /// **'Oldest'**
  String get oldest;

  /// Label for sorting by name
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Label for sorting by category
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// Label indicating an item is synced
  ///
  /// In en, this message translates to:
  /// **'Synced'**
  String get synced;

  /// Label for unsynced items
  ///
  /// In en, this message translates to:
  /// **'Unsynced'**
  String get unsynced;

  /// Label for items not synced yet
  ///
  /// In en, this message translates to:
  /// **'Not Synced'**
  String get notSynced;

  /// Label for search action
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Label for closing search
  ///
  /// In en, this message translates to:
  /// **'Close Search'**
  String get closeSearch;

  /// Label for list view mode
  ///
  /// In en, this message translates to:
  /// **'List View'**
  String get listView;

  /// Label for grid view mode
  ///
  /// In en, this message translates to:
  /// **'Grid View'**
  String get gridView;

  /// Button text to create the first entry
  ///
  /// In en, this message translates to:
  /// **'Create First Entry'**
  String get createFirstEntry;

  /// Message when no entries exist
  ///
  /// In en, this message translates to:
  /// **'No entries yet'**
  String get noEntriesYet;

  /// Description for creating new entries
  ///
  /// In en, this message translates to:
  /// **'Create your first task, note, or idea using the button below.'**
  String get createNewEntryDescription;

  /// Status for completed items
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// Message when no search results exist
  ///
  /// In en, this message translates to:
  /// **'No search results found'**
  String get noSearchResults;

  /// Label for clearing search
  ///
  /// In en, this message translates to:
  /// **'Clear Search'**
  String get clearSearch;

  /// Title for unassigned entries warning banner
  ///
  /// In en, this message translates to:
  /// **'Unassigned Entries'**
  String get unassignedEntriesWarning;

  /// Description for unassigned entries with count
  ///
  /// In en, this message translates to:
  /// **'You have {count} unassigned entries waiting to be assigned to a platform'**
  String unassignedEntriesDescription(int count);

  /// Button text to assign entries immediately
  ///
  /// In en, this message translates to:
  /// **'Assign Now'**
  String get assignNow;

  /// Title for the assigned entries section with count
  ///
  /// In en, this message translates to:
  /// **'Assigned Entries ({count})'**
  String assignedEntries(int count);

  /// Hint text for searching entries
  ///
  /// In en, this message translates to:
  /// **'Search entries...'**
  String get searchEntries;

  /// Label for archive action
  ///
  /// In en, this message translates to:
  /// **'Archive'**
  String get archive;

  /// Label for assign action
  ///
  /// In en, this message translates to:
  /// **'Assign'**
  String get assign;

  /// Message when an entry is archived
  ///
  /// In en, this message translates to:
  /// **'Entry archived'**
  String get entryArchived;

  /// Label for undo action
  ///
  /// In en, this message translates to:
  /// **'UNDO'**
  String get undo;

  /// Message shown when restore functionality is not implemented
  ///
  /// In en, this message translates to:
  /// **'Restore functionality not yet implemented'**
  String get restoreFunctionalityNotImplemented;

  /// Message when an entry is assigned to a platform
  ///
  /// In en, this message translates to:
  /// **'Assigned to {platform}'**
  String assignedToPlatform(String platform);

  /// Message shown when a recording file is not found
  ///
  /// In en, this message translates to:
  /// **'Recording file not found. It may have been deleted or moved.'**
  String get recordingFileNotFound;

  /// Message shown when there is an error playing a recording
  ///
  /// In en, this message translates to:
  /// **'Error playing recording'**
  String get errorPlayingRecording;

  /// Title for successful assignment notification
  ///
  /// In en, this message translates to:
  /// **'Assignment Successful'**
  String get assignmentSuccessful;

  /// Message showing which category was assigned to which platform
  ///
  /// In en, this message translates to:
  /// **'{category} assigned to {platform}'**
  String assignedCategoryToPlatform(String category, String platform);

  /// Title for tasks collection screen
  ///
  /// In en, this message translates to:
  /// **'My Tasks'**
  String get myTasks;

  /// Title for task collection screen
  ///
  /// In en, this message translates to:
  /// **'Task'**
  String get task;

  /// Title for ideas collection screen
  ///
  /// In en, this message translates to:
  /// **'My Ideas'**
  String get myIdeas;

  /// Title for idea collection screen
  ///
  /// In en, this message translates to:
  /// **'Idea'**
  String get idea;

  /// Title for notes collection screen
  ///
  /// In en, this message translates to:
  /// **'My Notes'**
  String get myNotes;

  /// Title for note collection screen
  ///
  /// In en, this message translates to:
  /// **'Note'**
  String get note;

  /// Message when there are no tasks
  ///
  /// In en, this message translates to:
  /// **'No tasks yet'**
  String get noTasksYet;

  /// Message when there are no ideas
  ///
  /// In en, this message translates to:
  /// **'No ideas yet'**
  String get noIdeasYet;

  /// Message when there are no notes
  ///
  /// In en, this message translates to:
  /// **'No notes yet'**
  String get noNotesYet;

  /// Label for add new button
  ///
  /// In en, this message translates to:
  /// **'Add New'**
  String get addNew;

  /// Text shown when content has been optimized for a specific category
  ///
  /// In en, this message translates to:
  /// **'Optimized for'**
  String get optimizedFor;

  /// Title for optimized content section
  ///
  /// In en, this message translates to:
  /// **'Optimized Content'**
  String get optimizedContent;

  /// Title for content optimization section
  ///
  /// In en, this message translates to:
  /// **'Content Optimization'**
  String get contentOptimization;

  /// Title for platform assignment section
  ///
  /// In en, this message translates to:
  /// **'Platform Assignment'**
  String get platformAssignment;

  /// Title shown when audio fails to play
  ///
  /// In en, this message translates to:
  /// **'Audio playback error'**
  String get audioPlaybackError;

  /// Message shown when audio file is corrupted
  ///
  /// In en, this message translates to:
  /// **'Unable to play the audio file. It may be corrupted.'**
  String get audioFileCorrupted;

  /// Text shown while loading audio player
  ///
  /// In en, this message translates to:
  /// **'Loading audio player...'**
  String get loadingAudioPlayer;

  /// Text shown when optimizing content for a specific category
  ///
  /// In en, this message translates to:
  /// **'Optimizing for'**
  String get optimizingFor;

  /// Prompt to choose a platform for assignment
  ///
  /// In en, this message translates to:
  /// **'Choose Platform to Assign'**
  String get choosePlatformToAssign;

  /// Message shown when assigning to a platform
  ///
  /// In en, this message translates to:
  /// **'Assigning to'**
  String get assigningTo;

  /// Message shown when auto-assignment is enabled
  ///
  /// In en, this message translates to:
  /// **'Auto-assignment is enabled. This item will be automatically processed.'**
  String get autoAssignmentEnabled;

  /// Confirmation message for deleting a recording
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this recording? This action cannot be undone.'**
  String get deleteRecordingConfirmation;

  /// Message shown when deleting a recording
  ///
  /// In en, this message translates to:
  /// **'Deleting recording...'**
  String get deletingRecording;

  /// Text for save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Label for when an item was created
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get created;

  /// Label for content section
  ///
  /// In en, this message translates to:
  /// **'Content'**
  String get content;

  /// Tooltip for create entry button
  ///
  /// In en, this message translates to:
  /// **'Create Entry'**
  String get createEntry;

  /// Title for task reminder notifications
  ///
  /// In en, this message translates to:
  /// **'Task Reminder'**
  String get taskReminder;

  /// Title for time selection dialog
  ///
  /// In en, this message translates to:
  /// **'Select time'**
  String get selectTime;

  /// Button text to change time
  ///
  /// In en, this message translates to:
  /// **'Change Time'**
  String get changeTime;

  /// Button text for disabling reminder
  ///
  /// In en, this message translates to:
  /// **'No Reminder'**
  String get noReminder;

  /// Button text to edit an entry
  ///
  /// In en, this message translates to:
  /// **'Edit Entry'**
  String get editEntry;

  /// Label for original content when no audio is available
  ///
  /// In en, this message translates to:
  /// **'Original Content'**
  String get originalContent;

  /// Label for text-only entries
  ///
  /// In en, this message translates to:
  /// **'Text Entry'**
  String get textEntry;

  /// Label for voice entries
  ///
  /// In en, this message translates to:
  /// **'Voice Entry'**
  String get voiceEntry;

  /// Label for optimization button prefix
  ///
  /// In en, this message translates to:
  /// **'Optimize as'**
  String get optimizeAs;

  /// Description for task optimization
  ///
  /// In en, this message translates to:
  /// **'Convert content into an actionable task with clear steps and deadlines'**
  String get optimizeAsTaskDescription;

  /// Description for idea optimization
  ///
  /// In en, this message translates to:
  /// **'Structure content as a creative idea with potential applications and benefits'**
  String get optimizeAsIdeaDescription;

  /// Description for note optimization
  ///
  /// In en, this message translates to:
  /// **'Format content as a well-structured note with key points and references'**
  String get optimizeAsNoteDescription;

  /// Message shown during optimization process
  ///
  /// In en, this message translates to:
  /// **'Using AI to enhance and structure your content'**
  String get optimizationInProgress;

  /// Title for auto assignment section
  ///
  /// In en, this message translates to:
  /// **'Auto Assignment'**
  String get autoAssignment;

  /// Informational text about auto assignment
  ///
  /// In en, this message translates to:
  /// **'Content will be automatically analyzed and assigned to the most appropriate connected platform'**
  String get autoAssignmentInfo;

  /// Button text to configure auto assignment settings
  ///
  /// In en, this message translates to:
  /// **'Configure Auto Assignment'**
  String get configureAutoAssignment;

  /// Message when navigating to settings page
  ///
  /// In en, this message translates to:
  /// **'Opening Settings...'**
  String get navigateToSettings;

  /// Button text to manage platform connections
  ///
  /// In en, this message translates to:
  /// **'Manage Connections'**
  String get manageConnections;

  /// Message when opening platform connections management
  ///
  /// In en, this message translates to:
  /// **'Opening platform connections management...'**
  String get platformConnectionsManagement;

  /// Message confirming successful assignment to a platform
  ///
  /// In en, this message translates to:
  /// **'Successfully assigned to'**
  String get successfullyAssignedTo;

  /// Text shown while recording is active
  ///
  /// In en, this message translates to:
  /// **'Recording'**
  String get recording;

  /// Text shown when recording is paused
  ///
  /// In en, this message translates to:
  /// **'Paused'**
  String get recordingPaused;

  /// Title for the reset recording confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Reset Recording?'**
  String get resetRecording;

  /// Confirmation message for resetting a recording
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to reset this recording? This action cannot be undone.'**
  String get resetRecordingConfirmation;

  /// Title for the recording guide section
  ///
  /// In en, this message translates to:
  /// **'How it works'**
  String get recordingGuideTitle;

  /// First step in recording guide
  ///
  /// In en, this message translates to:
  /// **'Record your voice note'**
  String get recordingGuideStep1;

  /// Second step in recording guide
  ///
  /// In en, this message translates to:
  /// **'ProjectPilot magically transcribes and enhances it'**
  String get recordingGuideStep2;

  /// Third step in recording guide
  ///
  /// In en, this message translates to:
  /// **'Send it to your favorite platform'**
  String get recordingGuideStep3;

  /// Error message when no internet connection is available
  ///
  /// In en, this message translates to:
  /// **'No internet connection'**
  String get noInternetConnection;

  /// Message shown while processing a purchase
  ///
  /// In en, this message translates to:
  /// **'Processing your purchase...'**
  String get processingPurchase;

  /// Title for package selection
  ///
  /// In en, this message translates to:
  /// **'Choose a Package'**
  String get choosePackage;

  /// Subtitle explaining the purpose of token packages
  ///
  /// In en, this message translates to:
  /// **'Get more tokens to use for AI text generation and audio transcription'**
  String get packageSelectionSubtitle;

  /// Message shown after successful token purchase
  ///
  /// In en, this message translates to:
  /// **'Successfully purchased {count} tokens!'**
  String purchaseSuccessful(int count);

  /// Label for content type chips section
  ///
  /// In en, this message translates to:
  /// **'Send to this platform'**
  String get sendToThisPlatform;

  /// No description provided for @choosePlatformTitle.
  ///
  /// In en, this message translates to:
  /// **'Choose Platform'**
  String get choosePlatformTitle;

  /// No description provided for @choosePlatformDescription.
  ///
  /// In en, this message translates to:
  /// **'Choose a platform to dispatch your content to'**
  String get choosePlatformDescription;

  /// No description provided for @contentPreview.
  ///
  /// In en, this message translates to:
  /// **'Content Preview'**
  String get contentPreview;

  /// No description provided for @suggested.
  ///
  /// In en, this message translates to:
  /// **'Suggested'**
  String get suggested;

  /// No description provided for @connectPlatform.
  ///
  /// In en, this message translates to:
  /// **'Connect Platform'**
  String get connectPlatform;

  /// Text shown during authentication process
  ///
  /// In en, this message translates to:
  /// **'Authenticating...'**
  String get authenticating;

  /// Button text for creating a new account
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// Hint text for email input field
  ///
  /// In en, this message translates to:
  /// **'Enter your email address'**
  String get emailFieldHint;

  /// Login button text
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// Hint text for password input field
  ///
  /// In en, this message translates to:
  /// **'Enter your password'**
  String get passwordFieldHint;

  /// Label for confirm password field
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// Hint text for confirm password field
  ///
  /// In en, this message translates to:
  /// **'Confirm your password'**
  String get confirmPasswordHint;

  /// Error message when confirm password is empty
  ///
  /// In en, this message translates to:
  /// **'Please confirm your password'**
  String get confirmPasswordRequired;

  /// Error message when passwords do not match
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// Hint text for name input field
  ///
  /// In en, this message translates to:
  /// **'Enter your full name'**
  String get nameFieldHint;

  /// Title for the history screen
  ///
  /// In en, this message translates to:
  /// **'History'**
  String get history;

  /// Title for the record tab
  ///
  /// In en, this message translates to:
  /// **'Record'**
  String get record;

  /// Title for the new entry dialog
  ///
  /// In en, this message translates to:
  /// **'New Entry'**
  String get newEntry;

  /// Validation message when content is empty
  ///
  /// In en, this message translates to:
  /// **'Please enter content'**
  String get pleaseEnterContent;

  /// Button text to update an entry
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// Message when there are no entries
  ///
  /// In en, this message translates to:
  /// **'No entries'**
  String get noEntries;

  /// Title for delete all audio files dialog
  ///
  /// In en, this message translates to:
  /// **'Delete All Audio Files'**
  String get deleteAllAudioFiles;

  /// Content for delete all audio files dialog
  ///
  /// In en, this message translates to:
  /// **'This will delete all voice recording files to free up storage space. The text content of your recordings will be preserved.\n\nThis action cannot be undone. Are you sure?'**
  String get deleteAllAudioFilesContent;

  /// Button text to delete all items
  ///
  /// In en, this message translates to:
  /// **'Delete All'**
  String get deleteAll;

  /// Tooltip for delete all audio files button
  ///
  /// In en, this message translates to:
  /// **'Delete all audio files'**
  String get deleteAllAudioFilesTooltip;

  /// Title for language selection dialog
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguageDialogTitle;

  /// Status text for enabled features
  ///
  /// In en, this message translates to:
  /// **'Enabled'**
  String get enabled;

  /// Status text for disabled features
  ///
  /// In en, this message translates to:
  /// **'Disabled'**
  String get disabled;

  /// Description when auto assignment is enabled
  ///
  /// In en, this message translates to:
  /// **'Tasks are automatically assigned to platforms'**
  String get autoAssignEnabledDesc;

  /// Description when auto assignment is disabled
  ///
  /// In en, this message translates to:
  /// **'Manual platform selection for tasks'**
  String get autoAssignDisabledDesc;

  /// Description for token packages
  ///
  /// In en, this message translates to:
  /// **'{name} package with {tokenAmount} tokens'**
  String packageDescription(String name, int tokenAmount);

  /// Title for about app screen
  ///
  /// In en, this message translates to:
  /// **'About {appName}'**
  String aboutAppTitle(String appName);

  /// App version display
  ///
  /// In en, this message translates to:
  /// **'Version {version}'**
  String appVersion(String version);

  /// Title for key features section
  ///
  /// In en, this message translates to:
  /// **'Key Features'**
  String get keyFeatures;

  /// Voice-to-task feature name
  ///
  /// In en, this message translates to:
  /// **'Voice-to-Task'**
  String get voiceToTask;

  /// Voice-to-task feature description
  ///
  /// In en, this message translates to:
  /// **'Create tasks using your voice'**
  String get voiceToTaskDesc;

  /// Multi-platform feature name
  ///
  /// In en, this message translates to:
  /// **'Multi-Platform'**
  String get multiPlatform;

  /// Multi-platform feature description
  ///
  /// In en, this message translates to:
  /// **'Connect to ClickUp, Notion, and more'**
  String get multiPlatformDesc;

  /// Multi-language feature name
  ///
  /// In en, this message translates to:
  /// **'Multi-Language'**
  String get multiLanguage;

  /// Multi-language feature description
  ///
  /// In en, this message translates to:
  /// **'Supports English, German, Russian, Turkish, and Arabic'**
  String get multiLanguageDesc;

  /// AI-powered feature name
  ///
  /// In en, this message translates to:
  /// **'AI-Powered'**
  String get aiPowered;

  /// AI-powered feature description
  ///
  /// In en, this message translates to:
  /// **'Smart task categorization and optimization'**
  String get aiPoweredDesc;

  /// Title for our team section
  ///
  /// In en, this message translates to:
  /// **'Our Team'**
  String get ourTeam;

  /// Copyright text
  ///
  /// In en, this message translates to:
  /// **'© {year} ProjectPilot. All rights reserved.'**
  String copyright(String year);

  /// Description for contact support screen
  ///
  /// In en, this message translates to:
  /// **'Have a question or need help? Fill out the form below and our support team will get back to you as soon as possible.'**
  String get contactSupportDesc;

  /// Label for name input field
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get nameField;

  /// Label for email input field
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get emailField;

  /// Label for message input field
  ///
  /// In en, this message translates to:
  /// **'Message'**
  String get messageField;

  /// Hint text for message input field
  ///
  /// In en, this message translates to:
  /// **'Enter your message'**
  String get messageFieldHint;

  /// Button text to submit a form
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submitButton;

  /// Validation error for name field
  ///
  /// In en, this message translates to:
  /// **'Please enter your name'**
  String get nameValidationError;

  /// Validation error for email field
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get emailValidationError;

  /// Validation error for message field
  ///
  /// In en, this message translates to:
  /// **'Please enter your message'**
  String get messageValidationError;

  /// Title for initialization error dialog
  ///
  /// In en, this message translates to:
  /// **'Initialization Error'**
  String get initializationError;

  /// Error message during app initialization
  ///
  /// In en, this message translates to:
  /// **'An error occurred during app initialization:\n{error}'**
  String errorDuringInit(String error);

  /// Button text to retry an action
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retryButton;

  /// Title for FAQ screen
  ///
  /// In en, this message translates to:
  /// **'FAQ'**
  String get faqTitle;

  /// Subtitle for FAQ screen
  ///
  /// In en, this message translates to:
  /// **'Frequently Asked Questions'**
  String get frequentlyAskedQuestions;

  /// Title for privacy policy screen
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicyTitle;

  /// Last updated timestamp
  ///
  /// In en, this message translates to:
  /// **'Last updated: {time}'**
  String lastUpdated(String time);

  /// Introduction text for privacy policy
  ///
  /// In en, this message translates to:
  /// **'This Privacy Policy describes how ProjectPilot (\"we\", \"us\", or \"our\") collects, uses, and discloses your personal information when you use our mobile application (the \"App\").'**
  String get privacyPolicyIntro;

  /// Section title for information collection
  ///
  /// In en, this message translates to:
  /// **'Information We Collect'**
  String get infoWeCollect;

  /// Content for information collection section
  ///
  /// In en, this message translates to:
  /// **'We collect information that you provide directly to us, such as when you create an account, update your profile, use the interactive features of our App, request customer support, or otherwise communicate with us.'**
  String get infoWeCollectContent;

  /// Section title for information usage
  ///
  /// In en, this message translates to:
  /// **'How We Use Your Information'**
  String get howWeUseInfo;

  /// Content for information usage section
  ///
  /// In en, this message translates to:
  /// **'We use the information we collect to provide, maintain, and improve our services, including to process transactions, send you related information, and provide customer support.'**
  String get howWeUseInfoContent;

  /// Section title for information sharing
  ///
  /// In en, this message translates to:
  /// **'Sharing of Information'**
  String get sharingOfInfo;

  /// Content for information sharing section
  ///
  /// In en, this message translates to:
  /// **'We may share the information we collect as follows: with third-party vendors, consultants, and other service providers who need access to such information to carry out work on our behalf; in response to a request for information if we believe disclosure is in accordance with any applicable law, regulation, or legal process.'**
  String get sharingOfInfoContent;

  /// Section title for user choices
  ///
  /// In en, this message translates to:
  /// **'Your Choices'**
  String get yourChoices;

  /// Content for user choices section
  ///
  /// In en, this message translates to:
  /// **'You may update, correct, or delete your account information at any time by logging into your account or contacting us. You may opt out of receiving promotional communications from us by following the instructions in those communications.'**
  String get yourChoicesContent;

  /// Section title for contact information
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// Contact information for privacy policy
  ///
  /// In en, this message translates to:
  /// **'If you have any questions about this Privacy Policy, please contact us at: <EMAIL>'**
  String get contactUsPrivacyContent;

  /// Title for terms of service screen
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfServiceTitle;

  /// Introduction text for terms of service
  ///
  /// In en, this message translates to:
  /// **'Please read these Terms of Service (\"Terms\", \"Terms of Service\") carefully before using the ProjectPilot mobile application (the \"Service\") operated by ProjectPilot (\"us\", \"we\", or \"our\").'**
  String get termsOfServiceIntro;

  /// Section title for acceptance of terms
  ///
  /// In en, this message translates to:
  /// **'Acceptance of Terms'**
  String get acceptanceOfTerms;

  /// Content for acceptance of terms section
  ///
  /// In en, this message translates to:
  /// **'By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.'**
  String get acceptanceOfTermsContent;

  /// Section title for service usage
  ///
  /// In en, this message translates to:
  /// **'Use of the Service'**
  String get useOfService;

  /// Content for service usage section
  ///
  /// In en, this message translates to:
  /// **'Our Service allows you to create, manage, and organize tasks and notes using voice commands. You are responsible for maintaining the confidentiality of your account and password and for restricting access to your computer or mobile device.'**
  String get useOfServiceContent;

  /// Section title for intellectual property
  ///
  /// In en, this message translates to:
  /// **'Intellectual Property'**
  String get intellectualProperty;

  /// Content for intellectual property section
  ///
  /// In en, this message translates to:
  /// **'The Service and its original content, features, and functionality are and will remain the exclusive property of ProjectPilot and its licensors. The Service is protected by copyright, trademark, and other laws.'**
  String get intellectualPropertyContent;

  /// Section title for termination
  ///
  /// In en, this message translates to:
  /// **'Termination'**
  String get termination;

  /// Content for termination section
  ///
  /// In en, this message translates to:
  /// **'We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.'**
  String get terminationContent;

  /// Section title for limitation of liability
  ///
  /// In en, this message translates to:
  /// **'Limitation of Liability'**
  String get limitationOfLiability;

  /// Content for limitation of liability section
  ///
  /// In en, this message translates to:
  /// **'In no event shall ProjectPilot, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.'**
  String get limitationOfLiabilityContent;

  /// Section title for changes to terms
  ///
  /// In en, this message translates to:
  /// **'Changes to Terms'**
  String get changesToTerms;

  /// Content for changes to terms section
  ///
  /// In en, this message translates to:
  /// **'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days\' notice prior to any new terms taking effect.'**
  String get changesToTermsContent;

  /// Contact information for terms of service
  ///
  /// In en, this message translates to:
  /// **'If you have any questions about these Terms, please contact us at: <EMAIL>'**
  String get contactUsTermsContent;

  /// FAQ question about ProjectPilot
  ///
  /// In en, this message translates to:
  /// **'What is ProjectPilot?'**
  String get whatIsProjectPilot;

  /// FAQ answer about ProjectPilot
  ///
  /// In en, this message translates to:
  /// **'ProjectPilot is a voice-to-task application that allows you to create tasks, notes, and ideas using voice commands. It can transcribe your voice and send the tasks to various platforms like ClickUp, Notion, and more.'**
  String get whatIsProjectPilotAnswer;

  /// FAQ question about connecting platforms
  ///
  /// In en, this message translates to:
  /// **'How do I connect to external platforms?'**
  String get howToConnectPlatforms;

  /// FAQ answer about connecting platforms
  ///
  /// In en, this message translates to:
  /// **'Go to the Settings screen and navigate to the \"Platform Connections\" section. There you can connect to various platforms like ClickUp, Notion, and Monday by clicking the Connect button and following the authentication process.'**
  String get howToConnectPlatformsAnswer;

  /// FAQ question about supported languages
  ///
  /// In en, this message translates to:
  /// **'What languages are supported?'**
  String get whatLanguagesSupported;

  /// FAQ answer about supported languages
  ///
  /// In en, this message translates to:
  /// **'ProjectPilot currently supports English, German, Russian, Turkish, and Arabic. You can change the language in the Settings screen.'**
  String get whatLanguagesSupportedAnswer;

  /// FAQ question about tokens
  ///
  /// In en, this message translates to:
  /// **'How do tokens work?'**
  String get howTokensWork;

  /// FAQ answer about tokens
  ///
  /// In en, this message translates to:
  /// **'Tokens are used for AI text generation and audio transcription. Each transcription and optimization consumes a certain number of tokens. You can purchase more tokens in the \"Upgrade Package\" section of the Settings screen.'**
  String get howTokensWorkAnswer;

  /// FAQ question about offline usage
  ///
  /// In en, this message translates to:
  /// **'Can I use ProjectPilot offline?'**
  String get canUseOffline;

  /// FAQ answer about offline usage
  ///
  /// In en, this message translates to:
  /// **'Some features of ProjectPilot require an internet connection, such as sending tasks to external platforms and using online transcription services. However, basic voice recording can work offline.'**
  String get canUseOfflineAnswer;

  /// FAQ question about profile customization
  ///
  /// In en, this message translates to:
  /// **'How do I customize my profile?'**
  String get howCustomizeProfile;

  /// FAQ answer about profile customization
  ///
  /// In en, this message translates to:
  /// **'You can update your profile information in the Settings screen under the \"Profile\" section.'**
  String get howCustomizeProfileAnswer;

  /// FAQ question about data security
  ///
  /// In en, this message translates to:
  /// **'Is my data secure?'**
  String get isDataSecure;

  /// FAQ answer about data security
  ///
  /// In en, this message translates to:
  /// **'Yes, we take data security seriously. Your data is encrypted and stored securely. We do not share your personal information with third parties without your consent. Please refer to our Privacy Policy for more details.'**
  String get isDataSecureAnswer;

  /// FAQ question about subscription cancellation
  ///
  /// In en, this message translates to:
  /// **'How do I cancel my subscription?'**
  String get howCancelSubscription;

  /// FAQ answer about subscription cancellation
  ///
  /// In en, this message translates to:
  /// **'You can manage your subscription through your app store account (Google Play Store or Apple App Store). Go to the subscription management section of your app store and cancel the ProjectPilot subscription.'**
  String get howCancelSubscriptionAnswer;

  /// The title of the ProjectPilot application
  ///
  /// In en, this message translates to:
  /// **'ProjectPilot'**
  String get appTitleProjectPilot;

  /// Title for project dashboard screen
  ///
  /// In en, this message translates to:
  /// **'Project Dashboard'**
  String get projectDashboard;

  /// Title for project analysis
  ///
  /// In en, this message translates to:
  /// **'Project Analysis'**
  String get projectAnalysis;

  /// Button text to ask questions about project
  ///
  /// In en, this message translates to:
  /// **'Ask About Project'**
  String get askAboutProject;

  /// Label for project status
  ///
  /// In en, this message translates to:
  /// **'Project Status'**
  String get projectStatus;

  /// Title for blockers section
  ///
  /// In en, this message translates to:
  /// **'Blockers'**
  String get blockers;

  /// Title for recommendations section
  ///
  /// In en, this message translates to:
  /// **'Recommendations'**
  String get recommendations;

  /// Label for critical path analysis
  ///
  /// In en, this message translates to:
  /// **'Critical Path'**
  String get criticalPath;

  /// Label for project progress
  ///
  /// In en, this message translates to:
  /// **'Project Progress'**
  String get projectProgress;

  /// Label for team members
  ///
  /// In en, this message translates to:
  /// **'Team Members'**
  String get teamMembers;

  /// Label for budget information
  ///
  /// In en, this message translates to:
  /// **'Budget'**
  String get budget;

  /// Label for project timeline
  ///
  /// In en, this message translates to:
  /// **'Timeline'**
  String get timeline;

  /// Label for project risks
  ///
  /// In en, this message translates to:
  /// **'Risks'**
  String get risks;

  /// Label for project insights
  ///
  /// In en, this message translates to:
  /// **'Insights'**
  String get insights;

  /// Loading message for project analysis
  ///
  /// In en, this message translates to:
  /// **'Analyzing project...'**
  String get analysisLoading;

  /// Error message when analysis fails
  ///
  /// In en, this message translates to:
  /// **'Analysis failed. Please try again.'**
  String get analysisFailed;

  /// Button text to refresh project data
  ///
  /// In en, this message translates to:
  /// **'Refresh Project'**
  String get refreshProject;

  /// Label when project is overdue
  ///
  /// In en, this message translates to:
  /// **'Project Overdue'**
  String get projectOverdue;

  /// Label when project is on track
  ///
  /// In en, this message translates to:
  /// **'Project On Track'**
  String get projectOnTrack;

  /// Label when project is at risk
  ///
  /// In en, this message translates to:
  /// **'Project At Risk'**
  String get projectAtRisk;

  /// Label for high priority blockers
  ///
  /// In en, this message translates to:
  /// **'High Priority Blockers'**
  String get highPriorityBlockers;

  /// Label for urgent recommendations
  ///
  /// In en, this message translates to:
  /// **'Urgent Recommendations'**
  String get urgentRecommendations;

  /// Days remaining until deadline
  ///
  /// In en, this message translates to:
  /// **'{count} days remaining'**
  String daysRemaining(int count);

  /// Tasks completion status
  ///
  /// In en, this message translates to:
  /// **'{completed} of {total} tasks completed'**
  String tasksCompleted(int completed, int total);

  /// Budget utilization display
  ///
  /// In en, this message translates to:
  /// **'Budget: {spent} of {total} used'**
  String budgetUtilization(double spent, double total);

  /// Placeholder text for project query input
  ///
  /// In en, this message translates to:
  /// **'Ask me anything about this project...'**
  String get queryProject;

  /// Loading message for query processing
  ///
  /// In en, this message translates to:
  /// **'Processing your question...'**
  String get processingQuery;

  /// Error message when query fails
  ///
  /// In en, this message translates to:
  /// **'Could not process your question. Please try again.'**
  String get queryFailed;

  /// Error message when project is not found
  ///
  /// In en, this message translates to:
  /// **'Project not found'**
  String get projectNotFound;

  /// Loading message for data sync
  ///
  /// In en, this message translates to:
  /// **'Syncing project data...'**
  String get syncingData;

  /// Label for active tasks
  ///
  /// In en, this message translates to:
  /// **'Active Tasks'**
  String get activeTasks;

  /// Label for completed tasks
  ///
  /// In en, this message translates to:
  /// **'Completed Tasks'**
  String get completedTasks;

  /// Label for blocked tasks
  ///
  /// In en, this message translates to:
  /// **'Blocked Tasks'**
  String get blockedTasks;

  /// Label for overdue tasks
  ///
  /// In en, this message translates to:
  /// **'Overdue Tasks'**
  String get overdueTasks;

  /// AI analysis confidence level
  ///
  /// In en, this message translates to:
  /// **'Confidence: {level}%'**
  String confidenceLevel(int level);

  /// Project risk score display
  ///
  /// In en, this message translates to:
  /// **'Risk Score: {score}/10'**
  String riskScore(double score);

  /// Title for risk assessment section
  ///
  /// In en, this message translates to:
  /// **'Risk Assessment'**
  String get riskAssessment;

  /// Title for budget overview section
  ///
  /// In en, this message translates to:
  /// **'Budget Overview'**
  String get budgetOverview;

  /// Title for key insights section
  ///
  /// In en, this message translates to:
  /// **'Key Insights'**
  String get keyInsights;

  /// Error message when analysis fails to load
  ///
  /// In en, this message translates to:
  /// **'Error Loading Analysis'**
  String get errorLoadingAnalysis;

  /// Button text to retry an operation
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Instruction text for analyzing project
  ///
  /// In en, this message translates to:
  /// **'Tap analyze to get AI insights'**
  String get tapAnalyzeToGetInsights;

  /// Button text to analyze project
  ///
  /// In en, this message translates to:
  /// **'Analyze Project'**
  String get analyzeProject;

  /// Title for AI analysis summary section
  ///
  /// In en, this message translates to:
  /// **'AI Analysis Summary'**
  String get aiAnalysisSummary;

  /// Status text for active items
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// Status text for inactive items
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// Label when project is over budget
  ///
  /// In en, this message translates to:
  /// **'Over Budget'**
  String get overBudget;

  /// Label for total budget amount
  ///
  /// In en, this message translates to:
  /// **'Total Budget'**
  String get totalBudget;

  /// Label for spent amount
  ///
  /// In en, this message translates to:
  /// **'Spent'**
  String get spent;

  /// Label for remaining amount
  ///
  /// In en, this message translates to:
  /// **'Remaining'**
  String get remaining;

  /// Title for analysis details section
  ///
  /// In en, this message translates to:
  /// **'Analysis Details'**
  String get analysisDetails;

  /// Label for confidence level
  ///
  /// In en, this message translates to:
  /// **'Confidence Level'**
  String get confidenceLevelLabel;

  /// Label for analysis date
  ///
  /// In en, this message translates to:
  /// **'Analysis Date'**
  String get analysisDate;

  /// Label for predicted completion date
  ///
  /// In en, this message translates to:
  /// **'Predicted Completion'**
  String get predictedCompletion;

  /// Critical priority/severity level
  ///
  /// In en, this message translates to:
  /// **'Critical'**
  String get critical;

  /// High priority/severity level
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// Medium priority/severity level
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// Low priority/severity level
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// Time indicator for very recent events
  ///
  /// In en, this message translates to:
  /// **'just now'**
  String get justNow;

  /// Title for critical paths section
  ///
  /// In en, this message translates to:
  /// **'Critical Paths'**
  String get criticalPaths;

  /// Generic error title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Message when no projects exist
  ///
  /// In en, this message translates to:
  /// **'No Projects Yet'**
  String get noProjectsYet;

  /// Instructions for connecting platforms
  ///
  /// In en, this message translates to:
  /// **'Connect your project management platforms to get started'**
  String get connectProjectManagementPlatforms;

  /// Title for ProjectPilot dashboard
  ///
  /// In en, this message translates to:
  /// **'ProjectPilot Dashboard'**
  String get projectPilotDashboard;

  /// Label for total projects count
  ///
  /// In en, this message translates to:
  /// **'Total Projects'**
  String get totalProjects;

  /// Title for recent projects section
  ///
  /// In en, this message translates to:
  /// **'Recent Projects'**
  String get recentProjects;

  /// Button text to view all items
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// Title for tasks overview section
  ///
  /// In en, this message translates to:
  /// **'Tasks Overview'**
  String get tasksOverview;

  /// Title for recent tasks section
  ///
  /// In en, this message translates to:
  /// **'Recent Tasks'**
  String get recentTasks;

  /// Message when no tasks are found
  ///
  /// In en, this message translates to:
  /// **'No tasks found'**
  String get noTasksFound;

  /// Status for tasks in progress
  ///
  /// In en, this message translates to:
  /// **'In Progress'**
  String get inProgress;

  /// Status for blocked tasks
  ///
  /// In en, this message translates to:
  /// **'Blocked'**
  String get blocked;

  /// Status for overdue tasks
  ///
  /// In en, this message translates to:
  /// **'Overdue'**
  String get overdue;

  /// Status for tasks to do
  ///
  /// In en, this message translates to:
  /// **'To Do'**
  String get toDo;

  /// Status for tasks in review
  ///
  /// In en, this message translates to:
  /// **'Review'**
  String get review;

  /// Status for completed tasks
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// Status for cancelled tasks
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// Label for progress information
  ///
  /// In en, this message translates to:
  /// **'Progress'**
  String get progress;

  /// Label for creation date
  ///
  /// In en, this message translates to:
  /// **'Created At'**
  String get createdAt;

  /// Label for deadline
  ///
  /// In en, this message translates to:
  /// **'Deadline'**
  String get deadline;

  /// Label for platform information
  ///
  /// In en, this message translates to:
  /// **'Platform'**
  String get platform;

  /// Project status - planning
  ///
  /// In en, this message translates to:
  /// **'Planning'**
  String get planning;

  /// Project status - on hold
  ///
  /// In en, this message translates to:
  /// **'On Hold'**
  String get onHold;

  /// Title for AI project assistant
  ///
  /// In en, this message translates to:
  /// **'AI Project Assistant'**
  String get aiProjectAssistant;

  /// Sample query for project assistant
  ///
  /// In en, this message translates to:
  /// **'What projects are behind schedule?'**
  String get whatProjectsAreBehindSchedule;

  /// Sample query for project assistant
  ///
  /// In en, this message translates to:
  /// **'Show me project risks'**
  String get showMeProjectRisks;

  /// Sample query for project assistant
  ///
  /// In en, this message translates to:
  /// **'Which tasks are blocked?'**
  String get whichTasksAreBlocked;

  /// Sample query for project assistant
  ///
  /// In en, this message translates to:
  /// **'Project progress summary'**
  String get projectProgressSummary;

  /// Sample query for project assistant
  ///
  /// In en, this message translates to:
  /// **'Team workload analysis'**
  String get teamWorkloadAnalysis;

  /// Button text to dismiss a notification
  ///
  /// In en, this message translates to:
  /// **'Dismiss'**
  String get dismiss;

  /// Title for role-based example prompts
  ///
  /// In en, this message translates to:
  /// **'Examples for {role}'**
  String examplesForRole(String role);

  /// Text to indicate user can tap prompt to use it
  ///
  /// In en, this message translates to:
  /// **'Tap to use'**
  String get tapToUse;

  /// Developer role display name
  ///
  /// In en, this message translates to:
  /// **'Developer'**
  String get roleDeveloper;

  /// Project Manager role display name
  ///
  /// In en, this message translates to:
  /// **'Project Manager'**
  String get roleProjectManager;

  /// Product Owner role display name
  ///
  /// In en, this message translates to:
  /// **'Product Owner'**
  String get roleProductOwner;

  /// QA Tester role display name
  ///
  /// In en, this message translates to:
  /// **'QA Tester'**
  String get roleQaTester;

  /// CTO/CEO role display name
  ///
  /// In en, this message translates to:
  /// **'CTO/CEO'**
  String get roleCtoCeo;

  /// Default text for all roles
  ///
  /// In en, this message translates to:
  /// **'all roles'**
  String get roleAllRoles;

  /// Developer role prompt 1
  ///
  /// In en, this message translates to:
  /// **'What tasks are currently assigned to me?'**
  String get developerPrompt1;

  /// Developer role prompt 2
  ///
  /// In en, this message translates to:
  /// **'What\'s blocking my code review?'**
  String get developerPrompt2;

  /// Developer role prompt 3
  ///
  /// In en, this message translates to:
  /// **'What\'s coming up in the next sprint?'**
  String get developerPrompt3;

  /// Developer role prompt 4
  ///
  /// In en, this message translates to:
  /// **'Which bugs have highest priority?'**
  String get developerPrompt4;

  /// Project Manager role prompt 1
  ///
  /// In en, this message translates to:
  /// **'What\'s delaying the current sprint?'**
  String get projectManagerPrompt1;

  /// Project Manager role prompt 2
  ///
  /// In en, this message translates to:
  /// **'Which tasks are overdue?'**
  String get projectManagerPrompt2;

  /// Project Manager role prompt 3
  ///
  /// In en, this message translates to:
  /// **'Who worked on the project last?'**
  String get projectManagerPrompt3;

  /// Project Manager role prompt 4
  ///
  /// In en, this message translates to:
  /// **'How is the current project progress?'**
  String get projectManagerPrompt4;

  /// Product Owner role prompt 1
  ///
  /// In en, this message translates to:
  /// **'What features are planned for the next release?'**
  String get productOwnerPrompt1;

  /// Product Owner role prompt 2
  ///
  /// In en, this message translates to:
  /// **'What\'s the feedback on our latest update?'**
  String get productOwnerPrompt2;

  /// Product Owner role prompt 3
  ///
  /// In en, this message translates to:
  /// **'Which user stories have highest priority?'**
  String get productOwnerPrompt3;

  /// Product Owner role prompt 4
  ///
  /// In en, this message translates to:
  /// **'How is our product backlog evolving?'**
  String get productOwnerPrompt4;

  /// QA Tester role prompt 1
  ///
  /// In en, this message translates to:
  /// **'What tests still need to be performed?'**
  String get qaTesterPrompt1;

  /// QA Tester role prompt 2
  ///
  /// In en, this message translates to:
  /// **'Are there any open critical bugs?'**
  String get qaTesterPrompt2;

  /// QA Tester role prompt 3
  ///
  /// In en, this message translates to:
  /// **'What\'s the current test coverage?'**
  String get qaTesterPrompt3;

  /// QA Tester role prompt 4
  ///
  /// In en, this message translates to:
  /// **'Which features are ready for testing?'**
  String get qaTesterPrompt4;

  /// CTO/CEO role prompt 1
  ///
  /// In en, this message translates to:
  /// **'How is the project running overall?'**
  String get ctoCeoPrompt1;

  /// CTO/CEO role prompt 2
  ///
  /// In en, this message translates to:
  /// **'What\'s currently costing us the most time?'**
  String get ctoCeoPrompt2;

  /// CTO/CEO role prompt 3
  ///
  /// In en, this message translates to:
  /// **'Which teams need support?'**
  String get ctoCeoPrompt3;

  /// CTO/CEO role prompt 4
  ///
  /// In en, this message translates to:
  /// **'How are we doing with budget and timeline?'**
  String get ctoCeoPrompt4;

  /// Default role prompt 1
  ///
  /// In en, this message translates to:
  /// **'What tasks are on the agenda today?'**
  String get defaultPrompt1;

  /// Default role prompt 2
  ///
  /// In en, this message translates to:
  /// **'What\'s the current project status?'**
  String get defaultPrompt2;

  /// Default role prompt 3
  ///
  /// In en, this message translates to:
  /// **'What are the next important steps?'**
  String get defaultPrompt3;

  /// Default role prompt 4
  ///
  /// In en, this message translates to:
  /// **'Are there any obstacles that need to be resolved?'**
  String get defaultPrompt4;

  /// Loading message while analyzing project
  ///
  /// In en, this message translates to:
  /// **'Analyzing project...'**
  String get analyzingProject;

  /// Title for suggested actions
  ///
  /// In en, this message translates to:
  /// **'Suggested Actions'**
  String get suggestedActions;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'de', 'en', 'ru', 'tr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'ru':
      return AppLocalizationsRu();
    case 'tr':
      return AppLocalizationsTr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
