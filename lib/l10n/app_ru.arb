{"@@locale": "ru", "appTitle": "ProjectPilot", "homeTitle": "Главная", "settingsTitle": "Настройки", "welcome": "Добро пожаловать", "@welcome": {"description": "Welcome text on splash screen"}, "welcomeSubtitle": "ProjectPilot – будущее управления проектами", "@welcomeSubtitle": {"description": "Subtitle on splash screen"}, "languageTitle": "Язык", "englishLanguage": "Английский", "germanLanguage": "Немецкий", "russianLanguage": "Русский", "turkishLanguage": "Турецкий", "arabicLanguage": "Арабский", "saveButton": "Сохранить", "cancelButton": "Отмена", "errorOccurred": "Произошла ошибка", "tryAgain": "Попробовать снова", "selectLanguage": "Выберите язык", "tokenBalance": "Балан<PERSON> токенов: {count}", "transcriptionTitle": "Транскрипция", "startRecording": "Начать запись", "stopRecording": "Остановить запись", "noTranscriptionYet": "Транскрипция отсутствует", "shareTranscription": "Поделиться транскрипцией", "themeTitle": "Тема", "selectTheme": "Выберите тему", "systemTheme": "Системная", "lightTheme": "Светлая", "darkTheme": "Тёмная", "toggleTheme": "Сменить тему", "apiKeysSection": "API-ключи", "openaiApiKey": "API-ключ OpenAI", "whisperApiKey": "API-к<PERSON><PERSON><PERSON>per", "apiKeyHint": "Введите ваш API-ключ", "tokenSection": "Токены и использование", "remainingMinutes": "Осталось минут: {count}", "purchasePackages": "Приобрести пакеты", "buyTokens": "Купить токены", "starterPackage": "Стартовый", "proPackage": "Профессиональный", "businessPackage": "Бизнес", "ultimatePackage": "Максимальный", "tokens": "{count} то<PERSON><PERSON>нов", "price": "€{price}", "profileSection": "Профиль", "username": "Имя пользователя", "email": "Эл. почта", "notAvailable": "Недоступно", "logoutButton": "Выйти", "apiKeySaved": "API-ключ успешно сохранен!", "introScreen1Title": "ИИ-Управляемая Проектная Аналитика", "@introScreen1Title": {"description": "Title for first intro screen"}, "introScreen1Description": "Держите красную нить в ваших проектах. Выявляйте проблемы заранее и держитесь курса с умными инсайтами.", "@introScreen1Description": {"description": "Description for first intro screen"}, "introScreen2Title": "Мульти-Платформенный Обзор Проектов", "@introScreen2Title": {"description": "Title for second intro screen"}, "introScreen2Description": "Соедините все ваши инструменты проектов в одной панели. ClickUp, Notion, Jira и другие - все синхронизировано.", "@introScreen2Description": {"description": "Description for second intro screen"}, "introScreen3Title": "Умное Обнаружение Проблем", "@introScreen3Title": {"description": "Title for third intro screen"}, "introScreen3Description": "Автоматически обнаруживайте препятствия, оценивайте риски и получайте действенные рекомендации для движения проектов.", "@introScreen3Description": {"description": "Description for third intro screen"}, "introMainTitle": "ProjectPilot", "@introMainTitle": {"description": "Main title for intro screens"}, "introMainSubtitle": "Никогда больше не теряйте след ваших проектов", "@introMainSubtitle": {"description": "Main subtitle for intro screens"}, "getStarted": "Начать", "@getStarted": {"description": "Text for get started button"}, "skip": "Пропустить", "@skip": {"description": "Text for skip button on intro screens"}, "next": "Далее", "@next": {"description": "Text for next button on intro screens"}, "loginTitle": "Добро пожаловать в ProjectPilot", "loginSubtitle": "Войдите, чтобы продолжить", "continueWithGoogle": "Продолжить с Google", "continueWithApple": "Продолжить с Apple", "continueWithEmail": "Продолжить с email", "moreOptions": "Больше вариантов", "continueWithLinkedIn": "Продолжить с LinkedIn", "continueWithPhone": "Продолжить с телефоном", "continueWithAzure": "Продолжить с Azure", "continueWithNotion": "Продолжить с Notion", "password": "Пароль", "forgotPassword": "Забыли пароль?", "signIn": "Войти", "signUp": "Регистрация", "dontHaveAccount": "Нет аккаунта?", "alreadyHaveAccount": "Уже есть аккаунт?", "phoneNumber": "Номер телефона", "or": "или", "createAccountText": "Создайте свой аккаунт ProjectPilot", "@createAccountText": {"description": "Subtitle text for sign-up screen"}, "usernameRequired": "Необходимо указать имя пользователя", "@usernameRequired": {"description": "Error message when username is empty"}, "usernameMinLength": "Имя пользователя должно содержать не менее 3 символов", "@usernameMinLength": {"description": "Error message when username is too short"}, "emailRequired": "Необходимо указать электронную почту", "@emailRequired": {"description": "Error message when email is empty"}, "invalidEmail": "Введите действительный адрес электронной почты", "@invalidEmail": {"description": "Error message when email format is invalid"}, "passwordRequired": "Необходимо указать пароль", "@passwordRequired": {"description": "Error message when password is empty"}, "passwordMinLength": "Пароль должен содержать не менее 6 символов", "@passwordMinLength": {"description": "Error message when password is too short"}, "signUpSuccessful": "Регистрация успешна!", "@signUpSuccessful": {"description": "Success message after signing up"}, "onboarding": "Заполните свой профиль", "@onboarding": {"description": "Title for the onboarding screen"}, "camera": "Камера", "@camera": {"description": "Option for taking a photo with camera"}, "gallery": "Галерея", "@gallery": {"description": "Option for selecting a photo from gallery"}, "birthYear": "Год рождения", "@birthYear": {"description": "Label for birth year selection"}, "selectBirthYear": "Выберите год рождения", "@selectBirthYear": {"description": "Title for birth year picker"}, "userRole": "Роль пользователя", "@userRole": {"description": "Label for user role selection"}, "developer": "Разработчик", "@developer": {"description": "Developer role option"}, "projectManager": "Проектный менеджер", "@projectManager": {"description": "Project Manager role option"}, "private": "Частное лицо", "@private": {"description": "Private role option"}, "other": "Другое", "@other": {"description": "Other option for selections"}, "usagePurpose": "Цель использования", "@usagePurpose": {"description": "Label for usage purpose selection"}, "tasks": "Зада<PERSON>и", "@tasks": {"description": "Tasks purpose option"}, "ideas": "Идеи", "@ideas": {"description": "Ideas purpose option"}, "notes": "Заметки", "@notes": {"description": "Notes purpose option"}, "connectPlatforms": "Подключение платформ", "@connectPlatforms": {"description": "Title for the platform connection screen"}, "platformsDescription": "Подключитесь к вашим любимым платформам", "@platformsDescription": {"description": "Description text for platform connection screen"}, "connectMore": "Подключить больше платформ позже", "@connectMore": {"description": "Text for connecting more platforms in the future"}, "finish": "Завершить", "@finish": {"description": "Text for finish button"}, "platformConnections": "Подключения платформ", "@platformConnections": {"description": "Title for the platform connections screen"}, "connectToPlatform": "Подключиться к {platform}", "@connectToPlatform": {"description": "Button text to connect to a specific platform", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "connected": "Подключено", "@connected": {"description": "Status text when a platform is connected"}, "disconnected": "Не подключено", "@disconnected": {"description": "Status text when a platform is not connected"}, "disconnect": "Отключить", "@disconnect": {"description": "Button text to disconnect from a platform"}, "connecting": "Подключение...", "@connecting": {"description": "Status text when connecting to a platform"}, "authorizationFailed": "Ошибка авторизации", "@authorizationFailed": {"description": "Error message when platform authorization fails"}, "connectionSuccessful": "Подключение успешно", "@connectionSuccessful": {"description": "Success message when platform connection is established"}, "connectionFailed": "Ошибка подключения", "@connectionFailed": {"description": "Error message when platform connection fails"}, "integrationAvailableFor": "Интеграция доступна для {platform}", "@integrationAvailableFor": {"description": "Text indicating a platform integration is available", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "allPlatforms": "Все платформы", "@allPlatforms": {"description": "Text for showing all available platforms"}, "managePlatformConnections": "Управление подключениями платформ", "@managePlatformConnections": {"description": "Description for the platform connections screen"}, "connectPlatformToSendTasks": "Подключите ваши любимые платформы для отправки задач напрямую", "@connectPlatformToSendTasks": {"description": "Explanation text for platform connections"}, "cancel": "Отмена", "@cancel": {"description": "Text for cancel button"}, "continueButton": "Продолжить", "@continueButton": {"description": "Text for continue button"}, "whatWouldYouLikeToDo": "Что бы вы хотели сделать?", "@whatWouldYouLikeToDo": {"description": "Prompt text for the recording screen"}, "recordingInProgress": "Идет запись...", "@recordingInProgress": {"description": "Text shown while recording is in progress"}, "processingYourVoice": "Обработка вашего голоса...", "@processingYourVoice": {"description": "Text shown while transcribing audio"}, "whereToSave": "Куда бы вы хотели сохранить это?", "@whereToSave": {"description": "Prompt for selecting a platform to save to"}, "noPlatformsConnected": "Нет подключенных платформ", "@noPlatformsConnected": {"description": "Text shown when no platforms are connected"}, "newRecording": "Новая запись", "@newRecording": {"description": "<PERSON><PERSON> text to start a new recording"}, "task": "Задача", "@task": {"description": "Label for task category"}, "note": "Заметка", "@note": {"description": "Label for note category"}, "idea": "Идея", "@idea": {"description": "Label for idea category"}, "uncategorized": "Без категории", "@uncategorized": {"description": "Label for uncategorized items"}, "edit": "Редактировать", "@edit": {"description": "Text for edit button"}, "share": "Поделиться", "@share": {"description": "Text for share button"}, "transcriptionResult": "Результат транскрипции", "@transcriptionResult": {"description": "Title for transcription result"}, "detectedIntent": "Обнаруженное намерение", "@detectedIntent": {"description": "Label for detected intent"}, "detectedEntities": "Обнаруженные сущности", "@detectedEntities": {"description": "Label for detected entities"}, "originalTranscription": "Оригинальная транскрипция", "@originalTranscription": {"description": "Label for original transcription"}, "entries": "Записи", "@entries": {"description": "Title for entries screen"}, "entryDetails": "Детали записи", "@entryDetails": {"description": "Title for entry details screen"}, "addUpdate": "Добавить обновление", "@addUpdate": {"description": "Button text to add an update to an entry"}, "updateDetails": "Детали обновления", "@updateDetails": {"description": "Title for update details dialog"}, "deleteUpdate": "Удалить обновление", "@deleteUpdate": {"description": "Title for delete update confirmation dialog"}, "deleteUpdateConfirmation": "Вы уверены, что хотите удалить это обновление?", "@deleteUpdateConfirmation": {"description": "Confirmation message for deleting an update"}, "deleteEntry": "Удалить запись", "@deleteEntry": {"description": "Title for delete entry confirmation dialog"}, "deleteEntryConfirmation": "Вы уверены, что хотите удалить эту запись и все её обновления?", "@deleteEntryConfirmation": {"description": "Confirmation message for deleting an entry"}, "entryCreated": "Запись успешно создана", "@entryCreated": {"description": "Success message when an entry is created"}, "entryDeleted": "Запись успешно удалена", "@entryDeleted": {"description": "Success message when an entry is deleted"}, "entryUpdateAdded": "Обновление успешно добавлено", "@entryUpdateAdded": {"description": "Success message when an update is added to an entry"}, "noEntriesFound": "Записи не найдены", "@noEntriesFound": {"description": "Message when no entries are found"}, "createEntryPrompt": "Создайте новую запись, нажав на кнопку +", "@createEntryPrompt": {"description": "Prompt to create a new entry"}, "createEntry": "Создать запись", "@createEntry": {"description": "Button text to create a new entry"}, "content": "Содержание", "@content": {"description": "Label for content input field"}, "type": "Тип", "@type": {"description": "Label for type selection"}, "create": "Создать", "@create": {"description": "Button text to create something"}, "search": "Поиск", "@search": {"description": "Button text for search"}, "close": "Закрыть", "@close": {"description": "Button text to close a dialog or screen"}, "save": "Сохранить", "@save": {"description": "Button text to save changes"}, "editEntry": "Редактировать запись", "@editEntry": {"description": "Title for edit entry dialog"}, "sharingNotImplemented": "Функция поделиться еще не реализована", "@sharingNotImplemented": {"description": "Message when sharing functionality is not implemented"}, "updates": "Обновления", "@updates": {"description": "Label for updates section"}, "voice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@voice": {"description": "Label for voice source type"}, "manual": "Вручн<PERSON>ю", "@manual": {"description": "Label for manual source type"}, "addedAt": "Добавлено", "@addedAt": {"description": "Label for when an update was added"}, "source": "Источник", "@source": {"description": "Label for source of an update"}, "created": "Создано", "@created": {"description": "Label for creation date"}, "selectEntry": "Выберите запись", "@selectEntry": {"description": "Title for entry selection dialog"}, "entryReferenceDetected": "Я обнаружил ссылку на существующую запись", "@entryReferenceDetected": {"description": "Message when a reference to an existing entry is detected"}, "selectEntryToUpdate": "Выберите запись, которую хотите обновить", "@selectEntryToUpdate": {"description": "Prompt to select an entry to update"}, "createNewInstead": "Создать новую вместо этого", "@createNewInstead": {"description": "Button text to create a new entry instead of updating an existing one"}, "creatingNewEntry": "Создание новой записи", "@creatingNewEntry": {"description": "Message when creating a new entry instead of updating an existing one"}, "all": "Все", "@all": {"description": "Label for showing all items"}, "chatHistory": "История чата", "@chatHistory": {"description": "Title for the chat history section in recording view"}, "authenticating": "Аутентификация...", "@authenticating": {"description": "Text shown during authentication process"}, "createAccount": "Создать аккаунт", "@createAccount": {"description": "Button text for creating a new account"}, "login": "Войти", "@login": {"description": "Login button text"}, "confirmPassword": "Подтвердите пароль", "@confirmPassword": {"description": "Label for confirm password field"}, "confirmPasswordHint": "Подтвердите ваш пароль", "@confirmPasswordHint": {"description": "Hint text for confirm password field"}, "confirmPasswordRequired": "Пожалуйста, подтвердите ваш пароль", "@confirmPasswordRequired": {"description": "Error message when confirm password is empty"}, "passwordsDoNotMatch": "Пароли не совпадают", "@passwordsDoNotMatch": {"description": "Error message when passwords do not match"}, "nameFieldHint": "Введите ваше полное имя", "@nameFieldHint": {"description": "Hint text for name input field"}, "connectedSuccessfully": "Успешно подключено!", "@connectedSuccessfully": {"description": "Success message when connection is established"}, "riskAssessment": "Оценка рисков", "@riskAssessment": {"description": "Title for risk assessment section"}, "budgetOverview": "Обзор бюджета", "@budgetOverview": {"description": "Title for budget overview section"}, "keyInsights": "Ключевые выводы", "@keyInsights": {"description": "Title for key insights section"}, "errorLoadingAnalysis": "Ошибка загрузки анализа", "@errorLoadingAnalysis": {"description": "Error message when analysis fails to load"}, "retry": "Повторить", "@retry": {"description": "Button text to retry an operation"}, "tapAnalyzeToGetInsights": "Нажмите \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" для получения AI-инсайтов", "@tapAnalyzeToGetInsights": {"description": "Instruction text for analyzing project"}, "analyzeProject": "Анализировать проект", "@analyzeProject": {"description": "Button text to analyze project"}, "aiAnalysisSummary": "Сводка AI-анализа", "@aiAnalysisSummary": {"description": "Title for AI analysis summary section"}, "active": "Активный", "@active": {"description": "Status text for active items"}, "inactive": "Неактивный", "@inactive": {"description": "Status text for inactive items"}, "overBudget": "Превышение бюджета", "@overBudget": {"description": "Label when project is over budget"}, "totalBudget": "<PERSON><PERSON><PERSON><PERSON> бюджет", "@totalBudget": {"description": "Label for total budget amount"}, "spent": "Потрачено", "@spent": {"description": "Label for spent amount"}, "remaining": "Остается", "@remaining": {"description": "Label for remaining amount"}, "analysisDetails": "Детали анализа", "@analysisDetails": {"description": "Title for analysis details section"}, "confidenceLevelLabel": "Уровень уверенности", "@confidenceLevelLabel": {"description": "Label for confidence level"}, "analysisDate": "Дата анализа", "@analysisDate": {"description": "Label for analysis date"}, "predictedCompletion": "Прогнозируемое завершение", "@predictedCompletion": {"description": "Label for predicted completion date"}, "critical": "Критический", "@critical": {"description": "Critical priority/severity level"}, "high": "Высокий", "@high": {"description": "High priority/severity level"}, "medium": "Средний", "@medium": {"description": "Medium priority/severity level"}, "low": "Низкий", "@low": {"description": "Low priority/severity level"}, "justNow": "только что", "@justNow": {"description": "Time indicator for very recent events"}, "blockers": "Блокировщики", "@blockers": {"description": "Title for blockers section"}, "criticalPaths": "Критические пути", "@criticalPaths": {"description": "Title for critical paths section"}, "error": "Ошибка", "@error": {"description": "Generic error title"}, "noProjectsYet": "Пока нет проектов", "@noProjectsYet": {"description": "Message when no projects exist"}, "connectProjectManagementPlatforms": "Подключите платформы управления проектами для начала работы", "@connectProjectManagementPlatforms": {"description": "Instructions for connecting platforms"}, "projectPilotDashboard": "Панель управления ProjectPilot", "@projectPilotDashboard": {"description": "Title for ProjectPilot dashboard"}, "totalProjects": "Всего проектов", "@totalProjects": {"description": "Label for total projects count"}, "recentProjects": "Недавние проекты", "@recentProjects": {"description": "Title for recent projects section"}, "viewAll": "Показать все", "@viewAll": {"description": "Button text to view all items"}, "tasksOverview": "Обзор задач", "@tasksOverview": {"description": "Title for tasks overview section"}, "recentTasks": "Недавние задачи", "@recentTasks": {"description": "Title for recent tasks section"}, "noTasksFound": "Задачи не найдены", "@noTasksFound": {"description": "Message when no tasks are found"}, "inProgress": "В процессе", "@inProgress": {"description": "Status for tasks in progress"}, "blocked": "Заблокированная", "@blocked": {"description": "Status for blocked tasks"}, "overdue": "Просрочено", "@overdue": {"description": "Status for overdue tasks"}, "toDo": "К выполнению", "@toDo": {"description": "Status for tasks to do"}, "review": "На проверке", "@review": {"description": "Status for tasks in review"}, "done": "Выполнено", "@done": {"description": "Status for completed tasks"}, "cancelled": "Отменено", "@cancelled": {"description": "Status for cancelled tasks"}, "progress": "Прогресс", "@progress": {"description": "Label for progress information"}, "createdAt": "Создано", "@createdAt": {"description": "Label for creation date"}, "deadline": "Крайний срок", "@deadline": {"description": "Label for deadline"}, "platform": "Платформа", "@platform": {"description": "Label for platform information"}, "planning": "Планирование", "@planning": {"description": "Project status - planning"}, "onHold": "Приостановлен", "@onHold": {"description": "Project status - on hold"}, "aiProjectAssistant": "AI-помощник проекта", "@aiProjectAssistant": {"description": "Title for AI project assistant"}, "showMeProjectRisks": "Покажи мне риски проекта", "@showMeProjectRisks": {"description": "Sample query for project assistant"}, "projectProgressSummary": "Сводка прогресса проекта", "@projectProgressSummary": {"description": "Sample query for project assistant"}, "teamWorkloadAnalysis": "Анализ нагрузки команды", "@teamWorkloadAnalysis": {"description": "Sample query for project assistant"}, "dismiss": "Скрыть", "@dismiss": {"description": "Button text to dismiss a notification"}, "recommendations": "Рекомендации", "@recommendations": {"description": "Title for recommendations section"}, "projectAnalysis": "Анализ проекта", "@projectAnalysis": {"description": "Title for project analysis"}, "analyzingProject": "Анализ проекта...", "@analyzingProject": {"description": "Loading message while analyzing project"}, "suggestedActions": "Предлагаемые действия", "@suggestedActions": {"description": "Title for suggested actions"}, "examplesForRole": "Примеры для {role}", "@examplesForRole": {"description": "Title for role-based example prompts", "placeholders": {"role": {"type": "String", "example": "Developer"}}}, "tapToUse": "Нажмите для использования", "@tapToUse": {"description": "Text to indicate user can tap prompt to use it"}, "roleDeveloper": "Разработчик", "@roleDeveloper": {"description": "Developer role display name"}, "roleProjectManager": "Проект-менеджер", "@roleProjectManager": {"description": "Project Manager role display name"}, "roleProductOwner": "Владелец продукта", "@roleProductOwner": {"description": "Product Owner role display name"}, "roleQaTester": "QA-тестировщик", "@roleQaTester": {"description": "QA Tester role display name"}, "roleCtoCeo": "CTO/CEO", "@roleCtoCeo": {"description": "CTO/CEO role display name"}, "roleAllRoles": "все роли", "@roleAllRoles": {"description": "Default text for all roles"}, "developerPrompt1": "Какие задачи мне сейчас назначены?", "@developerPrompt1": {"description": "Developer role prompt 1"}, "developerPrompt2": "Что блокирует мой код-ревью?", "@developerPrompt2": {"description": "Developer role prompt 2"}, "developerPrompt3": "Что запланировано на следующий спринт?", "@developerPrompt3": {"description": "Developer role prompt 3"}, "developerPrompt4": "Какие баги имеют высший приоритет?", "@developerPrompt4": {"description": "Developer role prompt 4"}, "projectManagerPrompt1": "Что задерживает текущий спринт?", "@projectManagerPrompt1": {"description": "Project Manager role prompt 1"}, "projectManagerPrompt2": "Какие задачи просрочены?", "@projectManagerPrompt2": {"description": "Project Manager role prompt 2"}, "projectManagerPrompt3": "Кто последним работал над проектом?", "@projectManagerPrompt3": {"description": "Project Manager role prompt 3"}, "projectManagerPrompt4": "Каков текущий прогресс проекта?", "@projectManagerPrompt4": {"description": "Project Manager role prompt 4"}, "productOwnerPrompt1": "Какие функции запланированы на следующий релиз?", "@productOwnerPrompt1": {"description": "Product Owner role prompt 1"}, "productOwnerPrompt2": "Какие отзывы о нашем последнем обновлении?", "@productOwnerPrompt2": {"description": "Product Owner role prompt 2"}, "productOwnerPrompt3": "Какие пользовательские истории имеют высший приоритет?", "@productOwnerPrompt3": {"description": "Product Owner role prompt 3"}, "productOwnerPrompt4": "Как развивается наш продуктовый бэклог?", "@productOwnerPrompt4": {"description": "Product Owner role prompt 4"}, "qaTesterPrompt1": "Какие тесты еще нужно провести?", "@qaTesterPrompt1": {"description": "QA Tester role prompt 1"}, "qaTesterPrompt2": "Есть ли открытые критические баги?", "@qaTesterPrompt2": {"description": "QA Tester role prompt 2"}, "qaTesterPrompt3": "Какое текущее покрытие тестами?", "@qaTesterPrompt3": {"description": "QA Tester role prompt 3"}, "qaTesterPrompt4": "Какие функции готовы к тестированию?", "@qaTesterPrompt4": {"description": "QA Tester role prompt 4"}, "ctoCeoPrompt1": "Как идет проект в целом?", "@ctoCeoPrompt1": {"description": "CTO/CEO role prompt 1"}, "ctoCeoPrompt2": "Что сейчас отнимает у нас больше всего времени?", "@ctoCeoPrompt2": {"description": "CTO/CEO role prompt 2"}, "ctoCeoPrompt3": "Какие команды нуждаются в поддержке?", "@ctoCeoPrompt3": {"description": "CTO/CEO role prompt 3"}, "ctoCeoPrompt4": "Как обстоят дела с бюджетом и сроками?", "@ctoCeoPrompt4": {"description": "CTO/CEO role prompt 4"}, "defaultPrompt1": "Какие задачи на сегодня?", "@defaultPrompt1": {"description": "Default role prompt 1"}, "defaultPrompt2": "Каков текущий статус проекта?", "@defaultPrompt2": {"description": "Default role prompt 2"}, "defaultPrompt3": "Какие следующие важные шаги?", "@defaultPrompt3": {"description": "Default role prompt 3"}, "defaultPrompt4": "Есть ли препятствия, которые нужно устранить?", "@defaultPrompt4": {"description": "Default role prompt 4"}}