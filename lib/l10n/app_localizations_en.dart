// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'ProjectPilot';

  @override
  String get homeTitle => 'Home';

  @override
  String get settingsTitle => 'Settings';

  @override
  String get welcome => 'Welcome';

  @override
  String get welcomeSubtitle =>
      'ProjectPilot – the future of project management';

  @override
  String get languageTitle => 'Language';

  @override
  String get englishLanguage => 'English';

  @override
  String get germanLanguage => 'German';

  @override
  String get russianLanguage => 'Russian';

  @override
  String get turkishLanguage => 'Turkish';

  @override
  String get arabicLanguage => 'Arabic';

  @override
  String get saveButton => 'Save';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get errorOccurred => 'An error occurred';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String tokenBalance(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Token Balance: $countString';
  }

  @override
  String get tokenBalanceDescription =>
      'Tokens for AI text generation with GPT';

  @override
  String get transcriptionTitle => 'Transcription';

  @override
  String get startRecording => 'Start Recording';

  @override
  String get stopRecording => 'Stop Recording';

  @override
  String get noTranscriptionYet => 'No transcription yet';

  @override
  String get shareTranscription => 'Share Transcription';

  @override
  String get themeTitle => 'Theme';

  @override
  String get selectTheme => 'Select Theme';

  @override
  String get systemTheme => 'System Default';

  @override
  String get lightTheme => 'Light';

  @override
  String get darkTheme => 'Dark';

  @override
  String get toggleTheme => 'Toggle Theme';

  @override
  String get apiKeysSection => 'API Keys';

  @override
  String get openaiApiKey => 'OpenAI API Key';

  @override
  String get whisperApiKey => 'Whisper API Key';

  @override
  String get apiKeyHint => 'Enter your API key here';

  @override
  String get tokenSection => 'Tokens & Usage';

  @override
  String remainingMinutes(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Remaining Minutes: $countString';
  }

  @override
  String get minutesBalanceDescription =>
      'Minutes available for audio transcription with Whisper';

  @override
  String get purchasePackages => 'Purchase Packages';

  @override
  String get buyTokens => 'Buy Tokens';

  @override
  String get starterPackage => 'Starter';

  @override
  String get proPackage => 'Pro';

  @override
  String get businessPackage => 'Business';

  @override
  String get ultimatePackage => 'Ultimate';

  @override
  String tokens(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return '$countString Tokens';
  }

  @override
  String price(double price) {
    final intl.NumberFormat priceNumberFormat = intl
        .NumberFormat.simpleCurrency(locale: localeName, name: 'EUR');
    final String priceString = priceNumberFormat.format(price);

    return '€$priceString';
  }

  @override
  String get profileSection => 'Profile';

  @override
  String get username => 'Username';

  @override
  String get email => 'Email';

  @override
  String get notAvailable => 'Not available';

  @override
  String get logoutButton => 'Logout';

  @override
  String get apiKeySaved => 'API key saved successfully!';

  @override
  String get introScreen1Title => 'AI-Powered Project Intelligence';

  @override
  String get introScreen1Description =>
      'Keep the red thread in your projects. Identify problems early and stay on track with intelligent insights.';

  @override
  String get introScreen2Title => 'Multi-Platform Project Overview';

  @override
  String get introScreen2Description =>
      'Connect all your project tools in one dashboard. ClickUp, Notion, Jira, and more - all synchronized.';

  @override
  String get introScreen3Title => 'Smart Problem Detection';

  @override
  String get introScreen3Description =>
      'Automatically detect blockers, assess risks, and get actionable recommendations to keep your projects moving.';

  @override
  String get introMainTitle => 'ProjectPilot';

  @override
  String get introMainSubtitle => 'Never lose track of your projects again';

  @override
  String get getStarted => 'Get Started';

  @override
  String get skip => 'Skip';

  @override
  String get next => 'Next';

  @override
  String get loginTitle => 'Welcome to ProjectPilot';

  @override
  String get loginSubtitle => 'Sign in to continue';

  @override
  String get continueWithGoogle => 'Continue with Google';

  @override
  String get continueWithApple => 'Continue with Apple';

  @override
  String get continueWithEmail => 'Continue with Email';

  @override
  String get moreOptions => 'More Options';

  @override
  String get continueWithLinkedIn => 'Continue with LinkedIn';

  @override
  String get continueWithPhone => 'Continue with Phone';

  @override
  String get continueWithAzure => 'Continue with Azure';

  @override
  String get continueWithNotion => 'Continue with Notion';

  @override
  String get password => 'Password';

  @override
  String get forgotPassword => 'Forgot password?';

  @override
  String get signIn => 'Sign In';

  @override
  String get signUp => 'Sign Up';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get or => 'or';

  @override
  String get createAccountText => 'Create your ProjectPilot account';

  @override
  String get usernameRequired => 'Username is required';

  @override
  String get usernameMinLength => 'Username must be at least 3 characters';

  @override
  String get emailRequired => 'Please enter your email';

  @override
  String get invalidEmail => 'Enter a valid email';

  @override
  String get passwordRequired => 'Password is required';

  @override
  String get passwordMinLength => 'Password must be at least 6 characters';

  @override
  String get signUpSuccessful => 'Signup successful!';

  @override
  String get onboarding => 'Complete Your Profile';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get birthYear => 'Birth Year';

  @override
  String get selectBirthYear => 'Select Birth Year';

  @override
  String get userRole => 'User Role';

  @override
  String get developer => 'Developer';

  @override
  String get projectManager => 'Project Manager';

  @override
  String get private => 'Private';

  @override
  String get other => 'Other';

  @override
  String get usagePurpose => 'Usage Purpose';

  @override
  String get tasks => 'Tasks';

  @override
  String get notes => 'Notes';

  @override
  String get ideas => 'Ideas';

  @override
  String get connectPlatforms => 'Connect Platforms';

  @override
  String get platformsDescription => 'Connect to your favorite platforms';

  @override
  String get connectMore => 'Connect more platforms later';

  @override
  String get finish => 'Finish';

  @override
  String get platformConnections => 'Platform Connections';

  @override
  String connectToPlatform(String platform) {
    return 'Connect to $platform';
  }

  @override
  String get connected => 'Connected';

  @override
  String get disconnected => 'Not Connected';

  @override
  String get disconnect => 'Disconnect';

  @override
  String get connecting => 'Connecting...';

  @override
  String get authorizationFailed => 'Authorization Failed';

  @override
  String get connectionSuccessful => 'Connection Successful';

  @override
  String get connectionFailed => 'Connection Failed';

  @override
  String integrationAvailableFor(String platform) {
    return 'Integration available for $platform';
  }

  @override
  String get allPlatforms => 'All Platforms';

  @override
  String get managePlatformConnections => 'Manage platform connections';

  @override
  String get connectPlatformToSendTasks =>
      'Connect your favorite platforms to send tasks directly';

  @override
  String get cancel => 'Cancel';

  @override
  String get whatWouldYouLikeToDo => 'What would you like to do?';

  @override
  String get recordingInProgress => 'Recording in progress...';

  @override
  String get processingYourVoice => 'Processing your voice...';

  @override
  String get whereToSave => 'Where would you like to save this?';

  @override
  String get noPlatformsConnected =>
      'No platforms connected. Connect a platform to continue.';

  @override
  String get newRecording => 'New Recording';

  @override
  String get uncategorized => 'Uncategorized';

  @override
  String get preferences => 'Preferences';

  @override
  String get upgradePackage => 'Upgrade Package';

  @override
  String get infoAndSupport => 'Info & Support';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get platformConnected => 'Connected';

  @override
  String get platformNotConnected => 'Not connected';

  @override
  String get connect => 'Connect';

  @override
  String get upgradeMessage =>
      'Get more tokens and recording minutes by upgrading your package';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get faq => 'FAQ';

  @override
  String get contactSupport => 'Contact Support';

  @override
  String get aboutApp => 'About ProjectPilot';

  @override
  String get settingsLanguageSection => 'Language';

  @override
  String get appearanceAndBehavior => 'Appearance & Behavior';

  @override
  String get settingsInfoSection => 'Information & Support';

  @override
  String get helpAndSupport => 'Help & Support';

  @override
  String get privacyTermsFaq => 'Privacy, Terms, FAQ';

  @override
  String get settingsAbout => 'About';

  @override
  String get storageManagement => 'Storage Management';

  @override
  String get voiceRecordings => 'Voice Recordings';

  @override
  String usingStorage(String size) {
    return 'Using $size of storage';
  }

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get automaticAssignment => 'Automatic Assignment';

  @override
  String get whereToSendTask => 'Where should I send this?';

  @override
  String get selectPlatform => 'Select Platform';

  @override
  String get selectProject => 'Select Project / Notebook / Board';

  @override
  String get sendNow => 'Send Now';

  @override
  String get noProjectsFound => 'No projects found';

  @override
  String get taskSentSuccessfully => 'Task sent successfully';

  @override
  String get platformDetected => 'Platform detected from your voice';

  @override
  String get projectDetected => 'Project detected from your voice';

  @override
  String get intentDetected => 'Intent detected from your voice';

  @override
  String get editContent => 'Edit content';

  @override
  String get noConnectedPlatforms => 'No connected platforms';

  @override
  String get connectPlatformsInSettings =>
      'Connect platforms in settings to send tasks';

  @override
  String get newTask => 'New Task';

  @override
  String get newNote => 'New Note';

  @override
  String get newIdea => 'New Idea';

  @override
  String get edit => 'Edit';

  @override
  String get share => 'Share';

  @override
  String get transcriptionResult => 'Transcription Result';

  @override
  String get detectedIntent => 'Detected Intent';

  @override
  String get detectedEntities => 'Detected Entities';

  @override
  String get originalTranscription => 'Original Transcription';

  @override
  String get addConnection => 'Add Connection';

  @override
  String get noConnections => 'No Connections';

  @override
  String get addConnectionsDescription =>
      'Connect your favorite platforms to send tasks directly';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String confirmDeleteConnectionMessage(String platform) {
    return 'Are you sure you want to delete the connection to $platform?';
  }

  @override
  String get delete => 'Delete';

  @override
  String platformConnectedMessage(String platform) {
    return 'Connected to $platform';
  }

  @override
  String get speechHistoryTitle => 'Speech History';

  @override
  String get clearHistory => 'Clear History';

  @override
  String get tapMicToStart => 'Tap the microphone to start recording';

  @override
  String get suggestedPlatform => 'Suggested platform based on your speech:';

  @override
  String get confirm => 'Confirm';

  @override
  String get back => 'Back';

  @override
  String taskSent(String platform) {
    return 'Task sent to $platform';
  }

  @override
  String get iRecognized => 'I recognized:';

  @override
  String get targetPlatform => 'Target:';

  @override
  String get editBeforeSending => 'Edit before sending';

  @override
  String get selectCommandType => 'What would you like to create?';

  @override
  String get createTaskCommand => 'Create Task';

  @override
  String get addNoteCommand => 'Add Note';

  @override
  String get saveIdeaCommand => 'Save Idea';

  @override
  String get enterProjectName => 'Enter project name';

  @override
  String get selectProjectPrompt => 'Select Project';

  @override
  String get entries => 'Entries';

  @override
  String get entryDetails => 'Entry Details';

  @override
  String get addUpdate => 'Add Update';

  @override
  String get updateDetails => 'Update Details';

  @override
  String get deleteUpdate => 'Delete Update';

  @override
  String get deleteUpdateConfirmation =>
      'Are you sure you want to delete this update?';

  @override
  String get deleteEntry => 'Delete Entry';

  @override
  String get deleteEntryConfirmation =>
      'Are you sure you want to delete this entry? This action cannot be undone.';

  @override
  String get entryCreated => 'Entry created successfully';

  @override
  String get entryDeleted => 'Entry deleted';

  @override
  String get entryUpdateAdded => 'Update added successfully';

  @override
  String get noEntriesFound => 'No entries found';

  @override
  String get createEntryPrompt => 'Create a new entry by tapping the + button';

  @override
  String get createNewEntry => 'Create New Entry';

  @override
  String get title => 'Title';

  @override
  String get titleHint => 'Enter title here';

  @override
  String get description => 'Description';

  @override
  String get descriptionHint => 'Enter description here';

  @override
  String get time => 'Time';

  @override
  String get date => 'Date';

  @override
  String dateFormat(int month, int day, int year) {
    return '$month/$day/$year';
  }

  @override
  String get type => 'Type';

  @override
  String get priority => 'Priority';

  @override
  String get priorityLow => 'Low';

  @override
  String get priorityMedium => 'Medium';

  @override
  String get priorityHigh => 'High';

  @override
  String get priorityUrgent => 'Urgent';

  @override
  String get destination => 'Destination';

  @override
  String get clickupPlatform => 'ClickUp';

  @override
  String get notionPlatform => 'Notion';

  @override
  String get asanaPlatform => 'Asana';

  @override
  String get mondayPlatform => 'Monday';

  @override
  String get jiraPlatform => 'Jira';

  @override
  String get trelloPlatform => 'Trello';

  @override
  String get create => 'Create';

  @override
  String get pleaseEnterTitle => 'Please enter a title';

  @override
  String get all => 'All';

  @override
  String get searchHint => 'Search';

  @override
  String get chats => 'Chats';

  @override
  String get newChat => 'New Chat';

  @override
  String get searchResults => 'Search Results';

  @override
  String get noResultsFound => 'No results found';

  @override
  String get projects => 'Projects';

  @override
  String get newFolder => 'New Folder';

  @override
  String get createNewFolder => 'Create New Folder';

  @override
  String get enterFolderName => 'Enter folder name';

  @override
  String get rename => 'Rename';

  @override
  String get renameChat => 'Rename Chat';

  @override
  String get renameFolder => 'Rename Folder';

  @override
  String get enterNewName => 'Enter new name';

  @override
  String get moveToFolder => 'Move to folder';

  @override
  String get deleteChat => 'Delete Chat';

  @override
  String get deleteFolder => 'Delete Folder';

  @override
  String get deleteChatConfirmation =>
      'Are you sure you want to delete this chat? This action cannot be undone.';

  @override
  String get deleteFolderConfirmation =>
      'Are you sure you want to delete this folder? This action cannot be undone.';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String daysAgo(int count) {
    return '$count days ago';
  }

  @override
  String get chatHistory => 'Chat History';

  @override
  String get addNote => 'Add Note';

  @override
  String get pinMessage => 'Pin Message';

  @override
  String get bookmark => 'Bookmark';

  @override
  String get messageOptions => 'Message Options';

  @override
  String get messageDeleted => 'Message deleted';

  @override
  String get messagePinned => 'Message pinned';

  @override
  String get messageBookmarked => 'Message bookmarked';

  @override
  String get noteAdded => 'Note added';

  @override
  String get enterNote => 'Enter your note';

  @override
  String get addNoteToMessage => 'Add Note to Message';

  @override
  String get editMessage => 'Edit Message';

  @override
  String get enterMessage => 'Enter your message';

  @override
  String get messageMoved => 'Message moved to folder';

  @override
  String get selectFolder => 'Select Folder';

  @override
  String get folderCreated => 'Folder created';

  @override
  String get noFolders => 'No folders yet';

  @override
  String get createFolderPrompt => 'Create a folder to organize your chats';

  @override
  String get noChatsInFolder => 'No chats in this folder';

  @override
  String get addChatsToFolder => 'Add chats by dragging them here';

  @override
  String get noChatsYet => 'No conversations yet';

  @override
  String get startNewChatPrompt => 'Start a new chat to begin';

  @override
  String get noRecentItems => 'No recent items';

  @override
  String get recentItems => 'Recent Items';

  @override
  String get todaysTasks => 'Today\'s Tasks';

  @override
  String get tapToViewTasks => 'Tap to view your tasks';

  @override
  String get appDescription => 'Your smart voice-to-task assistant';

  @override
  String get offlineMode => 'Offline Mode';

  @override
  String get offlineModeDescription =>
      'You\'re currently offline. Some features may be limited.';

  @override
  String get syncPending => 'Sync Pending';

  @override
  String get syncComplete => 'Sync Complete';

  @override
  String get syncFailed => 'Sync Failed';

  @override
  String get retrySync => 'Retry Sync';

  @override
  String get continueInOfflineMode => 'Continue in Offline Mode';

  @override
  String get networkError => 'Network Error';

  @override
  String get checkConnection =>
      'Please check your internet connection and try again.';

  @override
  String get pause => 'Pause';

  @override
  String get resume => 'Resume';

  @override
  String get reset => 'Reset';

  @override
  String get transcriptionComplete => 'Transcription Complete';

  @override
  String get viewDetails => 'View Details';

  @override
  String get continueButton => 'Continue';

  @override
  String get collection => 'Collection';

  @override
  String get newest => 'Newest';

  @override
  String get oldest => 'Oldest';

  @override
  String get name => 'Name';

  @override
  String get category => 'Category';

  @override
  String get synced => 'Synced';

  @override
  String get unsynced => 'Unsynced';

  @override
  String get notSynced => 'Not Synced';

  @override
  String get search => 'Search';

  @override
  String get closeSearch => 'Close Search';

  @override
  String get listView => 'List View';

  @override
  String get gridView => 'Grid View';

  @override
  String get createFirstEntry => 'Create First Entry';

  @override
  String get noEntriesYet => 'No entries yet';

  @override
  String get createNewEntryDescription =>
      'Create your first task, note, or idea using the button below.';

  @override
  String get completed => 'Completed';

  @override
  String get noSearchResults => 'No search results found';

  @override
  String get clearSearch => 'Clear Search';

  @override
  String get unassignedEntriesWarning => 'Unassigned Entries';

  @override
  String unassignedEntriesDescription(int count) {
    return 'You have $count unassigned entries waiting to be assigned to a platform';
  }

  @override
  String get assignNow => 'Assign Now';

  @override
  String assignedEntries(int count) {
    return 'Assigned Entries ($count)';
  }

  @override
  String get searchEntries => 'Search entries...';

  @override
  String get archive => 'Archive';

  @override
  String get assign => 'Assign';

  @override
  String get entryArchived => 'Entry archived';

  @override
  String get undo => 'UNDO';

  @override
  String get restoreFunctionalityNotImplemented =>
      'Restore functionality not yet implemented';

  @override
  String assignedToPlatform(String platform) {
    return 'Assigned to $platform';
  }

  @override
  String get recordingFileNotFound =>
      'Recording file not found. It may have been deleted or moved.';

  @override
  String get errorPlayingRecording => 'Error playing recording';

  @override
  String get assignmentSuccessful => 'Assignment Successful';

  @override
  String assignedCategoryToPlatform(String category, String platform) {
    return '$category assigned to $platform';
  }

  @override
  String get myTasks => 'My Tasks';

  @override
  String get task => 'Task';

  @override
  String get myIdeas => 'My Ideas';

  @override
  String get idea => 'Idea';

  @override
  String get myNotes => 'My Notes';

  @override
  String get note => 'Note';

  @override
  String get noTasksYet => 'No tasks yet';

  @override
  String get noIdeasYet => 'No ideas yet';

  @override
  String get noNotesYet => 'No notes yet';

  @override
  String get addNew => 'Add New';

  @override
  String get optimizedFor => 'Optimized for';

  @override
  String get optimizedContent => 'Optimized Content';

  @override
  String get contentOptimization => 'Content Optimization';

  @override
  String get platformAssignment => 'Platform Assignment';

  @override
  String get audioPlaybackError => 'Audio playback error';

  @override
  String get audioFileCorrupted =>
      'Unable to play the audio file. It may be corrupted.';

  @override
  String get loadingAudioPlayer => 'Loading audio player...';

  @override
  String get optimizingFor => 'Optimizing for';

  @override
  String get choosePlatformToAssign => 'Choose Platform to Assign';

  @override
  String get assigningTo => 'Assigning to';

  @override
  String get autoAssignmentEnabled =>
      'Auto-assignment is enabled. This item will be automatically processed.';

  @override
  String get deleteRecordingConfirmation =>
      'Are you sure you want to delete this recording? This action cannot be undone.';

  @override
  String get deletingRecording => 'Deleting recording...';

  @override
  String get save => 'Save';

  @override
  String get created => 'Created';

  @override
  String get content => 'Content';

  @override
  String get createEntry => 'Create Entry';

  @override
  String get taskReminder => 'Task Reminder';

  @override
  String get selectTime => 'Select time';

  @override
  String get changeTime => 'Change Time';

  @override
  String get noReminder => 'No Reminder';

  @override
  String get editEntry => 'Edit Entry';

  @override
  String get originalContent => 'Original Content';

  @override
  String get textEntry => 'Text Entry';

  @override
  String get voiceEntry => 'Voice Entry';

  @override
  String get optimizeAs => 'Optimize as';

  @override
  String get optimizeAsTaskDescription =>
      'Convert content into an actionable task with clear steps and deadlines';

  @override
  String get optimizeAsIdeaDescription =>
      'Structure content as a creative idea with potential applications and benefits';

  @override
  String get optimizeAsNoteDescription =>
      'Format content as a well-structured note with key points and references';

  @override
  String get optimizationInProgress =>
      'Using AI to enhance and structure your content';

  @override
  String get autoAssignment => 'Auto Assignment';

  @override
  String get autoAssignmentInfo =>
      'Content will be automatically analyzed and assigned to the most appropriate connected platform';

  @override
  String get configureAutoAssignment => 'Configure Auto Assignment';

  @override
  String get navigateToSettings => 'Opening Settings...';

  @override
  String get manageConnections => 'Manage Connections';

  @override
  String get platformConnectionsManagement =>
      'Opening platform connections management...';

  @override
  String get successfullyAssignedTo => 'Successfully assigned to';

  @override
  String get recording => 'Recording';

  @override
  String get recordingPaused => 'Paused';

  @override
  String get resetRecording => 'Reset Recording?';

  @override
  String get resetRecordingConfirmation =>
      'Are you sure you want to reset this recording? This action cannot be undone.';

  @override
  String get recordingGuideTitle => 'How it works';

  @override
  String get recordingGuideStep1 => 'Record your voice note';

  @override
  String get recordingGuideStep2 =>
      'ProjectPilot magically transcribes and enhances it';

  @override
  String get recordingGuideStep3 => 'Send it to your favorite platform';

  @override
  String get noInternetConnection => 'No internet connection';

  @override
  String get processingPurchase => 'Processing your purchase...';

  @override
  String get choosePackage => 'Choose a Package';

  @override
  String get packageSelectionSubtitle =>
      'Get more tokens to use for AI text generation and audio transcription';

  @override
  String purchaseSuccessful(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Successfully purchased $countString tokens!';
  }

  @override
  String get sendToThisPlatform => 'Send to this platform';

  @override
  String get choosePlatformTitle => 'Choose Platform';

  @override
  String get choosePlatformDescription =>
      'Choose a platform to dispatch your content to';

  @override
  String get contentPreview => 'Content Preview';

  @override
  String get suggested => 'Suggested';

  @override
  String get connectPlatform => 'Connect Platform';

  @override
  String get authenticating => 'Authenticating...';

  @override
  String get createAccount => 'Create Account';

  @override
  String get emailFieldHint => 'Enter your email address';

  @override
  String get login => 'Login';

  @override
  String get passwordFieldHint => 'Enter your password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get confirmPasswordHint => 'Confirm your password';

  @override
  String get confirmPasswordRequired => 'Please confirm your password';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get nameFieldHint => 'Enter your full name';

  @override
  String get history => 'History';

  @override
  String get record => 'Record';

  @override
  String get newEntry => 'New Entry';

  @override
  String get pleaseEnterContent => 'Please enter content';

  @override
  String get update => 'Update';

  @override
  String get noEntries => 'No entries';

  @override
  String get deleteAllAudioFiles => 'Delete All Audio Files';

  @override
  String get deleteAllAudioFilesContent =>
      'This will delete all voice recording files to free up storage space. The text content of your recordings will be preserved.\n\nThis action cannot be undone. Are you sure?';

  @override
  String get deleteAll => 'Delete All';

  @override
  String get deleteAllAudioFilesTooltip => 'Delete all audio files';

  @override
  String get selectLanguageDialogTitle => 'Select Language';

  @override
  String get enabled => 'Enabled';

  @override
  String get disabled => 'Disabled';

  @override
  String get autoAssignEnabledDesc =>
      'Tasks are automatically assigned to platforms';

  @override
  String get autoAssignDisabledDesc => 'Manual platform selection for tasks';

  @override
  String packageDescription(String name, int tokenAmount) {
    return '$name package with $tokenAmount tokens';
  }

  @override
  String aboutAppTitle(String appName) {
    return 'About $appName';
  }

  @override
  String appVersion(String version) {
    return 'Version $version';
  }

  @override
  String get keyFeatures => 'Key Features';

  @override
  String get voiceToTask => 'Voice-to-Task';

  @override
  String get voiceToTaskDesc => 'Create tasks using your voice';

  @override
  String get multiPlatform => 'Multi-Platform';

  @override
  String get multiPlatformDesc => 'Connect to ClickUp, Notion, and more';

  @override
  String get multiLanguage => 'Multi-Language';

  @override
  String get multiLanguageDesc =>
      'Supports English, German, Russian, Turkish, and Arabic';

  @override
  String get aiPowered => 'AI-Powered';

  @override
  String get aiPoweredDesc => 'Smart task categorization and optimization';

  @override
  String get ourTeam => 'Our Team';

  @override
  String copyright(String year) {
    return '© $year ProjectPilot. All rights reserved.';
  }

  @override
  String get contactSupportDesc =>
      'Have a question or need help? Fill out the form below and our support team will get back to you as soon as possible.';

  @override
  String get nameField => 'Name';

  @override
  String get emailField => 'Email';

  @override
  String get messageField => 'Message';

  @override
  String get messageFieldHint => 'Enter your message';

  @override
  String get submitButton => 'Submit';

  @override
  String get nameValidationError => 'Please enter your name';

  @override
  String get emailValidationError => 'Please enter a valid email';

  @override
  String get messageValidationError => 'Please enter your message';

  @override
  String get initializationError => 'Initialization Error';

  @override
  String errorDuringInit(String error) {
    return 'An error occurred during app initialization:\n$error';
  }

  @override
  String get retryButton => 'Retry';

  @override
  String get faqTitle => 'FAQ';

  @override
  String get frequentlyAskedQuestions => 'Frequently Asked Questions';

  @override
  String get privacyPolicyTitle => 'Privacy Policy';

  @override
  String lastUpdated(String time) {
    return 'Last updated: $time';
  }

  @override
  String get privacyPolicyIntro =>
      'This Privacy Policy describes how ProjectPilot (\"we\", \"us\", or \"our\") collects, uses, and discloses your personal information when you use our mobile application (the \"App\").';

  @override
  String get infoWeCollect => 'Information We Collect';

  @override
  String get infoWeCollectContent =>
      'We collect information that you provide directly to us, such as when you create an account, update your profile, use the interactive features of our App, request customer support, or otherwise communicate with us.';

  @override
  String get howWeUseInfo => 'How We Use Your Information';

  @override
  String get howWeUseInfoContent =>
      'We use the information we collect to provide, maintain, and improve our services, including to process transactions, send you related information, and provide customer support.';

  @override
  String get sharingOfInfo => 'Sharing of Information';

  @override
  String get sharingOfInfoContent =>
      'We may share the information we collect as follows: with third-party vendors, consultants, and other service providers who need access to such information to carry out work on our behalf; in response to a request for information if we believe disclosure is in accordance with any applicable law, regulation, or legal process.';

  @override
  String get yourChoices => 'Your Choices';

  @override
  String get yourChoicesContent =>
      'You may update, correct, or delete your account information at any time by logging into your account or contacting us. You may opt out of receiving promotional communications from us by following the instructions in those communications.';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get contactUsPrivacyContent =>
      'If you have any questions about this Privacy Policy, please contact us at: <EMAIL>';

  @override
  String get termsOfServiceTitle => 'Terms of Service';

  @override
  String get termsOfServiceIntro =>
      'Please read these Terms of Service (\"Terms\", \"Terms of Service\") carefully before using the ProjectPilot mobile application (the \"Service\") operated by ProjectPilot (\"us\", \"we\", or \"our\").';

  @override
  String get acceptanceOfTerms => 'Acceptance of Terms';

  @override
  String get acceptanceOfTermsContent =>
      'By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.';

  @override
  String get useOfService => 'Use of the Service';

  @override
  String get useOfServiceContent =>
      'Our Service allows you to create, manage, and organize tasks and notes using voice commands. You are responsible for maintaining the confidentiality of your account and password and for restricting access to your computer or mobile device.';

  @override
  String get intellectualProperty => 'Intellectual Property';

  @override
  String get intellectualPropertyContent =>
      'The Service and its original content, features, and functionality are and will remain the exclusive property of ProjectPilot and its licensors. The Service is protected by copyright, trademark, and other laws.';

  @override
  String get termination => 'Termination';

  @override
  String get terminationContent =>
      'We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.';

  @override
  String get limitationOfLiability => 'Limitation of Liability';

  @override
  String get limitationOfLiabilityContent =>
      'In no event shall ProjectPilot, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.';

  @override
  String get changesToTerms => 'Changes to Terms';

  @override
  String get changesToTermsContent =>
      'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days\' notice prior to any new terms taking effect.';

  @override
  String get contactUsTermsContent =>
      'If you have any questions about these Terms, please contact us at: <EMAIL>';

  @override
  String get whatIsProjectPilot => 'What is ProjectPilot?';

  @override
  String get whatIsProjectPilotAnswer =>
      'ProjectPilot is a voice-to-task application that allows you to create tasks, notes, and ideas using voice commands. It can transcribe your voice and send the tasks to various platforms like ClickUp, Notion, and more.';

  @override
  String get howToConnectPlatforms => 'How do I connect to external platforms?';

  @override
  String get howToConnectPlatformsAnswer =>
      'Go to the Settings screen and navigate to the \"Platform Connections\" section. There you can connect to various platforms like ClickUp, Notion, and Monday by clicking the Connect button and following the authentication process.';

  @override
  String get whatLanguagesSupported => 'What languages are supported?';

  @override
  String get whatLanguagesSupportedAnswer =>
      'ProjectPilot currently supports English, German, Russian, Turkish, and Arabic. You can change the language in the Settings screen.';

  @override
  String get howTokensWork => 'How do tokens work?';

  @override
  String get howTokensWorkAnswer =>
      'Tokens are used for AI text generation and audio transcription. Each transcription and optimization consumes a certain number of tokens. You can purchase more tokens in the \"Upgrade Package\" section of the Settings screen.';

  @override
  String get canUseOffline => 'Can I use ProjectPilot offline?';

  @override
  String get canUseOfflineAnswer =>
      'Some features of ProjectPilot require an internet connection, such as sending tasks to external platforms and using online transcription services. However, basic voice recording can work offline.';

  @override
  String get howCustomizeProfile => 'How do I customize my profile?';

  @override
  String get howCustomizeProfileAnswer =>
      'You can update your profile information in the Settings screen under the \"Profile\" section.';

  @override
  String get isDataSecure => 'Is my data secure?';

  @override
  String get isDataSecureAnswer =>
      'Yes, we take data security seriously. Your data is encrypted and stored securely. We do not share your personal information with third parties without your consent. Please refer to our Privacy Policy for more details.';

  @override
  String get howCancelSubscription => 'How do I cancel my subscription?';

  @override
  String get howCancelSubscriptionAnswer =>
      'You can manage your subscription through your app store account (Google Play Store or Apple App Store). Go to the subscription management section of your app store and cancel the ProjectPilot subscription.';

  @override
  String get appTitleProjectPilot => 'ProjectPilot';

  @override
  String get projectDashboard => 'Project Dashboard';

  @override
  String get projectAnalysis => 'Project Analysis';

  @override
  String get askAboutProject => 'Ask About Project';

  @override
  String get projectStatus => 'Project Status';

  @override
  String get blockers => 'Blockers';

  @override
  String get recommendations => 'Recommendations';

  @override
  String get criticalPath => 'Critical Path';

  @override
  String get projectProgress => 'Project Progress';

  @override
  String get teamMembers => 'Team Members';

  @override
  String get budget => 'Budget';

  @override
  String get timeline => 'Timeline';

  @override
  String get risks => 'Risks';

  @override
  String get insights => 'Insights';

  @override
  String get analysisLoading => 'Analyzing project...';

  @override
  String get analysisFailed => 'Analysis failed. Please try again.';

  @override
  String get refreshProject => 'Refresh Project';

  @override
  String get projectOverdue => 'Project Overdue';

  @override
  String get projectOnTrack => 'Project On Track';

  @override
  String get projectAtRisk => 'Project At Risk';

  @override
  String get highPriorityBlockers => 'High Priority Blockers';

  @override
  String get urgentRecommendations => 'Urgent Recommendations';

  @override
  String daysRemaining(int count) {
    return '$count days remaining';
  }

  @override
  String tasksCompleted(int completed, int total) {
    return '$completed of $total tasks completed';
  }

  @override
  String budgetUtilization(double spent, double total) {
    final intl.NumberFormat spentNumberFormat = intl.NumberFormat.currency(
      locale: localeName,
    );
    final String spentString = spentNumberFormat.format(spent);
    final intl.NumberFormat totalNumberFormat = intl.NumberFormat.currency(
      locale: localeName,
    );
    final String totalString = totalNumberFormat.format(total);

    return 'Budget: $spentString of $totalString used';
  }

  @override
  String get queryProject => 'Ask me anything about this project...';

  @override
  String get processingQuery => 'Processing your question...';

  @override
  String get queryFailed =>
      'Could not process your question. Please try again.';

  @override
  String get projectNotFound => 'Project not found';

  @override
  String get syncingData => 'Syncing project data...';

  @override
  String get activeTasks => 'Active Tasks';

  @override
  String get completedTasks => 'Completed Tasks';

  @override
  String get blockedTasks => 'Blocked Tasks';

  @override
  String get overdueTasks => 'Overdue Tasks';

  @override
  String confidenceLevel(int level) {
    return 'Confidence: $level%';
  }

  @override
  String riskScore(double score) {
    final intl.NumberFormat scoreNumberFormat = intl
        .NumberFormat.decimalPattern(localeName);
    final String scoreString = scoreNumberFormat.format(score);

    return 'Risk Score: $scoreString/10';
  }

  @override
  String get riskAssessment => 'Risk Assessment';

  @override
  String get budgetOverview => 'Budget Overview';

  @override
  String get keyInsights => 'Key Insights';

  @override
  String get errorLoadingAnalysis => 'Error Loading Analysis';

  @override
  String get retry => 'Retry';

  @override
  String get tapAnalyzeToGetInsights => 'Tap analyze to get AI insights';

  @override
  String get analyzeProject => 'Analyze Project';

  @override
  String get aiAnalysisSummary => 'AI Analysis Summary';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get overBudget => 'Over Budget';

  @override
  String get totalBudget => 'Total Budget';

  @override
  String get spent => 'Spent';

  @override
  String get remaining => 'Remaining';

  @override
  String get analysisDetails => 'Analysis Details';

  @override
  String get confidenceLevelLabel => 'Confidence Level';

  @override
  String get analysisDate => 'Analysis Date';

  @override
  String get predictedCompletion => 'Predicted Completion';

  @override
  String get critical => 'Critical';

  @override
  String get high => 'High';

  @override
  String get medium => 'Medium';

  @override
  String get low => 'Low';

  @override
  String get justNow => 'just now';

  @override
  String get criticalPaths => 'Critical Paths';

  @override
  String get error => 'Error';

  @override
  String get noProjectsYet => 'No Projects Yet';

  @override
  String get connectProjectManagementPlatforms =>
      'Connect your project management platforms to get started';

  @override
  String get projectPilotDashboard => 'ProjectPilot Dashboard';

  @override
  String get totalProjects => 'Total Projects';

  @override
  String get recentProjects => 'Recent Projects';

  @override
  String get viewAll => 'View All';

  @override
  String get tasksOverview => 'Tasks Overview';

  @override
  String get recentTasks => 'Recent Tasks';

  @override
  String get noTasksFound => 'No tasks found';

  @override
  String get inProgress => 'In Progress';

  @override
  String get blocked => 'Blocked';

  @override
  String get overdue => 'Overdue';

  @override
  String get toDo => 'To Do';

  @override
  String get review => 'Review';

  @override
  String get done => 'Done';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get progress => 'Progress';

  @override
  String get createdAt => 'Created At';

  @override
  String get deadline => 'Deadline';

  @override
  String get platform => 'Platform';

  @override
  String get planning => 'Planning';

  @override
  String get onHold => 'On Hold';

  @override
  String get aiProjectAssistant => 'AI Project Assistant';

  @override
  String get whatProjectsAreBehindSchedule =>
      'What projects are behind schedule?';

  @override
  String get showMeProjectRisks => 'Show me project risks';

  @override
  String get whichTasksAreBlocked => 'Which tasks are blocked?';

  @override
  String get projectProgressSummary => 'Project progress summary';

  @override
  String get teamWorkloadAnalysis => 'Team workload analysis';

  @override
  String get dismiss => 'Dismiss';

  @override
  String examplesForRole(String role) {
    return 'Examples for $role';
  }

  @override
  String get tapToUse => 'Tap to use';

  @override
  String get roleDeveloper => 'Developer';

  @override
  String get roleProjectManager => 'Project Manager';

  @override
  String get roleProductOwner => 'Product Owner';

  @override
  String get roleQaTester => 'QA Tester';

  @override
  String get roleCtoCeo => 'CTO/CEO';

  @override
  String get roleAllRoles => 'all roles';

  @override
  String get developerPrompt1 => 'What tasks are currently assigned to me?';

  @override
  String get developerPrompt2 => 'What\'s blocking my code review?';

  @override
  String get developerPrompt3 => 'What\'s coming up in the next sprint?';

  @override
  String get developerPrompt4 => 'Which bugs have highest priority?';

  @override
  String get projectManagerPrompt1 => 'What\'s delaying the current sprint?';

  @override
  String get projectManagerPrompt2 => 'Which tasks are overdue?';

  @override
  String get projectManagerPrompt3 => 'Who worked on the project last?';

  @override
  String get projectManagerPrompt4 => 'How is the current project progress?';

  @override
  String get productOwnerPrompt1 =>
      'What features are planned for the next release?';

  @override
  String get productOwnerPrompt2 =>
      'What\'s the feedback on our latest update?';

  @override
  String get productOwnerPrompt3 => 'Which user stories have highest priority?';

  @override
  String get productOwnerPrompt4 => 'How is our product backlog evolving?';

  @override
  String get qaTesterPrompt1 => 'What tests still need to be performed?';

  @override
  String get qaTesterPrompt2 => 'Are there any open critical bugs?';

  @override
  String get qaTesterPrompt3 => 'What\'s the current test coverage?';

  @override
  String get qaTesterPrompt4 => 'Which features are ready for testing?';

  @override
  String get ctoCeoPrompt1 => 'How is the project running overall?';

  @override
  String get ctoCeoPrompt2 => 'What\'s currently costing us the most time?';

  @override
  String get ctoCeoPrompt3 => 'Which teams need support?';

  @override
  String get ctoCeoPrompt4 => 'How are we doing with budget and timeline?';

  @override
  String get defaultPrompt1 => 'What tasks are on the agenda today?';

  @override
  String get defaultPrompt2 => 'What\'s the current project status?';

  @override
  String get defaultPrompt3 => 'What are the next important steps?';

  @override
  String get defaultPrompt4 =>
      'Are there any obstacles that need to be resolved?';

  @override
  String get analyzingProject => 'Analyzing project...';

  @override
  String get suggestedActions => 'Suggested Actions';
}
