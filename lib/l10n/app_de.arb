{"@@locale": "de", "appTitle": "ProjectPilot", "homeTitle": "Startseite", "settingsTitle": "Einstellungen", "welcome": "<PERSON><PERSON><PERSON><PERSON>", "@welcome": {"description": "Welcome text on splash screen"}, "welcomeSubtitle": "ProjectPilot – die Zukunft des Projektmanagements", "@welcomeSubtitle": {"description": "Subtitle on splash screen"}, "languageTitle": "<PERSON><PERSON><PERSON>", "englishLanguage": "<PERSON><PERSON><PERSON>", "germanLanguage": "De<PERSON>ch", "russianLanguage": "<PERSON><PERSON>", "turkishLanguage": "Türkisch", "arabicLanguage": "Arabisch", "saveButton": "Speichern", "cancelButton": "Abbrechen", "errorOccurred": "Ein Fehler ist aufgetreten", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "selectLanguage": "Sprache auswählen", "tokenBalance": "Token-Guthaben: {count}", "transcriptionTitle": "Transkription", "startRecording": "Aufnah<PERSON> starten", "stopRecording": "<PERSON><PERSON><PERSON><PERSON>den", "noTranscriptionYet": "Noch keine Transkription", "shareTranscription": "Transkription teilen", "themeTitle": "Design", "selectTheme": "Design auswählen", "systemTheme": "Systemeinstellung", "lightTheme": "Hell", "darkTheme": "<PERSON><PERSON><PERSON>", "toggleTheme": "Design wechseln", "apiKeysSection": "API-Schlüssel", "openaiApiKey": "OpenAI API-Schlüssel", "whisperApiKey": "Whisper API-Schlüssel", "apiKeyHint": "Geben Sie Ihren API-Schlüssel ein", "tokenSection": "Tokens & Nutzung", "remainingMinutes": "Verbleibende Minuten: {count}", "purchasePackages": "<PERSON><PERSON> kaufen", "buyTokens": "Tokens kaufen", "starterPackage": "Starter", "proPackage": "Pro", "businessPackage": "Business", "ultimatePackage": "Ultimate", "tokens": "{count} Tokens", "price": "€{price}", "profileSection": "Profil", "username": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-Mail", "notAvailable": "Nicht verfügbar", "logoutButton": "Abmelden", "apiKeySaved": "API-Schlüssel erfolgreich gespeichert!", "introScreen1Title": "KI-gesteuerte Projektintelligenz", "@introScreen1Title": {"description": "Title for first intro screen"}, "introScreen1Description": "Behalten Sie den roten Faden in Ihren Projekten. Erkennen Sie Probleme frühzeitig und bleiben Sie mit intelligenten Einsichten auf Kurs.", "@introScreen1Description": {"description": "Description for first intro screen"}, "introScreen2Title": "Multi-Plattform Projektübersicht", "@introScreen2Title": {"description": "Title for second intro screen"}, "introScreen2Description": "Verbinden Sie all Ihre Projekt-Tools in einem Dashboard. ClickUp, Notion, Jira und mehr - alles synchronisiert.", "@introScreen2Description": {"description": "Description for second intro screen"}, "introScreen3Title": "Intelligente Problemerkennung", "@introScreen3Title": {"description": "Title for third intro screen"}, "introScreen3Description": "Erkennen Sie automatisch Hindernisse, bewerten Sie Risiken und erhalten Sie umsetzbare Empfehlungen, um Ihre Projekte in Bewegung zu halten.", "@introScreen3Description": {"description": "Description for third intro screen"}, "introMainTitle": "ProjectPilot", "@introMainTitle": {"description": "Main title for intro screens"}, "introMainSubtitle": "Verlieren Sie nie wieder den Überblick über Ihre Projekte", "@introMainSubtitle": {"description": "Main subtitle for intro screens"}, "getStarted": "Loslegen", "@getStarted": {"description": "Text for get started button"}, "skip": "Überspringen", "@skip": {"description": "Text for skip button on intro screens"}, "next": "<PERSON><PERSON>", "@next": {"description": "Text for next button on intro screens"}, "loginTitle": "Willkommen bei ProjectPilot", "loginSubtitle": "<PERSON>de dich an, um fortzufahren", "continueWithGoogle": "Mit Google fortfahren", "continueWithApple": "<PERSON>t <PERSON> fortfahren", "continueWithEmail": "Mit E-Mail fortfahren", "moreOptions": "Weitere Optionen", "continueWithLinkedIn": "Mit LinkedIn fortfahren", "continueWithPhone": "Mit Telefon fortfahren", "continueWithAzure": "Mit Azure fortfahren", "continueWithNotion": "Mit Notion fortfahren", "password": "Passwort", "forgotPassword": "Passwort vergessen?", "signIn": "Anmelden", "signUp": "Registrieren", "dontHaveAccount": "Noch kein Konto?", "alreadyHaveAccount": "Bereits ein Konto?", "phoneNumber": "Telefonnummer", "or": "oder", "createAccountText": "Erstelle dein ProjectPilot-Konto", "usernameRequired": "Benutzername ist erforderlich", "usernameMinLength": "Benutzername muss mindestens 3 Zeichen lang sein", "emailRequired": "E-Mail ist erforderlich", "invalidEmail": "Gib eine gültige E-Mail-Adresse ein", "passwordRequired": "Passwort ist erforderlich", "passwordMinLength": "Passwort muss mindestens 6 <PERSON>eichen lang sein", "signUpSuccessful": "Registrierung erfolgreich!", "onboarding": "Vervollständige dein Profil", "camera": "<PERSON><PERSON><PERSON>", "gallery": "Galerie", "birthYear": "Geburtsjahr", "selectBirthYear": "Geburtsjahr auswählen", "userRole": "Benutzerrolle", "developer": "<PERSON><PERSON><PERSON><PERSON>", "projectManager": "Projektmanager", "private": "Privat", "other": "Sonstiges", "usagePurpose": "Verwendungszweck", "tasks": "Aufgaben", "ideas": "Ideen", "notes": "Notizen", "connectPlatforms": "Plattformen verbinden", "platformsDescription": "Verbinde dich mit deinen Lieblingsplattformen", "connectMore": "Weitere Plattformen später verbinden", "finish": "Fertigstellen", "platformConnections": "Plattformverbindungen", "@platformConnections": {"description": "Title for the platform connections screen"}, "connectToPlatform": "Verbinde mit {platform}", "@connectToPlatform": {"description": "Button text to connect to a specific platform", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "connected": "Verbunden", "@connected": {"description": "Status text when a platform is connected"}, "disconnected": "Nicht verbunden", "@disconnected": {"description": "Status text when a platform is not connected"}, "disconnect": "<PERSON><PERSON><PERSON>", "@disconnect": {"description": "Button text to disconnect from a platform"}, "connecting": "Verbin<PERSON>...", "@connecting": {"description": "Status text when connecting to a platform"}, "authorizationFailed": "Autorisierung fehlgeschlagen", "@authorizationFailed": {"description": "Error message when platform authorization fails"}, "connectionSuccessful": "Verbindung erfolgreich", "@connectionSuccessful": {"description": "Success message when platform connection is established"}, "connectionFailed": "Verbindung fehlgeschlagen", "@connectionFailed": {"description": "Error message when platform connection fails"}, "integrationAvailableFor": "Integration verfügbar für {platform}", "@integrationAvailableFor": {"description": "Text indicating a platform integration is available", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "allPlatforms": "Alle Plattformen", "@allPlatforms": {"description": "Text for showing all available platforms"}, "managePlatformConnections": "Plattformverbindungen verwalten", "@managePlatformConnections": {"description": "Description for the platform connections screen"}, "connectPlatformToSendTasks": "Verbinde deine Lieblingsplattformen, um Aufgaben direkt zu senden", "@connectPlatformToSendTasks": {"description": "Explanation text for platform connections"}, "cancel": "Abbrechen", "@cancel": {"description": "Text for cancel button"}, "continueButton": "Fortfahren", "@continueButton": {"description": "Text for continue button"}, "whatWouldYouLikeToDo": "Was möchtest du tun?", "taskReminder": "Aufgabenerinnerung", "@whatWouldYouLikeToDo": {"description": "Prompt text for the recording screen"}, "recordingInProgress": "Aufnahme läuft...", "@recordingInProgress": {"description": "Text shown while recording is in progress"}, "processingYourVoice": "Verarbeite deine Stimme...", "@processingYourVoice": {"description": "Text shown while transcribing audio"}, "whereToSave": "Wo möchtest du das speichern?", "@whereToSave": {"description": "Prompt for selecting a platform to save to"}, "noPlatformsConnected": "Noch keine Plattformen verbunden", "@noPlatformsConnected": {"description": "Text shown when no platforms are connected"}, "newRecording": "Neue Aufnahme", "@newRecording": {"description": "<PERSON><PERSON> text to start a new recording"}, "task": "Aufgabe", "@task": {"description": "Label for task category"}, "note": "Notiz", "@note": {"description": "Label for note category"}, "idea": "Idee", "@idea": {"description": "Label for idea category"}, "uncategorized": "<PERSON><PERSON> ka<PERSON>", "@uncategorized": {"description": "Label for uncategorized items"}, "edit": "<PERSON><PERSON><PERSON>", "@edit": {"description": "Text for edit button"}, "share": "Teilen", "@share": {"description": "Text for share button"}, "transcriptionResult": "Transkriptionsergebnis", "@transcriptionResult": {"description": "Title for transcription result"}, "detectedIntent": "Erkannte Absicht", "@detectedIntent": {"description": "Label for detected intent"}, "detectedEntities": "Erkannte Entitäten", "@detectedEntities": {"description": "Label for detected entities"}, "originalTranscription": "Originaltranskription", "@originalTranscription": {"description": "Label for original transcription"}, "speechHistoryTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@speechHistoryTitle": {"description": "Title for the speech history screen"}, "clearHistory": "<PERSON><PERSON><PERSON><PERSON>", "@clearHistory": {"description": "Button text to clear conversation history"}, "tapMicToStart": "<PERSON><PERSON><PERSON> auf das Mikrofon, um die Aufnahme zu starten", "@tapMicToStart": {"description": "Instruction text for starting recording"}, "suggestedPlatform": "Vorgeschlagene Plattform basierend auf deiner Sprache:", "@suggestedPlatform": {"description": "Label for suggested platform"}, "confirm": "Bestätigen", "@confirm": {"description": "Text for confirm button"}, "back": "Zurück", "@back": {"description": "Text for back button"}, "taskSent": "Aufgabe an {platform} gesendet", "@taskSent": {"description": "Message shown when task is sent to a platform", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "iRecognized": "Ich habe erkannt:", "@iRecognized": {"description": "Text for AI recognition message"}, "targetPlatform": "Ziel:", "@targetPlatform": {"description": "Label for target platform"}, "editBeforeSending": "Vor dem Senden bearbeiten", "@editBeforeSending": {"description": "Button text to edit content before sending"}, "selectCommandType": "Was möchtest du erstellen?", "@selectCommandType": {"description": "Title for command type selection prompt"}, "createTaskCommand": "Aufgabe erstellen", "@createTaskCommand": {"description": "Button text to create a task"}, "addNoteCommand": "<PERSON><PERSON>", "@addNoteCommand": {"description": "Button text to add a note"}, "saveIdeaCommand": "<PERSON><PERSON><PERSON> speichern", "@saveIdeaCommand": {"description": "<PERSON><PERSON> text to save an idea"}, "enterProjectName": "Projektname eingeben", "@enterProjectName": {"description": "Hint text for project name input"}, "selectProjectPrompt": "Projekt auswählen", "@selectProjectPrompt": {"description": "Title for project selection prompt"}, "entries": "Einträge", "@entries": {"description": "Title for entries screen"}, "entryDetails": "Eintragsdetails", "@entryDetails": {"description": "Title for entry details screen"}, "addUpdate": "Aktualisierung hinzufügen", "@addUpdate": {"description": "Button text to add an update to an entry"}, "updateDetails": "Aktualisierungsdetails", "@updateDetails": {"description": "Title for update details dialog"}, "deleteUpdate": "Aktualisierung löschen", "@deleteUpdate": {"description": "Title for delete update confirmation dialog"}, "deleteUpdateConfirmation": "B<PERSON> du sicher, dass du diese Aktualisierung löschen möchtest?", "@deleteUpdateConfirmation": {"description": "Confirmation message for deleting an update"}, "deleteEntry": "Eintrag löschen", "@deleteEntry": {"description": "Title for delete entry confirmation dialog"}, "deleteEntryConfirmation": "B<PERSON> du sicher, dass du diesen Eintrag und alle seine Aktualisierungen löschen möchtest?", "@deleteEntryConfirmation": {"description": "Confirmation message for deleting an entry"}, "entryCreated": "Eintrag erfolgreich erstellt", "@entryCreated": {"description": "Success message when an entry is created"}, "entryDeleted": "Eintrag erfolgreich <PERSON>t", "@entryDeleted": {"description": "Success message when an entry is deleted"}, "entryUpdateAdded": "Aktualisierung erfolgreich hinzugefügt", "@entryUpdateAdded": {"description": "Success message when an update is added to an entry"}, "noEntriesFound": "<PERSON><PERSON>äge gefunden", "@noEntriesFound": {"description": "Message when no entries are found"}, "createEntryPrompt": "<PERSON><PERSON><PERSON> einen neuen Eintrag, indem du auf die + Schaltfläche tippst", "@createEntryPrompt": {"description": "Prompt to create a new entry"}, "createEntry": "Eintrag erstellen", "@createEntry": {"description": "Button text to create a new entry"}, "content": "Inhalt", "@content": {"description": "Label for content input field"}, "type": "<PERSON><PERSON>", "@type": {"description": "Label for type selection"}, "create": "<PERSON><PERSON><PERSON><PERSON>", "@create": {"description": "Button text to create something"}, "search": "<PERSON><PERSON>", "@search": {"description": "Button text for search"}, "close": "Schließen", "@close": {"description": "Button text to close a dialog or screen"}, "save": "Speichern", "@save": {"description": "Button text to save changes"}, "editEntry": "Eintrag bearbeiten", "@editEntry": {"description": "Title for edit entry dialog"}, "sharingNotImplemented": "<PERSON><PERSON>n ist noch nicht implementiert", "@sharingNotImplemented": {"description": "Message when sharing functionality is not implemented"}, "updates": "Aktualisierungen", "@updates": {"description": "Label for updates section"}, "voice": "Stimme", "@voice": {"description": "Label for voice source type"}, "manual": "<PERSON><PERSON>", "@manual": {"description": "Label for manual source type"}, "addedAt": "Hinzugefügt am", "@addedAt": {"description": "Label for when an update was added"}, "source": "<PERSON><PERSON>", "@source": {"description": "Label for source of an update"}, "created": "<PERSON><PERSON><PERSON><PERSON>", "@created": {"description": "Label for creation date"}, "selectEntry": "Eintrag auswählen", "@selectEntry": {"description": "Title for entry selection dialog"}, "entryReferenceDetected": "Ich habe einen Verweis auf einen bestehenden Eintrag erkannt", "@entryReferenceDetected": {"description": "Message when a reference to an existing entry is detected"}, "selectEntryToUpdate": "<PERSON><PERSON><PERSON><PERSON> den Eintrag aus, den du aktualisieren möchtest", "@selectEntryToUpdate": {"description": "Prompt to select an entry to update"}, "createNewInstead": "Stattdessen neu erstellen", "@createNewInstead": {"description": "Button text to create a new entry instead of updating an existing one"}, "creatingNewEntry": "<PERSON><PERSON><PERSON> stattdessen einen neuen Eintrag", "@creatingNewEntry": {"description": "Message when creating a new entry instead of updating an existing one"}, "all": "Alle", "@all": {"description": "Label for showing all items"}, "chatHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@chatHistory": {"description": "Title for the chat history section in recording view"}, "authenticating": "Authentifizierung...", "@authenticating": {"description": "Text shown during authentication process"}, "createAccount": "<PERSON><PERSON> er<PERSON>", "@createAccount": {"description": "Button text for creating a new account"}, "login": "Anmelden", "@login": {"description": "Login button text"}, "confirmPassword": "Passwort bestätigen", "@confirmPassword": {"description": "Label for confirm password field"}, "confirmPasswordHint": "Bestätige dein Passwort", "@confirmPasswordHint": {"description": "Hint text for confirm password field"}, "confirmPasswordRequired": "Bitte bestätige dein Passwort", "@confirmPasswordRequired": {"description": "Error message when confirm password is empty"}, "passwordsDoNotMatch": "Passwörter stimmen nicht überein", "@passwordsDoNotMatch": {"description": "Error message when passwords do not match"}, "nameFieldHint": "Gib deinen vollständigen Namen ein", "@nameFieldHint": {"description": "Hint text for name input field"}, "history": "<PERSON><PERSON><PERSON><PERSON>", "@history": {"description": "Title for the history screen"}, "record": "<PERSON><PERSON><PERSON><PERSON>", "@record": {"description": "Title for the record tab"}, "newEntry": "<PERSON><PERSON><PERSON> Eintrag", "@newEntry": {"description": "Title for the new entry dialog"}, "pleaseEnterContent": "<PERSON>te geben Si<PERSON> Inhalt ein", "@pleaseEnterContent": {"description": "Validation message when content is empty"}, "update": "Aktualisieren", "@update": {"description": "Button text to update an entry"}, "noEntries": "<PERSON><PERSON>", "@noEntries": {"description": "Message when there are no entries"}, "createFirstEntry": "<PERSON><PERSON>", "@createFirstEntry": {"description": "Button text to create the first entry"}, "deleteAllAudioFiles": "Alle Audiodateien löschen", "@deleteAllAudioFiles": {"description": "Title for delete all audio files dialog"}, "deleteAllAudioFilesContent": "Dies wird alle Sprachaufnahmedateien löschen, um Speicherplatz freizugeben. Der Textinhalt Ihrer Aufnahmen bleibt erhalten.\n\nDiese Aktion kann nicht rückgängig gemacht werden. Sind Sie sicher?", "@deleteAllAudioFilesContent": {"description": "Content for delete all audio files dialog"}, "deleteAll": "Alle löschen", "@deleteAll": {"description": "Button text to delete all items"}, "deleteAllAudioFilesTooltip": "Alle Audiodateien löschen", "@deleteAllAudioFilesTooltip": {"description": "Tooltip for delete all audio files button"}, "selectLanguageDialogTitle": "Sprache auswählen", "@selectLanguageDialogTitle": {"description": "Title for language selection dialog"}, "enabled": "Aktiviert", "@enabled": {"description": "Status text for enabled features"}, "disabled": "Deaktiviert", "@disabled": {"description": "Status text for disabled features"}, "autoAssignEnabledDesc": "Aufgaben werden automatisch Plattformen zugewiesen", "@autoAssignEnabledDesc": {"description": "Description when auto assignment is enabled"}, "autoAssignDisabledDesc": "Manuelle Plattformauswahl für Aufgaben", "@autoAssignDisabledDesc": {"description": "Description when auto assignment is disabled"}, "packageDescription": "{name} Paket mit {tokenAmount} Tokens", "@packageDescription": {"description": "Description for token packages", "placeholders": {"name": {"type": "String", "example": "Pro"}, "tokenAmount": {"type": "int", "example": "1000"}}}, "aboutAppTitle": "Über {appName}", "@aboutAppTitle": {"description": "Title for about app screen", "placeholders": {"appName": {"type": "String", "example": "ProjectPilot"}}}, "appVersion": "Version {version}", "@appVersion": {"description": "App version display", "placeholders": {"version": {"type": "String", "example": "1.0.0"}}}, "keyFeatures": "Hauptfunktionen", "@keyFeatures": {"description": "Title for key features section"}, "voiceToTask": "<PERSON><PERSON><PERSON><PERSON>zu-Aufgabe", "@voiceToTask": {"description": "Voice-to-task feature name"}, "voiceToTaskDesc": "Erstellen Sie Aufgaben mit Ihrer Stimme", "@voiceToTaskDesc": {"description": "Voice-to-task feature description"}, "multiPlatform": "Multi-Plattform", "@multiPlatform": {"description": "Multi-platform feature name"}, "multiPlatformDesc": "Verbin<PERSON><PERSON> zu ClickUp, Notion und mehr", "@multiPlatformDesc": {"description": "Multi-platform feature description"}, "multiLanguage": "Mehrsprachig", "@multiLanguage": {"description": "Multi-language feature name"}, "multiLanguageDesc": "Unterstützt Englisch, Deutsch, Russisch, Türkisch und Arabisch", "@multiLanguageDesc": {"description": "Multi-language feature description"}, "aiPowered": "KI-gestützt", "@aiPowered": {"description": "AI-powered feature name"}, "aiPoweredDesc": "Intelligente Aufgabenkategorisierung und -optimierung", "@aiPoweredDesc": {"description": "AI-powered feature description"}, "ourTeam": "Unser Team", "@ourTeam": {"description": "Title for our team section"}, "copyright": "© {year} ProjectPilot. Alle Rechte vorbehalten.", "@copyright": {"description": "Copyright text", "placeholders": {"year": {"type": "String", "example": "2024"}}}, "contactSupportDesc": "Haben Sie eine Frage oder benötigen Hilfe? Füllen Sie das untenstehende Formular aus und unser Support-Team wird sich so schnell wie möglich bei Ihnen melden.", "@contactSupportDesc": {"description": "Description for contact support screen"}, "nameField": "Name", "@nameField": {"description": "Label for name input field"}, "emailField": "E-Mail", "@emailField": {"description": "Label for email input field"}, "messageField": "Nachricht", "@messageField": {"description": "Label for message input field"}, "messageFieldHint": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Nachricht ein", "@messageFieldHint": {"description": "Hint text for message input field"}, "submitButton": "<PERSON><PERSON><PERSON><PERSON>", "@submitButton": {"description": "Button text to submit a form"}, "nameValidationError": "<PERSON>te geben Si<PERSON> Ihren Namen ein", "@nameValidationError": {"description": "Validation error for name field"}, "emailValidationError": "Bitte geben Si<PERSON> eine gültige E-Mail ein", "@emailValidationError": {"description": "Validation error for email field"}, "messageValidationError": "<PERSON>te geben Si<PERSON> Ihre Nachricht ein", "@messageValidationError": {"description": "Validation error for message field"}, "initializationError": "Initialisierungsfehler", "@initializationError": {"description": "Title for initialization error dialog"}, "errorDuringInit": "Ein Fehler ist während der App-Initialisierung aufgetreten:\n{error}", "@errorDuringInit": {"description": "Error message during app initialization", "placeholders": {"error": {"type": "String", "example": "Netzwerkfehler"}}}, "retryButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@retryButton": {"description": "Button text to retry an action"}, "faqTitle": "FAQ", "@faqTitle": {"description": "Title for FAQ screen"}, "frequentlyAskedQuestions": "Häufig gestellte Fragen", "@frequentlyAskedQuestions": {"description": "Subtitle for FAQ screen"}, "privacyPolicyTitle": "Datenschutzrichtlinie", "@privacyPolicyTitle": {"description": "Title for privacy policy screen"}, "lastUpdated": "Zuletzt aktualisiert: {time}", "@lastUpdated": {"description": "Last updated timestamp", "placeholders": {"time": {"type": "String"}}}, "privacyPolicyIntro": "<PERSON>se <PERSON>tzrichtlini<PERSON> be<PERSON>, wie ProjectPilot (\"wir\", \"uns\" oder \"unser\") Ihre persönlichen Informationen sammelt, verwendet und offenlegt, wenn Sie unsere mobile Anwendung (die \"App\") verwenden.", "@privacyPolicyIntro": {"description": "Introduction text for privacy policy"}, "infoWeCollect": "<PERSON><PERSON>, die wir sammeln", "@infoWeCollect": {"description": "Section title for information collection"}, "infoWeCollectContent": "Wir sammeln Informationen, die Sie uns direkt zur Verfügung stellen, beispielsweise wenn Si<PERSON> ein Konto erstellen, Ihr Profil aktualisieren, die interaktiven Funktionen unserer App <PERSON>, Kundensupport anfordern oder anderweitig mit uns kommunizieren.", "@infoWeCollectContent": {"description": "Content for information collection section"}, "howWeUseInfo": "Wie wir Ihre Informationen verwenden", "@howWeUseInfo": {"description": "Section title for information usage"}, "howWeUseInfoContent": "Wir verwenden die von uns gesammelten Informationen, um unsere Dienste bereitz<PERSON>ellen, zu warten und zu verbessern, einschließlich der Verarbeitung von Transaktionen, dem Senden verwandter Informationen und der Bereitstellung von Kundensupport.", "@howWeUseInfoContent": {"description": "Content for information usage section"}, "sharingOfInfo": "Weitergabe von <PERSON>en", "@sharingOfInfo": {"description": "Section title for information sharing"}, "sharingOfInfoContent": "Wir können die von uns gesammelten Informationen wie folgt weitergeben: mit Drittanbietern, Beratern und anderen Dienstleistern, die Zugang zu solchen Informationen benötigen, um Arbeiten in unserem Auftrag auszuführen; als Antwort auf eine Informationsanfrage, wenn wir glauben, dass die Offenlegung im Einklang mit geltendem Recht, Vorschriften oder Rechtsverfahren steht.", "@sharingOfInfoContent": {"description": "Content for information sharing section"}, "yourChoices": "<PERSON><PERSON><PERSON>öglichkeiten", "@yourChoices": {"description": "Section title for user choices"}, "yourChoicesContent": "Sie können Ihre Kontoinformationen jederzeit aktualisieren, korrigieren oder löschen, indem Si<PERSON> sich in Ihr Konto einloggen oder uns kontaktieren. Sie können sich von Werbekommunikation von uns abmelden, indem Sie den Anweisungen in diesen Mitteilungen folgen.", "@yourChoicesContent": {"description": "Content for user choices section"}, "contactUs": "Kontaktieren Sie uns", "@contactUs": {"description": "Section title for contact information"}, "contactUsPrivacyContent": "<PERSON><PERSON> Sie Fragen zu dieser Datenschutzrichtlinie haben, kontaktieren Si<PERSON> uns bitte unter: <EMAIL>", "@contactUsPrivacyContent": {"description": "Contact information for privacy policy"}, "termsOfServiceTitle": "Nutzungsbedingungen", "@termsOfServiceTitle": {"description": "Title for terms of service screen"}, "termsOfServiceIntro": "Bitte lesen Sie diese Nutzungsbedingungen (\"Bedingungen\", \"Nutzungsbedingungen\") sorg<PERSON><PERSON><PERSON><PERSON> durch, bevor <PERSON>e die ProjectPilot Mobile-Anwendung (der \"Service\") verwenden, die von ProjectPilot (\"uns\", \"wir\" oder \"unser\") betrieben wird.", "@termsOfServiceIntro": {"description": "Introduction text for terms of service"}, "acceptanceOfTerms": "<PERSON><PERSON><PERSON> der Bedingungen", "@acceptanceOfTerms": {"description": "Section title for acceptance of terms"}, "acceptanceOfTermsContent": "Durch den Zugriff auf oder die Nutzung des Services stimmen Sie zu, an diese Bedingungen gebunden zu sein. Wenn Sie mit einem Teil der Bedingungen nicht einverstanden sind, dürfen Sie nicht auf den Service zugreifen.", "@acceptanceOfTermsContent": {"description": "Content for acceptance of terms section"}, "useOfService": "Nutzung des Services", "@useOfService": {"description": "Section title for service usage"}, "useOfServiceContent": "Unser Service ermöglicht es Ihnen, Aufgaben und Notizen mit Sprachbefehlen zu erstellen, zu verwalten und zu organisieren. Sie sind dafür verantwortlich, die Vertraulichkeit Ihres Kontos und Passworts zu wahren und den Zugang zu Ihrem Computer oder mobilen Gerät zu beschränken.", "@useOfServiceContent": {"description": "Content for service usage section"}, "intellectualProperty": "Geistiges Eigentum", "@intellectualProperty": {"description": "Section title for intellectual property"}, "intellectualPropertyContent": "Der Service und seine ursprünglichen Inhalte, Funktionen und Funktionalitäten sind und bleiben das ausschließliche Eigentum von ProjectPilot und seinen Lizenzgebern. Der Service ist durch Urheberrecht, Markenrecht und andere Gesetze geschützt.", "@intellectualPropertyContent": {"description": "Content for intellectual property section"}, "termination": "Kündigung", "@termination": {"description": "Section title for termination"}, "terminationContent": "Wir können Ihr Konto sofort kündigen oder sperren, ohne vorherige Ankündigung oder Haftung, aus jedem beliebigen Grund, einsch<PERSON>ßlich ohne Einschränkung, wenn <PERSON>e gegen die Bedingungen verstoßen.", "@terminationContent": {"description": "Content for termination section"}, "limitationOfLiability": "Haftungsbeschränkung", "@limitationOfLiability": {"description": "Section title for limitation of liability"}, "limitationOfLiabilityContent": "In keinem Fall haften ProjectPilot oder seine Direktoren, <PERSON><PERSON><PERSON><PERSON>, Partner, Agenten, Lieferanten oder Verbundenen für indirekte, zufällige, besondere, Folge- oder Strafschäden, einsch<PERSON>ßlich ohne Einschränkung Verlust von Gewinnen, Daten, Nutzung, Goodwill oder anderen immateriellen Verlusten.", "@limitationOfLiabilityContent": {"description": "Content for limitation of liability section"}, "changesToTerms": "Änderungen der Bedingungen", "@changesToTerms": {"description": "Section title for changes to terms"}, "changesToTermsContent": "Wir behalten uns das Recht vor, nach unserem alleinigen Ermessen diese Bedingungen jederzeit zu ändern oder zu ersetzen. Wenn eine Überarbeitung wesentlich ist, werden wir versuchen, mindestens 30 Tage im Voraus zu benachrichtigen, bevor neue Bedingungen in Kraft treten.", "@changesToTermsContent": {"description": "Content for changes to terms section"}, "contactUsTermsContent": "<PERSON>n Sie Fragen zu diesen Bedingungen haben, kontaktieren Si<PERSON> uns bitte unter: <EMAIL>", "@contactUsTermsContent": {"description": "Contact information for terms of service"}, "whatIsProjectPilot": "Was ist ProjectPilot?", "@whatIsProjectPilot": {"description": "FAQ question about ProjectPilot"}, "whatIsProjectPilotAnswer": "ProjectPilot ist eine Sprache-zu-Aufgabe-Anwendung, die es Ihnen ermöglicht, Aufgaben, Notizen und Ideen mit Sprachbefehlen zu erstellen. Es kann Ihre Stimme transkribieren und die Aufgaben an verschiedene Plattformen wie ClickUp, Notion und mehr senden.", "@whatIsProjectPilotAnswer": {"description": "FAQ answer about ProjectPilot"}, "howToConnectPlatforms": "Wie verbinde ich mich mit externen Plattformen?", "@howToConnectPlatforms": {"description": "FAQ question about connecting platforms"}, "howToConnectPlatformsAnswer": "Gehen Sie zum Einstellungsbildschirm und navigieren Sie zum Bereich \"Plattformverbindungen\". Dort können Sie sich mit verschiedenen Plattformen wie ClickUp, Notion und Monday verbinden, indem Sie auf die Schaltfläche Verbinden klicken und dem Authentifizierungsprozess folgen.", "@howToConnectPlatformsAnswer": {"description": "FAQ answer about connecting platforms"}, "whatLanguagesSupported": "Welche Sprachen werden unterstützt?", "@whatLanguagesSupported": {"description": "FAQ question about supported languages"}, "whatLanguagesSupportedAnswer": "ProjectPilot unterstützt derzeit Englisch, Deutsch, Russisch, Türkisch und Arabisch. Sie können die Sprache im Einstellungsbildschirm ändern.", "@whatLanguagesSupportedAnswer": {"description": "FAQ answer about supported languages"}, "howTokensWork": "Wie funktionieren <PERSON>?", "@howTokensWork": {"description": "FAQ question about tokens"}, "howTokensWorkAnswer": "Tokens werden für KI-Textgenerierung und Audio-Transkription verwendet. Jede Transkription und Optimierung verbraucht eine bestimmte Anzahl von Tokens. Sie können weitere Tokens im Bereich \"Paket aktualisieren\" der Einstellungen kaufen.", "@howTokensWorkAnswer": {"description": "FAQ answer about tokens"}, "canUseOffline": "Kann ich ProjectPilot offline verwenden?", "@canUseOffline": {"description": "FAQ question about offline usage"}, "canUseOfflineAnswer": "Einige Funktionen von ProjectPilot erfordern eine Internetverbindung, wie das Senden von Aufgaben an externe Plattformen und die Verwendung von Online-Transkriptionsdiensten. Grundlegende Sprachaufnahmen können jedoch offline funktionieren.", "@canUseOfflineAnswer": {"description": "FAQ answer about offline usage"}, "howCustomizeProfile": "Wie kann ich mein Profil anpassen?", "@howCustomizeProfile": {"description": "FAQ question about profile customization"}, "howCustomizeProfileAnswer": "Sie können Ihre Profilinformationen im Einstellungsbildschirm unter dem Bereich \"Profil\" aktualisieren.", "@howCustomizeProfileAnswer": {"description": "FAQ answer about profile customization"}, "isDataSecure": "Sind meine Daten sicher?", "@isDataSecure": {"description": "FAQ question about data security"}, "isDataSecureAnswer": "<PERSON><PERSON>, wir nehmen Datensicherheit ernst. Ihre Daten werden verschlüsselt und sicher gespeichert. Wir teilen Ihre persönlichen Informationen nicht mit Dritten ohne Ihre Zustimmung. Weitere Details finden Sie in unserer Datenschutzrichtlinie.", "@isDataSecureAnswer": {"description": "FAQ answer about data security"}, "howCancelSubscription": "Wie kann ich mein Abonnement kündigen?", "@howCancelSubscription": {"description": "FAQ question about subscription cancellation"}, "howCancelSubscriptionAnswer": "Sie können Ihr Abonnement über Ihr App-Store-Konto (Google Play Store oder Apple App Store) verwalten. Gehen Sie zum Abonnementverwaltungsbereich Ihres App-Stores und kündigen Sie das ProjectPilot-Abonnement.", "@howCancelSubscriptionAnswer": {"description": "FAQ answer about subscription cancellation"}, "appTitleProjectPilot": "ProjectPilot", "@appTitleProjectPilot": {"description": "The title of the ProjectPilot application"}, "projectDashboard": "Projekt-Dashboard", "@projectDashboard": {"description": "Title for project dashboard screen"}, "projects": "Projekte", "@projects": {"description": "General term for projects"}, "projectAnalysis": "Projektanalyse", "@projectAnalysis": {"description": "Title for project analysis feature"}, "askAboutProject": "Über Projekt fragen", "@askAboutProject": {"description": "Button text to ask questions about project"}, "projectStatus": "Projektstatus", "@projectStatus": {"description": "Label for project status"}, "blockers": "<PERSON><PERSON><PERSON>", "@blockers": {"description": "Title for blockers section"}, "recommendations": "Empfehlungen", "@recommendations": {"description": "Label for AI recommendations"}, "criticalPath": "Kritischer Pfad", "@criticalPath": {"description": "Label for critical path analysis"}, "projectProgress": "Projektfortschritt", "@projectProgress": {"description": "Label for project progress"}, "teamMembers": "Teammitglieder", "@teamMembers": {"description": "Label for team members"}, "budget": "Budget", "@budget": {"description": "Label for budget information"}, "timeline": "Zeitplan", "@timeline": {"description": "Label for project timeline"}, "risks": "Risiken", "@risks": {"description": "Label for project risks"}, "insights": "Einblick<PERSON>", "@insights": {"description": "Label for project insights"}, "analysisLoading": "Projekt wird analysiert...", "@analysisLoading": {"description": "Loading message for project analysis"}, "analysisFailed": "Analyse fehlgeschlagen. Bitte versuchen Sie es erneut.", "@analysisFailed": {"description": "Error message when analysis fails"}, "refreshProject": "Projekt aktualisieren", "@refreshProject": {"description": "Button text to refresh project data"}, "projectOverdue": "Projekt überfällig", "@projectOverdue": {"description": "Label when project is overdue"}, "projectOnTrack": "Projekt auf Ku<PERSON>", "@projectOnTrack": {"description": "Label when project is on track"}, "projectAtRisk": "Projekt gefährdet", "@projectAtRisk": {"description": "Label when project is at risk"}, "highPriorityBlockers": "Hochprioritäts-Blocker", "@highPriorityBlockers": {"description": "Label for high priority blockers"}, "urgentRecommendations": "Dringende Empfehlungen", "@urgentRecommendations": {"description": "Label for urgent recommendations"}, "daysRemaining": "{count} <PERSON><PERSON> verbleibend", "@daysRemaining": {"description": "Days remaining until deadline", "placeholders": {"count": {"type": "int"}}}, "tasksCompleted": "{completed} von {total} Aufgaben abgeschlossen", "@tasksCompleted": {"description": "Tasks completion status", "placeholders": {"completed": {"type": "int"}, "total": {"type": "int"}}}, "budgetUtilization": "Budget: {spent} von {total} verwendet", "@budgetUtilization": {"description": "Budget utilization display", "placeholders": {"spent": {"type": "double", "format": "currency"}, "total": {"type": "double", "format": "currency"}}}, "queryProject": "Fragen Sie mich alles über dieses Projekt...", "@queryProject": {"description": "Placeholder text for project query input"}, "processingQuery": "Ihre Frage wird bearbeitet...", "@processingQuery": {"description": "Loading message for query processing"}, "queryFailed": "<PERSON>hre Frage konnte nicht bearbeitet werden. Bitte versuchen Si<PERSON> es erneut.", "@queryFailed": {"description": "Error message when query fails"}, "projectNotFound": "Projekt nicht gefunden", "@projectNotFound": {"description": "Error message when project is not found"}, "noProjectsFound": "<PERSON>ine Projekte gefunden", "@noProjectsFound": {"description": "Message when no projects are available"}, "syncingData": "Projektdaten werden synchronisiert...", "@syncingData": {"description": "Loading message for data sync"}, "activeTasks": "Aktive Aufgaben", "@activeTasks": {"description": "Label for active tasks"}, "completedTasks": "Abgeschlossene Aufgaben", "@completedTasks": {"description": "Label for completed tasks"}, "blockedTasks": "Blockierte Aufgaben", "@blockedTasks": {"description": "Label for blocked tasks"}, "overdueTasks": "Überfällige Aufgaben", "@overdueTasks": {"description": "Label for overdue tasks"}, "confidenceLevel": "Vertrauen: {level}%", "@confidenceLevel": {"description": "AI analysis confidence level", "placeholders": {"level": {"type": "int"}}}, "riskScore": "<PERSON><PERSON>ko-Score: {score}/10", "@riskScore": {"description": "Project risk score display", "placeholders": {"score": {"type": "double", "format": "decimalPattern"}}}, "riskAssessment": "Risikobewertung", "@riskAssessment": {"description": "Title for risk assessment section"}, "budgetOverview": "Budgetübersicht", "@budgetOverview": {"description": "Title for budget overview section"}, "keyInsights": "Wichtige Erkenntnisse", "@keyInsights": {"description": "Title for key insights section"}, "errorLoadingAnalysis": "Fehler beim Laden der Analyse", "@errorLoadingAnalysis": {"description": "Error message when analysis fails to load"}, "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@retry": {"description": "Button text to retry an operation"}, "tapAnalyzeToGetInsights": "<PERSON><PERSON><PERSON> auf Analysieren, um KI-Einblicke zu erhalten", "@tapAnalyzeToGetInsights": {"description": "Instruction text for analyzing project"}, "analyzeProject": "Projekt analysieren", "@analyzeProject": {"description": "Button text to analyze project"}, "aiAnalysisSummary": "KI-Analysezusammenfassung", "@aiAnalysisSummary": {"description": "Title for AI analysis summary section"}, "active": "Aktiv", "@active": {"description": "Status text for active items"}, "inactive": "Inaktiv", "@inactive": {"description": "Status text for inactive items"}, "overBudget": "Über Budget", "@overBudget": {"description": "Label when project is over budget"}, "totalBudget": "Gesamtbudget", "@totalBudget": {"description": "Label for total budget amount"}, "spent": "Ausgegeben", "@spent": {"description": "Label for spent amount"}, "remaining": "Verbleibend", "@remaining": {"description": "Label for remaining amount"}, "analysisDetails": "Analysedetails", "@analysisDetails": {"description": "Title for analysis details section"}, "confidenceLevelLabel": "Vertrauensniveau", "@confidenceLevelLabel": {"description": "Label for confidence level"}, "analysisDate": "Analysedatum", "@analysisDate": {"description": "Label for analysis date"}, "predictedCompletion": "Voraussichtliche Fertigstellung", "@predictedCompletion": {"description": "Label for predicted completion date"}, "critical": "<PERSON><PERSON><PERSON>", "@critical": {"description": "Critical priority/severity level"}, "high": "Hoch", "@high": {"description": "High priority/severity level"}, "medium": "<PERSON><PERSON><PERSON>", "@medium": {"description": "Medium priority/severity level"}, "low": "<PERSON><PERSON><PERSON>", "@low": {"description": "Low priority/severity level"}, "justNow": "gerade jetzt", "@justNow": {"description": "Time indicator for very recent events"}, "criticalPaths": "Kritische Pfade", "@criticalPaths": {"description": "Title for critical paths section"}, "error": "<PERSON><PERSON>", "@error": {"description": "Generic error title"}, "noProjectsYet": "Noch keine Projekte", "@noProjectsYet": {"description": "Message when no projects exist"}, "connectProjectManagementPlatforms": "Verbinden Sie Ihre Projektmanagement-Plattformen, um zu beginnen", "@connectProjectManagementPlatforms": {"description": "Instructions for connecting platforms"}, "@connectPlatforms": {"description": "Button text to connect platforms"}, "projectPilotDashboard": "ProjectPilot Dashboard", "@projectPilotDashboard": {"description": "Title for ProjectPilot dashboard"}, "totalProjects": "Gesamtprojekte", "@totalProjects": {"description": "Label for total projects count"}, "completed": "Abgeschlossen", "@completed": {"description": "Status for completed items"}, "recentProjects": "Aktuelle Projekte", "@recentProjects": {"description": "Title for recent projects section"}, "viewAll": "Alle anzeigen", "@viewAll": {"description": "Button text to view all items"}, "tasksOverview": "Aufgabenübersicht", "@tasksOverview": {"description": "Title for tasks overview section"}, "recentTasks": "Aktuelle Aufgaben", "@recentTasks": {"description": "Title for recent tasks section"}, "noTasksFound": "<PERSON><PERSON>ben gefunden", "@noTasksFound": {"description": "Message when no tasks are found"}, "inProgress": "In Bearbeitung", "@inProgress": {"description": "Status for tasks in progress"}, "blocked": "<PERSON><PERSON><PERSON>", "@blocked": {"description": "Status for blocked tasks"}, "overdue": "Überfällig", "@overdue": {"description": "Status for overdue tasks"}, "toDo": "<PERSON><PERSON>", "@toDo": {"description": "Status for tasks to do"}, "review": "Überprüfung", "@review": {"description": "Status for tasks in review"}, "done": "<PERSON><PERSON><PERSON>", "@done": {"description": "Status for completed tasks"}, "cancelled": "Abgebrochen", "@cancelled": {"description": "Status for cancelled tasks"}, "progress": "Fort<PERSON><PERSON>t", "@progress": {"description": "Label for progress information"}, "createdAt": "Erstellt am", "@createdAt": {"description": "Label for creation date"}, "deadline": "Frist", "@deadline": {"description": "Label for deadline"}, "platform": "Plattform", "@platform": {"description": "Label for platform information"}, "planning": "Planung", "@planning": {"description": "Project status - planning"}, "onHold": "<PERSON><PERSON><PERSON><PERSON>", "@onHold": {"description": "Project status - on hold"}, "aiProjectAssistant": "KI-Projektassistent", "@aiProjectAssistant": {"description": "Title for AI project assistant"}, "@tryAgain": {"description": "<PERSON><PERSON> text to try again"}, "whatProjectsAreBehindSchedule": "Welche Projekte sind im Rückstand?", "@whatProjectsAreBehindSchedule": {"description": "Sample query for project assistant"}, "showMeProjectRisks": "Zeige mir Projektrisiken", "@showMeProjectRisks": {"description": "Sample query for project assistant"}, "whichTasksAreBlocked": "Welche Aufgaben sind blockiert?", "@whichTasksAreBlocked": {"description": "Sample query for project assistant"}, "projectProgressSummary": "Projektfortschrittszusammenfassung", "@projectProgressSummary": {"description": "Sample query for project assistant"}, "teamWorkloadAnalysis": "Teamarbeitsbelastungsanalyse", "@teamWorkloadAnalysis": {"description": "Sample query for project assistant"}, "dismiss": "Schließen", "@dismiss": {"description": "Button text to dismiss a notification"}, "examplesForRole": "<PERSON><PERSON><PERSON><PERSON> {role}", "@examplesForRole": {"description": "Title for role-based example prompts", "placeholders": {"role": {"type": "String", "example": "Developer"}}}, "tapToUse": "<PERSON><PERSON>pen zum Verwenden", "@tapToUse": {"description": "Text to indicate user can tap prompt to use it"}, "roleDeveloper": "<PERSON><PERSON><PERSON><PERSON>", "@roleDeveloper": {"description": "Developer role display name"}, "roleProjectManager": "Projektmanager", "@roleProjectManager": {"description": "Project Manager role display name"}, "roleProductOwner": "Product Owner", "@roleProductOwner": {"description": "Product Owner role display name"}, "roleQaTester": "QA Tester", "@roleQaTester": {"description": "QA Tester role display name"}, "roleCtoCeo": "CTO/CEO", "@roleCtoCeo": {"description": "CTO/CEO role display name"}, "roleAllRoles": "alle Rollen", "@roleAllRoles": {"description": "Default text for all roles"}, "developerPrompt1": "Welche Aufgaben sind mir aktuell zugewiesen?", "@developerPrompt1": {"description": "Developer role prompt 1"}, "developerPrompt2": "Was blockiert meinen Code Review?", "@developerPrompt2": {"description": "Developer role prompt 2"}, "developerPrompt3": "Was steht im nächsten Sprint an?", "@developerPrompt3": {"description": "Developer role prompt 3"}, "developerPrompt4": "Welche Bugs haben höchste Priorität?", "@developerPrompt4": {"description": "Developer role prompt 4"}, "projectManagerPrompt1": "Was verzögert den aktuellen Sprint?", "@projectManagerPrompt1": {"description": "Project Manager role prompt 1"}, "projectManagerPrompt2": "Welche Tasks sind overdue?", "@projectManagerPrompt2": {"description": "Project Manager role prompt 2"}, "projectManagerPrompt3": "Wer hat zuletzt am Projekt gearbeitet?", "@projectManagerPrompt3": {"description": "Project Manager role prompt 3"}, "projectManagerPrompt4": "Wie ist der aktuelle Projektfortschritt?", "@projectManagerPrompt4": {"description": "Project Manager role prompt 4"}, "productOwnerPrompt1": "Welche Features sind für den nächsten Release geplant?", "@productOwnerPrompt1": {"description": "Product Owner role prompt 1"}, "productOwnerPrompt2": "Was ist das Feedback zu unserem letzten Update?", "@productOwnerPrompt2": {"description": "Product Owner role prompt 2"}, "productOwnerPrompt3": "Welche User Stories haben höchste Priorität?", "@productOwnerPrompt3": {"description": "Product Owner role prompt 3"}, "productOwnerPrompt4": "Wie entwickelt sich unser Product Backlog?", "@productOwnerPrompt4": {"description": "Product Owner role prompt 4"}, "qaTesterPrompt1": "Welche Tests müssen noch durchgeführt werden?", "@qaTesterPrompt1": {"description": "QA Tester role prompt 1"}, "qaTesterPrompt2": "Gibt es offene Critical Bugs?", "@qaTesterPrompt2": {"description": "QA Tester role prompt 2"}, "qaTesterPrompt3": "Wie ist die aktuelle Testabdeckung?", "@qaTesterPrompt3": {"description": "QA Tester role prompt 3"}, "qaTesterPrompt4": "Welche Features sind testbereit?", "@qaTesterPrompt4": {"description": "QA Tester role prompt 4"}, "ctoCeoPrompt1": "Wie läuft das Projekt insgesamt?", "@ctoCeoPrompt1": {"description": "CTO/CEO role prompt 1"}, "ctoCeoPrompt2": "Was kostet uns aktuell am meisten Zeit?", "@ctoCeoPrompt2": {"description": "CTO/CEO role prompt 2"}, "ctoCeoPrompt3": "Welche Teams brauchen Unterstützung?", "@ctoCeoPrompt3": {"description": "CTO/CEO role prompt 3"}, "ctoCeoPrompt4": "Wie steht es um unser Budget und Timeline?", "@ctoCeoPrompt4": {"description": "CTO/CEO role prompt 4"}, "defaultPrompt1": "Welche Aufgaben stehen heute an?", "@defaultPrompt1": {"description": "Default role prompt 1"}, "defaultPrompt2": "Wie ist der aktuelle Projektstatus?", "@defaultPrompt2": {"description": "Default role prompt 2"}, "defaultPrompt3": "Was sind die nächsten wichtigen Schritte?", "@defaultPrompt3": {"description": "Default role prompt 3"}, "defaultPrompt4": "Gibt es Hindernisse, die behoben werden müssen?", "@defaultPrompt4": {"description": "Default role prompt 4"}}