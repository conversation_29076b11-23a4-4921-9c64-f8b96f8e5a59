// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appTitle => 'ProjectPilot';

  @override
  String get homeTitle => 'Startseite';

  @override
  String get settingsTitle => 'Einstellungen';

  @override
  String get welcome => 'Willkommen';

  @override
  String get welcomeSubtitle =>
      'ProjectPilot – die Zukunft des Projektmanagements';

  @override
  String get languageTitle => 'Sprache';

  @override
  String get englishLanguage => 'Englisch';

  @override
  String get germanLanguage => 'Deutsch';

  @override
  String get russianLanguage => 'Russisch';

  @override
  String get turkishLanguage => 'Türkisch';

  @override
  String get arabicLanguage => 'Arabisch';

  @override
  String get saveButton => 'Speichern';

  @override
  String get cancelButton => 'Abbrechen';

  @override
  String get errorOccurred => 'Ein Fehler ist aufgetreten';

  @override
  String get tryAgain => 'Erneut versuchen';

  @override
  String get selectLanguage => 'Sprache auswählen';

  @override
  String tokenBalance(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Token-Guthaben: $countString';
  }

  @override
  String get tokenBalanceDescription =>
      'Tokens for AI text generation with GPT';

  @override
  String get transcriptionTitle => 'Transkription';

  @override
  String get startRecording => 'Aufnahme starten';

  @override
  String get stopRecording => 'Aufnahme beenden';

  @override
  String get noTranscriptionYet => 'Noch keine Transkription';

  @override
  String get shareTranscription => 'Transkription teilen';

  @override
  String get themeTitle => 'Design';

  @override
  String get selectTheme => 'Design auswählen';

  @override
  String get systemTheme => 'Systemeinstellung';

  @override
  String get lightTheme => 'Hell';

  @override
  String get darkTheme => 'Dunkel';

  @override
  String get toggleTheme => 'Design wechseln';

  @override
  String get apiKeysSection => 'API-Schlüssel';

  @override
  String get openaiApiKey => 'OpenAI API-Schlüssel';

  @override
  String get whisperApiKey => 'Whisper API-Schlüssel';

  @override
  String get apiKeyHint => 'Geben Sie Ihren API-Schlüssel ein';

  @override
  String get tokenSection => 'Tokens & Nutzung';

  @override
  String remainingMinutes(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Verbleibende Minuten: $countString';
  }

  @override
  String get minutesBalanceDescription =>
      'Minutes available for audio transcription with Whisper';

  @override
  String get purchasePackages => 'Pakete kaufen';

  @override
  String get buyTokens => 'Tokens kaufen';

  @override
  String get starterPackage => 'Starter';

  @override
  String get proPackage => 'Pro';

  @override
  String get businessPackage => 'Business';

  @override
  String get ultimatePackage => 'Ultimate';

  @override
  String tokens(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return '$countString Tokens';
  }

  @override
  String price(double price) {
    final intl.NumberFormat priceNumberFormat = intl
        .NumberFormat.simpleCurrency(locale: localeName, name: 'EUR');
    final String priceString = priceNumberFormat.format(price);

    return '€$priceString';
  }

  @override
  String get profileSection => 'Profil';

  @override
  String get username => 'Benutzername';

  @override
  String get email => 'E-Mail';

  @override
  String get notAvailable => 'Nicht verfügbar';

  @override
  String get logoutButton => 'Abmelden';

  @override
  String get apiKeySaved => 'API-Schlüssel erfolgreich gespeichert!';

  @override
  String get introScreen1Title => 'KI-gesteuerte Projektintelligenz';

  @override
  String get introScreen1Description =>
      'Behalten Sie den roten Faden in Ihren Projekten. Erkennen Sie Probleme frühzeitig und bleiben Sie mit intelligenten Einsichten auf Kurs.';

  @override
  String get introScreen2Title => 'Multi-Plattform Projektübersicht';

  @override
  String get introScreen2Description =>
      'Verbinden Sie all Ihre Projekt-Tools in einem Dashboard. ClickUp, Notion, Jira und mehr - alles synchronisiert.';

  @override
  String get introScreen3Title => 'Intelligente Problemerkennung';

  @override
  String get introScreen3Description =>
      'Erkennen Sie automatisch Hindernisse, bewerten Sie Risiken und erhalten Sie umsetzbare Empfehlungen, um Ihre Projekte in Bewegung zu halten.';

  @override
  String get introMainTitle => 'ProjectPilot';

  @override
  String get introMainSubtitle =>
      'Verlieren Sie nie wieder den Überblick über Ihre Projekte';

  @override
  String get getStarted => 'Loslegen';

  @override
  String get skip => 'Überspringen';

  @override
  String get next => 'Weiter';

  @override
  String get loginTitle => 'Willkommen bei ProjectPilot';

  @override
  String get loginSubtitle => 'Melde dich an, um fortzufahren';

  @override
  String get continueWithGoogle => 'Mit Google fortfahren';

  @override
  String get continueWithApple => 'Mit Apple fortfahren';

  @override
  String get continueWithEmail => 'Mit E-Mail fortfahren';

  @override
  String get moreOptions => 'Weitere Optionen';

  @override
  String get continueWithLinkedIn => 'Mit LinkedIn fortfahren';

  @override
  String get continueWithPhone => 'Mit Telefon fortfahren';

  @override
  String get continueWithAzure => 'Mit Azure fortfahren';

  @override
  String get continueWithNotion => 'Mit Notion fortfahren';

  @override
  String get password => 'Passwort';

  @override
  String get forgotPassword => 'Passwort vergessen?';

  @override
  String get signIn => 'Anmelden';

  @override
  String get signUp => 'Registrieren';

  @override
  String get dontHaveAccount => 'Noch kein Konto?';

  @override
  String get alreadyHaveAccount => 'Bereits ein Konto?';

  @override
  String get phoneNumber => 'Telefonnummer';

  @override
  String get or => 'oder';

  @override
  String get createAccountText => 'Erstelle dein ProjectPilot-Konto';

  @override
  String get usernameRequired => 'Benutzername ist erforderlich';

  @override
  String get usernameMinLength =>
      'Benutzername muss mindestens 3 Zeichen lang sein';

  @override
  String get emailRequired => 'E-Mail ist erforderlich';

  @override
  String get invalidEmail => 'Gib eine gültige E-Mail-Adresse ein';

  @override
  String get passwordRequired => 'Passwort ist erforderlich';

  @override
  String get passwordMinLength =>
      'Passwort muss mindestens 6 Zeichen lang sein';

  @override
  String get signUpSuccessful => 'Registrierung erfolgreich!';

  @override
  String get onboarding => 'Vervollständige dein Profil';

  @override
  String get camera => 'Kamera';

  @override
  String get gallery => 'Galerie';

  @override
  String get birthYear => 'Geburtsjahr';

  @override
  String get selectBirthYear => 'Geburtsjahr auswählen';

  @override
  String get userRole => 'Benutzerrolle';

  @override
  String get developer => 'Entwickler';

  @override
  String get projectManager => 'Projektmanager';

  @override
  String get private => 'Privat';

  @override
  String get other => 'Sonstiges';

  @override
  String get usagePurpose => 'Verwendungszweck';

  @override
  String get tasks => 'Aufgaben';

  @override
  String get notes => 'Notizen';

  @override
  String get ideas => 'Ideen';

  @override
  String get connectPlatforms => 'Plattformen verbinden';

  @override
  String get platformsDescription =>
      'Verbinde dich mit deinen Lieblingsplattformen';

  @override
  String get connectMore => 'Weitere Plattformen später verbinden';

  @override
  String get finish => 'Fertigstellen';

  @override
  String get platformConnections => 'Plattformverbindungen';

  @override
  String connectToPlatform(String platform) {
    return 'Verbinde mit $platform';
  }

  @override
  String get connected => 'Verbunden';

  @override
  String get disconnected => 'Nicht verbunden';

  @override
  String get disconnect => 'Trennen';

  @override
  String get connecting => 'Verbinde...';

  @override
  String get authorizationFailed => 'Autorisierung fehlgeschlagen';

  @override
  String get connectionSuccessful => 'Verbindung erfolgreich';

  @override
  String get connectionFailed => 'Verbindung fehlgeschlagen';

  @override
  String integrationAvailableFor(String platform) {
    return 'Integration verfügbar für $platform';
  }

  @override
  String get allPlatforms => 'Alle Plattformen';

  @override
  String get managePlatformConnections => 'Plattformverbindungen verwalten';

  @override
  String get connectPlatformToSendTasks =>
      'Verbinde deine Lieblingsplattformen, um Aufgaben direkt zu senden';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get whatWouldYouLikeToDo => 'Was möchtest du tun?';

  @override
  String get recordingInProgress => 'Aufnahme läuft...';

  @override
  String get processingYourVoice => 'Verarbeite deine Stimme...';

  @override
  String get whereToSave => 'Wo möchtest du das speichern?';

  @override
  String get noPlatformsConnected => 'Noch keine Plattformen verbunden';

  @override
  String get newRecording => 'Neue Aufnahme';

  @override
  String get uncategorized => 'Nicht kategorisiert';

  @override
  String get preferences => 'Preferences';

  @override
  String get upgradePackage => 'Upgrade Package';

  @override
  String get infoAndSupport => 'Info & Support';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get platformConnected => 'Connected';

  @override
  String get platformNotConnected => 'Not connected';

  @override
  String get connect => 'Connect';

  @override
  String get upgradeMessage =>
      'Get more tokens and recording minutes by upgrading your package';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get faq => 'FAQ';

  @override
  String get contactSupport => 'Contact Support';

  @override
  String get aboutApp => 'About ProjectPilot';

  @override
  String get settingsLanguageSection => 'Language';

  @override
  String get appearanceAndBehavior => 'Appearance & Behavior';

  @override
  String get settingsInfoSection => 'Information & Support';

  @override
  String get helpAndSupport => 'Help & Support';

  @override
  String get privacyTermsFaq => 'Privacy, Terms, FAQ';

  @override
  String get settingsAbout => 'About';

  @override
  String get storageManagement => 'Storage Management';

  @override
  String get voiceRecordings => 'Voice Recordings';

  @override
  String usingStorage(String size) {
    return 'Using $size of storage';
  }

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get automaticAssignment => 'Automatic Assignment';

  @override
  String get whereToSendTask => 'Where should I send this?';

  @override
  String get selectPlatform => 'Select Platform';

  @override
  String get selectProject => 'Select Project / Notebook / Board';

  @override
  String get sendNow => 'Send Now';

  @override
  String get noProjectsFound => 'Keine Projekte gefunden';

  @override
  String get taskSentSuccessfully => 'Task sent successfully';

  @override
  String get platformDetected => 'Platform detected from your voice';

  @override
  String get projectDetected => 'Project detected from your voice';

  @override
  String get intentDetected => 'Intent detected from your voice';

  @override
  String get editContent => 'Edit content';

  @override
  String get noConnectedPlatforms => 'No connected platforms';

  @override
  String get connectPlatformsInSettings =>
      'Connect platforms in settings to send tasks';

  @override
  String get newTask => 'New Task';

  @override
  String get newNote => 'New Note';

  @override
  String get newIdea => 'New Idea';

  @override
  String get edit => 'Bearbeiten';

  @override
  String get share => 'Teilen';

  @override
  String get transcriptionResult => 'Transkriptionsergebnis';

  @override
  String get detectedIntent => 'Erkannte Absicht';

  @override
  String get detectedEntities => 'Erkannte Entitäten';

  @override
  String get originalTranscription => 'Originaltranskription';

  @override
  String get addConnection => 'Add Connection';

  @override
  String get noConnections => 'No Connections';

  @override
  String get addConnectionsDescription =>
      'Connect your favorite platforms to send tasks directly';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String confirmDeleteConnectionMessage(String platform) {
    return 'Are you sure you want to delete the connection to $platform?';
  }

  @override
  String get delete => 'Delete';

  @override
  String platformConnectedMessage(String platform) {
    return 'Connected to $platform';
  }

  @override
  String get speechHistoryTitle => 'Sprachverlauf';

  @override
  String get clearHistory => 'Verlauf löschen';

  @override
  String get tapMicToStart =>
      'Tippe auf das Mikrofon, um die Aufnahme zu starten';

  @override
  String get suggestedPlatform =>
      'Vorgeschlagene Plattform basierend auf deiner Sprache:';

  @override
  String get confirm => 'Bestätigen';

  @override
  String get back => 'Zurück';

  @override
  String taskSent(String platform) {
    return 'Aufgabe an $platform gesendet';
  }

  @override
  String get iRecognized => 'Ich habe erkannt:';

  @override
  String get targetPlatform => 'Ziel:';

  @override
  String get editBeforeSending => 'Vor dem Senden bearbeiten';

  @override
  String get selectCommandType => 'Was möchtest du erstellen?';

  @override
  String get createTaskCommand => 'Aufgabe erstellen';

  @override
  String get addNoteCommand => 'Notiz hinzufügen';

  @override
  String get saveIdeaCommand => 'Idee speichern';

  @override
  String get enterProjectName => 'Projektname eingeben';

  @override
  String get selectProjectPrompt => 'Projekt auswählen';

  @override
  String get entries => 'Einträge';

  @override
  String get entryDetails => 'Eintragsdetails';

  @override
  String get addUpdate => 'Aktualisierung hinzufügen';

  @override
  String get updateDetails => 'Aktualisierungsdetails';

  @override
  String get deleteUpdate => 'Aktualisierung löschen';

  @override
  String get deleteUpdateConfirmation =>
      'Bist du sicher, dass du diese Aktualisierung löschen möchtest?';

  @override
  String get deleteEntry => 'Eintrag löschen';

  @override
  String get deleteEntryConfirmation =>
      'Bist du sicher, dass du diesen Eintrag und alle seine Aktualisierungen löschen möchtest?';

  @override
  String get entryCreated => 'Eintrag erfolgreich erstellt';

  @override
  String get entryDeleted => 'Eintrag erfolgreich gelöscht';

  @override
  String get entryUpdateAdded => 'Aktualisierung erfolgreich hinzugefügt';

  @override
  String get noEntriesFound => 'Keine Einträge gefunden';

  @override
  String get createEntryPrompt =>
      'Erstelle einen neuen Eintrag, indem du auf die + Schaltfläche tippst';

  @override
  String get createNewEntry => 'Create New Entry';

  @override
  String get title => 'Title';

  @override
  String get titleHint => 'Enter title here';

  @override
  String get description => 'Description';

  @override
  String get descriptionHint => 'Enter description here';

  @override
  String get time => 'Time';

  @override
  String get date => 'Date';

  @override
  String dateFormat(int month, int day, int year) {
    return '$month/$day/$year';
  }

  @override
  String get type => 'Typ';

  @override
  String get priority => 'Priority';

  @override
  String get priorityLow => 'Low';

  @override
  String get priorityMedium => 'Medium';

  @override
  String get priorityHigh => 'High';

  @override
  String get priorityUrgent => 'Urgent';

  @override
  String get destination => 'Destination';

  @override
  String get clickupPlatform => 'ClickUp';

  @override
  String get notionPlatform => 'Notion';

  @override
  String get asanaPlatform => 'Asana';

  @override
  String get mondayPlatform => 'Monday';

  @override
  String get jiraPlatform => 'Jira';

  @override
  String get trelloPlatform => 'Trello';

  @override
  String get create => 'Erstellen';

  @override
  String get pleaseEnterTitle => 'Please enter a title';

  @override
  String get all => 'Alle';

  @override
  String get searchHint => 'Search';

  @override
  String get chats => 'Chats';

  @override
  String get newChat => 'New Chat';

  @override
  String get searchResults => 'Search Results';

  @override
  String get noResultsFound => 'No results found';

  @override
  String get projects => 'Projekte';

  @override
  String get newFolder => 'New Folder';

  @override
  String get createNewFolder => 'Create New Folder';

  @override
  String get enterFolderName => 'Enter folder name';

  @override
  String get rename => 'Rename';

  @override
  String get renameChat => 'Rename Chat';

  @override
  String get renameFolder => 'Rename Folder';

  @override
  String get enterNewName => 'Enter new name';

  @override
  String get moveToFolder => 'Move to folder';

  @override
  String get deleteChat => 'Delete Chat';

  @override
  String get deleteFolder => 'Delete Folder';

  @override
  String get deleteChatConfirmation =>
      'Are you sure you want to delete this chat? This action cannot be undone.';

  @override
  String get deleteFolderConfirmation =>
      'Are you sure you want to delete this folder? This action cannot be undone.';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String daysAgo(int count) {
    return '$count days ago';
  }

  @override
  String get chatHistory => 'Chat-Verlauf';

  @override
  String get addNote => 'Add Note';

  @override
  String get pinMessage => 'Pin Message';

  @override
  String get bookmark => 'Bookmark';

  @override
  String get messageOptions => 'Message Options';

  @override
  String get messageDeleted => 'Message deleted';

  @override
  String get messagePinned => 'Message pinned';

  @override
  String get messageBookmarked => 'Message bookmarked';

  @override
  String get noteAdded => 'Note added';

  @override
  String get enterNote => 'Enter your note';

  @override
  String get addNoteToMessage => 'Add Note to Message';

  @override
  String get editMessage => 'Edit Message';

  @override
  String get enterMessage => 'Enter your message';

  @override
  String get messageMoved => 'Message moved to folder';

  @override
  String get selectFolder => 'Select Folder';

  @override
  String get folderCreated => 'Folder created';

  @override
  String get noFolders => 'No folders yet';

  @override
  String get createFolderPrompt => 'Create a folder to organize your chats';

  @override
  String get noChatsInFolder => 'No chats in this folder';

  @override
  String get addChatsToFolder => 'Add chats by dragging them here';

  @override
  String get noChatsYet => 'No conversations yet';

  @override
  String get startNewChatPrompt => 'Start a new chat to begin';

  @override
  String get noRecentItems => 'No recent items';

  @override
  String get recentItems => 'Recent Items';

  @override
  String get todaysTasks => 'Today\'s Tasks';

  @override
  String get tapToViewTasks => 'Tap to view your tasks';

  @override
  String get appDescription => 'Your smart voice-to-task assistant';

  @override
  String get offlineMode => 'Offline Mode';

  @override
  String get offlineModeDescription =>
      'You\'re currently offline. Some features may be limited.';

  @override
  String get syncPending => 'Sync Pending';

  @override
  String get syncComplete => 'Sync Complete';

  @override
  String get syncFailed => 'Sync Failed';

  @override
  String get retrySync => 'Retry Sync';

  @override
  String get continueInOfflineMode => 'Continue in Offline Mode';

  @override
  String get networkError => 'Network Error';

  @override
  String get checkConnection =>
      'Please check your internet connection and try again.';

  @override
  String get pause => 'Pause';

  @override
  String get resume => 'Resume';

  @override
  String get reset => 'Reset';

  @override
  String get transcriptionComplete => 'Transcription Complete';

  @override
  String get viewDetails => 'View Details';

  @override
  String get continueButton => 'Fortfahren';

  @override
  String get collection => 'Collection';

  @override
  String get newest => 'Newest';

  @override
  String get oldest => 'Oldest';

  @override
  String get name => 'Name';

  @override
  String get category => 'Category';

  @override
  String get synced => 'Synced';

  @override
  String get unsynced => 'Unsynced';

  @override
  String get notSynced => 'Not Synced';

  @override
  String get search => 'Suchen';

  @override
  String get closeSearch => 'Close Search';

  @override
  String get listView => 'List View';

  @override
  String get gridView => 'Grid View';

  @override
  String get createFirstEntry => 'Ersten Eintrag erstellen';

  @override
  String get noEntriesYet => 'No entries yet';

  @override
  String get createNewEntryDescription =>
      'Create your first task, note, or idea using the button below.';

  @override
  String get completed => 'Abgeschlossen';

  @override
  String get noSearchResults => 'No search results found';

  @override
  String get clearSearch => 'Clear Search';

  @override
  String get unassignedEntriesWarning => 'Unassigned Entries';

  @override
  String unassignedEntriesDescription(int count) {
    return 'You have $count unassigned entries waiting to be assigned to a platform';
  }

  @override
  String get assignNow => 'Assign Now';

  @override
  String assignedEntries(int count) {
    return 'Assigned Entries ($count)';
  }

  @override
  String get searchEntries => 'Search entries...';

  @override
  String get archive => 'Archive';

  @override
  String get assign => 'Assign';

  @override
  String get entryArchived => 'Entry archived';

  @override
  String get undo => 'UNDO';

  @override
  String get restoreFunctionalityNotImplemented =>
      'Restore functionality not yet implemented';

  @override
  String assignedToPlatform(String platform) {
    return 'Assigned to $platform';
  }

  @override
  String get recordingFileNotFound =>
      'Recording file not found. It may have been deleted or moved.';

  @override
  String get errorPlayingRecording => 'Error playing recording';

  @override
  String get assignmentSuccessful => 'Assignment Successful';

  @override
  String assignedCategoryToPlatform(String category, String platform) {
    return '$category assigned to $platform';
  }

  @override
  String get myTasks => 'My Tasks';

  @override
  String get task => 'Aufgabe';

  @override
  String get myIdeas => 'My Ideas';

  @override
  String get idea => 'Idee';

  @override
  String get myNotes => 'My Notes';

  @override
  String get note => 'Notiz';

  @override
  String get noTasksYet => 'No tasks yet';

  @override
  String get noIdeasYet => 'No ideas yet';

  @override
  String get noNotesYet => 'No notes yet';

  @override
  String get addNew => 'Add New';

  @override
  String get optimizedFor => 'Optimized for';

  @override
  String get optimizedContent => 'Optimized Content';

  @override
  String get contentOptimization => 'Content Optimization';

  @override
  String get platformAssignment => 'Platform Assignment';

  @override
  String get audioPlaybackError => 'Audio playback error';

  @override
  String get audioFileCorrupted =>
      'Unable to play the audio file. It may be corrupted.';

  @override
  String get loadingAudioPlayer => 'Loading audio player...';

  @override
  String get optimizingFor => 'Optimizing for';

  @override
  String get choosePlatformToAssign => 'Choose Platform to Assign';

  @override
  String get assigningTo => 'Assigning to';

  @override
  String get autoAssignmentEnabled =>
      'Auto-assignment is enabled. This item will be automatically processed.';

  @override
  String get deleteRecordingConfirmation =>
      'Are you sure you want to delete this recording? This action cannot be undone.';

  @override
  String get deletingRecording => 'Deleting recording...';

  @override
  String get save => 'Speichern';

  @override
  String get created => 'Erstellt';

  @override
  String get content => 'Inhalt';

  @override
  String get createEntry => 'Eintrag erstellen';

  @override
  String get taskReminder => 'Aufgabenerinnerung';

  @override
  String get selectTime => 'Select time';

  @override
  String get changeTime => 'Change Time';

  @override
  String get noReminder => 'No Reminder';

  @override
  String get editEntry => 'Eintrag bearbeiten';

  @override
  String get originalContent => 'Original Content';

  @override
  String get textEntry => 'Text Entry';

  @override
  String get voiceEntry => 'Voice Entry';

  @override
  String get optimizeAs => 'Optimize as';

  @override
  String get optimizeAsTaskDescription =>
      'Convert content into an actionable task with clear steps and deadlines';

  @override
  String get optimizeAsIdeaDescription =>
      'Structure content as a creative idea with potential applications and benefits';

  @override
  String get optimizeAsNoteDescription =>
      'Format content as a well-structured note with key points and references';

  @override
  String get optimizationInProgress =>
      'Using AI to enhance and structure your content';

  @override
  String get autoAssignment => 'Auto Assignment';

  @override
  String get autoAssignmentInfo =>
      'Content will be automatically analyzed and assigned to the most appropriate connected platform';

  @override
  String get configureAutoAssignment => 'Configure Auto Assignment';

  @override
  String get navigateToSettings => 'Opening Settings...';

  @override
  String get manageConnections => 'Manage Connections';

  @override
  String get platformConnectionsManagement =>
      'Opening platform connections management...';

  @override
  String get successfullyAssignedTo => 'Successfully assigned to';

  @override
  String get recording => 'Recording';

  @override
  String get recordingPaused => 'Paused';

  @override
  String get resetRecording => 'Reset Recording?';

  @override
  String get resetRecordingConfirmation =>
      'Are you sure you want to reset this recording? This action cannot be undone.';

  @override
  String get recordingGuideTitle => 'How it works';

  @override
  String get recordingGuideStep1 => 'Record your voice note';

  @override
  String get recordingGuideStep2 =>
      'ProjectPilot magically transcribes and enhances it';

  @override
  String get recordingGuideStep3 => 'Send it to your favorite platform';

  @override
  String get noInternetConnection => 'No internet connection';

  @override
  String get processingPurchase => 'Processing your purchase...';

  @override
  String get choosePackage => 'Choose a Package';

  @override
  String get packageSelectionSubtitle =>
      'Get more tokens to use for AI text generation and audio transcription';

  @override
  String purchaseSuccessful(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Successfully purchased $countString tokens!';
  }

  @override
  String get sendToThisPlatform => 'Send to this platform';

  @override
  String get choosePlatformTitle => 'Choose Platform';

  @override
  String get choosePlatformDescription =>
      'Choose a platform to dispatch your content to';

  @override
  String get contentPreview => 'Content Preview';

  @override
  String get suggested => 'Suggested';

  @override
  String get connectPlatform => 'Connect Platform';

  @override
  String get authenticating => 'Authentifizierung...';

  @override
  String get createAccount => 'Konto erstellen';

  @override
  String get emailFieldHint => 'Enter your email address';

  @override
  String get login => 'Anmelden';

  @override
  String get passwordFieldHint => 'Enter your password';

  @override
  String get confirmPassword => 'Passwort bestätigen';

  @override
  String get confirmPasswordHint => 'Bestätige dein Passwort';

  @override
  String get confirmPasswordRequired => 'Bitte bestätige dein Passwort';

  @override
  String get passwordsDoNotMatch => 'Passwörter stimmen nicht überein';

  @override
  String get nameFieldHint => 'Gib deinen vollständigen Namen ein';

  @override
  String get history => 'Verlauf';

  @override
  String get record => 'Aufnahme';

  @override
  String get newEntry => 'Neuer Eintrag';

  @override
  String get pleaseEnterContent => 'Bitte geben Sie Inhalt ein';

  @override
  String get update => 'Aktualisieren';

  @override
  String get noEntries => 'Keine Einträge';

  @override
  String get deleteAllAudioFiles => 'Alle Audiodateien löschen';

  @override
  String get deleteAllAudioFilesContent =>
      'Dies wird alle Sprachaufnahmedateien löschen, um Speicherplatz freizugeben. Der Textinhalt Ihrer Aufnahmen bleibt erhalten.\n\nDiese Aktion kann nicht rückgängig gemacht werden. Sind Sie sicher?';

  @override
  String get deleteAll => 'Alle löschen';

  @override
  String get deleteAllAudioFilesTooltip => 'Alle Audiodateien löschen';

  @override
  String get selectLanguageDialogTitle => 'Sprache auswählen';

  @override
  String get enabled => 'Aktiviert';

  @override
  String get disabled => 'Deaktiviert';

  @override
  String get autoAssignEnabledDesc =>
      'Aufgaben werden automatisch Plattformen zugewiesen';

  @override
  String get autoAssignDisabledDesc => 'Manuelle Plattformauswahl für Aufgaben';

  @override
  String packageDescription(String name, int tokenAmount) {
    return '$name Paket mit $tokenAmount Tokens';
  }

  @override
  String aboutAppTitle(String appName) {
    return 'Über $appName';
  }

  @override
  String appVersion(String version) {
    return 'Version $version';
  }

  @override
  String get keyFeatures => 'Hauptfunktionen';

  @override
  String get voiceToTask => 'Sprache-zu-Aufgabe';

  @override
  String get voiceToTaskDesc => 'Erstellen Sie Aufgaben mit Ihrer Stimme';

  @override
  String get multiPlatform => 'Multi-Plattform';

  @override
  String get multiPlatformDesc => 'Verbindung zu ClickUp, Notion und mehr';

  @override
  String get multiLanguage => 'Mehrsprachig';

  @override
  String get multiLanguageDesc =>
      'Unterstützt Englisch, Deutsch, Russisch, Türkisch und Arabisch';

  @override
  String get aiPowered => 'KI-gestützt';

  @override
  String get aiPoweredDesc =>
      'Intelligente Aufgabenkategorisierung und -optimierung';

  @override
  String get ourTeam => 'Unser Team';

  @override
  String copyright(String year) {
    return '© $year ProjectPilot. Alle Rechte vorbehalten.';
  }

  @override
  String get contactSupportDesc =>
      'Haben Sie eine Frage oder benötigen Hilfe? Füllen Sie das untenstehende Formular aus und unser Support-Team wird sich so schnell wie möglich bei Ihnen melden.';

  @override
  String get nameField => 'Name';

  @override
  String get emailField => 'E-Mail';

  @override
  String get messageField => 'Nachricht';

  @override
  String get messageFieldHint => 'Geben Sie Ihre Nachricht ein';

  @override
  String get submitButton => 'Absenden';

  @override
  String get nameValidationError => 'Bitte geben Sie Ihren Namen ein';

  @override
  String get emailValidationError => 'Bitte geben Sie eine gültige E-Mail ein';

  @override
  String get messageValidationError => 'Bitte geben Sie Ihre Nachricht ein';

  @override
  String get initializationError => 'Initialisierungsfehler';

  @override
  String errorDuringInit(String error) {
    return 'Ein Fehler ist während der App-Initialisierung aufgetreten:\n$error';
  }

  @override
  String get retryButton => 'Wiederholen';

  @override
  String get faqTitle => 'FAQ';

  @override
  String get frequentlyAskedQuestions => 'Häufig gestellte Fragen';

  @override
  String get privacyPolicyTitle => 'Datenschutzrichtlinie';

  @override
  String lastUpdated(String time) {
    return 'Zuletzt aktualisiert: $time';
  }

  @override
  String get privacyPolicyIntro =>
      'Diese Datenschutzrichtlinie beschreibt, wie ProjectPilot (\"wir\", \"uns\" oder \"unser\") Ihre persönlichen Informationen sammelt, verwendet und offenlegt, wenn Sie unsere mobile Anwendung (die \"App\") verwenden.';

  @override
  String get infoWeCollect => 'Informationen, die wir sammeln';

  @override
  String get infoWeCollectContent =>
      'Wir sammeln Informationen, die Sie uns direkt zur Verfügung stellen, beispielsweise wenn Sie ein Konto erstellen, Ihr Profil aktualisieren, die interaktiven Funktionen unserer App nutzen, Kundensupport anfordern oder anderweitig mit uns kommunizieren.';

  @override
  String get howWeUseInfo => 'Wie wir Ihre Informationen verwenden';

  @override
  String get howWeUseInfoContent =>
      'Wir verwenden die von uns gesammelten Informationen, um unsere Dienste bereitzustellen, zu warten und zu verbessern, einschließlich der Verarbeitung von Transaktionen, dem Senden verwandter Informationen und der Bereitstellung von Kundensupport.';

  @override
  String get sharingOfInfo => 'Weitergabe von Informationen';

  @override
  String get sharingOfInfoContent =>
      'Wir können die von uns gesammelten Informationen wie folgt weitergeben: mit Drittanbietern, Beratern und anderen Dienstleistern, die Zugang zu solchen Informationen benötigen, um Arbeiten in unserem Auftrag auszuführen; als Antwort auf eine Informationsanfrage, wenn wir glauben, dass die Offenlegung im Einklang mit geltendem Recht, Vorschriften oder Rechtsverfahren steht.';

  @override
  String get yourChoices => 'Ihre Wahlmöglichkeiten';

  @override
  String get yourChoicesContent =>
      'Sie können Ihre Kontoinformationen jederzeit aktualisieren, korrigieren oder löschen, indem Sie sich in Ihr Konto einloggen oder uns kontaktieren. Sie können sich von Werbekommunikation von uns abmelden, indem Sie den Anweisungen in diesen Mitteilungen folgen.';

  @override
  String get contactUs => 'Kontaktieren Sie uns';

  @override
  String get contactUsPrivacyContent =>
      'Wenn Sie Fragen zu dieser Datenschutzrichtlinie haben, kontaktieren Sie uns bitte unter: <EMAIL>';

  @override
  String get termsOfServiceTitle => 'Nutzungsbedingungen';

  @override
  String get termsOfServiceIntro =>
      'Bitte lesen Sie diese Nutzungsbedingungen (\"Bedingungen\", \"Nutzungsbedingungen\") sorgfältig durch, bevor Sie die ProjectPilot Mobile-Anwendung (der \"Service\") verwenden, die von ProjectPilot (\"uns\", \"wir\" oder \"unser\") betrieben wird.';

  @override
  String get acceptanceOfTerms => 'Annahme der Bedingungen';

  @override
  String get acceptanceOfTermsContent =>
      'Durch den Zugriff auf oder die Nutzung des Services stimmen Sie zu, an diese Bedingungen gebunden zu sein. Wenn Sie mit einem Teil der Bedingungen nicht einverstanden sind, dürfen Sie nicht auf den Service zugreifen.';

  @override
  String get useOfService => 'Nutzung des Services';

  @override
  String get useOfServiceContent =>
      'Unser Service ermöglicht es Ihnen, Aufgaben und Notizen mit Sprachbefehlen zu erstellen, zu verwalten und zu organisieren. Sie sind dafür verantwortlich, die Vertraulichkeit Ihres Kontos und Passworts zu wahren und den Zugang zu Ihrem Computer oder mobilen Gerät zu beschränken.';

  @override
  String get intellectualProperty => 'Geistiges Eigentum';

  @override
  String get intellectualPropertyContent =>
      'Der Service und seine ursprünglichen Inhalte, Funktionen und Funktionalitäten sind und bleiben das ausschließliche Eigentum von ProjectPilot und seinen Lizenzgebern. Der Service ist durch Urheberrecht, Markenrecht und andere Gesetze geschützt.';

  @override
  String get termination => 'Kündigung';

  @override
  String get terminationContent =>
      'Wir können Ihr Konto sofort kündigen oder sperren, ohne vorherige Ankündigung oder Haftung, aus jedem beliebigen Grund, einschließlich ohne Einschränkung, wenn Sie gegen die Bedingungen verstoßen.';

  @override
  String get limitationOfLiability => 'Haftungsbeschränkung';

  @override
  String get limitationOfLiabilityContent =>
      'In keinem Fall haften ProjectPilot oder seine Direktoren, Mitarbeiter, Partner, Agenten, Lieferanten oder Verbundenen für indirekte, zufällige, besondere, Folge- oder Strafschäden, einschließlich ohne Einschränkung Verlust von Gewinnen, Daten, Nutzung, Goodwill oder anderen immateriellen Verlusten.';

  @override
  String get changesToTerms => 'Änderungen der Bedingungen';

  @override
  String get changesToTermsContent =>
      'Wir behalten uns das Recht vor, nach unserem alleinigen Ermessen diese Bedingungen jederzeit zu ändern oder zu ersetzen. Wenn eine Überarbeitung wesentlich ist, werden wir versuchen, mindestens 30 Tage im Voraus zu benachrichtigen, bevor neue Bedingungen in Kraft treten.';

  @override
  String get contactUsTermsContent =>
      'Wenn Sie Fragen zu diesen Bedingungen haben, kontaktieren Sie uns bitte unter: <EMAIL>';

  @override
  String get whatIsProjectPilot => 'Was ist ProjectPilot?';

  @override
  String get whatIsProjectPilotAnswer =>
      'ProjectPilot ist eine Sprache-zu-Aufgabe-Anwendung, die es Ihnen ermöglicht, Aufgaben, Notizen und Ideen mit Sprachbefehlen zu erstellen. Es kann Ihre Stimme transkribieren und die Aufgaben an verschiedene Plattformen wie ClickUp, Notion und mehr senden.';

  @override
  String get howToConnectPlatforms =>
      'Wie verbinde ich mich mit externen Plattformen?';

  @override
  String get howToConnectPlatformsAnswer =>
      'Gehen Sie zum Einstellungsbildschirm und navigieren Sie zum Bereich \"Plattformverbindungen\". Dort können Sie sich mit verschiedenen Plattformen wie ClickUp, Notion und Monday verbinden, indem Sie auf die Schaltfläche Verbinden klicken und dem Authentifizierungsprozess folgen.';

  @override
  String get whatLanguagesSupported => 'Welche Sprachen werden unterstützt?';

  @override
  String get whatLanguagesSupportedAnswer =>
      'ProjectPilot unterstützt derzeit Englisch, Deutsch, Russisch, Türkisch und Arabisch. Sie können die Sprache im Einstellungsbildschirm ändern.';

  @override
  String get howTokensWork => 'Wie funktionieren Tokens?';

  @override
  String get howTokensWorkAnswer =>
      'Tokens werden für KI-Textgenerierung und Audio-Transkription verwendet. Jede Transkription und Optimierung verbraucht eine bestimmte Anzahl von Tokens. Sie können weitere Tokens im Bereich \"Paket aktualisieren\" der Einstellungen kaufen.';

  @override
  String get canUseOffline => 'Kann ich ProjectPilot offline verwenden?';

  @override
  String get canUseOfflineAnswer =>
      'Einige Funktionen von ProjectPilot erfordern eine Internetverbindung, wie das Senden von Aufgaben an externe Plattformen und die Verwendung von Online-Transkriptionsdiensten. Grundlegende Sprachaufnahmen können jedoch offline funktionieren.';

  @override
  String get howCustomizeProfile => 'Wie kann ich mein Profil anpassen?';

  @override
  String get howCustomizeProfileAnswer =>
      'Sie können Ihre Profilinformationen im Einstellungsbildschirm unter dem Bereich \"Profil\" aktualisieren.';

  @override
  String get isDataSecure => 'Sind meine Daten sicher?';

  @override
  String get isDataSecureAnswer =>
      'Ja, wir nehmen Datensicherheit ernst. Ihre Daten werden verschlüsselt und sicher gespeichert. Wir teilen Ihre persönlichen Informationen nicht mit Dritten ohne Ihre Zustimmung. Weitere Details finden Sie in unserer Datenschutzrichtlinie.';

  @override
  String get howCancelSubscription => 'Wie kann ich mein Abonnement kündigen?';

  @override
  String get howCancelSubscriptionAnswer =>
      'Sie können Ihr Abonnement über Ihr App-Store-Konto (Google Play Store oder Apple App Store) verwalten. Gehen Sie zum Abonnementverwaltungsbereich Ihres App-Stores und kündigen Sie das ProjectPilot-Abonnement.';

  @override
  String get appTitleProjectPilot => 'ProjectPilot';

  @override
  String get projectDashboard => 'Projekt-Dashboard';

  @override
  String get projectAnalysis => 'Projektanalyse';

  @override
  String get askAboutProject => 'Über Projekt fragen';

  @override
  String get projectStatus => 'Projektstatus';

  @override
  String get blockers => 'Blockierer';

  @override
  String get recommendations => 'Empfehlungen';

  @override
  String get criticalPath => 'Kritischer Pfad';

  @override
  String get projectProgress => 'Projektfortschritt';

  @override
  String get teamMembers => 'Teammitglieder';

  @override
  String get budget => 'Budget';

  @override
  String get timeline => 'Zeitplan';

  @override
  String get risks => 'Risiken';

  @override
  String get insights => 'Einblicke';

  @override
  String get analysisLoading => 'Projekt wird analysiert...';

  @override
  String get analysisFailed =>
      'Analyse fehlgeschlagen. Bitte versuchen Sie es erneut.';

  @override
  String get refreshProject => 'Projekt aktualisieren';

  @override
  String get projectOverdue => 'Projekt überfällig';

  @override
  String get projectOnTrack => 'Projekt auf Kurs';

  @override
  String get projectAtRisk => 'Projekt gefährdet';

  @override
  String get highPriorityBlockers => 'Hochprioritäts-Blocker';

  @override
  String get urgentRecommendations => 'Dringende Empfehlungen';

  @override
  String daysRemaining(int count) {
    return '$count Tage verbleibend';
  }

  @override
  String tasksCompleted(int completed, int total) {
    return '$completed von $total Aufgaben abgeschlossen';
  }

  @override
  String budgetUtilization(double spent, double total) {
    final intl.NumberFormat spentNumberFormat = intl.NumberFormat.currency(
      locale: localeName,
    );
    final String spentString = spentNumberFormat.format(spent);
    final intl.NumberFormat totalNumberFormat = intl.NumberFormat.currency(
      locale: localeName,
    );
    final String totalString = totalNumberFormat.format(total);

    return 'Budget: $spentString von $totalString verwendet';
  }

  @override
  String get queryProject => 'Fragen Sie mich alles über dieses Projekt...';

  @override
  String get processingQuery => 'Ihre Frage wird bearbeitet...';

  @override
  String get queryFailed =>
      'Ihre Frage konnte nicht bearbeitet werden. Bitte versuchen Sie es erneut.';

  @override
  String get projectNotFound => 'Projekt nicht gefunden';

  @override
  String get syncingData => 'Projektdaten werden synchronisiert...';

  @override
  String get activeTasks => 'Aktive Aufgaben';

  @override
  String get completedTasks => 'Abgeschlossene Aufgaben';

  @override
  String get blockedTasks => 'Blockierte Aufgaben';

  @override
  String get overdueTasks => 'Überfällige Aufgaben';

  @override
  String confidenceLevel(int level) {
    return 'Vertrauen: $level%';
  }

  @override
  String riskScore(double score) {
    final intl.NumberFormat scoreNumberFormat = intl
        .NumberFormat.decimalPattern(localeName);
    final String scoreString = scoreNumberFormat.format(score);

    return 'Risiko-Score: $scoreString/10';
  }

  @override
  String get riskAssessment => 'Risikobewertung';

  @override
  String get budgetOverview => 'Budgetübersicht';

  @override
  String get keyInsights => 'Wichtige Erkenntnisse';

  @override
  String get errorLoadingAnalysis => 'Fehler beim Laden der Analyse';

  @override
  String get retry => 'Wiederholen';

  @override
  String get tapAnalyzeToGetInsights =>
      'Tippen Sie auf Analysieren, um KI-Einblicke zu erhalten';

  @override
  String get analyzeProject => 'Projekt analysieren';

  @override
  String get aiAnalysisSummary => 'KI-Analysezusammenfassung';

  @override
  String get active => 'Aktiv';

  @override
  String get inactive => 'Inaktiv';

  @override
  String get overBudget => 'Über Budget';

  @override
  String get totalBudget => 'Gesamtbudget';

  @override
  String get spent => 'Ausgegeben';

  @override
  String get remaining => 'Verbleibend';

  @override
  String get analysisDetails => 'Analysedetails';

  @override
  String get confidenceLevelLabel => 'Vertrauensniveau';

  @override
  String get analysisDate => 'Analysedatum';

  @override
  String get predictedCompletion => 'Voraussichtliche Fertigstellung';

  @override
  String get critical => 'Kritisch';

  @override
  String get high => 'Hoch';

  @override
  String get medium => 'Mittel';

  @override
  String get low => 'Niedrig';

  @override
  String get justNow => 'gerade jetzt';

  @override
  String get criticalPaths => 'Kritische Pfade';

  @override
  String get error => 'Fehler';

  @override
  String get noProjectsYet => 'Noch keine Projekte';

  @override
  String get connectProjectManagementPlatforms =>
      'Verbinden Sie Ihre Projektmanagement-Plattformen, um zu beginnen';

  @override
  String get projectPilotDashboard => 'ProjectPilot Dashboard';

  @override
  String get totalProjects => 'Gesamtprojekte';

  @override
  String get recentProjects => 'Aktuelle Projekte';

  @override
  String get viewAll => 'Alle anzeigen';

  @override
  String get tasksOverview => 'Aufgabenübersicht';

  @override
  String get recentTasks => 'Aktuelle Aufgaben';

  @override
  String get noTasksFound => 'Keine Aufgaben gefunden';

  @override
  String get inProgress => 'In Bearbeitung';

  @override
  String get blocked => 'Blockiert';

  @override
  String get overdue => 'Überfällig';

  @override
  String get toDo => 'Zu erledigen';

  @override
  String get review => 'Überprüfung';

  @override
  String get done => 'Fertig';

  @override
  String get cancelled => 'Abgebrochen';

  @override
  String get progress => 'Fortschritt';

  @override
  String get createdAt => 'Erstellt am';

  @override
  String get deadline => 'Frist';

  @override
  String get platform => 'Plattform';

  @override
  String get planning => 'Planung';

  @override
  String get onHold => 'Pausiert';

  @override
  String get aiProjectAssistant => 'KI-Projektassistent';

  @override
  String get whatProjectsAreBehindSchedule =>
      'Welche Projekte sind im Rückstand?';

  @override
  String get showMeProjectRisks => 'Zeige mir Projektrisiken';

  @override
  String get whichTasksAreBlocked => 'Welche Aufgaben sind blockiert?';

  @override
  String get projectProgressSummary => 'Projektfortschrittszusammenfassung';

  @override
  String get teamWorkloadAnalysis => 'Teamarbeitsbelastungsanalyse';

  @override
  String get dismiss => 'Schließen';

  @override
  String examplesForRole(String role) {
    return 'Beispiele für $role';
  }

  @override
  String get tapToUse => 'Tippen zum Verwenden';

  @override
  String get roleDeveloper => 'Entwickler';

  @override
  String get roleProjectManager => 'Projektmanager';

  @override
  String get roleProductOwner => 'Product Owner';

  @override
  String get roleQaTester => 'QA Tester';

  @override
  String get roleCtoCeo => 'CTO/CEO';

  @override
  String get roleAllRoles => 'alle Rollen';

  @override
  String get developerPrompt1 => 'Welche Aufgaben sind mir aktuell zugewiesen?';

  @override
  String get developerPrompt2 => 'Was blockiert meinen Code Review?';

  @override
  String get developerPrompt3 => 'Was steht im nächsten Sprint an?';

  @override
  String get developerPrompt4 => 'Welche Bugs haben höchste Priorität?';

  @override
  String get projectManagerPrompt1 => 'Was verzögert den aktuellen Sprint?';

  @override
  String get projectManagerPrompt2 => 'Welche Tasks sind overdue?';

  @override
  String get projectManagerPrompt3 => 'Wer hat zuletzt am Projekt gearbeitet?';

  @override
  String get projectManagerPrompt4 =>
      'Wie ist der aktuelle Projektfortschritt?';

  @override
  String get productOwnerPrompt1 =>
      'Welche Features sind für den nächsten Release geplant?';

  @override
  String get productOwnerPrompt2 =>
      'Was ist das Feedback zu unserem letzten Update?';

  @override
  String get productOwnerPrompt3 =>
      'Welche User Stories haben höchste Priorität?';

  @override
  String get productOwnerPrompt4 =>
      'Wie entwickelt sich unser Product Backlog?';

  @override
  String get qaTesterPrompt1 => 'Welche Tests müssen noch durchgeführt werden?';

  @override
  String get qaTesterPrompt2 => 'Gibt es offene Critical Bugs?';

  @override
  String get qaTesterPrompt3 => 'Wie ist die aktuelle Testabdeckung?';

  @override
  String get qaTesterPrompt4 => 'Welche Features sind testbereit?';

  @override
  String get ctoCeoPrompt1 => 'Wie läuft das Projekt insgesamt?';

  @override
  String get ctoCeoPrompt2 => 'Was kostet uns aktuell am meisten Zeit?';

  @override
  String get ctoCeoPrompt3 => 'Welche Teams brauchen Unterstützung?';

  @override
  String get ctoCeoPrompt4 => 'Wie steht es um unser Budget und Timeline?';

  @override
  String get defaultPrompt1 => 'Welche Aufgaben stehen heute an?';

  @override
  String get defaultPrompt2 => 'Wie ist der aktuelle Projektstatus?';

  @override
  String get defaultPrompt3 => 'Was sind die nächsten wichtigen Schritte?';

  @override
  String get defaultPrompt4 =>
      'Gibt es Hindernisse, die behoben werden müssen?';

  @override
  String get analyzingProject => 'Analyzing project...';

  @override
  String get suggestedActions => 'Suggested Actions';
}
