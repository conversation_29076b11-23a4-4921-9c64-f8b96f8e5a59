// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'فويس بايلوت';

  @override
  String get homeTitle => 'الرئيسية';

  @override
  String get settingsTitle => 'الإعدادات';

  @override
  String get welcome => 'مرحباً';

  @override
  String get welcomeSubtitle => 'فويس بايلوت - مستقبل الإنتاجية';

  @override
  String get languageTitle => 'اللغة';

  @override
  String get englishLanguage => 'الإنجليزية';

  @override
  String get germanLanguage => 'الألمانية';

  @override
  String get russianLanguage => 'الروسية';

  @override
  String get turkishLanguage => 'التركية';

  @override
  String get arabicLanguage => 'العربية';

  @override
  String get saveButton => 'حفظ';

  @override
  String get cancelButton => 'إلغاء';

  @override
  String get errorOccurred => 'حدث خطأ';

  @override
  String get tryAgain => 'حاول مرة أخرى';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String tokenBalance(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'رصيد الرموز: $countString';
  }

  @override
  String get tokenBalanceDescription =>
      'Tokens for AI text generation with GPT';

  @override
  String get transcriptionTitle => 'النسخ';

  @override
  String get startRecording => 'بدء التسجيل';

  @override
  String get stopRecording => 'إيقاف التسجيل';

  @override
  String get noTranscriptionYet => 'لا يوجد نسخ بعد';

  @override
  String get shareTranscription => 'مشاركة النسخ';

  @override
  String get themeTitle => 'المظهر';

  @override
  String get selectTheme => 'اختر المظهر';

  @override
  String get systemTheme => 'افتراضي النظام';

  @override
  String get lightTheme => 'فاتح';

  @override
  String get darkTheme => 'داكن';

  @override
  String get toggleTheme => 'تبديل المظهر';

  @override
  String get apiKeysSection => 'مفاتيح API';

  @override
  String get openaiApiKey => 'مفتاح API لـ OpenAI';

  @override
  String get whisperApiKey => 'مفتاح API لـ Whisper';

  @override
  String get apiKeyHint => 'أدخل مفتاح API الخاص بك';

  @override
  String get tokenSection => 'الرموز والاستخدام';

  @override
  String remainingMinutes(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'الدقائق المتبقية: $countString';
  }

  @override
  String get minutesBalanceDescription =>
      'Minutes available for audio transcription with Whisper';

  @override
  String get purchasePackages => 'شراء الباقات';

  @override
  String get buyTokens => 'شراء رموز';

  @override
  String get starterPackage => 'مبتدئ';

  @override
  String get proPackage => 'محترف';

  @override
  String get businessPackage => 'أعمال';

  @override
  String get ultimatePackage => 'متميز';

  @override
  String tokens(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return '$countString رمز';
  }

  @override
  String price(double price) {
    final intl.NumberFormat priceNumberFormat = intl
        .NumberFormat.simpleCurrency(locale: localeName, name: 'EUR');
    final String priceString = priceNumberFormat.format(price);

    return '€$priceString';
  }

  @override
  String get profileSection => 'الملف الشخصي';

  @override
  String get username => 'اسم المستخدم';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get notAvailable => 'غير متوفر';

  @override
  String get logoutButton => 'تسجيل الخروج';

  @override
  String get apiKeySaved => 'تم حفظ مفتاح API بنجاح!';

  @override
  String get introScreen1Title => 'ذكاء المشاريع المدعوم بالذكاء الاصطناعي';

  @override
  String get introScreen1Description =>
      'احتفظ بالخيط الأحمر في مشاريعك. اكتشف المشاكل مبكراً وابق على المسار الصحيح بالرؤى الذكية.';

  @override
  String get introScreen2Title => 'نظرة عامة على المشاريع متعددة المنصات';

  @override
  String get introScreen2Description =>
      'اربط جميع أدوات مشاريعك في لوحة واحدة. ClickUp، Notion، Jira والمزيد - كلها متزامنة.';

  @override
  String get introScreen3Title => 'الكشف الذكي عن المشاكل';

  @override
  String get introScreen3Description =>
      'اكتشف العوائق تلقائياً، قيم المخاطر واحصل على توصيات قابلة للتنفيذ للحفاظ على تحرك مشاريعك.';

  @override
  String get introMainTitle => 'ProjectPilot';

  @override
  String get introMainSubtitle => 'لن تفقد أبداً مسار مشاريعك مرة أخرى';

  @override
  String get getStarted => 'ابدأ الآن';

  @override
  String get skip => 'تخطي';

  @override
  String get next => 'التالي';

  @override
  String get loginTitle => 'مرحبًا بك في ProjectPilot';

  @override
  String get loginSubtitle => 'سجل الدخول للمتابعة';

  @override
  String get continueWithGoogle => 'المتابعة باستخدام Google';

  @override
  String get continueWithApple => 'المتابعة باستخدام Apple';

  @override
  String get continueWithEmail => 'المتابعة باستخدام البريد الإلكتروني';

  @override
  String get moreOptions => 'خيارات أكثر';

  @override
  String get continueWithLinkedIn => 'المتابعة باستخدام LinkedIn';

  @override
  String get continueWithPhone => 'المتابعة باستخدام الهاتف';

  @override
  String get continueWithAzure => 'المتابعة باستخدام Azure';

  @override
  String get continueWithNotion => 'المتابعة باستخدام Notion';

  @override
  String get password => 'كلمة المرور';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get signUp => 'إنشاء حساب';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get or => 'أو';

  @override
  String get createAccountText => 'أنشئ حساب فويس بايلوت الخاص بك';

  @override
  String get usernameRequired => 'اسم المستخدم مطلوب';

  @override
  String get usernameMinLength => 'يجب أن يكون اسم المستخدم 3 أحرف على الأقل';

  @override
  String get emailRequired => 'البريد الإلكتروني مطلوب';

  @override
  String get invalidEmail => 'أدخل بريد إلكتروني صالح';

  @override
  String get passwordRequired => 'كلمة المرور مطلوبة';

  @override
  String get passwordMinLength => 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';

  @override
  String get signUpSuccessful => 'تم التسجيل بنجاح!';

  @override
  String get onboarding => 'أكمل ملفك الشخصي';

  @override
  String get camera => 'الكاميرا';

  @override
  String get gallery => 'المعرض';

  @override
  String get birthYear => 'سنة الميلاد';

  @override
  String get selectBirthYear => 'اختر سنة الميلاد';

  @override
  String get userRole => 'دور المستخدم';

  @override
  String get developer => 'مطور';

  @override
  String get projectManager => 'مدير مشروع';

  @override
  String get private => 'خاص';

  @override
  String get other => 'أخرى';

  @override
  String get usagePurpose => 'الغرض من الاستخدام';

  @override
  String get tasks => 'المهام';

  @override
  String get notes => 'الملاحظات';

  @override
  String get ideas => 'الأفكار';

  @override
  String get connectPlatforms => 'ربط المنصات';

  @override
  String get platformsDescription => 'اتصل بمنصاتك المفضلة';

  @override
  String get connectMore => 'ربط المزيد من المنصات لاحقًا';

  @override
  String get finish => 'إنهاء';

  @override
  String get platformConnections => 'اتصالات المنصات';

  @override
  String connectToPlatform(String platform) {
    return 'الاتصال بـ $platform';
  }

  @override
  String get connected => 'متصل';

  @override
  String get disconnected => 'غير متصل';

  @override
  String get disconnect => 'قطع الاتصال';

  @override
  String get connecting => 'جاري الاتصال...';

  @override
  String get authorizationFailed => 'فشل التفويض';

  @override
  String get connectionSuccessful => 'تم الاتصال بنجاح';

  @override
  String get connectionFailed => 'فشل الاتصال';

  @override
  String integrationAvailableFor(String platform) {
    return 'التكامل متاح لـ $platform';
  }

  @override
  String get allPlatforms => 'جميع المنصات';

  @override
  String get managePlatformConnections => 'إدارة اتصالات المنصات';

  @override
  String get connectPlatformToSendTasks =>
      'قم بتوصيل منصاتك المفضلة لإرسال المهام مباشرة';

  @override
  String get cancel => 'إلغاء';

  @override
  String get whatWouldYouLikeToDo => 'ماذا تريد أن تفعل؟';

  @override
  String get recordingInProgress => 'جاري التسجيل...';

  @override
  String get processingYourVoice => 'جاري معالجة صوتك...';

  @override
  String get whereToSave => 'أين تريد حفظ هذا؟';

  @override
  String get noPlatformsConnected => 'لا توجد منصات متصلة بعد';

  @override
  String get newRecording => 'تسجيل جديد';

  @override
  String get uncategorized => 'غير مصنف';

  @override
  String get preferences => 'Preferences';

  @override
  String get upgradePackage => 'Upgrade Package';

  @override
  String get infoAndSupport => 'Info & Support';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get platformConnected => 'Connected';

  @override
  String get platformNotConnected => 'Not connected';

  @override
  String get connect => 'Connect';

  @override
  String get upgradeMessage =>
      'Get more tokens and recording minutes by upgrading your package';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get faq => 'FAQ';

  @override
  String get contactSupport => 'Contact Support';

  @override
  String get aboutApp => 'About ProjectPilot';

  @override
  String get settingsLanguageSection => 'Language';

  @override
  String get appearanceAndBehavior => 'Appearance & Behavior';

  @override
  String get settingsInfoSection => 'Information & Support';

  @override
  String get helpAndSupport => 'Help & Support';

  @override
  String get privacyTermsFaq => 'Privacy, Terms, FAQ';

  @override
  String get settingsAbout => 'About';

  @override
  String get storageManagement => 'Storage Management';

  @override
  String get voiceRecordings => 'Voice Recordings';

  @override
  String usingStorage(String size) {
    return 'Using $size of storage';
  }

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get automaticAssignment => 'Automatic Assignment';

  @override
  String get whereToSendTask => 'Where should I send this?';

  @override
  String get selectPlatform => 'Select Platform';

  @override
  String get selectProject => 'Select Project / Notebook / Board';

  @override
  String get sendNow => 'Send Now';

  @override
  String get noProjectsFound => 'No projects found';

  @override
  String get taskSentSuccessfully => 'Task sent successfully';

  @override
  String get platformDetected => 'Platform detected from your voice';

  @override
  String get projectDetected => 'Project detected from your voice';

  @override
  String get intentDetected => 'Intent detected from your voice';

  @override
  String get editContent => 'Edit content';

  @override
  String get noConnectedPlatforms => 'No connected platforms';

  @override
  String get connectPlatformsInSettings =>
      'Connect platforms in settings to send tasks';

  @override
  String get newTask => 'New Task';

  @override
  String get newNote => 'New Note';

  @override
  String get newIdea => 'New Idea';

  @override
  String get edit => 'تعديل';

  @override
  String get share => 'مشاركة';

  @override
  String get transcriptionResult => 'نتيجة النسخ';

  @override
  String get detectedIntent => 'النية المكتشفة';

  @override
  String get detectedEntities => 'الكيانات المكتشفة';

  @override
  String get originalTranscription => 'النسخ الأصلي';

  @override
  String get addConnection => 'Add Connection';

  @override
  String get noConnections => 'No Connections';

  @override
  String get addConnectionsDescription =>
      'Connect your favorite platforms to send tasks directly';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String confirmDeleteConnectionMessage(String platform) {
    return 'Are you sure you want to delete the connection to $platform?';
  }

  @override
  String get delete => 'Delete';

  @override
  String platformConnectedMessage(String platform) {
    return 'Connected to $platform';
  }

  @override
  String get speechHistoryTitle => 'Speech History';

  @override
  String get clearHistory => 'Clear History';

  @override
  String get tapMicToStart => 'Tap the microphone to start recording';

  @override
  String get suggestedPlatform => 'Suggested platform based on your speech:';

  @override
  String get confirm => 'Confirm';

  @override
  String get back => 'Back';

  @override
  String taskSent(String platform) {
    return 'Task sent to $platform';
  }

  @override
  String get iRecognized => 'I recognized:';

  @override
  String get targetPlatform => 'Target:';

  @override
  String get editBeforeSending => 'Edit before sending';

  @override
  String get selectCommandType => 'What would you like to create?';

  @override
  String get createTaskCommand => 'Create Task';

  @override
  String get addNoteCommand => 'Add Note';

  @override
  String get saveIdeaCommand => 'Save Idea';

  @override
  String get enterProjectName => 'Enter project name';

  @override
  String get selectProjectPrompt => 'Select Project';

  @override
  String get entries => 'الإدخالات';

  @override
  String get entryDetails => 'تفاصيل الإدخال';

  @override
  String get addUpdate => 'إضافة تحديث';

  @override
  String get updateDetails => 'تفاصيل التحديث';

  @override
  String get deleteUpdate => 'حذف التحديث';

  @override
  String get deleteUpdateConfirmation =>
      'هل أنت متأكد أنك تريد حذف هذا التحديث؟';

  @override
  String get deleteEntry => 'حذف الإدخال';

  @override
  String get deleteEntryConfirmation =>
      'هل أنت متأكد أنك تريد حذف هذا الإدخال وجميع تحديثاته؟';

  @override
  String get entryCreated => 'تم إنشاء الإدخال بنجاح';

  @override
  String get entryDeleted => 'تم حذف الإدخال بنجاح';

  @override
  String get entryUpdateAdded => 'تمت إضافة التحديث بنجاح';

  @override
  String get noEntriesFound => 'لم يتم العثور على إدخالات';

  @override
  String get createEntryPrompt => 'قم بإنشاء إدخال جديد بالنقر على زر +';

  @override
  String get createNewEntry => 'Create New Entry';

  @override
  String get title => 'Title';

  @override
  String get titleHint => 'Enter title here';

  @override
  String get description => 'Description';

  @override
  String get descriptionHint => 'Enter description here';

  @override
  String get time => 'Time';

  @override
  String get date => 'Date';

  @override
  String dateFormat(int month, int day, int year) {
    return '$month/$day/$year';
  }

  @override
  String get type => 'النوع';

  @override
  String get priority => 'Priority';

  @override
  String get priorityLow => 'Low';

  @override
  String get priorityMedium => 'Medium';

  @override
  String get priorityHigh => 'High';

  @override
  String get priorityUrgent => 'Urgent';

  @override
  String get destination => 'Destination';

  @override
  String get clickupPlatform => 'ClickUp';

  @override
  String get notionPlatform => 'Notion';

  @override
  String get asanaPlatform => 'Asana';

  @override
  String get mondayPlatform => 'Monday';

  @override
  String get jiraPlatform => 'Jira';

  @override
  String get trelloPlatform => 'Trello';

  @override
  String get create => 'إنشاء';

  @override
  String get pleaseEnterTitle => 'Please enter a title';

  @override
  String get all => 'الكل';

  @override
  String get searchHint => 'Search';

  @override
  String get chats => 'Chats';

  @override
  String get newChat => 'New Chat';

  @override
  String get searchResults => 'Search Results';

  @override
  String get noResultsFound => 'No results found';

  @override
  String get projects => 'Projects';

  @override
  String get newFolder => 'New Folder';

  @override
  String get createNewFolder => 'Create New Folder';

  @override
  String get enterFolderName => 'Enter folder name';

  @override
  String get rename => 'Rename';

  @override
  String get renameChat => 'Rename Chat';

  @override
  String get renameFolder => 'Rename Folder';

  @override
  String get enterNewName => 'Enter new name';

  @override
  String get moveToFolder => 'Move to folder';

  @override
  String get deleteChat => 'Delete Chat';

  @override
  String get deleteFolder => 'Delete Folder';

  @override
  String get deleteChatConfirmation =>
      'Are you sure you want to delete this chat? This action cannot be undone.';

  @override
  String get deleteFolderConfirmation =>
      'Are you sure you want to delete this folder? This action cannot be undone.';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String daysAgo(int count) {
    return '$count days ago';
  }

  @override
  String get chatHistory => 'سجل المحادثة';

  @override
  String get addNote => 'Add Note';

  @override
  String get pinMessage => 'Pin Message';

  @override
  String get bookmark => 'Bookmark';

  @override
  String get messageOptions => 'Message Options';

  @override
  String get messageDeleted => 'Message deleted';

  @override
  String get messagePinned => 'Message pinned';

  @override
  String get messageBookmarked => 'Message bookmarked';

  @override
  String get noteAdded => 'Note added';

  @override
  String get enterNote => 'Enter your note';

  @override
  String get addNoteToMessage => 'Add Note to Message';

  @override
  String get editMessage => 'Edit Message';

  @override
  String get enterMessage => 'Enter your message';

  @override
  String get messageMoved => 'Message moved to folder';

  @override
  String get selectFolder => 'Select Folder';

  @override
  String get folderCreated => 'Folder created';

  @override
  String get noFolders => 'No folders yet';

  @override
  String get createFolderPrompt => 'Create a folder to organize your chats';

  @override
  String get noChatsInFolder => 'No chats in this folder';

  @override
  String get addChatsToFolder => 'Add chats by dragging them here';

  @override
  String get noChatsYet => 'No conversations yet';

  @override
  String get startNewChatPrompt => 'Start a new chat to begin';

  @override
  String get noRecentItems => 'No recent items';

  @override
  String get recentItems => 'Recent Items';

  @override
  String get todaysTasks => 'Today\'s Tasks';

  @override
  String get tapToViewTasks => 'Tap to view your tasks';

  @override
  String get appDescription => 'Your smart voice-to-task assistant';

  @override
  String get offlineMode => 'Offline Mode';

  @override
  String get offlineModeDescription =>
      'You\'re currently offline. Some features may be limited.';

  @override
  String get syncPending => 'Sync Pending';

  @override
  String get syncComplete => 'Sync Complete';

  @override
  String get syncFailed => 'Sync Failed';

  @override
  String get retrySync => 'Retry Sync';

  @override
  String get continueInOfflineMode => 'Continue in Offline Mode';

  @override
  String get networkError => 'Network Error';

  @override
  String get checkConnection =>
      'Please check your internet connection and try again.';

  @override
  String get pause => 'Pause';

  @override
  String get resume => 'Resume';

  @override
  String get reset => 'Reset';

  @override
  String get transcriptionComplete => 'Transcription Complete';

  @override
  String get viewDetails => 'View Details';

  @override
  String get continueButton => 'متابعة';

  @override
  String get collection => 'Collection';

  @override
  String get newest => 'Newest';

  @override
  String get oldest => 'Oldest';

  @override
  String get name => 'Name';

  @override
  String get category => 'Category';

  @override
  String get synced => 'Synced';

  @override
  String get unsynced => 'Unsynced';

  @override
  String get notSynced => 'Not Synced';

  @override
  String get search => 'بحث';

  @override
  String get closeSearch => 'Close Search';

  @override
  String get listView => 'List View';

  @override
  String get gridView => 'Grid View';

  @override
  String get createFirstEntry => 'Create First Entry';

  @override
  String get noEntriesYet => 'No entries yet';

  @override
  String get createNewEntryDescription =>
      'Create your first task, note, or idea using the button below.';

  @override
  String get completed => 'مكتمل';

  @override
  String get noSearchResults => 'No search results found';

  @override
  String get clearSearch => 'Clear Search';

  @override
  String get unassignedEntriesWarning => 'Unassigned Entries';

  @override
  String unassignedEntriesDescription(int count) {
    return 'You have $count unassigned entries waiting to be assigned to a platform';
  }

  @override
  String get assignNow => 'Assign Now';

  @override
  String assignedEntries(int count) {
    return 'Assigned Entries ($count)';
  }

  @override
  String get searchEntries => 'Search entries...';

  @override
  String get archive => 'Archive';

  @override
  String get assign => 'Assign';

  @override
  String get entryArchived => 'Entry archived';

  @override
  String get undo => 'UNDO';

  @override
  String get restoreFunctionalityNotImplemented =>
      'Restore functionality not yet implemented';

  @override
  String assignedToPlatform(String platform) {
    return 'Assigned to $platform';
  }

  @override
  String get recordingFileNotFound =>
      'Recording file not found. It may have been deleted or moved.';

  @override
  String get errorPlayingRecording => 'Error playing recording';

  @override
  String get assignmentSuccessful => 'Assignment Successful';

  @override
  String assignedCategoryToPlatform(String category, String platform) {
    return '$category assigned to $platform';
  }

  @override
  String get myTasks => 'My Tasks';

  @override
  String get task => 'مهمة';

  @override
  String get myIdeas => 'My Ideas';

  @override
  String get idea => 'فكرة';

  @override
  String get myNotes => 'My Notes';

  @override
  String get note => 'ملاحظة';

  @override
  String get noTasksYet => 'No tasks yet';

  @override
  String get noIdeasYet => 'No ideas yet';

  @override
  String get noNotesYet => 'No notes yet';

  @override
  String get addNew => 'Add New';

  @override
  String get optimizedFor => 'Optimized for';

  @override
  String get optimizedContent => 'Optimized Content';

  @override
  String get contentOptimization => 'Content Optimization';

  @override
  String get platformAssignment => 'Platform Assignment';

  @override
  String get audioPlaybackError => 'Audio playback error';

  @override
  String get audioFileCorrupted =>
      'Unable to play the audio file. It may be corrupted.';

  @override
  String get loadingAudioPlayer => 'Loading audio player...';

  @override
  String get optimizingFor => 'Optimizing for';

  @override
  String get choosePlatformToAssign => 'Choose Platform to Assign';

  @override
  String get assigningTo => 'Assigning to';

  @override
  String get autoAssignmentEnabled =>
      'Auto-assignment is enabled. This item will be automatically processed.';

  @override
  String get deleteRecordingConfirmation =>
      'Are you sure you want to delete this recording? This action cannot be undone.';

  @override
  String get deletingRecording => 'Deleting recording...';

  @override
  String get save => 'حفظ';

  @override
  String get created => 'تم الإنشاء';

  @override
  String get content => 'المحتوى';

  @override
  String get createEntry => 'إنشاء إدخال';

  @override
  String get taskReminder => 'Task Reminder';

  @override
  String get selectTime => 'Select time';

  @override
  String get changeTime => 'Change Time';

  @override
  String get noReminder => 'No Reminder';

  @override
  String get editEntry => 'تعديل الإدخال';

  @override
  String get originalContent => 'Original Content';

  @override
  String get textEntry => 'Text Entry';

  @override
  String get voiceEntry => 'Voice Entry';

  @override
  String get optimizeAs => 'Optimize as';

  @override
  String get optimizeAsTaskDescription =>
      'Convert content into an actionable task with clear steps and deadlines';

  @override
  String get optimizeAsIdeaDescription =>
      'Structure content as a creative idea with potential applications and benefits';

  @override
  String get optimizeAsNoteDescription =>
      'Format content as a well-structured note with key points and references';

  @override
  String get optimizationInProgress =>
      'Using AI to enhance and structure your content';

  @override
  String get autoAssignment => 'Auto Assignment';

  @override
  String get autoAssignmentInfo =>
      'Content will be automatically analyzed and assigned to the most appropriate connected platform';

  @override
  String get configureAutoAssignment => 'Configure Auto Assignment';

  @override
  String get navigateToSettings => 'Opening Settings...';

  @override
  String get manageConnections => 'Manage Connections';

  @override
  String get platformConnectionsManagement =>
      'Opening platform connections management...';

  @override
  String get successfullyAssignedTo => 'Successfully assigned to';

  @override
  String get recording => 'Recording';

  @override
  String get recordingPaused => 'Paused';

  @override
  String get resetRecording => 'Reset Recording?';

  @override
  String get resetRecordingConfirmation =>
      'Are you sure you want to reset this recording? This action cannot be undone.';

  @override
  String get recordingGuideTitle => 'How it works';

  @override
  String get recordingGuideStep1 => 'Record your voice note';

  @override
  String get recordingGuideStep2 =>
      'ProjectPilot magically transcribes and enhances it';

  @override
  String get recordingGuideStep3 => 'Send it to your favorite platform';

  @override
  String get noInternetConnection => 'No internet connection';

  @override
  String get processingPurchase => 'Processing your purchase...';

  @override
  String get choosePackage => 'Choose a Package';

  @override
  String get packageSelectionSubtitle =>
      'Get more tokens to use for AI text generation and audio transcription';

  @override
  String purchaseSuccessful(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Successfully purchased $countString tokens!';
  }

  @override
  String get sendToThisPlatform => 'Send to this platform';

  @override
  String get choosePlatformTitle => 'Choose Platform';

  @override
  String get choosePlatformDescription =>
      'Choose a platform to dispatch your content to';

  @override
  String get contentPreview => 'Content Preview';

  @override
  String get suggested => 'Suggested';

  @override
  String get connectPlatform => 'Connect Platform';

  @override
  String get authenticating => 'جارِ المصادقة...';

  @override
  String get createAccount => 'إنشاء حساب';

  @override
  String get emailFieldHint => 'Enter your email address';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get passwordFieldHint => 'Enter your password';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get confirmPasswordHint => 'أكد كلمة المرور الخاصة بك';

  @override
  String get confirmPasswordRequired => 'يرجى تأكيد كلمة المرور الخاصة بك';

  @override
  String get passwordsDoNotMatch => 'كلمات المرور غير متطابقة';

  @override
  String get nameFieldHint => 'أدخل اسمك الكامل';

  @override
  String get history => 'History';

  @override
  String get record => 'Record';

  @override
  String get newEntry => 'New Entry';

  @override
  String get pleaseEnterContent => 'Please enter content';

  @override
  String get update => 'Update';

  @override
  String get noEntries => 'No entries';

  @override
  String get deleteAllAudioFiles => 'Delete All Audio Files';

  @override
  String get deleteAllAudioFilesContent =>
      'This will delete all voice recording files to free up storage space. The text content of your recordings will be preserved.\n\nThis action cannot be undone. Are you sure?';

  @override
  String get deleteAll => 'Delete All';

  @override
  String get deleteAllAudioFilesTooltip => 'Delete all audio files';

  @override
  String get selectLanguageDialogTitle => 'Select Language';

  @override
  String get enabled => 'Enabled';

  @override
  String get disabled => 'Disabled';

  @override
  String get autoAssignEnabledDesc =>
      'Tasks are automatically assigned to platforms';

  @override
  String get autoAssignDisabledDesc => 'Manual platform selection for tasks';

  @override
  String packageDescription(String name, int tokenAmount) {
    return '$name package with $tokenAmount tokens';
  }

  @override
  String aboutAppTitle(String appName) {
    return 'About $appName';
  }

  @override
  String appVersion(String version) {
    return 'Version $version';
  }

  @override
  String get keyFeatures => 'Key Features';

  @override
  String get voiceToTask => 'Voice-to-Task';

  @override
  String get voiceToTaskDesc => 'Create tasks using your voice';

  @override
  String get multiPlatform => 'Multi-Platform';

  @override
  String get multiPlatformDesc => 'Connect to ClickUp, Notion, and more';

  @override
  String get multiLanguage => 'Multi-Language';

  @override
  String get multiLanguageDesc =>
      'Supports English, German, Russian, Turkish, and Arabic';

  @override
  String get aiPowered => 'AI-Powered';

  @override
  String get aiPoweredDesc => 'Smart task categorization and optimization';

  @override
  String get ourTeam => 'Our Team';

  @override
  String copyright(String year) {
    return '© $year ProjectPilot. All rights reserved.';
  }

  @override
  String get contactSupportDesc =>
      'Have a question or need help? Fill out the form below and our support team will get back to you as soon as possible.';

  @override
  String get nameField => 'Name';

  @override
  String get emailField => 'Email';

  @override
  String get messageField => 'Message';

  @override
  String get messageFieldHint => 'Enter your message';

  @override
  String get submitButton => 'Submit';

  @override
  String get nameValidationError => 'Please enter your name';

  @override
  String get emailValidationError => 'Please enter a valid email';

  @override
  String get messageValidationError => 'Please enter your message';

  @override
  String get initializationError => 'Initialization Error';

  @override
  String errorDuringInit(String error) {
    return 'An error occurred during app initialization:\n$error';
  }

  @override
  String get retryButton => 'Retry';

  @override
  String get faqTitle => 'FAQ';

  @override
  String get frequentlyAskedQuestions => 'Frequently Asked Questions';

  @override
  String get privacyPolicyTitle => 'Privacy Policy';

  @override
  String lastUpdated(String time) {
    return 'Last updated: $time';
  }

  @override
  String get privacyPolicyIntro =>
      'This Privacy Policy describes how ProjectPilot (\"we\", \"us\", or \"our\") collects, uses, and discloses your personal information when you use our mobile application (the \"App\").';

  @override
  String get infoWeCollect => 'Information We Collect';

  @override
  String get infoWeCollectContent =>
      'We collect information that you provide directly to us, such as when you create an account, update your profile, use the interactive features of our App, request customer support, or otherwise communicate with us.';

  @override
  String get howWeUseInfo => 'How We Use Your Information';

  @override
  String get howWeUseInfoContent =>
      'We use the information we collect to provide, maintain, and improve our services, including to process transactions, send you related information, and provide customer support.';

  @override
  String get sharingOfInfo => 'Sharing of Information';

  @override
  String get sharingOfInfoContent =>
      'We may share the information we collect as follows: with third-party vendors, consultants, and other service providers who need access to such information to carry out work on our behalf; in response to a request for information if we believe disclosure is in accordance with any applicable law, regulation, or legal process.';

  @override
  String get yourChoices => 'Your Choices';

  @override
  String get yourChoicesContent =>
      'You may update, correct, or delete your account information at any time by logging into your account or contacting us. You may opt out of receiving promotional communications from us by following the instructions in those communications.';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get contactUsPrivacyContent =>
      'If you have any questions about this Privacy Policy, please contact us at: <EMAIL>';

  @override
  String get termsOfServiceTitle => 'Terms of Service';

  @override
  String get termsOfServiceIntro =>
      'Please read these Terms of Service (\"Terms\", \"Terms of Service\") carefully before using the ProjectPilot mobile application (the \"Service\") operated by ProjectPilot (\"us\", \"we\", or \"our\").';

  @override
  String get acceptanceOfTerms => 'Acceptance of Terms';

  @override
  String get acceptanceOfTermsContent =>
      'By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.';

  @override
  String get useOfService => 'Use of the Service';

  @override
  String get useOfServiceContent =>
      'Our Service allows you to create, manage, and organize tasks and notes using voice commands. You are responsible for maintaining the confidentiality of your account and password and for restricting access to your computer or mobile device.';

  @override
  String get intellectualProperty => 'Intellectual Property';

  @override
  String get intellectualPropertyContent =>
      'The Service and its original content, features, and functionality are and will remain the exclusive property of ProjectPilot and its licensors. The Service is protected by copyright, trademark, and other laws.';

  @override
  String get termination => 'Termination';

  @override
  String get terminationContent =>
      'We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.';

  @override
  String get limitationOfLiability => 'Limitation of Liability';

  @override
  String get limitationOfLiabilityContent =>
      'In no event shall ProjectPilot, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.';

  @override
  String get changesToTerms => 'Changes to Terms';

  @override
  String get changesToTermsContent =>
      'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days\' notice prior to any new terms taking effect.';

  @override
  String get contactUsTermsContent =>
      'If you have any questions about these Terms, please contact us at: <EMAIL>';

  @override
  String get whatIsProjectPilot => 'What is ProjectPilot?';

  @override
  String get whatIsProjectPilotAnswer =>
      'ProjectPilot is a voice-to-task application that allows you to create tasks, notes, and ideas using voice commands. It can transcribe your voice and send the tasks to various platforms like ClickUp, Notion, and more.';

  @override
  String get howToConnectPlatforms => 'How do I connect to external platforms?';

  @override
  String get howToConnectPlatformsAnswer =>
      'Go to the Settings screen and navigate to the \"Platform Connections\" section. There you can connect to various platforms like ClickUp, Notion, and Monday by clicking the Connect button and following the authentication process.';

  @override
  String get whatLanguagesSupported => 'What languages are supported?';

  @override
  String get whatLanguagesSupportedAnswer =>
      'ProjectPilot currently supports English, German, Russian, Turkish, and Arabic. You can change the language in the Settings screen.';

  @override
  String get howTokensWork => 'How do tokens work?';

  @override
  String get howTokensWorkAnswer =>
      'Tokens are used for AI text generation and audio transcription. Each transcription and optimization consumes a certain number of tokens. You can purchase more tokens in the \"Upgrade Package\" section of the Settings screen.';

  @override
  String get canUseOffline => 'Can I use ProjectPilot offline?';

  @override
  String get canUseOfflineAnswer =>
      'Some features of ProjectPilot require an internet connection, such as sending tasks to external platforms and using online transcription services. However, basic voice recording can work offline.';

  @override
  String get howCustomizeProfile => 'How do I customize my profile?';

  @override
  String get howCustomizeProfileAnswer =>
      'You can update your profile information in the Settings screen under the \"Profile\" section.';

  @override
  String get isDataSecure => 'Is my data secure?';

  @override
  String get isDataSecureAnswer =>
      'Yes, we take data security seriously. Your data is encrypted and stored securely. We do not share your personal information with third parties without your consent. Please refer to our Privacy Policy for more details.';

  @override
  String get howCancelSubscription => 'How do I cancel my subscription?';

  @override
  String get howCancelSubscriptionAnswer =>
      'You can manage your subscription through your app store account (Google Play Store or Apple App Store). Go to the subscription management section of your app store and cancel the ProjectPilot subscription.';

  @override
  String get appTitleProjectPilot => 'ProjectPilot';

  @override
  String get projectDashboard => 'Project Dashboard';

  @override
  String get projectAnalysis => 'Project Analysis';

  @override
  String get askAboutProject => 'Ask About Project';

  @override
  String get projectStatus => 'Project Status';

  @override
  String get blockers => 'العوائق';

  @override
  String get recommendations => 'Recommendations';

  @override
  String get criticalPath => 'Critical Path';

  @override
  String get projectProgress => 'Project Progress';

  @override
  String get teamMembers => 'Team Members';

  @override
  String get budget => 'Budget';

  @override
  String get timeline => 'Timeline';

  @override
  String get risks => 'Risks';

  @override
  String get insights => 'Insights';

  @override
  String get analysisLoading => 'Analyzing project...';

  @override
  String get analysisFailed => 'Analysis failed. Please try again.';

  @override
  String get refreshProject => 'Refresh Project';

  @override
  String get projectOverdue => 'Project Overdue';

  @override
  String get projectOnTrack => 'Project On Track';

  @override
  String get projectAtRisk => 'Project At Risk';

  @override
  String get highPriorityBlockers => 'High Priority Blockers';

  @override
  String get urgentRecommendations => 'Urgent Recommendations';

  @override
  String daysRemaining(int count) {
    return '$count days remaining';
  }

  @override
  String tasksCompleted(int completed, int total) {
    return '$completed of $total tasks completed';
  }

  @override
  String budgetUtilization(double spent, double total) {
    final intl.NumberFormat spentNumberFormat = intl.NumberFormat.currency(
      locale: localeName,
    );
    final String spentString = spentNumberFormat.format(spent);
    final intl.NumberFormat totalNumberFormat = intl.NumberFormat.currency(
      locale: localeName,
    );
    final String totalString = totalNumberFormat.format(total);

    return 'Budget: $spentString of $totalString used';
  }

  @override
  String get queryProject => 'Ask me anything about this project...';

  @override
  String get processingQuery => 'Processing your question...';

  @override
  String get queryFailed =>
      'Could not process your question. Please try again.';

  @override
  String get projectNotFound => 'Project not found';

  @override
  String get syncingData => 'Syncing project data...';

  @override
  String get activeTasks => 'Active Tasks';

  @override
  String get completedTasks => 'Completed Tasks';

  @override
  String get blockedTasks => 'Blocked Tasks';

  @override
  String get overdueTasks => 'Overdue Tasks';

  @override
  String confidenceLevel(int level) {
    return 'Confidence: $level%';
  }

  @override
  String riskScore(double score) {
    final intl.NumberFormat scoreNumberFormat = intl
        .NumberFormat.decimalPattern(localeName);
    final String scoreString = scoreNumberFormat.format(score);

    return 'Risk Score: $scoreString/10';
  }

  @override
  String get riskAssessment => 'تقييم المخاطر';

  @override
  String get budgetOverview => 'نظرة عامة على الميزانية';

  @override
  String get keyInsights => 'الرؤى الرئيسية';

  @override
  String get errorLoadingAnalysis => 'خطأ في تحميل التحليل';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get tapAnalyzeToGetInsights =>
      'اضغط على التحليل للحصول على رؤى الذكاء الاصطناعي';

  @override
  String get analyzeProject => 'تحليل المشروع';

  @override
  String get aiAnalysisSummary => 'ملخص تحليل الذكاء الاصطناعي';

  @override
  String get active => 'نشط';

  @override
  String get inactive => 'غير نشط';

  @override
  String get overBudget => 'تجاوز الميزانية';

  @override
  String get totalBudget => 'إجمالي الميزانية';

  @override
  String get spent => 'مُنفق';

  @override
  String get remaining => 'متبقي';

  @override
  String get analysisDetails => 'تفاصيل التحليل';

  @override
  String get confidenceLevelLabel => 'مستوى الثقة';

  @override
  String get analysisDate => 'تاريخ التحليل';

  @override
  String get predictedCompletion => 'الإنجاز المتوقع';

  @override
  String get critical => 'حرج';

  @override
  String get high => 'عالي';

  @override
  String get medium => 'متوسط';

  @override
  String get low => 'منخفض';

  @override
  String get justNow => 'الآن';

  @override
  String get criticalPaths => 'المسارات الحرجة';

  @override
  String get error => 'خطأ';

  @override
  String get noProjectsYet => 'لا توجد مشاريع بعد';

  @override
  String get connectProjectManagementPlatforms =>
      'اربط منصات إدارة المشاريع الخاصة بك للبدء';

  @override
  String get projectPilotDashboard => 'لوحة تحكم ProjectPilot';

  @override
  String get totalProjects => 'إجمالي المشاريع';

  @override
  String get recentProjects => 'المشاريع الحديثة';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get tasksOverview => 'نظرة عامة على المهام';

  @override
  String get recentTasks => 'المهام الحديثة';

  @override
  String get noTasksFound => 'لم يتم العثور على مهام';

  @override
  String get inProgress => 'قيد التنفيذ';

  @override
  String get blocked => 'محجوب';

  @override
  String get overdue => 'متأخر';

  @override
  String get toDo => 'للقيام';

  @override
  String get review => 'مراجعة';

  @override
  String get done => 'تم';

  @override
  String get cancelled => 'ملغي';

  @override
  String get progress => 'التقدم';

  @override
  String get createdAt => 'تم الإنشاء في';

  @override
  String get deadline => 'الموعد النهائي';

  @override
  String get platform => 'المنصة';

  @override
  String get planning => 'التخطيط';

  @override
  String get onHold => 'معلق';

  @override
  String get aiProjectAssistant => 'مساعد المشروع بالذكاء الاصطناعي';

  @override
  String get whatProjectsAreBehindSchedule =>
      'ما هي المشاريع المتأخرة عن الجدول الزمني؟';

  @override
  String get showMeProjectRisks => 'أظهر لي مخاطر المشاريع';

  @override
  String get whichTasksAreBlocked => 'ما هي المهام المحجوبة؟';

  @override
  String get projectProgressSummary => 'ملخص تقدم المشروع';

  @override
  String get teamWorkloadAnalysis => 'تحليل عبء عمل الفريق';

  @override
  String get dismiss => 'تجاهل';

  @override
  String examplesForRole(String role) {
    return 'أمثلة لـ $role';
  }

  @override
  String get tapToUse => 'اضغط للاستخدام';

  @override
  String get roleDeveloper => 'مطور';

  @override
  String get roleProjectManager => 'مدير المشروع';

  @override
  String get roleProductOwner => 'مالك المنتج';

  @override
  String get roleQaTester => 'مختبر الجودة';

  @override
  String get roleCtoCeo => 'المدير التقني/الرئيس التنفيذي';

  @override
  String get roleAllRoles => 'جميع الأدوار';

  @override
  String get developerPrompt1 => 'ما هي المهام المخصصة لي حالياً؟';

  @override
  String get developerPrompt2 => 'ما الذي يمنع مراجعة الكود الخاص بي؟';

  @override
  String get developerPrompt3 => 'ما المقرر في السبرينت القادم؟';

  @override
  String get developerPrompt4 => 'أي الأخطاء لديها أعلى أولوية؟';

  @override
  String get projectManagerPrompt1 => 'ما الذي يؤخر السبرينت الحالي؟';

  @override
  String get projectManagerPrompt2 => 'أي المهام متأخرة؟';

  @override
  String get projectManagerPrompt3 => 'من عمل على المشروع مؤخراً؟';

  @override
  String get projectManagerPrompt4 => 'كيف هو تقدم المشروع الحالي؟';

  @override
  String get productOwnerPrompt1 => 'ما الميزات المخططة للإصدار القادم؟';

  @override
  String get productOwnerPrompt2 => 'ما التعليقات على آخر تحديث لنا؟';

  @override
  String get productOwnerPrompt3 => 'أي قصص المستخدمين لديها أعلى أولوية؟';

  @override
  String get productOwnerPrompt4 => 'كيف يتطور متراكم المنتج الخاص بنا؟';

  @override
  String get qaTesterPrompt1 => 'ما الاختبارات التي تحتاج إلى إجراء؟';

  @override
  String get qaTesterPrompt2 => 'هل هناك أخطاء حرجة مفتوحة؟';

  @override
  String get qaTesterPrompt3 => 'ما هو تغطية الاختبار الحالية؟';

  @override
  String get qaTesterPrompt4 => 'أي الميزات جاهزة للاختبار؟';

  @override
  String get ctoCeoPrompt1 => 'كيف يسير المشروع بشكل عام؟';

  @override
  String get ctoCeoPrompt2 => 'ما الذي يكلفنا معظم الوقت حالياً؟';

  @override
  String get ctoCeoPrompt3 => 'أي الفرق تحتاج إلى الدعم؟';

  @override
  String get ctoCeoPrompt4 => 'كيف نقوم بالميزانية والجدول الزمني؟';

  @override
  String get defaultPrompt1 => 'ما المهام في جدول اليوم؟';

  @override
  String get defaultPrompt2 => 'ما هو حالة المشروع الحالية؟';

  @override
  String get defaultPrompt3 => 'ما هي الخطوات المهمة التالية؟';

  @override
  String get defaultPrompt4 => 'هل هناك عوائق تحتاج إلى حل؟';

  @override
  String get analyzingProject => 'Analyzing project...';

  @override
  String get suggestedActions => 'Suggested Actions';
}
