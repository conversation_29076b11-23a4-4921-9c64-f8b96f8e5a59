{"@@locale": "en", "appTitle": "ProjectPilot", "@appTitle": {"description": "The title of the application"}, "homeTitle": "Home", "@homeTitle": {"description": "Title for the home screen"}, "settingsTitle": "Settings", "@settingsTitle": {"description": "Title for the settings screen"}, "welcome": "Welcome", "@welcome": {"description": "Welcome text on splash screen"}, "welcomeSubtitle": "ProjectPilot – the future of project management", "@welcomeSubtitle": {"description": "Subtitle on splash screen"}, "languageTitle": "Language", "@languageTitle": {"description": "Title for the language selection"}, "englishLanguage": "English", "@englishLanguage": {"description": "Name of English language"}, "germanLanguage": "German", "@germanLanguage": {"description": "Name of German language"}, "russianLanguage": "Russian", "@russianLanguage": {"description": "Name of Russian language"}, "turkishLanguage": "Turkish", "@turkishLanguage": {"description": "Name of Turkish language"}, "arabicLanguage": "Arabic", "@arabicLanguage": {"description": "Name of Arabic language"}, "saveButton": "Save", "@saveButton": {"description": "Text for save button"}, "cancelButton": "Cancel", "@cancelButton": {"description": "Text for cancel button"}, "errorOccurred": "An error occurred", "@errorOccurred": {"description": "Generic error message"}, "tryAgain": "Try Again", "@tryAgain": {"description": "<PERSON><PERSON> text to try again"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Title for language selection dialog"}, "tokenBalance": "Token Balance: {count}", "@tokenBalance": {"description": "Token balance display", "placeholders": {"count": {"type": "int", "format": "compact"}}}, "tokenBalanceDescription": "Tokens for AI text generation with GPT", "@tokenBalanceDescription": {"description": "Description of token usage for AI text generation"}, "transcriptionTitle": "Transcription", "@transcriptionTitle": {"description": "Title for transcription feature"}, "startRecording": "Start Recording", "@startRecording": {"description": "<PERSON><PERSON> text to start recording"}, "stopRecording": "Stop Recording", "@stopRecording": {"description": "But<PERSON> text to stop recording"}, "noTranscriptionYet": "No transcription yet", "@noTranscriptionYet": {"description": "Message when no transcription is available"}, "shareTranscription": "Share Transcription", "@shareTranscription": {"description": "Button text to share transcription"}, "themeTitle": "Theme", "@themeTitle": {"description": "Title for the theme selection"}, "selectTheme": "Select Theme", "@selectTheme": {"description": "Title for theme selection dialog"}, "systemTheme": "System Default", "@systemTheme": {"description": "Name of system default theme"}, "lightTheme": "Light", "@lightTheme": {"description": "Name of light theme"}, "darkTheme": "Dark", "@darkTheme": {"description": "Name of dark theme"}, "toggleTheme": "Toggle Theme", "@toggleTheme": {"description": "Button text to toggle between light and dark theme"}, "apiKeysSection": "API Keys", "@apiKeysSection": {"description": "Section title for API keys in settings"}, "openaiApiKey": "OpenAI API Key", "@openaiApiKey": {"description": "Label for OpenAI API key input"}, "whisperApiKey": "Whisper API Key", "@whisperApiKey": {"description": "Label for Whisper API key input"}, "apiKeyHint": "Enter your API key here", "@apiKeyHint": {"description": "Hint text for API key input fields"}, "tokenSection": "Tokens & Usage", "@tokenSection": {"description": "Section title for tokens and usage in settings"}, "remainingMinutes": "Remaining Minutes: {count}", "@remainingMinutes": {"description": "Remaining recording minutes display", "placeholders": {"count": {"type": "int", "format": "compact"}}}, "minutesBalanceDescription": "Minutes available for audio transcription with <PERSON><PERSON><PERSON>", "@minutesBalanceDescription": {"description": "Description of remaining recording minutes"}, "purchasePackages": "Purchase Packages", "@purchasePackages": {"description": "Title for purchase packages section"}, "buyTokens": "Buy Tokens", "@buyTokens": {"description": "Button text to buy tokens"}, "starterPackage": "Starter", "@starterPackage": {"description": "Name of starter package"}, "proPackage": "Pro", "@proPackage": {"description": "Name of pro package"}, "businessPackage": "Business", "@businessPackage": {"description": "Name of business package"}, "ultimatePackage": "Ultimate", "@ultimatePackage": {"description": "Name of ultimate package"}, "tokens": "{count} Tokens", "@tokens": {"description": "Number of tokens in a package", "placeholders": {"count": {"type": "int", "format": "compact"}}}, "price": "€{price}", "@price": {"description": "Price display", "placeholders": {"price": {"type": "double", "format": "simpleCurrency", "optionalParameters": {"name": "EUR"}}}}, "profileSection": "Profile", "@profileSection": {"description": "Section title for user profile in settings"}, "username": "Username", "@username": {"description": "Label for username"}, "email": "Email", "@email": {"description": "Label for email"}, "notAvailable": "Not available", "@notAvailable": {"description": "Text displayed when a value is not available"}, "logoutButton": "Logout", "@logoutButton": {"description": "Text for logout button"}, "apiKeySaved": "API key saved successfully!", "@apiKeySaved": {"description": "Confirmation message when API key is saved"}, "introScreen1Title": "AI-Powered Project Intelligence", "@introScreen1Title": {"description": "Title for first intro screen"}, "introScreen1Description": "Keep the red thread in your projects. Identify problems early and stay on track with intelligent insights.", "@introScreen1Description": {"description": "Description for first intro screen"}, "introScreen2Title": "Multi-Platform Project Overview", "@introScreen2Title": {"description": "Title for second intro screen"}, "introScreen2Description": "Connect all your project tools in one dashboard. ClickUp, Notion, Jira, and more - all synchronized.", "@introScreen2Description": {"description": "Description for second intro screen"}, "introScreen3Title": "Smart Problem Detection", "@introScreen3Title": {"description": "Title for third intro screen"}, "introScreen3Description": "Automatically detect blockers, assess risks, and get actionable recommendations to keep your projects moving.", "@introScreen3Description": {"description": "Description for third intro screen"}, "introMainTitle": "ProjectPilot", "@introMainTitle": {"description": "Main title for intro screens"}, "introMainSubtitle": "Never lose track of your projects again", "@introMainSubtitle": {"description": "Main subtitle for intro screens"}, "getStarted": "Get Started", "@getStarted": {"description": "Text for get started button"}, "skip": "<PERSON><PERSON>", "@skip": {"description": "Text for skip button on intro screens"}, "next": "Next", "@next": {"description": "Text for next button on intro screens"}, "loginTitle": "Welcome to ProjectPilot", "@loginTitle": {"description": "Title for the login screen"}, "loginSubtitle": "Sign in to continue", "@loginSubtitle": {"description": "Subtitle for the login screen"}, "continueWithGoogle": "Continue with Google", "@continueWithGoogle": {"description": "Button text for Google login"}, "continueWithApple": "Continue with Apple", "@continueWithApple": {"description": "Button text for Apple login"}, "continueWithEmail": "Continue with <PERSON>ail", "@continueWithEmail": {"description": "But<PERSON> text for <PERSON><PERSON> login"}, "moreOptions": "More Options", "@moreOptions": {"description": "Button text to show more login options"}, "continueWithLinkedIn": "Continue with LinkedIn", "@continueWithLinkedIn": {"description": "Button text for LinkedIn login"}, "continueWithPhone": "Continue with Phone", "@continueWithPhone": {"description": "Button text for Phone login"}, "continueWithAzure": "Continue with Azure", "@continueWithAzure": {"description": "Button text for Azure login"}, "continueWithNotion": "Continue with Notion", "@continueWithNotion": {"description": "Button text for Notion login"}, "password": "Password", "@password": {"description": "Label for password"}, "forgotPassword": "Forgot password?", "@forgotPassword": {"description": "Text for forgot password button"}, "signIn": "Sign In", "@signIn": {"description": "Button text for sign in"}, "signUp": "Sign Up", "@signUp": {"description": "Button text for sign up"}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Text for users without an account"}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {"description": "Text for users with an account"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Label for phone number"}, "or": "or", "@or": {"description": "Text for 'or' separator"}, "createAccountText": "Create your ProjectPilot account", "@createAccountText": {"description": "Subtitle text for sign-up screen"}, "usernameRequired": "Username is required", "@usernameRequired": {"description": "Error message when username is empty"}, "usernameMinLength": "Username must be at least 3 characters", "@usernameMinLength": {"description": "Error message when username is too short"}, "emailRequired": "Please enter your email", "@emailRequired": {"description": "Validation message when email is empty"}, "invalidEmail": "Enter a valid email", "@invalidEmail": {"description": "Error message when email format is invalid"}, "passwordRequired": "Password is required", "@passwordRequired": {"description": "Error message when password is empty"}, "passwordMinLength": "Password must be at least 6 characters", "@passwordMinLength": {"description": "Error message when password is too short"}, "signUpSuccessful": "Signup successful!", "@signUpSuccessful": {"description": "Success message after signing up"}, "onboarding": "Complete Your Profile", "@onboarding": {"description": "Title for the onboarding screen"}, "camera": "Camera", "@camera": {"description": "Option for taking a photo with camera"}, "gallery": "Gallery", "@gallery": {"description": "Option for selecting a photo from gallery"}, "birthYear": "Birth Year", "@birthYear": {"description": "Label for birth year selection"}, "selectBirthYear": "Select Birth Year", "@selectBirthYear": {"description": "Title for birth year picker"}, "userRole": "User Role", "@userRole": {"description": "Label for user role selection"}, "developer": "Developer", "@developer": {"description": "Developer role option"}, "projectManager": "Project Manager", "@projectManager": {"description": "Project Manager role option"}, "private": "Private", "@private": {"description": "Private role option"}, "other": "Other", "@other": {"description": "Other option for selections"}, "usagePurpose": "Usage Purpose", "@usagePurpose": {"description": "Label for usage purpose selection"}, "tasks": "Tasks", "@tasks": {"description": "Label for tasks content type"}, "notes": "Notes", "@notes": {"description": "Label for notes content type"}, "ideas": "Ideas", "@ideas": {"description": "Label for ideas content type"}, "connectPlatforms": "Connect Platforms", "@connectPlatforms": {"description": "Button text to connect platforms"}, "platformsDescription": "Connect to your favorite platforms", "@platformsDescription": {"description": "Description text for platform connection screen"}, "connectMore": "Connect more platforms later", "@connectMore": {"description": "Text for connecting more platforms in the future"}, "finish": "Finish", "@finish": {"description": "Text for finish button"}, "platformConnections": "Platform Connections", "@platformConnections": {"description": "Title for the platform connections screen"}, "connectToPlatform": "Connect to {platform}", "@connectToPlatform": {"description": "Button text to connect to a specific platform", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "connected": "Connected", "@connected": {"description": "Status text when a platform is connected"}, "disconnected": "Not Connected", "@disconnected": {"description": "Status text when a platform is not connected"}, "disconnect": "Disconnect", "@disconnect": {"description": "Button text to disconnect from a platform"}, "connecting": "Connecting...", "@connecting": {"description": "Status text when connecting to a platform"}, "authorizationFailed": "Authorization Failed", "@authorizationFailed": {"description": "Error message when platform authorization fails"}, "connectionSuccessful": "Connection Successful", "@connectionSuccessful": {"description": "Success message when platform connection is established"}, "connectionFailed": "Connection Failed", "@connectionFailed": {"description": "Error message when platform connection fails"}, "integrationAvailableFor": "Integration available for {platform}", "@integrationAvailableFor": {"description": "Text indicating a platform integration is available", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "allPlatforms": "All Platforms", "@allPlatforms": {"description": "Text for showing all available platforms"}, "managePlatformConnections": "Manage platform connections", "@managePlatformConnections": {"description": "Description for the platform connections screen"}, "connectPlatformToSendTasks": "Connect your favorite platforms to send tasks directly", "@connectPlatformToSendTasks": {"description": "Explanation text for platform connections"}, "cancel": "Cancel", "@cancel": {"description": "Label for cancel action"}, "whatWouldYouLikeToDo": "What would you like to do?", "@whatWouldYouLikeToDo": {"description": "Hint text for the title input field in the add entry bottom sheet"}, "recordingInProgress": "Recording in progress...", "@recordingInProgress": {"description": "Text shown while recording is in progress"}, "processingYourVoice": "Processing your voice...", "@processingYourVoice": {"description": "Text shown while transcribing audio"}, "whereToSave": "Where would you like to save this?", "@whereToSave": {"description": "Prompt for selecting a platform to save to"}, "noPlatformsConnected": "No platforms connected. Connect a platform to continue.", "@noPlatformsConnected": {"description": "Text shown when no platforms are connected"}, "newRecording": "New Recording", "@newRecording": {"description": "<PERSON><PERSON> text to start a new recording"}, "uncategorized": "Uncategorized", "@uncategorized": {"description": "Label for uncategorized items"}, "preferences": "Preferences", "@preferences": {"description": "Label for preferences"}, "upgradePackage": "Upgrade Package", "@upgradePackage": {"description": "Button text to upgrade package"}, "infoAndSupport": "Info & Support", "@infoAndSupport": {"description": "Title for info and support section"}, "lightMode": "Light Mode", "@lightMode": {"description": "Label for light mode option"}, "platformConnected": "Connected", "@platformConnected": {"description": "Label for connected platform"}, "platformNotConnected": "Not connected", "@platformNotConnected": {"description": "Label for not connected platform"}, "connect": "Connect", "@connect": {"description": "Button text to connect to a platform"}, "upgradeMessage": "Get more tokens and recording minutes by upgrading your package", "@upgradeMessage": {"description": "Message encouraging users to upgrade their package"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Link text for privacy policy"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Link text for terms of service"}, "faq": "FAQ", "@faq": {"description": "Link text for FAQ"}, "contactSupport": "Contact Support", "@contactSupport": {"description": "Link text for contact support"}, "aboutApp": "About ProjectPilot", "@aboutApp": {"description": "Link text for about app"}, "settingsLanguageSection": "Language", "@settingsLanguageSection": {"description": "Title for the language section in settings"}, "appearanceAndBehavior": "Appearance & Behavior", "@appearanceAndBehavior": {"description": "Title for the appearance and behavior section in settings"}, "settingsInfoSection": "Information & Support", "@settingsInfoSection": {"description": "Title for the information and support section in settings"}, "helpAndSupport": "Help & Support", "@helpAndSupport": {"description": "Title for help and support option in settings"}, "privacyTermsFaq": "Privacy, Terms, FAQ", "@privacyTermsFaq": {"description": "Subtitle for help and support option in settings"}, "settingsAbout": "About", "@settingsAbout": {"description": "Title for about option in settings"}, "storageManagement": "Storage Management", "@storageManagement": {"description": "Title for storage management section in settings"}, "voiceRecordings": "Voice Recordings", "@voiceRecordings": {"description": "Title for voice recordings option in storage management"}, "usingStorage": "Using {size} of storage", "@usingStorage": {"description": "Text showing storage usage", "placeholders": {"size": {"type": "String", "example": "10MB"}}}, "darkMode": "Dark Mode", "@darkMode": {"description": "Title for dark mode option in settings"}, "automaticAssignment": "Automatic Assignment", "@automaticAssignment": {"description": "Title for automatic assignment option in settings"}, "whereToSendTask": "Where should I send this?", "@whereToSendTask": {"description": "Title for the task destination selector"}, "selectPlatform": "Select Platform", "@selectPlatform": {"description": "Label for platform selection"}, "selectProject": "Select Project / Notebook / Board", "@selectProject": {"description": "Label for project selection"}, "sendNow": "Send Now", "@sendNow": {"description": "But<PERSON> text to send the task"}, "noProjectsFound": "No projects found", "@noProjectsFound": {"description": "Message when no projects are available"}, "taskSentSuccessfully": "Task sent successfully", "@taskSentSuccessfully": {"description": "Success message when task is sent"}, "platformDetected": "Platform detected from your voice", "@platformDetected": {"description": "Message shown when platform is detected from voice"}, "projectDetected": "Project detected from your voice", "@projectDetected": {"description": "Message shown when project is detected from voice"}, "intentDetected": "Intent detected from your voice", "@intentDetected": {"description": "Message shown when intent is detected from voice"}, "editContent": "Edit content", "@editContent": {"description": "Tooltip for edit button"}, "noConnectedPlatforms": "No connected platforms", "@noConnectedPlatforms": {"description": "Message when no platforms are connected"}, "connectPlatformsInSettings": "Connect platforms in settings to send tasks", "@connectPlatformsInSettings": {"description": "Message instructing user to connect platforms"}, "newTask": "New Task", "@newTask": {"description": "Title for new task screen"}, "newNote": "New Note", "@newNote": {"description": "Title for new note screen"}, "newIdea": "New Idea", "@newIdea": {"description": "Title for new idea screen"}, "edit": "Edit", "@edit": {"description": "Label for edit action"}, "share": "Share", "@share": {"description": "Text for share button"}, "transcriptionResult": "Transcription Result", "@transcriptionResult": {"description": "Title for transcription result"}, "detectedIntent": "Detected Intent", "@detectedIntent": {"description": "Label for detected intent"}, "detectedEntities": "Detected Entities", "@detectedEntities": {"description": "Label for detected entities"}, "originalTranscription": "Original Transcription", "@originalTranscription": {"description": "Label for original transcription"}, "addConnection": "Add Connection", "@addConnection": {"description": "Text for add connection button"}, "noConnections": "No Connections", "@noConnections": {"description": "Text shown when no connections are available"}, "addConnectionsDescription": "Connect your favorite platforms to send tasks directly", "@addConnectionsDescription": {"description": "Description for adding connections"}, "confirmDelete": "Confirm Delete", "@confirmDelete": {"description": "Title for delete confirmation dialog"}, "confirmDeleteConnectionMessage": "Are you sure you want to delete the connection to {platform}?", "@confirmDeleteConnectionMessage": {"description": "Message for delete connection confirmation dialog", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "delete": "Delete", "@delete": {"description": "Label for delete action"}, "platformConnectedMessage": "Connected to {platform}", "@platformConnectedMessage": {"description": "Message shown when platform is connected", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "speechHistoryTitle": "Speech History", "@speechHistoryTitle": {"description": "Title for the speech history screen"}, "clearHistory": "Clear History", "@clearHistory": {"description": "Button text to clear conversation history"}, "tapMicToStart": "Tap the microphone to start recording", "@tapMicToStart": {"description": "Instruction text for starting recording"}, "suggestedPlatform": "Suggested platform based on your speech:", "@suggestedPlatform": {"description": "Label for suggested platform"}, "confirm": "Confirm", "@confirm": {"description": "Text for confirm button"}, "back": "Back", "@back": {"description": "Text for back button"}, "taskSent": "Task sent to {platform}", "@taskSent": {"description": "Message shown when task is sent to a platform", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "iRecognized": "I recognized:", "@iRecognized": {"description": "Text for AI recognition message"}, "targetPlatform": "Target:", "@targetPlatform": {"description": "Label for target platform"}, "editBeforeSending": "Edit before sending", "@editBeforeSending": {"description": "Button text to edit content before sending"}, "selectCommandType": "What would you like to create?", "@selectCommandType": {"description": "Title for command type selection prompt"}, "createTaskCommand": "Create Task", "@createTaskCommand": {"description": "Button text to create a task"}, "addNoteCommand": "Add Note", "@addNoteCommand": {"description": "Button text to add a note"}, "saveIdeaCommand": "Save Idea", "@saveIdeaCommand": {"description": "<PERSON><PERSON> text to save an idea"}, "enterProjectName": "Enter project name", "@enterProjectName": {"description": "Hint text for project name input"}, "selectProjectPrompt": "Select Project", "@selectProjectPrompt": {"description": "Title for project selection prompt"}, "entries": "Entries", "@entries": {"description": "Title for entries screen"}, "entryDetails": "Entry Details", "@entryDetails": {"description": "Title for entry details screen"}, "addUpdate": "Add Update", "@addUpdate": {"description": "Button text to add an update to an entry"}, "updateDetails": "Update Details", "@updateDetails": {"description": "Title for update details dialog"}, "deleteUpdate": "Delete Update", "@deleteUpdate": {"description": "Title for delete update confirmation dialog"}, "deleteUpdateConfirmation": "Are you sure you want to delete this update?", "@deleteUpdateConfirmation": {"description": "Confirmation message for deleting an update"}, "deleteEntry": "Delete Entry", "@deleteEntry": {"description": "Title for delete entry dialog"}, "deleteEntryConfirmation": "Are you sure you want to delete this entry? This action cannot be undone.", "@deleteEntryConfirmation": {"description": "Confirmation message for deleting an entry"}, "entryCreated": "Entry created successfully", "@entryCreated": {"description": "Success message when an entry is created"}, "entryDeleted": "Entry deleted", "@entryDeleted": {"description": "Message when an entry is deleted"}, "entryUpdateAdded": "Update added successfully", "@entryUpdateAdded": {"description": "Success message when an update is added to an entry"}, "noEntriesFound": "No entries found", "@noEntriesFound": {"description": "Message when no entries are found"}, "createEntryPrompt": "Create a new entry by tapping the + button", "@createEntryPrompt": {"description": "Prompt to create a new entry"}, "createNewEntry": "Create New Entry", "@createNewEntry": {"description": "Title for adding a new entry"}, "title": "Title", "@title": {"description": "Label for title field"}, "titleHint": "Enter title here", "@titleHint": {"description": "Hint for title input field"}, "description": "Description", "@description": {"description": "Label for description field"}, "descriptionHint": "Enter description here", "@descriptionHint": {"description": "Hint for description input field"}, "time": "Time", "@time": {"description": "Label for time field"}, "date": "Date", "@date": {"description": "Label for date field"}, "dateFormat": "{month}/{day}/{year}", "@dateFormat": {"description": "Format for displaying date", "placeholders": {"month": {"type": "int"}, "day": {"type": "int"}, "year": {"type": "int"}}}, "type": "Type", "@type": {"description": "Label for type field"}, "priority": "Priority", "@priority": {"description": "Label for priority field"}, "priorityLow": "Low", "@priorityLow": {"description": "Low priority level"}, "priorityMedium": "Medium", "@priorityMedium": {"description": "Medium priority level"}, "priorityHigh": "High", "@priorityHigh": {"description": "High priority level"}, "priorityUrgent": "<PERSON><PERSON>", "@priorityUrgent": {"description": "Urgent priority level"}, "destination": "Destination", "@destination": {"description": "Label for destination field"}, "clickupPlatform": "ClickUp", "@clickupPlatform": {"description": "Name of ClickUp platform"}, "notionPlatform": "Notion", "@notionPlatform": {"description": "Name of Notion platform"}, "asanaPlatform": "<PERSON><PERSON>", "@asanaPlatform": {"description": "Asana platform name"}, "mondayPlatform": "Monday", "@mondayPlatform": {"description": "Monday platform name"}, "jiraPlatform": "<PERSON><PERSON>", "@jiraPlatform": {"description": "Jira platform name"}, "trelloPlatform": "Trello", "@trelloPlatform": {"description": "Trello platform name"}, "create": "Create", "@create": {"description": "Label for create button"}, "pleaseEnterTitle": "Please enter a title", "@pleaseEnterTitle": {"description": "Error message when title is empty"}, "all": "All", "@all": {"description": "Label for all items filter"}, "searchHint": "Search", "@searchHint": {"description": "Hint text for search input"}, "chats": "Chats", "@chats": {"description": "Title for the chats section in the drawer"}, "newChat": "New Chat", "@newChat": {"description": "Button text to create a new chat"}, "searchResults": "Search Results", "@searchResults": {"description": "Title for search results section"}, "noResultsFound": "No results found", "@noResultsFound": {"description": "Message shown when no search results are found"}, "projects": "Projects", "@projects": {"description": "General term for projects"}, "newFolder": "New Folder", "@newFolder": {"description": "Button text to create a new folder"}, "createNewFolder": "Create New Folder", "@createNewFolder": {"description": "Title for the create folder dialog"}, "enterFolderName": "Enter folder name", "@enterFolderName": {"description": "Hint text for folder name input"}, "rename": "<PERSON><PERSON>", "@rename": {"description": "Action to rename an item"}, "renameChat": "<PERSON><PERSON>", "@renameChat": {"description": "Title for the rename chat dialog"}, "renameFolder": "<PERSON><PERSON>", "@renameFolder": {"description": "Title for the rename folder dialog"}, "enterNewName": "Enter new name", "@enterNewName": {"description": "Hint text for new name input"}, "moveToFolder": "Move to folder", "@moveToFolder": {"description": "Action to move an item to a folder"}, "deleteChat": "Delete Chat", "@deleteChat": {"description": "Title for the delete chat confirmation dialog"}, "deleteFolder": "Delete Folder", "@deleteFolder": {"description": "Title for the delete folder confirmation dialog"}, "deleteChatConfirmation": "Are you sure you want to delete this chat? This action cannot be undone.", "@deleteChatConfirmation": {"description": "Confirmation message for deleting a chat"}, "deleteFolderConfirmation": "Are you sure you want to delete this folder? This action cannot be undone.", "@deleteFolderConfirmation": {"description": "Confirmation message for deleting a folder"}, "today": "Today", "@today": {"description": "Text for items created today"}, "yesterday": "Yesterday", "@yesterday": {"description": "Text for items created yesterday"}, "daysAgo": "{count} days ago", "@daysAgo": {"description": "Text for items created days ago", "placeholders": {"count": {"type": "int"}}}, "chatHistory": "Chat History", "@chatHistory": {"description": "Title for the chat history section in recording view"}, "addNote": "Add Note", "@addNote": {"description": "Button text to add a note to a message"}, "pinMessage": "Pin Message", "@pinMessage": {"description": "Button text to pin a message"}, "bookmark": "Bookmark", "@bookmark": {"description": "Button text to bookmark a message"}, "messageOptions": "Message Options", "@messageOptions": {"description": "Title for message options menu"}, "messageDeleted": "Message deleted", "@messageDeleted": {"description": "Confirmation message when a message is deleted"}, "messagePinned": "Message pinned", "@messagePinned": {"description": "Confirmation message when a message is pinned"}, "messageBookmarked": "Message bookmarked", "@messageBookmarked": {"description": "Confirmation message when a message is bookmarked"}, "noteAdded": "Note added", "@noteAdded": {"description": "Confirmation message when a note is added to a message"}, "enterNote": "Enter your note", "@enterNote": {"description": "Hint text for note input field"}, "addNoteToMessage": "Add Note to Message", "@addNoteToMessage": {"description": "Title for add note dialog"}, "editMessage": "Edit Message", "@editMessage": {"description": "Title for edit message dialog"}, "enterMessage": "Enter your message", "@enterMessage": {"description": "Hint text for message input field"}, "messageMoved": "Message moved to folder", "@messageMoved": {"description": "Confirmation message when a message is moved to a folder"}, "selectFolder": "Select Folder", "@selectFolder": {"description": "Title for folder selection dialog"}, "folderCreated": "Folder created", "@folderCreated": {"description": "Confirmation message when a folder is created"}, "noFolders": "No folders yet", "@noFolders": {"description": "Message shown when no folders exist"}, "createFolderPrompt": "Create a folder to organize your chats", "@createFolderPrompt": {"description": "Prompt to create a new folder"}, "noChatsInFolder": "No chats in this folder", "@noChatsInFolder": {"description": "Message shown when a folder has no chats"}, "addChatsToFolder": "Add chats by dragging them here", "@addChatsToFolder": {"description": "Prompt to add chats to a folder"}, "noChatsYet": "No conversations yet", "@noChatsYet": {"description": "Message shown when there are no chats"}, "startNewChatPrompt": "Start a new chat to begin", "@startNewChatPrompt": {"description": "Prompt to start a new chat"}, "noRecentItems": "No recent items", "@noRecentItems": {"description": "Message shown when there are no recent items"}, "recentItems": "Recent Items", "@recentItems": {"description": "Title for recent items section"}, "todaysTasks": "Today's Tasks", "@todaysTasks": {"description": "Title for today's tasks section"}, "tapToViewTasks": "Tap to view your tasks", "@tapToViewTasks": {"description": "Prompt to view tasks"}, "appDescription": "Your smart voice-to-task assistant", "@appDescription": {"description": "Short description of the app"}, "offlineMode": "Offline Mode", "@offlineMode": {"description": "Text shown when the app is in offline mode"}, "offlineModeDescription": "You're currently offline. Some features may be limited.", "@offlineModeDescription": {"description": "Description of offline mode limitations"}, "syncPending": "Sync Pending", "@syncPending": {"description": "Status text when sync is pending"}, "syncComplete": "Sync Complete", "@syncComplete": {"description": "Status text when sync is complete"}, "syncFailed": "Sync Failed", "@syncFailed": {"description": "Status text when sync fails"}, "retrySync": "Retry Sync", "@retrySync": {"description": "Button text to retry synchronization"}, "continueInOfflineMode": "Continue in Offline Mode", "@continueInOfflineMode": {"description": "Button text to continue in offline mode"}, "networkError": "Network Error", "@networkError": {"description": "Error message for network issues"}, "checkConnection": "Please check your internet connection and try again.", "@checkConnection": {"description": "Prompt to check internet connection"}, "pause": "Pause", "@pause": {"description": "Button text to pause recording"}, "resume": "Resume", "@resume": {"description": "But<PERSON> text to resume recording"}, "reset": "Reset", "@reset": {"description": "Text for reset button"}, "transcriptionComplete": "Transcription Complete", "@transcriptionComplete": {"description": "Message shown when transcription is complete"}, "viewDetails": "View Details", "@viewDetails": {"description": "Button text to view details of a transcription or entry"}, "continueButton": "Continue", "@continueButton": {"description": "But<PERSON> text to continue to next step"}, "collection": "Collection", "@collection": {"description": "Title for the collection screen"}, "newest": "Newest", "@newest": {"description": "Label for sorting by newest first"}, "oldest": "Oldest", "@oldest": {"description": "Label for sorting by oldest first"}, "name": "Name", "@name": {"description": "Label for sorting by name"}, "category": "Category", "@category": {"description": "Label for sorting by category"}, "synced": "Synced", "@synced": {"description": "Label indicating an item is synced"}, "unsynced": "Unsynced", "@unsynced": {"description": "Label for unsynced items"}, "notSynced": "Not Synced", "@notSynced": {"description": "Label for items not synced yet"}, "search": "Search", "@search": {"description": "Label for search action"}, "closeSearch": "Close Search", "@closeSearch": {"description": "Label for closing search"}, "listView": "List View", "@listView": {"description": "Label for list view mode"}, "gridView": "Grid View", "@gridView": {"description": "Label for grid view mode"}, "createFirstEntry": "Create First Entry", "@createFirstEntry": {"description": "Button text to create the first entry"}, "noEntriesYet": "No entries yet", "@noEntriesYet": {"description": "Message when no entries exist"}, "createNewEntryDescription": "Create your first task, note, or idea using the button below.", "@createNewEntryDescription": {"description": "Description for creating new entries"}, "completed": "Completed", "@completed": {"description": "Status for completed items"}, "noSearchResults": "No search results found", "@noSearchResults": {"description": "Message when no search results exist"}, "clearSearch": "Clear Search", "@clearSearch": {"description": "Label for clearing search"}, "unassignedEntriesWarning": "Unassigned Entries", "@unassignedEntriesWarning": {"description": "Title for unassigned entries warning banner"}, "unassignedEntriesDescription": "You have {count} unassigned entries waiting to be assigned to a platform", "@unassignedEntriesDescription": {"description": "Description for unassigned entries with count", "placeholders": {"count": {"type": "int", "example": "5"}}}, "assignNow": "Assign Now", "@assignNow": {"description": "Button text to assign entries immediately"}, "assignedEntries": "Assigned Entries ({count})", "@assignedEntries": {"description": "Title for the assigned entries section with count", "placeholders": {"count": {"type": "int", "example": "10"}}}, "searchEntries": "Search entries...", "@searchEntries": {"description": "Hint text for searching entries"}, "archive": "Archive", "@archive": {"description": "Label for archive action"}, "assign": "Assign", "@assign": {"description": "Label for assign action"}, "entryArchived": "Entry archived", "@entryArchived": {"description": "Message when an entry is archived"}, "undo": "UNDO", "@undo": {"description": "Label for undo action"}, "restoreFunctionalityNotImplemented": "Restore functionality not yet implemented", "@restoreFunctionalityNotImplemented": {"description": "Message shown when restore functionality is not implemented"}, "assignedToPlatform": "Assigned to {platform}", "@assignedToPlatform": {"description": "Message when an entry is assigned to a platform", "placeholders": {"platform": {"type": "String"}}}, "recordingFileNotFound": "Recording file not found. It may have been deleted or moved.", "@recordingFileNotFound": {"description": "Message shown when a recording file is not found"}, "errorPlayingRecording": "Error playing recording", "@errorPlayingRecording": {"description": "Message shown when there is an error playing a recording"}, "assignmentSuccessful": "Assignment Successful", "@assignmentSuccessful": {"description": "Title for successful assignment notification"}, "assignedCategoryToPlatform": "{category} assigned to {platform}", "@assignedCategoryToPlatform": {"description": "Message showing which category was assigned to which platform", "placeholders": {"category": {"type": "String", "example": "Task"}, "platform": {"type": "String", "example": "ClickUp"}}}, "myTasks": "My Tasks", "@myTasks": {"description": "Title for tasks collection screen"}, "task": "Task", "@task": {"description": "Title for task collection screen"}, "myIdeas": "My Ideas", "@myIdeas": {"description": "Title for ideas collection screen"}, "idea": "Idea", "@idea": {"description": "Title for idea collection screen"}, "myNotes": "My Notes", "@myNotes": {"description": "Title for notes collection screen"}, "note": "Note", "@note": {"description": "Title for note collection screen"}, "noTasksYet": "No tasks yet", "@noTasksYet": {"description": "Message when there are no tasks"}, "noIdeasYet": "No ideas yet", "@noIdeasYet": {"description": "Message when there are no ideas"}, "noNotesYet": "No notes yet", "@noNotesYet": {"description": "Message when there are no notes"}, "addNew": "Add New", "@addNew": {"description": "Label for add new button"}, "optimizedFor": "Optimized for", "@optimizedFor": {"description": "Text shown when content has been optimized for a specific category"}, "optimizedContent": "Optimized Content", "@optimizedContent": {"description": "Title for optimized content section"}, "contentOptimization": "Content Optimization", "@contentOptimization": {"description": "Title for content optimization section"}, "platformAssignment": "Platform Assignment", "@platformAssignment": {"description": "Title for platform assignment section"}, "audioPlaybackError": "Audio playback error", "@audioPlaybackError": {"description": "Title shown when audio fails to play"}, "audioFileCorrupted": "Unable to play the audio file. It may be corrupted.", "@audioFileCorrupted": {"description": "Message shown when audio file is corrupted"}, "loadingAudioPlayer": "Loading audio player...", "@loadingAudioPlayer": {"description": "Text shown while loading audio player"}, "optimizingFor": "Optimizing for", "@optimizingFor": {"description": "Text shown when optimizing content for a specific category"}, "choosePlatformToAssign": "<PERSON>ose Platform to Assign", "@choosePlatformToAssign": {"description": "Prompt to choose a platform for assignment"}, "assigningTo": "Assigning to", "@assigningTo": {"description": "Message shown when assigning to a platform"}, "autoAssignmentEnabled": "Auto-assignment is enabled. This item will be automatically processed.", "@autoAssignmentEnabled": {"description": "Message shown when auto-assignment is enabled"}, "deleteRecordingConfirmation": "Are you sure you want to delete this recording? This action cannot be undone.", "@deleteRecordingConfirmation": {"description": "Confirmation message for deleting a recording"}, "deletingRecording": "Deleting recording...", "@deletingRecording": {"description": "Message shown when deleting a recording"}, "save": "Save", "@save": {"description": "Text for save button"}, "created": "Created", "@created": {"description": "Label for when an item was created"}, "content": "Content", "@content": {"description": "Label for content section"}, "createEntry": "Create Entry", "@createEntry": {"description": "Tooltip for create entry button"}, "taskReminder": "Task Reminder", "@taskReminder": {"description": "Title for task reminder notifications"}, "selectTime": "Select time", "@selectTime": {"description": "Title for time selection dialog"}, "changeTime": "Change Time", "@changeTime": {"description": "Button text to change time"}, "noReminder": "No Reminder", "@noReminder": {"description": "Button text for disabling reminder"}, "editEntry": "Edit Entry", "@editEntry": {"description": "Button text to edit an entry"}, "originalContent": "Original Content", "@originalContent": {"description": "Label for original content when no audio is available"}, "textEntry": "Text Entry", "@textEntry": {"description": "Label for text-only entries"}, "voiceEntry": "Voice Entry", "@voiceEntry": {"description": "Label for voice entries"}, "optimizeAs": "Optimize as", "@optimizeAs": {"description": "Label for optimization button prefix"}, "optimizeAsTaskDescription": "Convert content into an actionable task with clear steps and deadlines", "@optimizeAsTaskDescription": {"description": "Description for task optimization"}, "optimizeAsIdeaDescription": "Structure content as a creative idea with potential applications and benefits", "@optimizeAsIdeaDescription": {"description": "Description for idea optimization"}, "optimizeAsNoteDescription": "Format content as a well-structured note with key points and references", "@optimizeAsNoteDescription": {"description": "Description for note optimization"}, "optimizationInProgress": "Using AI to enhance and structure your content", "@optimizationInProgress": {"description": "Message shown during optimization process"}, "autoAssignment": "Auto Assignment", "@autoAssignment": {"description": "Title for auto assignment section"}, "autoAssignmentInfo": "Content will be automatically analyzed and assigned to the most appropriate connected platform", "@autoAssignmentInfo": {"description": "Informational text about auto assignment"}, "configureAutoAssignment": "Configure Auto Assignment", "@configureAutoAssignment": {"description": "Button text to configure auto assignment settings"}, "navigateToSettings": "Opening Settings...", "@navigateToSettings": {"description": "Message when navigating to settings page"}, "manageConnections": "Manage Connections", "@manageConnections": {"description": "Button text to manage platform connections"}, "platformConnectionsManagement": "Opening platform connections management...", "@platformConnectionsManagement": {"description": "Message when opening platform connections management"}, "successfullyAssignedTo": "Successfully assigned to", "@successfullyAssignedTo": {"description": "Message confirming successful assignment to a platform"}, "recording": "Recording", "@recording": {"description": "Text shown while recording is active"}, "recordingPaused": "Paused", "@recordingPaused": {"description": "Text shown when recording is paused"}, "resetRecording": "Reset Recording?", "@resetRecording": {"description": "Title for the reset recording confirmation dialog"}, "resetRecordingConfirmation": "Are you sure you want to reset this recording? This action cannot be undone.", "@resetRecordingConfirmation": {"description": "Confirmation message for resetting a recording"}, "recordingGuideTitle": "How it works", "@recordingGuideTitle": {"description": "Title for the recording guide section"}, "recordingGuideStep1": "Record your voice note", "@recordingGuideStep1": {"description": "First step in recording guide"}, "recordingGuideStep2": "ProjectPilot magically transcribes and enhances it", "@recordingGuideStep2": {"description": "Second step in recording guide"}, "recordingGuideStep3": "Send it to your favorite platform", "@recordingGuideStep3": {"description": "Third step in recording guide"}, "noInternetConnection": "No internet connection", "@noInternetConnection": {"description": "Error message when no internet connection is available"}, "processingPurchase": "Processing your purchase...", "@processingPurchase": {"description": "Message shown while processing a purchase"}, "choosePackage": "Choose a Package", "@choosePackage": {"description": "Title for package selection"}, "packageSelectionSubtitle": "Get more tokens to use for AI text generation and audio transcription", "@packageSelectionSubtitle": {"description": "Subtitle explaining the purpose of token packages"}, "purchaseSuccessful": "Successfully purchased {count} tokens!", "@purchaseSuccessful": {"description": "Message shown after successful token purchase", "placeholders": {"count": {"type": "int", "format": "compact"}}}, "sendToThisPlatform": "Send to this platform", "@sendToThisPlatform": {"description": "Label for content type chips section"}, "choosePlatformTitle": "Choose Platform", "choosePlatformDescription": "Choose a platform to dispatch your content to", "contentPreview": "Content Preview", "suggested": "Suggested", "connectPlatform": "Connect Platform", "authenticating": "Authenticating...", "@authenticating": {"description": "Text shown during authentication process"}, "createAccount": "Create Account", "@createAccount": {"description": "Button text for creating a new account"}, "emailFieldHint": "Enter your email address", "@emailFieldHint": {"description": "Hint text for email input field"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "passwordFieldHint": "Enter your password", "@passwordFieldHint": {"description": "Hint text for password input field"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Label for confirm password field"}, "confirmPasswordHint": "Confirm your password", "@confirmPasswordHint": {"description": "Hint text for confirm password field"}, "confirmPasswordRequired": "Please confirm your password", "@confirmPasswordRequired": {"description": "Error message when confirm password is empty"}, "passwordsDoNotMatch": "Passwords do not match", "@passwordsDoNotMatch": {"description": "Error message when passwords do not match"}, "nameFieldHint": "Enter your full name", "@nameFieldHint": {"description": "Hint text for name input field"}, "history": "History", "@history": {"description": "Title for the history screen"}, "record": "Record", "@record": {"description": "Title for the record tab"}, "newEntry": "New Entry", "@newEntry": {"description": "Title for the new entry dialog"}, "pleaseEnterContent": "Please enter content", "@pleaseEnterContent": {"description": "Validation message when content is empty"}, "update": "Update", "@update": {"description": "Button text to update an entry"}, "noEntries": "No entries", "@noEntries": {"description": "Message when there are no entries"}, "deleteAllAudioFiles": "Delete All Audio Files", "@deleteAllAudioFiles": {"description": "Title for delete all audio files dialog"}, "deleteAllAudioFilesContent": "This will delete all voice recording files to free up storage space. The text content of your recordings will be preserved.\n\nThis action cannot be undone. Are you sure?", "@deleteAllAudioFilesContent": {"description": "Content for delete all audio files dialog"}, "deleteAll": "Delete All", "@deleteAll": {"description": "Button text to delete all items"}, "deleteAllAudioFilesTooltip": "Delete all audio files", "@deleteAllAudioFilesTooltip": {"description": "Tooltip for delete all audio files button"}, "selectLanguageDialogTitle": "Select Language", "@selectLanguageDialogTitle": {"description": "Title for language selection dialog"}, "enabled": "Enabled", "@enabled": {"description": "Status text for enabled features"}, "disabled": "Disabled", "@disabled": {"description": "Status text for disabled features"}, "autoAssignEnabledDesc": "Tasks are automatically assigned to platforms", "@autoAssignEnabledDesc": {"description": "Description when auto assignment is enabled"}, "autoAssignDisabledDesc": "Manual platform selection for tasks", "@autoAssignDisabledDesc": {"description": "Description when auto assignment is disabled"}, "packageDescription": "{name} package with {tokenAmount} tokens", "@packageDescription": {"description": "Description for token packages", "placeholders": {"name": {"type": "String", "example": "Pro"}, "tokenAmount": {"type": "int", "example": "1000"}}}, "aboutAppTitle": "About {appName}", "@aboutAppTitle": {"description": "Title for about app screen", "placeholders": {"appName": {"type": "String", "example": "ProjectPilot"}}}, "appVersion": "Version {version}", "@appVersion": {"description": "App version display", "placeholders": {"version": {"type": "String", "example": "1.0.0"}}}, "keyFeatures": "Key Features", "@keyFeatures": {"description": "Title for key features section"}, "voiceToTask": "Voice-to-Task", "@voiceToTask": {"description": "Voice-to-task feature name"}, "voiceToTaskDesc": "Create tasks using your voice", "@voiceToTaskDesc": {"description": "Voice-to-task feature description"}, "multiPlatform": "Multi-Platform", "@multiPlatform": {"description": "Multi-platform feature name"}, "multiPlatformDesc": "Connect to ClickUp, Notion, and more", "@multiPlatformDesc": {"description": "Multi-platform feature description"}, "multiLanguage": "Multi-Language", "@multiLanguage": {"description": "Multi-language feature name"}, "multiLanguageDesc": "Supports English, German, Russian, Turkish, and Arabic", "@multiLanguageDesc": {"description": "Multi-language feature description"}, "aiPowered": "AI-Powered", "@aiPowered": {"description": "AI-powered feature name"}, "aiPoweredDesc": "Smart task categorization and optimization", "@aiPoweredDesc": {"description": "AI-powered feature description"}, "ourTeam": "Our Team", "@ourTeam": {"description": "Title for our team section"}, "copyright": "© {year} ProjectPilot. All rights reserved.", "@copyright": {"description": "Copyright text", "placeholders": {"year": {"type": "String", "example": "2024"}}}, "contactSupportDesc": "Have a question or need help? Fill out the form below and our support team will get back to you as soon as possible.", "@contactSupportDesc": {"description": "Description for contact support screen"}, "nameField": "Name", "@nameField": {"description": "Label for name input field"}, "emailField": "Email", "@emailField": {"description": "Label for email input field"}, "messageField": "Message", "@messageField": {"description": "Label for message input field"}, "messageFieldHint": "Enter your message", "@messageFieldHint": {"description": "Hint text for message input field"}, "submitButton": "Submit", "@submitButton": {"description": "Button text to submit a form"}, "nameValidationError": "Please enter your name", "@nameValidationError": {"description": "Validation error for name field"}, "emailValidationError": "Please enter a valid email", "@emailValidationError": {"description": "Validation error for email field"}, "messageValidationError": "Please enter your message", "@messageValidationError": {"description": "Validation error for message field"}, "initializationError": "Initialization Error", "@initializationError": {"description": "Title for initialization error dialog"}, "errorDuringInit": "An error occurred during app initialization:\n{error}", "@errorDuringInit": {"description": "Error message during app initialization", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "retryButton": "Retry", "@retryButton": {"description": "Button text to retry an action"}, "faqTitle": "FAQ", "@faqTitle": {"description": "Title for FAQ screen"}, "frequentlyAskedQuestions": "Frequently Asked Questions", "@frequentlyAskedQuestions": {"description": "Subtitle for FAQ screen"}, "privacyPolicyTitle": "Privacy Policy", "@privacyPolicyTitle": {"description": "Title for privacy policy screen"}, "lastUpdated": "Last updated: {time}", "@lastUpdated": {"description": "Last updated timestamp", "placeholders": {"time": {"type": "String"}}}, "privacyPolicyIntro": "This Privacy Policy describes how ProjectPilot (\"we\", \"us\", or \"our\") collects, uses, and discloses your personal information when you use our mobile application (the \"App\").", "@privacyPolicyIntro": {"description": "Introduction text for privacy policy"}, "infoWeCollect": "Information We Collect", "@infoWeCollect": {"description": "Section title for information collection"}, "infoWeCollectContent": "We collect information that you provide directly to us, such as when you create an account, update your profile, use the interactive features of our App, request customer support, or otherwise communicate with us.", "@infoWeCollectContent": {"description": "Content for information collection section"}, "howWeUseInfo": "How We Use Your Information", "@howWeUseInfo": {"description": "Section title for information usage"}, "howWeUseInfoContent": "We use the information we collect to provide, maintain, and improve our services, including to process transactions, send you related information, and provide customer support.", "@howWeUseInfoContent": {"description": "Content for information usage section"}, "sharingOfInfo": "Sharing of Information", "@sharingOfInfo": {"description": "Section title for information sharing"}, "sharingOfInfoContent": "We may share the information we collect as follows: with third-party vendors, consultants, and other service providers who need access to such information to carry out work on our behalf; in response to a request for information if we believe disclosure is in accordance with any applicable law, regulation, or legal process.", "@sharingOfInfoContent": {"description": "Content for information sharing section"}, "yourChoices": "Your Choices", "@yourChoices": {"description": "Section title for user choices"}, "yourChoicesContent": "You may update, correct, or delete your account information at any time by logging into your account or contacting us. You may opt out of receiving promotional communications from us by following the instructions in those communications.", "@yourChoicesContent": {"description": "Content for user choices section"}, "contactUs": "Contact Us", "@contactUs": {"description": "Section title for contact information"}, "contactUsPrivacyContent": "If you have any questions about this Privacy Policy, please contact us at: <EMAIL>", "@contactUsPrivacyContent": {"description": "Contact information for privacy policy"}, "termsOfServiceTitle": "Terms of Service", "@termsOfServiceTitle": {"description": "Title for terms of service screen"}, "termsOfServiceIntro": "Please read these Terms of Service (\"Terms\", \"Terms of Service\") carefully before using the ProjectPilot mobile application (the \"Service\") operated by ProjectPilot (\"us\", \"we\", or \"our\").", "@termsOfServiceIntro": {"description": "Introduction text for terms of service"}, "acceptanceOfTerms": "Acceptance of Terms", "@acceptanceOfTerms": {"description": "Section title for acceptance of terms"}, "acceptanceOfTermsContent": "By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.", "@acceptanceOfTermsContent": {"description": "Content for acceptance of terms section"}, "useOfService": "Use of the Service", "@useOfService": {"description": "Section title for service usage"}, "useOfServiceContent": "Our Service allows you to create, manage, and organize tasks and notes using voice commands. You are responsible for maintaining the confidentiality of your account and password and for restricting access to your computer or mobile device.", "@useOfServiceContent": {"description": "Content for service usage section"}, "intellectualProperty": "Intellectual Property", "@intellectualProperty": {"description": "Section title for intellectual property"}, "intellectualPropertyContent": "The Service and its original content, features, and functionality are and will remain the exclusive property of ProjectPilot and its licensors. The Service is protected by copyright, trademark, and other laws.", "@intellectualPropertyContent": {"description": "Content for intellectual property section"}, "termination": "Termination", "@termination": {"description": "Section title for termination"}, "terminationContent": "We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.", "@terminationContent": {"description": "Content for termination section"}, "limitationOfLiability": "Limitation of Liability", "@limitationOfLiability": {"description": "Section title for limitation of liability"}, "limitationOfLiabilityContent": "In no event shall ProjectPilot, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.", "@limitationOfLiabilityContent": {"description": "Content for limitation of liability section"}, "changesToTerms": "Changes to Terms", "@changesToTerms": {"description": "Section title for changes to terms"}, "changesToTermsContent": "We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days' notice prior to any new terms taking effect.", "@changesToTermsContent": {"description": "Content for changes to terms section"}, "contactUsTermsContent": "If you have any questions about these Terms, please contact us at: <EMAIL>", "@contactUsTermsContent": {"description": "Contact information for terms of service"}, "whatIsProjectPilot": "What is ProjectPilot?", "@whatIsProjectPilot": {"description": "FAQ question about ProjectPilot"}, "whatIsProjectPilotAnswer": "ProjectPilot is a voice-to-task application that allows you to create tasks, notes, and ideas using voice commands. It can transcribe your voice and send the tasks to various platforms like ClickUp, Notion, and more.", "@whatIsProjectPilotAnswer": {"description": "FAQ answer about ProjectPilot"}, "howToConnectPlatforms": "How do I connect to external platforms?", "@howToConnectPlatforms": {"description": "FAQ question about connecting platforms"}, "howToConnectPlatformsAnswer": "Go to the Settings screen and navigate to the \"Platform Connections\" section. There you can connect to various platforms like ClickUp, Notion, and Monday by clicking the Connect button and following the authentication process.", "@howToConnectPlatformsAnswer": {"description": "FAQ answer about connecting platforms"}, "whatLanguagesSupported": "What languages are supported?", "@whatLanguagesSupported": {"description": "FAQ question about supported languages"}, "whatLanguagesSupportedAnswer": "ProjectPilot currently supports English, German, Russian, Turkish, and Arabic. You can change the language in the Settings screen.", "@whatLanguagesSupportedAnswer": {"description": "FAQ answer about supported languages"}, "howTokensWork": "How do tokens work?", "@howTokensWork": {"description": "FAQ question about tokens"}, "howTokensWorkAnswer": "Tokens are used for AI text generation and audio transcription. Each transcription and optimization consumes a certain number of tokens. You can purchase more tokens in the \"Upgrade Package\" section of the Settings screen.", "@howTokensWorkAnswer": {"description": "FAQ answer about tokens"}, "canUseOffline": "Can I use ProjectPilot offline?", "@canUseOffline": {"description": "FAQ question about offline usage"}, "canUseOfflineAnswer": "Some features of ProjectPilot require an internet connection, such as sending tasks to external platforms and using online transcription services. However, basic voice recording can work offline.", "@canUseOfflineAnswer": {"description": "FAQ answer about offline usage"}, "howCustomizeProfile": "How do I customize my profile?", "@howCustomizeProfile": {"description": "FAQ question about profile customization"}, "howCustomizeProfileAnswer": "You can update your profile information in the Settings screen under the \"Profile\" section.", "@howCustomizeProfileAnswer": {"description": "FAQ answer about profile customization"}, "isDataSecure": "Is my data secure?", "@isDataSecure": {"description": "FAQ question about data security"}, "isDataSecureAnswer": "Yes, we take data security seriously. Your data is encrypted and stored securely. We do not share your personal information with third parties without your consent. Please refer to our Privacy Policy for more details.", "@isDataSecureAnswer": {"description": "FAQ answer about data security"}, "howCancelSubscription": "How do I cancel my subscription?", "@howCancelSubscription": {"description": "FAQ question about subscription cancellation"}, "howCancelSubscriptionAnswer": "You can manage your subscription through your app store account (Google Play Store or Apple App Store). Go to the subscription management section of your app store and cancel the ProjectPilot subscription.", "@howCancelSubscriptionAnswer": {"description": "FAQ answer about subscription cancellation"}, "appTitleProjectPilot": "ProjectPilot", "@appTitleProjectPilot": {"description": "The title of the ProjectPilot application"}, "projectDashboard": "Project Dashboard", "@projectDashboard": {"description": "Title for project dashboard screen"}, "projectAnalysis": "Project Analysis", "@projectAnalysis": {"description": "Title for project analysis"}, "askAboutProject": "Ask About Project", "@askAboutProject": {"description": "Button text to ask questions about project"}, "projectStatus": "Project Status", "@projectStatus": {"description": "Label for project status"}, "blockers": "Blockers", "@blockers": {"description": "Title for blockers section"}, "recommendations": "Recommendations", "@recommendations": {"description": "Title for recommendations section"}, "criticalPath": "Critical Path", "@criticalPath": {"description": "Label for critical path analysis"}, "projectProgress": "Project Progress", "@projectProgress": {"description": "Label for project progress"}, "teamMembers": "Team Members", "@teamMembers": {"description": "Label for team members"}, "budget": "Budget", "@budget": {"description": "Label for budget information"}, "timeline": "Timeline", "@timeline": {"description": "Label for project timeline"}, "risks": "Risks", "@risks": {"description": "Label for project risks"}, "insights": "Insights", "@insights": {"description": "Label for project insights"}, "analysisLoading": "Analyzing project...", "@analysisLoading": {"description": "Loading message for project analysis"}, "analysisFailed": "Analysis failed. Please try again.", "@analysisFailed": {"description": "Error message when analysis fails"}, "refreshProject": "Refresh Project", "@refreshProject": {"description": "Button text to refresh project data"}, "projectOverdue": "Project Overdue", "@projectOverdue": {"description": "Label when project is overdue"}, "projectOnTrack": "Project On Track", "@projectOnTrack": {"description": "Label when project is on track"}, "projectAtRisk": "Project At Risk", "@projectAtRisk": {"description": "Label when project is at risk"}, "highPriorityBlockers": "High Priority Blockers", "@highPriorityBlockers": {"description": "Label for high priority blockers"}, "urgentRecommendations": "Urgent Recommendations", "@urgentRecommendations": {"description": "Label for urgent recommendations"}, "daysRemaining": "{count} days remaining", "@daysRemaining": {"description": "Days remaining until deadline", "placeholders": {"count": {"type": "int"}}}, "tasksCompleted": "{completed} of {total} tasks completed", "@tasksCompleted": {"description": "Tasks completion status", "placeholders": {"completed": {"type": "int"}, "total": {"type": "int"}}}, "budgetUtilization": "Budget: {spent} of {total} used", "@budgetUtilization": {"description": "Budget utilization display", "placeholders": {"spent": {"type": "double", "format": "currency"}, "total": {"type": "double", "format": "currency"}}}, "queryProject": "Ask me anything about this project...", "@queryProject": {"description": "Placeholder text for project query input"}, "processingQuery": "Processing your question...", "@processingQuery": {"description": "Loading message for query processing"}, "queryFailed": "Could not process your question. Please try again.", "@queryFailed": {"description": "Error message when query fails"}, "projectNotFound": "Project not found", "@projectNotFound": {"description": "Error message when project is not found"}, "syncingData": "Syncing project data...", "@syncingData": {"description": "Loading message for data sync"}, "activeTasks": "Active Tasks", "@activeTasks": {"description": "Label for active tasks"}, "completedTasks": "Completed Tasks", "@completedTasks": {"description": "Label for completed tasks"}, "blockedTasks": "Blocked Tasks", "@blockedTasks": {"description": "Label for blocked tasks"}, "overdueTasks": "Overdue Tasks", "@overdueTasks": {"description": "Label for overdue tasks"}, "confidenceLevel": "Confidence: {level}%", "@confidenceLevel": {"description": "AI analysis confidence level", "placeholders": {"level": {"type": "int"}}}, "riskScore": "Risk Score: {score}/10", "@riskScore": {"description": "Project risk score display", "placeholders": {"score": {"type": "double", "format": "decimalPattern"}}}, "riskAssessment": "Risk Assessment", "@riskAssessment": {"description": "Title for risk assessment section"}, "budgetOverview": "Budget Overview", "@budgetOverview": {"description": "Title for budget overview section"}, "keyInsights": "Key Insights", "@keyInsights": {"description": "Title for key insights section"}, "errorLoadingAnalysis": "Error Loading Analysis", "@errorLoadingAnalysis": {"description": "Error message when analysis fails to load"}, "retry": "Retry", "@retry": {"description": "Button text to retry an operation"}, "tapAnalyzeToGetInsights": "Tap analyze to get AI insights", "@tapAnalyzeToGetInsights": {"description": "Instruction text for analyzing project"}, "analyzeProject": "Analyze Project", "@analyzeProject": {"description": "Button text to analyze project"}, "aiAnalysisSummary": "AI Analysis Summary", "@aiAnalysisSummary": {"description": "Title for AI analysis summary section"}, "active": "Active", "@active": {"description": "Status text for active items"}, "inactive": "Inactive", "@inactive": {"description": "Status text for inactive items"}, "overBudget": "Over Budget", "@overBudget": {"description": "Label when project is over budget"}, "totalBudget": "Total Budget", "@totalBudget": {"description": "Label for total budget amount"}, "spent": "Spent", "@spent": {"description": "Label for spent amount"}, "remaining": "Remaining", "@remaining": {"description": "Label for remaining amount"}, "analysisDetails": "Analysis Details", "@analysisDetails": {"description": "Title for analysis details section"}, "confidenceLevelLabel": "Confidence Level", "@confidenceLevelLabel": {"description": "Label for confidence level"}, "analysisDate": "Analysis Date", "@analysisDate": {"description": "Label for analysis date"}, "predictedCompletion": "Predicted Co<PERSON>tion", "@predictedCompletion": {"description": "Label for predicted completion date"}, "critical": "Critical", "@critical": {"description": "Critical priority/severity level"}, "high": "High", "@high": {"description": "High priority/severity level"}, "medium": "Medium", "@medium": {"description": "Medium priority/severity level"}, "low": "Low", "@low": {"description": "Low priority/severity level"}, "justNow": "just now", "@justNow": {"description": "Time indicator for very recent events"}, "criticalPaths": "Critical Paths", "@criticalPaths": {"description": "Title for critical paths section"}, "error": "Error", "@error": {"description": "Generic error title"}, "noProjectsYet": "No Projects Yet", "@noProjectsYet": {"description": "Message when no projects exist"}, "connectProjectManagementPlatforms": "Connect your project management platforms to get started", "@connectProjectManagementPlatforms": {"description": "Instructions for connecting platforms"}, "projectPilotDashboard": "ProjectPilot Dashboard", "@projectPilotDashboard": {"description": "Title for ProjectPilot dashboard"}, "totalProjects": "Total Projects", "@totalProjects": {"description": "Label for total projects count"}, "recentProjects": "Recent Projects", "@recentProjects": {"description": "Title for recent projects section"}, "viewAll": "View All", "@viewAll": {"description": "Button text to view all items"}, "tasksOverview": "Tasks Overview", "@tasksOverview": {"description": "Title for tasks overview section"}, "recentTasks": "Recent Tasks", "@recentTasks": {"description": "Title for recent tasks section"}, "noTasksFound": "No tasks found", "@noTasksFound": {"description": "Message when no tasks are found"}, "inProgress": "In Progress", "@inProgress": {"description": "Status for tasks in progress"}, "blocked": "Blocked", "@blocked": {"description": "Status for blocked tasks"}, "overdue": "Overdue", "@overdue": {"description": "Status for overdue tasks"}, "toDo": "To Do", "@toDo": {"description": "Status for tasks to do"}, "review": "Review", "@review": {"description": "Status for tasks in review"}, "done": "Done", "@done": {"description": "Status for completed tasks"}, "cancelled": "Cancelled", "@cancelled": {"description": "Status for cancelled tasks"}, "progress": "Progress", "@progress": {"description": "Label for progress information"}, "createdAt": "Created At", "@createdAt": {"description": "Label for creation date"}, "deadline": "Deadline", "@deadline": {"description": "Label for deadline"}, "platform": "Platform", "@platform": {"description": "Label for platform information"}, "planning": "Planning", "@planning": {"description": "Project status - planning"}, "onHold": "On Hold", "@onHold": {"description": "Project status - on hold"}, "aiProjectAssistant": "AI Project Assistant", "@aiProjectAssistant": {"description": "Title for AI project assistant"}, "whatProjectsAreBehindSchedule": "What projects are behind schedule?", "@whatProjectsAreBehindSchedule": {"description": "Sample query for project assistant"}, "showMeProjectRisks": "Show me project risks", "@showMeProjectRisks": {"description": "Sample query for project assistant"}, "whichTasksAreBlocked": "Which tasks are blocked?", "@whichTasksAreBlocked": {"description": "Sample query for project assistant"}, "projectProgressSummary": "Project progress summary", "@projectProgressSummary": {"description": "Sample query for project assistant"}, "teamWorkloadAnalysis": "Team workload analysis", "@teamWorkloadAnalysis": {"description": "Sample query for project assistant"}, "dismiss": "<PERSON><PERSON><PERSON>", "@dismiss": {"description": "Button text to dismiss a notification"}, "examplesForRole": "Examples for {role}", "@examplesForRole": {"description": "Title for role-based example prompts", "placeholders": {"role": {"type": "String", "example": "Developer"}}}, "tapToUse": "Tap to use", "@tapToUse": {"description": "Text to indicate user can tap prompt to use it"}, "roleDeveloper": "Developer", "@roleDeveloper": {"description": "Developer role display name"}, "roleProjectManager": "Project Manager", "@roleProjectManager": {"description": "Project Manager role display name"}, "roleProductOwner": "Product Owner", "@roleProductOwner": {"description": "Product Owner role display name"}, "roleQaTester": "QA Tester", "@roleQaTester": {"description": "QA Tester role display name"}, "roleCtoCeo": "CTO/CEO", "@roleCtoCeo": {"description": "CTO/CEO role display name"}, "roleAllRoles": "all roles", "@roleAllRoles": {"description": "Default text for all roles"}, "developerPrompt1": "What tasks are currently assigned to me?", "@developerPrompt1": {"description": "Developer role prompt 1"}, "developerPrompt2": "What's blocking my code review?", "@developerPrompt2": {"description": "Developer role prompt 2"}, "developerPrompt3": "What's coming up in the next sprint?", "@developerPrompt3": {"description": "Developer role prompt 3"}, "developerPrompt4": "Which bugs have highest priority?", "@developerPrompt4": {"description": "Developer role prompt 4"}, "projectManagerPrompt1": "What's delaying the current sprint?", "@projectManagerPrompt1": {"description": "Project Manager role prompt 1"}, "projectManagerPrompt2": "Which tasks are overdue?", "@projectManagerPrompt2": {"description": "Project Manager role prompt 2"}, "projectManagerPrompt3": "Who worked on the project last?", "@projectManagerPrompt3": {"description": "Project Manager role prompt 3"}, "projectManagerPrompt4": "How is the current project progress?", "@projectManagerPrompt4": {"description": "Project Manager role prompt 4"}, "productOwnerPrompt1": "What features are planned for the next release?", "@productOwnerPrompt1": {"description": "Product Owner role prompt 1"}, "productOwnerPrompt2": "What's the feedback on our latest update?", "@productOwnerPrompt2": {"description": "Product Owner role prompt 2"}, "productOwnerPrompt3": "Which user stories have highest priority?", "@productOwnerPrompt3": {"description": "Product Owner role prompt 3"}, "productOwnerPrompt4": "How is our product backlog evolving?", "@productOwnerPrompt4": {"description": "Product Owner role prompt 4"}, "qaTesterPrompt1": "What tests still need to be performed?", "@qaTesterPrompt1": {"description": "QA Tester role prompt 1"}, "qaTesterPrompt2": "Are there any open critical bugs?", "@qaTesterPrompt2": {"description": "QA Tester role prompt 2"}, "qaTesterPrompt3": "What's the current test coverage?", "@qaTesterPrompt3": {"description": "QA Tester role prompt 3"}, "qaTesterPrompt4": "Which features are ready for testing?", "@qaTesterPrompt4": {"description": "QA Tester role prompt 4"}, "ctoCeoPrompt1": "How is the project running overall?", "@ctoCeoPrompt1": {"description": "CTO/CEO role prompt 1"}, "ctoCeoPrompt2": "What's currently costing us the most time?", "@ctoCeoPrompt2": {"description": "CTO/CEO role prompt 2"}, "ctoCeoPrompt3": "Which teams need support?", "@ctoCeoPrompt3": {"description": "CTO/CEO role prompt 3"}, "ctoCeoPrompt4": "How are we doing with budget and timeline?", "@ctoCeoPrompt4": {"description": "CTO/CEO role prompt 4"}, "defaultPrompt1": "What tasks are on the agenda today?", "@defaultPrompt1": {"description": "Default role prompt 1"}, "defaultPrompt2": "What's the current project status?", "@defaultPrompt2": {"description": "Default role prompt 2"}, "defaultPrompt3": "What are the next important steps?", "@defaultPrompt3": {"description": "Default role prompt 3"}, "defaultPrompt4": "Are there any obstacles that need to be resolved?", "@defaultPrompt4": {"description": "Default role prompt 4"}, "analyzingProject": "Analyzing project...", "@analyzingProject": {"description": "Loading message while analyzing project"}, "suggestedActions": "Suggested Actions", "@suggestedActions": {"description": "Title for suggested actions"}}