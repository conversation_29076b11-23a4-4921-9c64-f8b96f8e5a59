// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get appTitle => 'ProjectPilot';

  @override
  String get homeTitle => 'Главная';

  @override
  String get settingsTitle => 'Настройки';

  @override
  String get welcome => 'Добро пожаловать';

  @override
  String get welcomeSubtitle => 'ProjectPilot – будущее управления проектами';

  @override
  String get languageTitle => 'Язык';

  @override
  String get englishLanguage => 'Английский';

  @override
  String get germanLanguage => 'Немецкий';

  @override
  String get russianLanguage => 'Русский';

  @override
  String get turkishLanguage => 'Турецкий';

  @override
  String get arabicLanguage => 'Арабский';

  @override
  String get saveButton => 'Сохранить';

  @override
  String get cancelButton => 'Отмена';

  @override
  String get errorOccurred => 'Произошла ошибка';

  @override
  String get tryAgain => 'Попробовать снова';

  @override
  String get selectLanguage => 'Выберите язык';

  @override
  String tokenBalance(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Баланс токенов: $countString';
  }

  @override
  String get tokenBalanceDescription =>
      'Tokens for AI text generation with GPT';

  @override
  String get transcriptionTitle => 'Транскрипция';

  @override
  String get startRecording => 'Начать запись';

  @override
  String get stopRecording => 'Остановить запись';

  @override
  String get noTranscriptionYet => 'Транскрипция отсутствует';

  @override
  String get shareTranscription => 'Поделиться транскрипцией';

  @override
  String get themeTitle => 'Тема';

  @override
  String get selectTheme => 'Выберите тему';

  @override
  String get systemTheme => 'Системная';

  @override
  String get lightTheme => 'Светлая';

  @override
  String get darkTheme => 'Тёмная';

  @override
  String get toggleTheme => 'Сменить тему';

  @override
  String get apiKeysSection => 'API-ключи';

  @override
  String get openaiApiKey => 'API-ключ OpenAI';

  @override
  String get whisperApiKey => 'API-ключ Whisper';

  @override
  String get apiKeyHint => 'Введите ваш API-ключ';

  @override
  String get tokenSection => 'Токены и использование';

  @override
  String remainingMinutes(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Осталось минут: $countString';
  }

  @override
  String get minutesBalanceDescription =>
      'Minutes available for audio transcription with Whisper';

  @override
  String get purchasePackages => 'Приобрести пакеты';

  @override
  String get buyTokens => 'Купить токены';

  @override
  String get starterPackage => 'Стартовый';

  @override
  String get proPackage => 'Профессиональный';

  @override
  String get businessPackage => 'Бизнес';

  @override
  String get ultimatePackage => 'Максимальный';

  @override
  String tokens(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return '$countString токенов';
  }

  @override
  String price(double price) {
    final intl.NumberFormat priceNumberFormat = intl
        .NumberFormat.simpleCurrency(locale: localeName, name: 'EUR');
    final String priceString = priceNumberFormat.format(price);

    return '€$priceString';
  }

  @override
  String get profileSection => 'Профиль';

  @override
  String get username => 'Имя пользователя';

  @override
  String get email => 'Эл. почта';

  @override
  String get notAvailable => 'Недоступно';

  @override
  String get logoutButton => 'Выйти';

  @override
  String get apiKeySaved => 'API-ключ успешно сохранен!';

  @override
  String get introScreen1Title => 'ИИ-Управляемая Проектная Аналитика';

  @override
  String get introScreen1Description =>
      'Держите красную нить в ваших проектах. Выявляйте проблемы заранее и держитесь курса с умными инсайтами.';

  @override
  String get introScreen2Title => 'Мульти-Платформенный Обзор Проектов';

  @override
  String get introScreen2Description =>
      'Соедините все ваши инструменты проектов в одной панели. ClickUp, Notion, Jira и другие - все синхронизировано.';

  @override
  String get introScreen3Title => 'Умное Обнаружение Проблем';

  @override
  String get introScreen3Description =>
      'Автоматически обнаруживайте препятствия, оценивайте риски и получайте действенные рекомендации для движения проектов.';

  @override
  String get introMainTitle => 'ProjectPilot';

  @override
  String get introMainSubtitle =>
      'Никогда больше не теряйте след ваших проектов';

  @override
  String get getStarted => 'Начать';

  @override
  String get skip => 'Пропустить';

  @override
  String get next => 'Далее';

  @override
  String get loginTitle => 'Добро пожаловать в ProjectPilot';

  @override
  String get loginSubtitle => 'Войдите, чтобы продолжить';

  @override
  String get continueWithGoogle => 'Продолжить с Google';

  @override
  String get continueWithApple => 'Продолжить с Apple';

  @override
  String get continueWithEmail => 'Продолжить с email';

  @override
  String get moreOptions => 'Больше вариантов';

  @override
  String get continueWithLinkedIn => 'Продолжить с LinkedIn';

  @override
  String get continueWithPhone => 'Продолжить с телефоном';

  @override
  String get continueWithAzure => 'Продолжить с Azure';

  @override
  String get continueWithNotion => 'Продолжить с Notion';

  @override
  String get password => 'Пароль';

  @override
  String get forgotPassword => 'Забыли пароль?';

  @override
  String get signIn => 'Войти';

  @override
  String get signUp => 'Регистрация';

  @override
  String get dontHaveAccount => 'Нет аккаунта?';

  @override
  String get alreadyHaveAccount => 'Уже есть аккаунт?';

  @override
  String get phoneNumber => 'Номер телефона';

  @override
  String get or => 'или';

  @override
  String get createAccountText => 'Создайте свой аккаунт ProjectPilot';

  @override
  String get usernameRequired => 'Необходимо указать имя пользователя';

  @override
  String get usernameMinLength =>
      'Имя пользователя должно содержать не менее 3 символов';

  @override
  String get emailRequired => 'Необходимо указать электронную почту';

  @override
  String get invalidEmail => 'Введите действительный адрес электронной почты';

  @override
  String get passwordRequired => 'Необходимо указать пароль';

  @override
  String get passwordMinLength => 'Пароль должен содержать не менее 6 символов';

  @override
  String get signUpSuccessful => 'Регистрация успешна!';

  @override
  String get onboarding => 'Заполните свой профиль';

  @override
  String get camera => 'Камера';

  @override
  String get gallery => 'Галерея';

  @override
  String get birthYear => 'Год рождения';

  @override
  String get selectBirthYear => 'Выберите год рождения';

  @override
  String get userRole => 'Роль пользователя';

  @override
  String get developer => 'Разработчик';

  @override
  String get projectManager => 'Проектный менеджер';

  @override
  String get private => 'Частное лицо';

  @override
  String get other => 'Другое';

  @override
  String get usagePurpose => 'Цель использования';

  @override
  String get tasks => 'Задачи';

  @override
  String get notes => 'Заметки';

  @override
  String get ideas => 'Идеи';

  @override
  String get connectPlatforms => 'Подключение платформ';

  @override
  String get platformsDescription => 'Подключитесь к вашим любимым платформам';

  @override
  String get connectMore => 'Подключить больше платформ позже';

  @override
  String get finish => 'Завершить';

  @override
  String get platformConnections => 'Подключения платформ';

  @override
  String connectToPlatform(String platform) {
    return 'Подключиться к $platform';
  }

  @override
  String get connected => 'Подключено';

  @override
  String get disconnected => 'Не подключено';

  @override
  String get disconnect => 'Отключить';

  @override
  String get connecting => 'Подключение...';

  @override
  String get authorizationFailed => 'Ошибка авторизации';

  @override
  String get connectionSuccessful => 'Подключение успешно';

  @override
  String get connectionFailed => 'Ошибка подключения';

  @override
  String integrationAvailableFor(String platform) {
    return 'Интеграция доступна для $platform';
  }

  @override
  String get allPlatforms => 'Все платформы';

  @override
  String get managePlatformConnections => 'Управление подключениями платформ';

  @override
  String get connectPlatformToSendTasks =>
      'Подключите ваши любимые платформы для отправки задач напрямую';

  @override
  String get cancel => 'Отмена';

  @override
  String get whatWouldYouLikeToDo => 'Что бы вы хотели сделать?';

  @override
  String get recordingInProgress => 'Идет запись...';

  @override
  String get processingYourVoice => 'Обработка вашего голоса...';

  @override
  String get whereToSave => 'Куда бы вы хотели сохранить это?';

  @override
  String get noPlatformsConnected => 'Нет подключенных платформ';

  @override
  String get newRecording => 'Новая запись';

  @override
  String get uncategorized => 'Без категории';

  @override
  String get preferences => 'Preferences';

  @override
  String get upgradePackage => 'Upgrade Package';

  @override
  String get infoAndSupport => 'Info & Support';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get platformConnected => 'Connected';

  @override
  String get platformNotConnected => 'Not connected';

  @override
  String get connect => 'Connect';

  @override
  String get upgradeMessage =>
      'Get more tokens and recording minutes by upgrading your package';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get faq => 'FAQ';

  @override
  String get contactSupport => 'Contact Support';

  @override
  String get aboutApp => 'About ProjectPilot';

  @override
  String get settingsLanguageSection => 'Language';

  @override
  String get appearanceAndBehavior => 'Appearance & Behavior';

  @override
  String get settingsInfoSection => 'Information & Support';

  @override
  String get helpAndSupport => 'Help & Support';

  @override
  String get privacyTermsFaq => 'Privacy, Terms, FAQ';

  @override
  String get settingsAbout => 'About';

  @override
  String get storageManagement => 'Storage Management';

  @override
  String get voiceRecordings => 'Voice Recordings';

  @override
  String usingStorage(String size) {
    return 'Using $size of storage';
  }

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get automaticAssignment => 'Automatic Assignment';

  @override
  String get whereToSendTask => 'Where should I send this?';

  @override
  String get selectPlatform => 'Select Platform';

  @override
  String get selectProject => 'Select Project / Notebook / Board';

  @override
  String get sendNow => 'Send Now';

  @override
  String get noProjectsFound => 'No projects found';

  @override
  String get taskSentSuccessfully => 'Task sent successfully';

  @override
  String get platformDetected => 'Platform detected from your voice';

  @override
  String get projectDetected => 'Project detected from your voice';

  @override
  String get intentDetected => 'Intent detected from your voice';

  @override
  String get editContent => 'Edit content';

  @override
  String get noConnectedPlatforms => 'No connected platforms';

  @override
  String get connectPlatformsInSettings =>
      'Connect platforms in settings to send tasks';

  @override
  String get newTask => 'New Task';

  @override
  String get newNote => 'New Note';

  @override
  String get newIdea => 'New Idea';

  @override
  String get edit => 'Редактировать';

  @override
  String get share => 'Поделиться';

  @override
  String get transcriptionResult => 'Результат транскрипции';

  @override
  String get detectedIntent => 'Обнаруженное намерение';

  @override
  String get detectedEntities => 'Обнаруженные сущности';

  @override
  String get originalTranscription => 'Оригинальная транскрипция';

  @override
  String get addConnection => 'Add Connection';

  @override
  String get noConnections => 'No Connections';

  @override
  String get addConnectionsDescription =>
      'Connect your favorite platforms to send tasks directly';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String confirmDeleteConnectionMessage(String platform) {
    return 'Are you sure you want to delete the connection to $platform?';
  }

  @override
  String get delete => 'Delete';

  @override
  String platformConnectedMessage(String platform) {
    return 'Connected to $platform';
  }

  @override
  String get speechHistoryTitle => 'Speech History';

  @override
  String get clearHistory => 'Clear History';

  @override
  String get tapMicToStart => 'Tap the microphone to start recording';

  @override
  String get suggestedPlatform => 'Suggested platform based on your speech:';

  @override
  String get confirm => 'Confirm';

  @override
  String get back => 'Back';

  @override
  String taskSent(String platform) {
    return 'Task sent to $platform';
  }

  @override
  String get iRecognized => 'I recognized:';

  @override
  String get targetPlatform => 'Target:';

  @override
  String get editBeforeSending => 'Edit before sending';

  @override
  String get selectCommandType => 'What would you like to create?';

  @override
  String get createTaskCommand => 'Create Task';

  @override
  String get addNoteCommand => 'Add Note';

  @override
  String get saveIdeaCommand => 'Save Idea';

  @override
  String get enterProjectName => 'Enter project name';

  @override
  String get selectProjectPrompt => 'Select Project';

  @override
  String get entries => 'Записи';

  @override
  String get entryDetails => 'Детали записи';

  @override
  String get addUpdate => 'Добавить обновление';

  @override
  String get updateDetails => 'Детали обновления';

  @override
  String get deleteUpdate => 'Удалить обновление';

  @override
  String get deleteUpdateConfirmation =>
      'Вы уверены, что хотите удалить это обновление?';

  @override
  String get deleteEntry => 'Удалить запись';

  @override
  String get deleteEntryConfirmation =>
      'Вы уверены, что хотите удалить эту запись и все её обновления?';

  @override
  String get entryCreated => 'Запись успешно создана';

  @override
  String get entryDeleted => 'Запись успешно удалена';

  @override
  String get entryUpdateAdded => 'Обновление успешно добавлено';

  @override
  String get noEntriesFound => 'Записи не найдены';

  @override
  String get createEntryPrompt => 'Создайте новую запись, нажав на кнопку +';

  @override
  String get createNewEntry => 'Create New Entry';

  @override
  String get title => 'Title';

  @override
  String get titleHint => 'Enter title here';

  @override
  String get description => 'Description';

  @override
  String get descriptionHint => 'Enter description here';

  @override
  String get time => 'Time';

  @override
  String get date => 'Date';

  @override
  String dateFormat(int month, int day, int year) {
    return '$month/$day/$year';
  }

  @override
  String get type => 'Тип';

  @override
  String get priority => 'Priority';

  @override
  String get priorityLow => 'Low';

  @override
  String get priorityMedium => 'Medium';

  @override
  String get priorityHigh => 'High';

  @override
  String get priorityUrgent => 'Urgent';

  @override
  String get destination => 'Destination';

  @override
  String get clickupPlatform => 'ClickUp';

  @override
  String get notionPlatform => 'Notion';

  @override
  String get asanaPlatform => 'Asana';

  @override
  String get mondayPlatform => 'Monday';

  @override
  String get jiraPlatform => 'Jira';

  @override
  String get trelloPlatform => 'Trello';

  @override
  String get create => 'Создать';

  @override
  String get pleaseEnterTitle => 'Please enter a title';

  @override
  String get all => 'Все';

  @override
  String get searchHint => 'Search';

  @override
  String get chats => 'Chats';

  @override
  String get newChat => 'New Chat';

  @override
  String get searchResults => 'Search Results';

  @override
  String get noResultsFound => 'No results found';

  @override
  String get projects => 'Projects';

  @override
  String get newFolder => 'New Folder';

  @override
  String get createNewFolder => 'Create New Folder';

  @override
  String get enterFolderName => 'Enter folder name';

  @override
  String get rename => 'Rename';

  @override
  String get renameChat => 'Rename Chat';

  @override
  String get renameFolder => 'Rename Folder';

  @override
  String get enterNewName => 'Enter new name';

  @override
  String get moveToFolder => 'Move to folder';

  @override
  String get deleteChat => 'Delete Chat';

  @override
  String get deleteFolder => 'Delete Folder';

  @override
  String get deleteChatConfirmation =>
      'Are you sure you want to delete this chat? This action cannot be undone.';

  @override
  String get deleteFolderConfirmation =>
      'Are you sure you want to delete this folder? This action cannot be undone.';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String daysAgo(int count) {
    return '$count days ago';
  }

  @override
  String get chatHistory => 'История чата';

  @override
  String get addNote => 'Add Note';

  @override
  String get pinMessage => 'Pin Message';

  @override
  String get bookmark => 'Bookmark';

  @override
  String get messageOptions => 'Message Options';

  @override
  String get messageDeleted => 'Message deleted';

  @override
  String get messagePinned => 'Message pinned';

  @override
  String get messageBookmarked => 'Message bookmarked';

  @override
  String get noteAdded => 'Note added';

  @override
  String get enterNote => 'Enter your note';

  @override
  String get addNoteToMessage => 'Add Note to Message';

  @override
  String get editMessage => 'Edit Message';

  @override
  String get enterMessage => 'Enter your message';

  @override
  String get messageMoved => 'Message moved to folder';

  @override
  String get selectFolder => 'Select Folder';

  @override
  String get folderCreated => 'Folder created';

  @override
  String get noFolders => 'No folders yet';

  @override
  String get createFolderPrompt => 'Create a folder to organize your chats';

  @override
  String get noChatsInFolder => 'No chats in this folder';

  @override
  String get addChatsToFolder => 'Add chats by dragging them here';

  @override
  String get noChatsYet => 'No conversations yet';

  @override
  String get startNewChatPrompt => 'Start a new chat to begin';

  @override
  String get noRecentItems => 'No recent items';

  @override
  String get recentItems => 'Recent Items';

  @override
  String get todaysTasks => 'Today\'s Tasks';

  @override
  String get tapToViewTasks => 'Tap to view your tasks';

  @override
  String get appDescription => 'Your smart voice-to-task assistant';

  @override
  String get offlineMode => 'Offline Mode';

  @override
  String get offlineModeDescription =>
      'You\'re currently offline. Some features may be limited.';

  @override
  String get syncPending => 'Sync Pending';

  @override
  String get syncComplete => 'Sync Complete';

  @override
  String get syncFailed => 'Sync Failed';

  @override
  String get retrySync => 'Retry Sync';

  @override
  String get continueInOfflineMode => 'Continue in Offline Mode';

  @override
  String get networkError => 'Network Error';

  @override
  String get checkConnection =>
      'Please check your internet connection and try again.';

  @override
  String get pause => 'Pause';

  @override
  String get resume => 'Resume';

  @override
  String get reset => 'Reset';

  @override
  String get transcriptionComplete => 'Transcription Complete';

  @override
  String get viewDetails => 'View Details';

  @override
  String get continueButton => 'Продолжить';

  @override
  String get collection => 'Collection';

  @override
  String get newest => 'Newest';

  @override
  String get oldest => 'Oldest';

  @override
  String get name => 'Name';

  @override
  String get category => 'Category';

  @override
  String get synced => 'Synced';

  @override
  String get unsynced => 'Unsynced';

  @override
  String get notSynced => 'Not Synced';

  @override
  String get search => 'Поиск';

  @override
  String get closeSearch => 'Close Search';

  @override
  String get listView => 'List View';

  @override
  String get gridView => 'Grid View';

  @override
  String get createFirstEntry => 'Create First Entry';

  @override
  String get noEntriesYet => 'No entries yet';

  @override
  String get createNewEntryDescription =>
      'Create your first task, note, or idea using the button below.';

  @override
  String get completed => 'Completed';

  @override
  String get noSearchResults => 'No search results found';

  @override
  String get clearSearch => 'Clear Search';

  @override
  String get unassignedEntriesWarning => 'Unassigned Entries';

  @override
  String unassignedEntriesDescription(int count) {
    return 'You have $count unassigned entries waiting to be assigned to a platform';
  }

  @override
  String get assignNow => 'Assign Now';

  @override
  String assignedEntries(int count) {
    return 'Assigned Entries ($count)';
  }

  @override
  String get searchEntries => 'Search entries...';

  @override
  String get archive => 'Archive';

  @override
  String get assign => 'Assign';

  @override
  String get entryArchived => 'Entry archived';

  @override
  String get undo => 'UNDO';

  @override
  String get restoreFunctionalityNotImplemented =>
      'Restore functionality not yet implemented';

  @override
  String assignedToPlatform(String platform) {
    return 'Assigned to $platform';
  }

  @override
  String get recordingFileNotFound =>
      'Recording file not found. It may have been deleted or moved.';

  @override
  String get errorPlayingRecording => 'Error playing recording';

  @override
  String get assignmentSuccessful => 'Assignment Successful';

  @override
  String assignedCategoryToPlatform(String category, String platform) {
    return '$category assigned to $platform';
  }

  @override
  String get myTasks => 'My Tasks';

  @override
  String get task => 'Задача';

  @override
  String get myIdeas => 'My Ideas';

  @override
  String get idea => 'Идея';

  @override
  String get myNotes => 'My Notes';

  @override
  String get note => 'Заметка';

  @override
  String get noTasksYet => 'No tasks yet';

  @override
  String get noIdeasYet => 'No ideas yet';

  @override
  String get noNotesYet => 'No notes yet';

  @override
  String get addNew => 'Add New';

  @override
  String get optimizedFor => 'Optimized for';

  @override
  String get optimizedContent => 'Optimized Content';

  @override
  String get contentOptimization => 'Content Optimization';

  @override
  String get platformAssignment => 'Platform Assignment';

  @override
  String get audioPlaybackError => 'Audio playback error';

  @override
  String get audioFileCorrupted =>
      'Unable to play the audio file. It may be corrupted.';

  @override
  String get loadingAudioPlayer => 'Loading audio player...';

  @override
  String get optimizingFor => 'Optimizing for';

  @override
  String get choosePlatformToAssign => 'Choose Platform to Assign';

  @override
  String get assigningTo => 'Assigning to';

  @override
  String get autoAssignmentEnabled =>
      'Auto-assignment is enabled. This item will be automatically processed.';

  @override
  String get deleteRecordingConfirmation =>
      'Are you sure you want to delete this recording? This action cannot be undone.';

  @override
  String get deletingRecording => 'Deleting recording...';

  @override
  String get save => 'Сохранить';

  @override
  String get created => 'Создано';

  @override
  String get content => 'Содержание';

  @override
  String get createEntry => 'Создать запись';

  @override
  String get taskReminder => 'Task Reminder';

  @override
  String get selectTime => 'Select time';

  @override
  String get changeTime => 'Change Time';

  @override
  String get noReminder => 'No Reminder';

  @override
  String get editEntry => 'Редактировать запись';

  @override
  String get originalContent => 'Original Content';

  @override
  String get textEntry => 'Text Entry';

  @override
  String get voiceEntry => 'Voice Entry';

  @override
  String get optimizeAs => 'Optimize as';

  @override
  String get optimizeAsTaskDescription =>
      'Convert content into an actionable task with clear steps and deadlines';

  @override
  String get optimizeAsIdeaDescription =>
      'Structure content as a creative idea with potential applications and benefits';

  @override
  String get optimizeAsNoteDescription =>
      'Format content as a well-structured note with key points and references';

  @override
  String get optimizationInProgress =>
      'Using AI to enhance and structure your content';

  @override
  String get autoAssignment => 'Auto Assignment';

  @override
  String get autoAssignmentInfo =>
      'Content will be automatically analyzed and assigned to the most appropriate connected platform';

  @override
  String get configureAutoAssignment => 'Configure Auto Assignment';

  @override
  String get navigateToSettings => 'Opening Settings...';

  @override
  String get manageConnections => 'Manage Connections';

  @override
  String get platformConnectionsManagement =>
      'Opening platform connections management...';

  @override
  String get successfullyAssignedTo => 'Successfully assigned to';

  @override
  String get recording => 'Recording';

  @override
  String get recordingPaused => 'Paused';

  @override
  String get resetRecording => 'Reset Recording?';

  @override
  String get resetRecordingConfirmation =>
      'Are you sure you want to reset this recording? This action cannot be undone.';

  @override
  String get recordingGuideTitle => 'How it works';

  @override
  String get recordingGuideStep1 => 'Record your voice note';

  @override
  String get recordingGuideStep2 =>
      'ProjectPilot magically transcribes and enhances it';

  @override
  String get recordingGuideStep3 => 'Send it to your favorite platform';

  @override
  String get noInternetConnection => 'No internet connection';

  @override
  String get processingPurchase => 'Processing your purchase...';

  @override
  String get choosePackage => 'Choose a Package';

  @override
  String get packageSelectionSubtitle =>
      'Get more tokens to use for AI text generation and audio transcription';

  @override
  String purchaseSuccessful(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    return 'Successfully purchased $countString tokens!';
  }

  @override
  String get sendToThisPlatform => 'Send to this platform';

  @override
  String get choosePlatformTitle => 'Choose Platform';

  @override
  String get choosePlatformDescription =>
      'Choose a platform to dispatch your content to';

  @override
  String get contentPreview => 'Content Preview';

  @override
  String get suggested => 'Suggested';

  @override
  String get connectPlatform => 'Connect Platform';

  @override
  String get authenticating => 'Аутентификация...';

  @override
  String get createAccount => 'Создать аккаунт';

  @override
  String get emailFieldHint => 'Enter your email address';

  @override
  String get login => 'Войти';

  @override
  String get passwordFieldHint => 'Enter your password';

  @override
  String get confirmPassword => 'Подтвердите пароль';

  @override
  String get confirmPasswordHint => 'Подтвердите ваш пароль';

  @override
  String get confirmPasswordRequired => 'Пожалуйста, подтвердите ваш пароль';

  @override
  String get passwordsDoNotMatch => 'Пароли не совпадают';

  @override
  String get nameFieldHint => 'Введите ваше полное имя';

  @override
  String get history => 'History';

  @override
  String get record => 'Record';

  @override
  String get newEntry => 'New Entry';

  @override
  String get pleaseEnterContent => 'Please enter content';

  @override
  String get update => 'Update';

  @override
  String get noEntries => 'No entries';

  @override
  String get deleteAllAudioFiles => 'Delete All Audio Files';

  @override
  String get deleteAllAudioFilesContent =>
      'This will delete all voice recording files to free up storage space. The text content of your recordings will be preserved.\n\nThis action cannot be undone. Are you sure?';

  @override
  String get deleteAll => 'Delete All';

  @override
  String get deleteAllAudioFilesTooltip => 'Delete all audio files';

  @override
  String get selectLanguageDialogTitle => 'Select Language';

  @override
  String get enabled => 'Enabled';

  @override
  String get disabled => 'Disabled';

  @override
  String get autoAssignEnabledDesc =>
      'Tasks are automatically assigned to platforms';

  @override
  String get autoAssignDisabledDesc => 'Manual platform selection for tasks';

  @override
  String packageDescription(String name, int tokenAmount) {
    return '$name package with $tokenAmount tokens';
  }

  @override
  String aboutAppTitle(String appName) {
    return 'About $appName';
  }

  @override
  String appVersion(String version) {
    return 'Version $version';
  }

  @override
  String get keyFeatures => 'Key Features';

  @override
  String get voiceToTask => 'Voice-to-Task';

  @override
  String get voiceToTaskDesc => 'Create tasks using your voice';

  @override
  String get multiPlatform => 'Multi-Platform';

  @override
  String get multiPlatformDesc => 'Connect to ClickUp, Notion, and more';

  @override
  String get multiLanguage => 'Multi-Language';

  @override
  String get multiLanguageDesc =>
      'Supports English, German, Russian, Turkish, and Arabic';

  @override
  String get aiPowered => 'AI-Powered';

  @override
  String get aiPoweredDesc => 'Smart task categorization and optimization';

  @override
  String get ourTeam => 'Our Team';

  @override
  String copyright(String year) {
    return '© $year ProjectPilot. All rights reserved.';
  }

  @override
  String get contactSupportDesc =>
      'Have a question or need help? Fill out the form below and our support team will get back to you as soon as possible.';

  @override
  String get nameField => 'Name';

  @override
  String get emailField => 'Email';

  @override
  String get messageField => 'Message';

  @override
  String get messageFieldHint => 'Enter your message';

  @override
  String get submitButton => 'Submit';

  @override
  String get nameValidationError => 'Please enter your name';

  @override
  String get emailValidationError => 'Please enter a valid email';

  @override
  String get messageValidationError => 'Please enter your message';

  @override
  String get initializationError => 'Initialization Error';

  @override
  String errorDuringInit(String error) {
    return 'An error occurred during app initialization:\n$error';
  }

  @override
  String get retryButton => 'Retry';

  @override
  String get faqTitle => 'FAQ';

  @override
  String get frequentlyAskedQuestions => 'Frequently Asked Questions';

  @override
  String get privacyPolicyTitle => 'Privacy Policy';

  @override
  String lastUpdated(String time) {
    return 'Last updated: $time';
  }

  @override
  String get privacyPolicyIntro =>
      'This Privacy Policy describes how ProjectPilot (\"we\", \"us\", or \"our\") collects, uses, and discloses your personal information when you use our mobile application (the \"App\").';

  @override
  String get infoWeCollect => 'Information We Collect';

  @override
  String get infoWeCollectContent =>
      'We collect information that you provide directly to us, such as when you create an account, update your profile, use the interactive features of our App, request customer support, or otherwise communicate with us.';

  @override
  String get howWeUseInfo => 'How We Use Your Information';

  @override
  String get howWeUseInfoContent =>
      'We use the information we collect to provide, maintain, and improve our services, including to process transactions, send you related information, and provide customer support.';

  @override
  String get sharingOfInfo => 'Sharing of Information';

  @override
  String get sharingOfInfoContent =>
      'We may share the information we collect as follows: with third-party vendors, consultants, and other service providers who need access to such information to carry out work on our behalf; in response to a request for information if we believe disclosure is in accordance with any applicable law, regulation, or legal process.';

  @override
  String get yourChoices => 'Your Choices';

  @override
  String get yourChoicesContent =>
      'You may update, correct, or delete your account information at any time by logging into your account or contacting us. You may opt out of receiving promotional communications from us by following the instructions in those communications.';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get contactUsPrivacyContent =>
      'If you have any questions about this Privacy Policy, please contact us at: <EMAIL>';

  @override
  String get termsOfServiceTitle => 'Terms of Service';

  @override
  String get termsOfServiceIntro =>
      'Please read these Terms of Service (\"Terms\", \"Terms of Service\") carefully before using the ProjectPilot mobile application (the \"Service\") operated by ProjectPilot (\"us\", \"we\", or \"our\").';

  @override
  String get acceptanceOfTerms => 'Acceptance of Terms';

  @override
  String get acceptanceOfTermsContent =>
      'By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.';

  @override
  String get useOfService => 'Use of the Service';

  @override
  String get useOfServiceContent =>
      'Our Service allows you to create, manage, and organize tasks and notes using voice commands. You are responsible for maintaining the confidentiality of your account and password and for restricting access to your computer or mobile device.';

  @override
  String get intellectualProperty => 'Intellectual Property';

  @override
  String get intellectualPropertyContent =>
      'The Service and its original content, features, and functionality are and will remain the exclusive property of ProjectPilot and its licensors. The Service is protected by copyright, trademark, and other laws.';

  @override
  String get termination => 'Termination';

  @override
  String get terminationContent =>
      'We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.';

  @override
  String get limitationOfLiability => 'Limitation of Liability';

  @override
  String get limitationOfLiabilityContent =>
      'In no event shall ProjectPilot, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.';

  @override
  String get changesToTerms => 'Changes to Terms';

  @override
  String get changesToTermsContent =>
      'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days\' notice prior to any new terms taking effect.';

  @override
  String get contactUsTermsContent =>
      'If you have any questions about these Terms, please contact us at: <EMAIL>';

  @override
  String get whatIsProjectPilot => 'What is ProjectPilot?';

  @override
  String get whatIsProjectPilotAnswer =>
      'ProjectPilot is a voice-to-task application that allows you to create tasks, notes, and ideas using voice commands. It can transcribe your voice and send the tasks to various platforms like ClickUp, Notion, and more.';

  @override
  String get howToConnectPlatforms => 'How do I connect to external platforms?';

  @override
  String get howToConnectPlatformsAnswer =>
      'Go to the Settings screen and navigate to the \"Platform Connections\" section. There you can connect to various platforms like ClickUp, Notion, and Monday by clicking the Connect button and following the authentication process.';

  @override
  String get whatLanguagesSupported => 'What languages are supported?';

  @override
  String get whatLanguagesSupportedAnswer =>
      'ProjectPilot currently supports English, German, Russian, Turkish, and Arabic. You can change the language in the Settings screen.';

  @override
  String get howTokensWork => 'How do tokens work?';

  @override
  String get howTokensWorkAnswer =>
      'Tokens are used for AI text generation and audio transcription. Each transcription and optimization consumes a certain number of tokens. You can purchase more tokens in the \"Upgrade Package\" section of the Settings screen.';

  @override
  String get canUseOffline => 'Can I use ProjectPilot offline?';

  @override
  String get canUseOfflineAnswer =>
      'Some features of ProjectPilot require an internet connection, such as sending tasks to external platforms and using online transcription services. However, basic voice recording can work offline.';

  @override
  String get howCustomizeProfile => 'How do I customize my profile?';

  @override
  String get howCustomizeProfileAnswer =>
      'You can update your profile information in the Settings screen under the \"Profile\" section.';

  @override
  String get isDataSecure => 'Is my data secure?';

  @override
  String get isDataSecureAnswer =>
      'Yes, we take data security seriously. Your data is encrypted and stored securely. We do not share your personal information with third parties without your consent. Please refer to our Privacy Policy for more details.';

  @override
  String get howCancelSubscription => 'How do I cancel my subscription?';

  @override
  String get howCancelSubscriptionAnswer =>
      'You can manage your subscription through your app store account (Google Play Store or Apple App Store). Go to the subscription management section of your app store and cancel the ProjectPilot subscription.';

  @override
  String get appTitleProjectPilot => 'ProjectPilot';

  @override
  String get projectDashboard => 'Project Dashboard';

  @override
  String get projectAnalysis => 'Анализ проекта';

  @override
  String get askAboutProject => 'Ask About Project';

  @override
  String get projectStatus => 'Project Status';

  @override
  String get blockers => 'Блокировщики';

  @override
  String get recommendations => 'Рекомендации';

  @override
  String get criticalPath => 'Critical Path';

  @override
  String get projectProgress => 'Project Progress';

  @override
  String get teamMembers => 'Team Members';

  @override
  String get budget => 'Budget';

  @override
  String get timeline => 'Timeline';

  @override
  String get risks => 'Risks';

  @override
  String get insights => 'Insights';

  @override
  String get analysisLoading => 'Analyzing project...';

  @override
  String get analysisFailed => 'Analysis failed. Please try again.';

  @override
  String get refreshProject => 'Refresh Project';

  @override
  String get projectOverdue => 'Project Overdue';

  @override
  String get projectOnTrack => 'Project On Track';

  @override
  String get projectAtRisk => 'Project At Risk';

  @override
  String get highPriorityBlockers => 'High Priority Blockers';

  @override
  String get urgentRecommendations => 'Urgent Recommendations';

  @override
  String daysRemaining(int count) {
    return '$count days remaining';
  }

  @override
  String tasksCompleted(int completed, int total) {
    return '$completed of $total tasks completed';
  }

  @override
  String budgetUtilization(double spent, double total) {
    final intl.NumberFormat spentNumberFormat = intl.NumberFormat.currency(
      locale: localeName,
    );
    final String spentString = spentNumberFormat.format(spent);
    final intl.NumberFormat totalNumberFormat = intl.NumberFormat.currency(
      locale: localeName,
    );
    final String totalString = totalNumberFormat.format(total);

    return 'Budget: $spentString of $totalString used';
  }

  @override
  String get queryProject => 'Ask me anything about this project...';

  @override
  String get processingQuery => 'Processing your question...';

  @override
  String get queryFailed =>
      'Could not process your question. Please try again.';

  @override
  String get projectNotFound => 'Project not found';

  @override
  String get syncingData => 'Syncing project data...';

  @override
  String get activeTasks => 'Active Tasks';

  @override
  String get completedTasks => 'Completed Tasks';

  @override
  String get blockedTasks => 'Blocked Tasks';

  @override
  String get overdueTasks => 'Overdue Tasks';

  @override
  String confidenceLevel(int level) {
    return 'Confidence: $level%';
  }

  @override
  String riskScore(double score) {
    final intl.NumberFormat scoreNumberFormat = intl
        .NumberFormat.decimalPattern(localeName);
    final String scoreString = scoreNumberFormat.format(score);

    return 'Risk Score: $scoreString/10';
  }

  @override
  String get riskAssessment => 'Оценка рисков';

  @override
  String get budgetOverview => 'Обзор бюджета';

  @override
  String get keyInsights => 'Ключевые выводы';

  @override
  String get errorLoadingAnalysis => 'Ошибка загрузки анализа';

  @override
  String get retry => 'Повторить';

  @override
  String get tapAnalyzeToGetInsights =>
      'Нажмите \"Анализ\" для получения AI-инсайтов';

  @override
  String get analyzeProject => 'Анализировать проект';

  @override
  String get aiAnalysisSummary => 'Сводка AI-анализа';

  @override
  String get active => 'Активный';

  @override
  String get inactive => 'Неактивный';

  @override
  String get overBudget => 'Превышение бюджета';

  @override
  String get totalBudget => 'Общий бюджет';

  @override
  String get spent => 'Потрачено';

  @override
  String get remaining => 'Остается';

  @override
  String get analysisDetails => 'Детали анализа';

  @override
  String get confidenceLevelLabel => 'Уровень уверенности';

  @override
  String get analysisDate => 'Дата анализа';

  @override
  String get predictedCompletion => 'Прогнозируемое завершение';

  @override
  String get critical => 'Критический';

  @override
  String get high => 'Высокий';

  @override
  String get medium => 'Средний';

  @override
  String get low => 'Низкий';

  @override
  String get justNow => 'только что';

  @override
  String get criticalPaths => 'Критические пути';

  @override
  String get error => 'Ошибка';

  @override
  String get noProjectsYet => 'Пока нет проектов';

  @override
  String get connectProjectManagementPlatforms =>
      'Подключите платформы управления проектами для начала работы';

  @override
  String get projectPilotDashboard => 'Панель управления ProjectPilot';

  @override
  String get totalProjects => 'Всего проектов';

  @override
  String get recentProjects => 'Недавние проекты';

  @override
  String get viewAll => 'Показать все';

  @override
  String get tasksOverview => 'Обзор задач';

  @override
  String get recentTasks => 'Недавние задачи';

  @override
  String get noTasksFound => 'Задачи не найдены';

  @override
  String get inProgress => 'В процессе';

  @override
  String get blocked => 'Заблокированная';

  @override
  String get overdue => 'Просрочено';

  @override
  String get toDo => 'К выполнению';

  @override
  String get review => 'На проверке';

  @override
  String get done => 'Выполнено';

  @override
  String get cancelled => 'Отменено';

  @override
  String get progress => 'Прогресс';

  @override
  String get createdAt => 'Создано';

  @override
  String get deadline => 'Крайний срок';

  @override
  String get platform => 'Платформа';

  @override
  String get planning => 'Планирование';

  @override
  String get onHold => 'Приостановлен';

  @override
  String get aiProjectAssistant => 'AI-помощник проекта';

  @override
  String get whatProjectsAreBehindSchedule =>
      'What projects are behind schedule?';

  @override
  String get showMeProjectRisks => 'Покажи мне риски проекта';

  @override
  String get whichTasksAreBlocked => 'Which tasks are blocked?';

  @override
  String get projectProgressSummary => 'Сводка прогресса проекта';

  @override
  String get teamWorkloadAnalysis => 'Анализ нагрузки команды';

  @override
  String get dismiss => 'Скрыть';

  @override
  String examplesForRole(String role) {
    return 'Примеры для $role';
  }

  @override
  String get tapToUse => 'Нажмите для использования';

  @override
  String get roleDeveloper => 'Разработчик';

  @override
  String get roleProjectManager => 'Проект-менеджер';

  @override
  String get roleProductOwner => 'Владелец продукта';

  @override
  String get roleQaTester => 'QA-тестировщик';

  @override
  String get roleCtoCeo => 'CTO/CEO';

  @override
  String get roleAllRoles => 'все роли';

  @override
  String get developerPrompt1 => 'Какие задачи мне сейчас назначены?';

  @override
  String get developerPrompt2 => 'Что блокирует мой код-ревью?';

  @override
  String get developerPrompt3 => 'Что запланировано на следующий спринт?';

  @override
  String get developerPrompt4 => 'Какие баги имеют высший приоритет?';

  @override
  String get projectManagerPrompt1 => 'Что задерживает текущий спринт?';

  @override
  String get projectManagerPrompt2 => 'Какие задачи просрочены?';

  @override
  String get projectManagerPrompt3 => 'Кто последним работал над проектом?';

  @override
  String get projectManagerPrompt4 => 'Каков текущий прогресс проекта?';

  @override
  String get productOwnerPrompt1 =>
      'Какие функции запланированы на следующий релиз?';

  @override
  String get productOwnerPrompt2 =>
      'Какие отзывы о нашем последнем обновлении?';

  @override
  String get productOwnerPrompt3 =>
      'Какие пользовательские истории имеют высший приоритет?';

  @override
  String get productOwnerPrompt4 => 'Как развивается наш продуктовый бэклог?';

  @override
  String get qaTesterPrompt1 => 'Какие тесты еще нужно провести?';

  @override
  String get qaTesterPrompt2 => 'Есть ли открытые критические баги?';

  @override
  String get qaTesterPrompt3 => 'Какое текущее покрытие тестами?';

  @override
  String get qaTesterPrompt4 => 'Какие функции готовы к тестированию?';

  @override
  String get ctoCeoPrompt1 => 'Как идет проект в целом?';

  @override
  String get ctoCeoPrompt2 => 'Что сейчас отнимает у нас больше всего времени?';

  @override
  String get ctoCeoPrompt3 => 'Какие команды нуждаются в поддержке?';

  @override
  String get ctoCeoPrompt4 => 'Как обстоят дела с бюджетом и сроками?';

  @override
  String get defaultPrompt1 => 'Какие задачи на сегодня?';

  @override
  String get defaultPrompt2 => 'Каков текущий статус проекта?';

  @override
  String get defaultPrompt3 => 'Какие следующие важные шаги?';

  @override
  String get defaultPrompt4 => 'Есть ли препятствия, которые нужно устранить?';

  @override
  String get analyzingProject => 'Анализ проекта...';

  @override
  String get suggestedActions => 'Предлагаемые действия';
}
