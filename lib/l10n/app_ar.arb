{"@@locale": "ar", "appTitle": "فويس بايلوت", "homeTitle": "الرئيسية", "settingsTitle": "الإعدادات", "welcome": "مرحباً", "@welcome": {"description": "Welcome text on splash screen"}, "welcomeSubtitle": "فويس بايلوت - مستقبل الإنتاجية", "@welcomeSubtitle": {"description": "Subtitle on splash screen"}, "languageTitle": "اللغة", "englishLanguage": "الإنجليزية", "germanLanguage": "الألمانية", "russianLanguage": "الروسية", "turkishLanguage": "التركية", "arabicLanguage": "العربية", "saveButton": "<PERSON><PERSON><PERSON>", "cancelButton": "إلغاء", "errorOccurred": "<PERSON><PERSON><PERSON>", "tryAgain": "حاول مرة أخرى", "selectLanguage": "اختر اللغة", "tokenBalance": "رصيد الرموز: {count}", "transcriptionTitle": "النسخ", "startRecording": "بدء التسجيل", "stopRecording": "<PERSON>ي<PERSON><PERSON><PERSON> التسجيل", "noTranscriptionYet": "لا يوجد نسخ بعد", "shareTranscription": "مشاركة النسخ", "themeTitle": "المظهر", "selectTheme": "اخت<PERSON> المظهر", "systemTheme": "افتراضي النظام", "lightTheme": "فاتح", "darkTheme": "دا<PERSON>ن", "toggleTheme": "تبديل المظهر", "apiKeysSection": "مفاتيح API", "openaiApiKey": "مفتاح API لـ OpenAI", "whisperApiKey": "مفتاح API لـ Whisper", "apiKeyHint": "أدخل مفتاح API الخاص بك", "tokenSection": "الرموز والاستخدام", "remainingMinutes": "الدقائق المتبقية: {count}", "purchasePackages": "شراء الباقات", "buyTokens": "شراء رموز", "starterPackage": "م<PERSON><PERSON><PERSON><PERSON>", "proPackage": "مح<PERSON><PERSON><PERSON>", "businessPackage": "أعمال", "ultimatePackage": "متميز", "tokens": "{count} <PERSON><PERSON><PERSON>", "price": "€{price}", "profileSection": "الملف الشخصي", "username": "اسم المستخدم", "email": "الب<PERSON>يد الإلكتروني", "notAvailable": "<PERSON>ير متوفر", "logoutButton": "تسجيل الخروج", "apiKeySaved": "تم حفظ مفتاح API بنجاح!", "introScreen1Title": "ذكاء المشاريع المدعوم بالذكاء الاصطناعي", "@introScreen1Title": {"description": "Title for first intro screen"}, "introScreen1Description": "احتفظ بالخيط الأحمر في مشاريعك. اكتشف المشاكل مبكراً وابق على المسار الصحيح بالرؤى الذكية.", "@introScreen1Description": {"description": "Description for first intro screen"}, "introScreen2Title": "نظرة عامة على المشاريع متعددة المنصات", "@introScreen2Title": {"description": "Title for second intro screen"}, "introScreen2Description": "اربط جميع أدوات مشاريعك في لوحة واحدة. ClickUp، Notion، Jira والمزيد - كلها متزامنة.", "@introScreen2Description": {"description": "Description for second intro screen"}, "introScreen3Title": "الكشف الذكي عن المشاكل", "@introScreen3Title": {"description": "Title for third intro screen"}, "introScreen3Description": "اكتشف العوائق تلقائياً، قيم المخاطر واحصل على توصيات قابلة للتنفيذ للحفاظ على تحرك مشاريعك.", "@introScreen3Description": {"description": "Description for third intro screen"}, "introMainTitle": "ProjectPilot", "@introMainTitle": {"description": "Main title for intro screens"}, "introMainSubtitle": "لن تفقد أبداً مسار مشاريعك مرة أخرى", "@introMainSubtitle": {"description": "Main subtitle for intro screens"}, "getStarted": "اب<PERSON><PERSON> الآن", "@getStarted": {"description": "Text for get started button"}, "skip": "تخطي", "@skip": {"description": "Text for skip button on intro screens"}, "next": "التالي", "@next": {"description": "Text for next button on intro screens"}, "loginTitle": "مرحبًا بك في ProjectPilot", "loginSubtitle": "سجل الدخول للمتابعة", "continueWithGoogle": "المتابعة باستخدام Google", "continueWithApple": "المتابعة باستخدام Apple", "continueWithEmail": "المتابعة باستخدام البريد الإلكتروني", "moreOptions": "خيا<PERSON>ات أكثر", "continueWithLinkedIn": "المتابعة باستخدام LinkedIn", "continueWithPhone": "المتابعة باستخدام الهاتف", "continueWithAzure": "المتابعة باستخدام Azure", "continueWithNotion": "المتابعة باستخدام Notion", "password": "كلمة المرور", "forgotPassword": "نسيت كلمة المرور؟", "signIn": "تسجيل الدخول", "signUp": "إنشاء حساب", "dontHaveAccount": "ليس لديك حساب؟", "alreadyHaveAccount": "لديك حساب بالفعل؟", "phoneNumber": "رقم الهاتف", "or": "أو", "createAccountText": "أنشئ حساب فويس بايلوت الخاص بك", "@createAccountText": {"description": "Subtitle text for sign-up screen"}, "usernameRequired": "اسم المستخدم مطلوب", "@usernameRequired": {"description": "Error message when username is empty"}, "usernameMinLength": "يجب أن يكون اسم المستخدم 3 أحرف على الأقل", "@usernameMinLength": {"description": "Error message when username is too short"}, "emailRequired": "البريد الإلكتروني مطلوب", "@emailRequired": {"description": "Error message when email is empty"}, "invalidEmail": "أ<PERSON><PERSON><PERSON> بريد إلكتروني صالح", "@invalidEmail": {"description": "Error message when email format is invalid"}, "passwordRequired": "كلمة المرور مطلوبة", "@passwordRequired": {"description": "Error message when password is empty"}, "passwordMinLength": "يجب أن تكون كلمة المرور 6 أحرف على الأقل", "@passwordMinLength": {"description": "Error message when password is too short"}, "signUpSuccessful": "تم التسجيل بنجاح!", "@signUpSuccessful": {"description": "Success message after signing up"}, "onboarding": "أكمل ملفك الشخصي", "@onboarding": {"description": "Title for the onboarding screen"}, "camera": "الكاميرا", "@camera": {"description": "Option for taking a photo with camera"}, "gallery": "المعرض", "@gallery": {"description": "Option for selecting a photo from gallery"}, "birthYear": "سنة الميلاد", "@birthYear": {"description": "Label for birth year selection"}, "selectBirthYear": "اختر سنة الميلاد", "@selectBirthYear": {"description": "Title for birth year picker"}, "userRole": "دور المستخدم", "@userRole": {"description": "Label for user role selection"}, "developer": "مطور", "@developer": {"description": "Developer role option"}, "projectManager": "مدير مشروع", "@projectManager": {"description": "Project Manager role option"}, "private": "خاص", "@private": {"description": "Private role option"}, "other": "أ<PERSON><PERSON><PERSON>", "@other": {"description": "Other option for selections"}, "usagePurpose": "الغرض من الاستخدام", "@usagePurpose": {"description": "Label for usage purpose selection"}, "tasks": "المهام", "@tasks": {"description": "Tasks purpose option"}, "ideas": "الأفكار", "@ideas": {"description": "Ideas purpose option"}, "notes": "الملاحظات", "@notes": {"description": "Notes purpose option"}, "connectPlatforms": "ربط المنصات", "@connectPlatforms": {"description": "Title for the platform connection screen"}, "platformsDescription": "اتصل بمنصاتك المفضلة", "@platformsDescription": {"description": "Description text for platform connection screen"}, "connectMore": "ربط المزيد من المنصات لاحقًا", "@connectMore": {"description": "Text for connecting more platforms in the future"}, "finish": "إنهاء", "@finish": {"description": "Text for finish button"}, "platformConnections": "اتصالات المنصات", "@platformConnections": {"description": "Title for the platform connections screen"}, "connectToPlatform": "الاتصال بـ {platform}", "@connectToPlatform": {"description": "Button text to connect to a specific platform", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "connected": "متصل", "@connected": {"description": "Status text when a platform is connected"}, "disconnected": "<PERSON>ير متصل", "@disconnected": {"description": "Status text when a platform is not connected"}, "disconnect": "قطع الاتصال", "@disconnect": {"description": "Button text to disconnect from a platform"}, "connecting": "جاري الاتصال...", "@connecting": {"description": "Status text when connecting to a platform"}, "authorizationFailed": "فشل التفويض", "@authorizationFailed": {"description": "Error message when platform authorization fails"}, "connectionSuccessful": "تم الاتصال بنجاح", "@connectionSuccessful": {"description": "Success message when platform connection is established"}, "connectionFailed": "فشل الاتصال", "@connectionFailed": {"description": "Error message when platform connection fails"}, "integrationAvailableFor": "التكامل متاح لـ {platform}", "@integrationAvailableFor": {"description": "Text indicating a platform integration is available", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "allPlatforms": "جميع المنصات", "@allPlatforms": {"description": "Text for showing all available platforms"}, "managePlatformConnections": "إدارة اتصالات المنصات", "@managePlatformConnections": {"description": "Description for the platform connections screen"}, "connectPlatformToSendTasks": "قم بتوصيل منصاتك المفضلة لإرسال المهام مباشرة", "@connectPlatformToSendTasks": {"description": "Explanation text for platform connections"}, "cancel": "إلغاء", "@cancel": {"description": "Text for cancel button"}, "continueButton": "متابعة", "@continueButton": {"description": "Text for continue button"}, "whatWouldYouLikeToDo": "ماذا تريد أن تفعل؟", "@whatWouldYouLikeToDo": {"description": "Prompt text for the recording screen"}, "recordingInProgress": "جاري التسجيل...", "@recordingInProgress": {"description": "Text shown while recording is in progress"}, "processingYourVoice": "جاري معالجة صوتك...", "@processingYourVoice": {"description": "Text shown while transcribing audio"}, "whereToSave": "أين تريد حفظ هذا؟", "@whereToSave": {"description": "Prompt for selecting a platform to save to"}, "noPlatformsConnected": "لا توجد منصات متصلة بعد", "@noPlatformsConnected": {"description": "Text shown when no platforms are connected"}, "newRecording": "تسجيل جديد", "@newRecording": {"description": "<PERSON><PERSON> text to start a new recording"}, "task": "مهمة", "@task": {"description": "Label for task category"}, "note": "ملاحظة", "@note": {"description": "Label for note category"}, "idea": "فكرة", "@idea": {"description": "Label for idea category"}, "uncategorized": "غير مصنف", "@uncategorized": {"description": "Label for uncategorized items"}, "edit": "تعديل", "@edit": {"description": "Text for edit button"}, "share": "مشاركة", "@share": {"description": "Text for share button"}, "transcriptionResult": "نتيجة النسخ", "@transcriptionResult": {"description": "Title for transcription result"}, "detectedIntent": "النية المكتشفة", "@detectedIntent": {"description": "Label for detected intent"}, "detectedEntities": "الكيانات المكتشفة", "@detectedEntities": {"description": "Label for detected entities"}, "originalTranscription": "النسخ الأصلي", "@originalTranscription": {"description": "Label for original transcription"}, "entries": "الإدخالات", "@entries": {"description": "Title for entries screen"}, "entryDetails": "تفاصيل الإدخال", "@entryDetails": {"description": "Title for entry details screen"}, "addUpdate": "إضافة تحديث", "@addUpdate": {"description": "Button text to add an update to an entry"}, "updateDetails": "تفاصيل التحديث", "@updateDetails": {"description": "Title for update details dialog"}, "deleteUpdate": "حذ<PERSON> التحديث", "@deleteUpdate": {"description": "Title for delete update confirmation dialog"}, "deleteUpdateConfirmation": "هل أنت متأكد أنك تريد حذف هذا التحديث؟", "@deleteUpdateConfirmation": {"description": "Confirmation message for deleting an update"}, "deleteEntry": "<PERSON>ذ<PERSON> الإدخال", "@deleteEntry": {"description": "Title for delete entry confirmation dialog"}, "deleteEntryConfirmation": "هل أنت متأكد أنك تريد حذف هذا الإدخال وجميع تحديثاته؟", "@deleteEntryConfirmation": {"description": "Confirmation message for deleting an entry"}, "entryCreated": "تم إنشاء الإدخال بنجاح", "@entryCreated": {"description": "Success message when an entry is created"}, "entryDeleted": "تم حذف الإدخال بنجاح", "@entryDeleted": {"description": "Success message when an entry is deleted"}, "entryUpdateAdded": "تمت إضافة التحديث بنجاح", "@entryUpdateAdded": {"description": "Success message when an update is added to an entry"}, "noEntriesFound": "لم يتم العثور على إدخالات", "@noEntriesFound": {"description": "Message when no entries are found"}, "createEntryPrompt": "قم بإنشاء إدخا<PERSON> جديد بالنقر على زر +", "@createEntryPrompt": {"description": "Prompt to create a new entry"}, "createEntry": "إنشاء إدخال", "@createEntry": {"description": "Button text to create a new entry"}, "content": "المحتوى", "@content": {"description": "Label for content input field"}, "type": "النوع", "@type": {"description": "Label for type selection"}, "create": "إنشاء", "@create": {"description": "Button text to create something"}, "search": "ب<PERSON><PERSON>", "@search": {"description": "Button text for search"}, "close": "إغلاق", "@close": {"description": "Button text to close a dialog or screen"}, "save": "<PERSON><PERSON><PERSON>", "@save": {"description": "Button text to save changes"}, "editEntry": "تعديل الإدخال", "@editEntry": {"description": "Title for edit entry dialog"}, "sharingNotImplemented": "ميزة المشاركة غير متوفرة بعد", "@sharingNotImplemented": {"description": "Message when sharing functionality is not implemented"}, "updates": "التحديثات", "@updates": {"description": "Label for updates section"}, "voice": "صوت", "@voice": {"description": "Label for voice source type"}, "manual": "يدوي", "@manual": {"description": "Label for manual source type"}, "addedAt": "أضيف في", "@addedAt": {"description": "Label for when an update was added"}, "source": "المصدر", "@source": {"description": "Label for source of an update"}, "created": "تم الإنشاء", "@created": {"description": "Label for creation date"}, "selectEntry": "اختر الإدخال", "@selectEntry": {"description": "Title for entry selection dialog"}, "entryReferenceDetected": "اكتشفت إشارة إلى إدخال موجود", "@entryReferenceDetected": {"description": "Message when a reference to an existing entry is detected"}, "selectEntryToUpdate": "اختر الإدخال الذي تريد تحديثه", "@selectEntryToUpdate": {"description": "Prompt to select an entry to update"}, "createNewInstead": "إنشاء جديد بدلاً من ذلك", "@createNewInstead": {"description": "Button text to create a new entry instead of updating an existing one"}, "creatingNewEntry": "جاري إنشاء إدخال جديد بدلاً من ذلك", "@creatingNewEntry": {"description": "Message when creating a new entry instead of updating an existing one"}, "all": "الكل", "@all": {"description": "Label for showing all items"}, "chatHistory": "سجل المحادثة", "@chatHistory": {"description": "Title for the chat history section in recording view"}, "tokenBalanceDescription": "Tokens for AI text generation with GPT", "@tokenBalanceDescription": {"description": "Description of token usage for AI text generation"}, "minutesBalanceDescription": "Minutes available for audio transcription with <PERSON><PERSON><PERSON>", "@minutesBalanceDescription": {"description": "Description of remaining recording minutes"}, "preferences": "Preferences", "@preferences": {"description": "Label for preferences"}, "upgradePackage": "Upgrade Package", "@upgradePackage": {"description": "Button text to upgrade package"}, "infoAndSupport": "Info & Support", "@infoAndSupport": {"description": "Title for info and support section"}, "lightMode": "Light Mode", "@lightMode": {"description": "Label for light mode option"}, "platformConnected": "Connected", "@platformConnected": {"description": "Label for connected platform"}, "platformNotConnected": "Not connected", "@platformNotConnected": {"description": "Label for not connected platform"}, "connect": "Connect", "@connect": {"description": "Button text to connect to a platform"}, "upgradeMessage": "Get more tokens and recording minutes by upgrading your package", "@upgradeMessage": {"description": "Message encouraging users to upgrade their package"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Link text for privacy policy"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Link text for terms of service"}, "faq": "FAQ", "@faq": {"description": "Link text for FAQ"}, "contactSupport": "Contact Support", "@contactSupport": {"description": "Link text for contact support"}, "aboutApp": "About ProjectPilot", "@aboutApp": {"description": "Link text for about app"}, "settingsLanguageSection": "Language", "@settingsLanguageSection": {"description": "Title for the language section in settings"}, "appearanceAndBehavior": "Appearance & Behavior", "@appearanceAndBehavior": {"description": "Title for the appearance and behavior section in settings"}, "settingsInfoSection": "Information & Support", "@settingsInfoSection": {"description": "Title for the information and support section in settings"}, "helpAndSupport": "Help & Support", "@helpAndSupport": {"description": "Title for help and support option in settings"}, "privacyTermsFaq": "Privacy, Terms, FAQ", "@privacyTermsFaq": {"description": "Subtitle for help and support option in settings"}, "settingsAbout": "About", "@settingsAbout": {"description": "Title for about option in settings"}, "storageManagement": "Storage Management", "@storageManagement": {"description": "Title for storage management section in settings"}, "voiceRecordings": "Voice Recordings", "@voiceRecordings": {"description": "Title for voice recordings option in storage management"}, "usingStorage": "Using {size} of storage", "@usingStorage": {"description": "Text showing storage usage", "placeholders": {"size": {"type": "String", "example": "10MB"}}}, "darkMode": "Dark Mode", "@darkMode": {"description": "Title for dark mode option in settings"}, "automaticAssignment": "Automatic Assignment", "@automaticAssignment": {"description": "Title for automatic assignment option in settings"}, "whereToSendTask": "Where should I send this?", "@whereToSendTask": {"description": "Title for the task destination selector"}, "selectPlatform": "Select Platform", "@selectPlatform": {"description": "Label for platform selection"}, "selectProject": "Select Project / Notebook / Board", "@selectProject": {"description": "Label for project selection"}, "sendNow": "Send Now", "@sendNow": {"description": "But<PERSON> text to send the task"}, "noProjectsFound": "No projects found", "@noProjectsFound": {"description": "Message when no projects are found"}, "taskSentSuccessfully": "Task sent successfully", "@taskSentSuccessfully": {"description": "Success message when task is sent"}, "platformDetected": "Platform detected from your voice", "@platformDetected": {"description": "Message shown when platform is detected from voice"}, "projectDetected": "Project detected from your voice", "@projectDetected": {"description": "Message shown when project is detected from voice"}, "intentDetected": "Intent detected from your voice", "@intentDetected": {"description": "Message shown when intent is detected from voice"}, "editContent": "Edit content", "@editContent": {"description": "Tooltip for edit button"}, "noConnectedPlatforms": "No connected platforms", "@noConnectedPlatforms": {"description": "Message when no platforms are connected"}, "connectPlatformsInSettings": "Connect platforms in settings to send tasks", "@connectPlatformsInSettings": {"description": "Message instructing user to connect platforms"}, "newTask": "New Task", "@newTask": {"description": "Title for new task screen"}, "newNote": "New Note", "@newNote": {"description": "Title for new note screen"}, "newIdea": "New Idea", "@newIdea": {"description": "Title for new idea screen"}, "addConnection": "Add Connection", "@addConnection": {"description": "Text for add connection button"}, "noConnections": "No Connections", "@noConnections": {"description": "Text shown when no connections are available"}, "addConnectionsDescription": "Connect your favorite platforms to send tasks directly", "@addConnectionsDescription": {"description": "Description for adding connections"}, "confirmDelete": "Confirm Delete", "@confirmDelete": {"description": "Title for delete confirmation dialog"}, "confirmDeleteConnectionMessage": "Are you sure you want to delete the connection to {platform}?", "@confirmDeleteConnectionMessage": {"description": "Message for delete connection confirmation dialog", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "delete": "Delete", "@delete": {"description": "Label for delete action"}, "platformConnectedMessage": "Connected to {platform}", "@platformConnectedMessage": {"description": "Message shown when platform is connected", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "speechHistoryTitle": "Speech History", "@speechHistoryTitle": {"description": "Title for the speech history screen"}, "clearHistory": "Clear History", "@clearHistory": {"description": "Button text to clear conversation history"}, "tapMicToStart": "Tap the microphone to start recording", "@tapMicToStart": {"description": "Instruction text for starting recording"}, "suggestedPlatform": "Suggested platform based on your speech:", "@suggestedPlatform": {"description": "Label for suggested platform"}, "confirm": "Confirm", "@confirm": {"description": "Text for confirm button"}, "back": "Back", "@back": {"description": "Text for back button"}, "taskSent": "Task sent to {platform}", "@taskSent": {"description": "Message shown when task is sent to a platform", "placeholders": {"platform": {"type": "String", "example": "ClickUp"}}}, "iRecognized": "I recognized:", "@iRecognized": {"description": "Text for AI recognition message"}, "targetPlatform": "Target:", "@targetPlatform": {"description": "Label for target platform"}, "editBeforeSending": "Edit before sending", "@editBeforeSending": {"description": "Button text to edit content before sending"}, "selectCommandType": "What would you like to create?", "@selectCommandType": {"description": "Title for command type selection prompt"}, "createTaskCommand": "Create Task", "@createTaskCommand": {"description": "Button text to create a task"}, "addNoteCommand": "Add Note", "@addNoteCommand": {"description": "Button text to add a note"}, "saveIdeaCommand": "Save Idea", "@saveIdeaCommand": {"description": "<PERSON><PERSON> text to save an idea"}, "enterProjectName": "Enter project name", "@enterProjectName": {"description": "Hint text for project name input"}, "selectProjectPrompt": "Select Project", "@selectProjectPrompt": {"description": "Title for project selection prompt"}, "createNewEntry": "Create New Entry", "@createNewEntry": {"description": "Title for adding a new entry"}, "title": "Title", "@title": {"description": "Label for title field"}, "titleHint": "Enter title here", "@titleHint": {"description": "Hint for title input field"}, "description": "Description", "@description": {"description": "Label for description field"}, "descriptionHint": "Enter description here", "@descriptionHint": {"description": "Hint for description input field"}, "time": "Time", "@time": {"description": "Label for time field"}, "date": "Date", "@date": {"description": "Label for date field"}, "authenticating": "جارِ المصادقة...", "@authenticating": {"description": "Text shown during authentication process"}, "createAccount": "إنشاء حساب", "@createAccount": {"description": "Button text for creating a new account"}, "login": "تسجيل الدخول", "@login": {"description": "Login button text"}, "confirmPassword": "تأكيد كلمة المرور", "@confirmPassword": {"description": "Label for confirm password field"}, "confirmPasswordHint": "أك<PERSON> كلمة المرور الخاصة بك", "@confirmPasswordHint": {"description": "Hint text for confirm password field"}, "confirmPasswordRequired": "يرجى تأكيد كلمة المرور الخاصة بك", "@confirmPasswordRequired": {"description": "Error message when confirm password is empty"}, "passwordsDoNotMatch": "كلمات المرور غير متطابقة", "@passwordsDoNotMatch": {"description": "Error message when passwords do not match"}, "nameFieldHint": "أد<PERSON>ل اسمك الكامل", "@nameFieldHint": {"description": "Hint text for name input field"}, "riskAssessment": "تقييم المخاطر", "@riskAssessment": {"description": "Title for risk assessment section"}, "budgetOverview": "نظرة عامة على الميزانية", "@budgetOverview": {"description": "Title for budget overview section"}, "keyInsights": "الرؤى الرئيسية", "@keyInsights": {"description": "Title for key insights section"}, "errorLoadingAnalysis": "خطأ في تحميل التحليل", "@errorLoadingAnalysis": {"description": "Error message when analysis fails to load"}, "retry": "إعادة المحاولة", "@retry": {"description": "Button text to retry an operation"}, "tapAnalyzeToGetInsights": "اضغط على التحليل للحصول على رؤى الذكاء الاصطناعي", "@tapAnalyzeToGetInsights": {"description": "Instruction text for analyzing project"}, "analyzeProject": "تحليل المشروع", "@analyzeProject": {"description": "Button text to analyze project"}, "aiAnalysisSummary": "ملخص تحليل الذكاء الاصطناعي", "@aiAnalysisSummary": {"description": "Title for AI analysis summary section"}, "active": "نشط", "@active": {"description": "Status text for active items"}, "inactive": "غير نشط", "@inactive": {"description": "Status text for inactive items"}, "overBudget": "تجاوز الميزانية", "@overBudget": {"description": "Label when project is over budget"}, "totalBudget": "إجمالي الميزانية", "@totalBudget": {"description": "Label for total budget amount"}, "spent": "مُنفق", "@spent": {"description": "Label for spent amount"}, "remaining": "متبقي", "@remaining": {"description": "Label for remaining amount"}, "analysisDetails": "تفاصيل التحليل", "@analysisDetails": {"description": "Title for analysis details section"}, "confidenceLevelLabel": "مستوى الثقة", "@confidenceLevelLabel": {"description": "Label for confidence level"}, "analysisDate": "تاريخ التحليل", "@analysisDate": {"description": "Label for analysis date"}, "predictedCompletion": "الإنجاز المتوقع", "@predictedCompletion": {"description": "Label for predicted completion date"}, "critical": "<PERSON><PERSON><PERSON>", "@critical": {"description": "Critical priority/severity level"}, "high": "عالي", "@high": {"description": "High priority/severity level"}, "medium": "متوسط", "@medium": {"description": "Medium priority/severity level"}, "low": "من<PERSON><PERSON>ض", "@low": {"description": "Low priority/severity level"}, "justNow": "الآن", "@justNow": {"description": "Time indicator for very recent events"}, "blockers": "العوائق", "@blockers": {"description": "Title for blockers section"}, "criticalPaths": "المسارات الحرجة", "@criticalPaths": {"description": "Title for critical paths section"}, "error": "خطأ", "@error": {"description": "Generic error title"}, "noProjectsYet": "لا توجد مشاريع بعد", "@noProjectsYet": {"description": "Message when no projects exist"}, "connectProjectManagementPlatforms": "اربط منصات إدارة المشاريع الخاصة بك للبدء", "@connectProjectManagementPlatforms": {"description": "Instructions for connecting platforms"}, "projectPilotDashboard": "لوحة تحكم ProjectPilot", "@projectPilotDashboard": {"description": "Title for ProjectPilot dashboard"}, "totalProjects": "إجمالي المشاريع", "@totalProjects": {"description": "Label for total projects count"}, "completed": "مكتمل", "@completed": {"description": "Status for completed items"}, "recentProjects": "المشاريع الحديثة", "@recentProjects": {"description": "Title for recent projects section"}, "viewAll": "عر<PERSON> الكل", "@viewAll": {"description": "Button text to view all items"}, "tasksOverview": "نظرة عامة على المهام", "@tasksOverview": {"description": "Title for tasks overview section"}, "recentTasks": "المهام الحديثة", "@recentTasks": {"description": "Title for recent tasks section"}, "noTasksFound": "لم يتم العثور على مهام", "@noTasksFound": {"description": "Message when no tasks are found"}, "inProgress": "قيد التنفيذ", "@inProgress": {"description": "Status for tasks in progress"}, "blocked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@blocked": {"description": "Status for blocked tasks"}, "overdue": "متأخر", "@overdue": {"description": "Status for overdue tasks"}, "toDo": "للقيام", "@toDo": {"description": "Status for tasks to do"}, "review": "مراجعة", "@review": {"description": "Status for tasks in review"}, "done": "تم", "@done": {"description": "Status for completed tasks"}, "cancelled": "ملغي", "@cancelled": {"description": "Status for cancelled tasks"}, "progress": "التقدم", "@progress": {"description": "Label for progress information"}, "createdAt": "تم الإنشاء في", "@createdAt": {"description": "Label for creation date"}, "deadline": "الموعد النهائي", "@deadline": {"description": "Label for deadline"}, "platform": "المنصة", "@platform": {"description": "Label for platform information"}, "planning": "التخطيط", "@planning": {"description": "Project status - planning"}, "onHold": "معلق", "@onHold": {"description": "Project status - on hold"}, "aiProjectAssistant": "مساعد المشروع بالذكاء الاصطناعي", "@aiProjectAssistant": {"description": "Title for AI project assistant"}, "@tryAgain": {"description": "<PERSON><PERSON> text to try again"}, "whatProjectsAreBehindSchedule": "ما هي المشاريع المتأخرة عن الجدول الزمني؟", "@whatProjectsAreBehindSchedule": {"description": "Sample query for project assistant"}, "showMeProjectRisks": "أظهر لي مخاطر المشاريع", "@showMeProjectRisks": {"description": "Sample query for project assistant"}, "whichTasksAreBlocked": "ما هي المهام المحجوبة؟", "@whichTasksAreBlocked": {"description": "Sample query for project assistant"}, "projectProgressSummary": "ملخص تقدم المشروع", "@projectProgressSummary": {"description": "Sample query for project assistant"}, "teamWorkloadAnalysis": "تحليل عبء عمل الفريق", "@teamWorkloadAnalysis": {"description": "Sample query for project assistant"}, "dismiss": "تجاهل", "@dismiss": {"description": "Button text to dismiss a notification"}, "examplesForRole": "أمثلة لـ {role}", "@examplesForRole": {"description": "Title for role-based example prompts", "placeholders": {"role": {"type": "String", "example": "Developer"}}}, "tapToUse": "اضغط للاستخدام", "@tapToUse": {"description": "Text to indicate user can tap prompt to use it"}, "roleDeveloper": "مطور", "@roleDeveloper": {"description": "Developer role display name"}, "roleProjectManager": "مدير المشروع", "@roleProjectManager": {"description": "Project Manager role display name"}, "roleProductOwner": "مال<PERSON> المنتج", "@roleProductOwner": {"description": "Product Owner role display name"}, "roleQaTester": "مخت<PERSON>ر الجودة", "@roleQaTester": {"description": "QA Tester role display name"}, "roleCtoCeo": "المدير التقني/الرئيس التنفيذي", "@roleCtoCeo": {"description": "CTO/CEO role display name"}, "roleAllRoles": "جميع الأدوار", "@roleAllRoles": {"description": "Default text for all roles"}, "developerPrompt1": "ما هي المهام المخصصة لي حالياً؟", "@developerPrompt1": {"description": "Developer role prompt 1"}, "developerPrompt2": "ما الذي يمنع مراجعة الكود الخاص بي؟", "@developerPrompt2": {"description": "Developer role prompt 2"}, "developerPrompt3": "ما المقرر في السبرينت القادم؟", "@developerPrompt3": {"description": "Developer role prompt 3"}, "developerPrompt4": "أي الأخطاء لديها أعلى أولوية؟", "@developerPrompt4": {"description": "Developer role prompt 4"}, "projectManagerPrompt1": "ما الذي يؤخر السبرينت الحالي؟", "@projectManagerPrompt1": {"description": "Project Manager role prompt 1"}, "projectManagerPrompt2": "أي المهام متأخرة؟", "@projectManagerPrompt2": {"description": "Project Manager role prompt 2"}, "projectManagerPrompt3": "من عمل على المشروع مؤخراً؟", "@projectManagerPrompt3": {"description": "Project Manager role prompt 3"}, "projectManagerPrompt4": "كيف هو تقدم المشروع الحالي؟", "@projectManagerPrompt4": {"description": "Project Manager role prompt 4"}, "productOwnerPrompt1": "ما الميزات المخططة للإصدار القادم؟", "@productOwnerPrompt1": {"description": "Product Owner role prompt 1"}, "productOwnerPrompt2": "ما التعليقات على آخر تحديث لنا؟", "@productOwnerPrompt2": {"description": "Product Owner role prompt 2"}, "productOwnerPrompt3": "أي قصص المستخدمين لديها أعلى أولوية؟", "@productOwnerPrompt3": {"description": "Product Owner role prompt 3"}, "productOwnerPrompt4": "كيف يتطور متراكم المنتج الخاص بنا؟", "@productOwnerPrompt4": {"description": "Product Owner role prompt 4"}, "qaTesterPrompt1": "ما الاختبارات التي تحتاج إلى إجراء؟", "@qaTesterPrompt1": {"description": "QA Tester role prompt 1"}, "qaTesterPrompt2": "هل هناك أخطاء حرجة مفتوحة؟", "@qaTesterPrompt2": {"description": "QA Tester role prompt 2"}, "qaTesterPrompt3": "ما هو تغطية الاختبار الحالية؟", "@qaTesterPrompt3": {"description": "QA Tester role prompt 3"}, "qaTesterPrompt4": "أي الميزات جاهزة للاختبار؟", "@qaTesterPrompt4": {"description": "QA Tester role prompt 4"}, "ctoCeoPrompt1": "كيف يسير المشروع بشكل عام؟", "@ctoCeoPrompt1": {"description": "CTO/CEO role prompt 1"}, "ctoCeoPrompt2": "ما الذي يكلفنا معظم الوقت حالياً؟", "@ctoCeoPrompt2": {"description": "CTO/CEO role prompt 2"}, "ctoCeoPrompt3": "أي الفرق تحتاج إلى الدعم؟", "@ctoCeoPrompt3": {"description": "CTO/CEO role prompt 3"}, "ctoCeoPrompt4": "كيف نقوم بالميزانية والجدول الزمني؟", "@ctoCeoPrompt4": {"description": "CTO/CEO role prompt 4"}, "defaultPrompt1": "ما المهام في جدول اليوم؟", "@defaultPrompt1": {"description": "Default role prompt 1"}, "defaultPrompt2": "ما هو حالة المشروع الحالية؟", "@defaultPrompt2": {"description": "Default role prompt 2"}, "defaultPrompt3": "ما هي الخطوات المهمة التالية؟", "@defaultPrompt3": {"description": "Default role prompt 3"}, "defaultPrompt4": "هل هناك عوائق تحتاج إلى حل؟", "@defaultPrompt4": {"description": "Default role prompt 4"}}