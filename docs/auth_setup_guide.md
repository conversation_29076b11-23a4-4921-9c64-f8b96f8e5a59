# Authentication Setup Guide

This guide provides step-by-step instructions for setting up Google and Apple authentication in ProjectPilot.

## 1. Google Authentication Setup

### Google Cloud Configuration

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Go to **APIs & Services** > **OAuth consent screen**
   - Set up the consent screen with your app information
   - Add the necessary scopes (email, profile)
4. Go to **APIs & Services** > **Credentials**
5. Click **Create Credentials** > **OAuth client ID**
6. Create separate client IDs for each platform:

   **For Android:**
   - Application type: Android
   - Name: ProjectPilot Android
   - Package name: your app's package name (e.g., `com.innovatio.projectpilot`)
   - SHA-1 certificate fingerprint: Generate with:
     ```
     cd android && ./gradlew signingReport
     ```

   **For iOS:**
   - Application type: iOS
   - Name: ProjectPilot iOS
   - Bundle ID: your app's bundle ID (found in Xcode)

   **For Web:**
   - Application type: Web application
   - Name: ProjectPilot Web
   - Authorized JavaScript origins: Add your app's domain
   - Authorized redirect URIs: Add `https://YOUR_PROJECT_REF.supabase.co/auth/v1/callback`

### Android Configuration

1. Open `android/app/src/main/AndroidManifest.xml`
2. Ensure the following intent filter exists (it should already be there):
   ```xml
   <intent-filter>
     <action android:name="android.intent.action.VIEW" />
     <category android:name="android.intent.category.DEFAULT" />
     <category android:name="android.intent.category.BROWSABLE" />
     <data android:scheme="io.supabase.flutterquickstart" android:host="login-callback" />
   </intent-filter>
   ```

### iOS Configuration

1. Open `ios/Runner/Info.plist`
2. Replace the placeholder in:
   ```xml
   <string>com.googleusercontent.apps.YOUR_REVERSED_CLIENT_ID</string>
   ```
   with your actual reversed client ID from Google (format: `com.googleusercontent.apps.*********-abcdefghijklmn`)

## 2. Apple Authentication Setup

### Apple Developer Configuration

1. Go to [Apple Developer Portal](https://developer.apple.com/account/)
2. Go to **Certificates, IDs & Profiles**
3. Register your app in **Identifiers** (if not already registered)
4. Enable **Sign In with Apple** capability for your app ID
5. Create a **Services ID** for web authentication (if you need web support)
   - Register a new identifier (Services ID)
   - Enable Sign In with Apple
   - Configure domains and redirect URLs

### Xcode Configuration

1. Open the Runner.xcworkspace file in Xcode
2. Select the Runner project in the left sidebar
3. Go to the **Signing & Capabilities** tab
4. Make sure your Team is selected
5. Click the + button and add **Sign In with Apple** capability

## 3. Supabase Configuration

1. Go to [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Go to **Authentication** > **Providers**
4. Enable **Google** provider
   - Add your **Client ID** and **Client Secret** from Google Cloud Console
5. Enable **Apple** provider
   - Add your **Service ID** (for web authentication)
   - Add your **Team ID** (from your Apple Developer account)
   - Add your **Key ID** (from Apple Developer account)
   - Upload the Private Key file (downloaded from Apple Developer account)

## 4. Using Google and Apple Sign-In

The app is already configured to use these authentication providers. When users tap the "Continue with Google" or "Continue with Apple" buttons on the authentication screen, it will:

1. For Google: Open the Google sign-in flow using the native Google Sign-In SDK
2. For Apple: Open the Apple sign-in flow using Sign In with Apple
3. Connect the authentication with Supabase using `SocialAuthService`

## 5. Testing Your Authentication

1. Complete all the setup steps above
2. Run the app on each platform (Android, iOS, web)
3. Test both Google and Apple sign-in flows
4. Verify that user accounts are created in Supabase

## 6. Troubleshooting

If you encounter authentication issues, check the following:

### Google Sign-In Issues

- Verify client IDs are correct for each platform
- Ensure SHA-1 fingerprint is correctly added for Android
- Check that redirect URLs match in Google Console and your app

### Apple Sign-In Issues

- Verify Sign In with Apple capability is enabled in Xcode
- Check that Apple Developer account has Sign In with Apple enabled
- Ensure all certificates and identifiers are properly configured

### General Issues

- Check app logs for specific error messages
- Verify Supabase configuration is correct
- Test on actual devices, not just simulators 