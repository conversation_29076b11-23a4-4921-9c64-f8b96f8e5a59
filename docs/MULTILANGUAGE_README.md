# Multilanguage Support in ProjectPilot

This document explains how the multilanguage support is implemented in the ProjectPilot application.

## Supported Languages

The application currently supports the following languages:

- English (en)
- German (de)
- Russian (ru)
- Turkish (tr)
- Arabic (ar)

## Implementation Details

### 1. Localization Files

The localization files are stored in the `lib/l10n` directory:

- `app_en.arb`: English translations (template)
- `app_de.arb`: German translations
- `app_ru.arb`: Russian translations
- `app_tr.arb`: Turkish translations
- `app_ar.arb`: Arabic translations

### 2. Configuration

The localization is configured in the `l10n.yaml` file at the root of the project:

```yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
nullable-getter: false
```

### 3. State Management

The language selection is managed using a Cubit:

- `LanguageCubit`: Manages the current language state
- `LanguageState`: Holds the current language code

The language preference is persisted using SharedPreferences.

### 4. UI Components

- `LanguageSelector`: A widget that allows users to select their preferred language

### 5. Usage in Code

To use translations in your code:

```dart
// Get the localized strings
final l10n = AppLocalizations.of(context)!;

// Use the translations
Text(l10n.appTitle);
```

For strings with parameters:

```dart
// String with a parameter
Text(l10n.tokenBalance(count));
```

### 6. Helper Utilities

The `TranslationHelper` class provides utility methods for working with translations:

```dart
// Get the current language code
String languageCode = TranslationHelper.getLanguageCode(context);

// Check if the current language is RTL
bool isRtl = TranslationHelper.isRtl(context);

// Get the text direction
TextDirection direction = TranslationHelper.getTextDirection(context);
```

## Adding New Translations

To add a new translation:

1. Add a new key and description to the template file (`app_en.arb`)
2. Add the translated value to each language file
3. Run `flutter gen-l10n` to regenerate the localization files

Example:

```json
// In app_en.arb
"newFeature": "New Feature",
"@newFeature": {
  "description": "Title for the new feature"
}

// In app_de.arb
"newFeature": "Neue Funktion"
```

## Adding a New Language

To add a new language:

1. Create a new ARB file with the language code (e.g., `app_fr.arb` for French)
2. Add all the translation keys from the template file with the translated values
3. Add the new locale to the supported locales in `main.dart`
4. Add the language to the language selector widget

## Best Practices

1. Always use translation keys instead of hardcoded strings
2. Keep translations concise and clear
3. Use parameters for dynamic content
4. Test the application in all supported languages
5. Consider text expansion/contraction in different languages when designing UI
6. Pay special attention to RTL languages (like Arabic)

## Generating Translation Files

Run the following command to generate the localization files:

```bash
flutter gen-l10n
```

This will generate the necessary Dart files for accessing translations in your code.
