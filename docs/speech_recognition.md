# Speech Recognition in ProjectPilot

ProjectPilot uses offline speech recognition to provide a seamless voice command experience without requiring an internet connection. This document explains how the speech recognition system works and how to configure it.

## Overview

ProjectPilot uses the [sherpa_onnx](https://pub.dev/packages/sherpa_onnx) package for offline speech recognition. This package provides high-quality speech recognition that works on both Android and iOS devices without requiring an internet connection.

## Features

- **Cross-platform support**: Works on both Android and iOS
- **Offline operation**: No internet connection required for speech recognition
- **Multiple language support**: English, German, Russian, Turkish, and Arabic
- **High accuracy**: Uses state-of-the-art speech recognition models
- **Low latency**: Fast recognition for a responsive user experience

## Setup

### 1. Dependencies

The following dependencies are required for speech recognition:

```yaml
dependencies:
  sherpa_onnx: ^1.11.5  # For offline speech recognition
  path_provider: ^2.1.1  # For accessing device storage
  archive: ^4.0.7  # For extracting model files
  http: ^1.1.0  # For downloading models
```

### 2. Configuration

Speech recognition can be configured through environment variables:

- `ENABLE_OFFLINE_TRANSCRIPTION`: Set to `true` to enable offline speech recognition (default: `true`)
- `ENABLE_ONLINE_TRANSCRIPTION`: Set to `true` to enable online speech recognition as a fallback (default: `true`)
- `DEFAULT_LANGUAGE`: Set the default language for speech recognition (default: `en`)

These settings can be configured in the `.env` file or through the app settings.

### 3. Models

Speech recognition models are downloaded automatically when needed. The models are stored in the application documents directory.

Available language models:
- English (en)
- German (de)
- Russian (ru)
- Turkish (tr)
- Arabic (ar)

## Usage

The speech recognition system is integrated into the app through the `SherpaService` class, which provides methods for transcribing audio data.

### Basic Usage

```dart
// Initialize the service
final sherpaService = SherpaService();
await sherpaService.init();

// Set the language (optional, defaults to 'en')
await sherpaService.setLanguage('de');

// Transcribe audio file
final transcription = await sherpaService.transcribeFile('/path/to/audio.wav');
print('Transcription: $transcription');

// Don't forget to dispose when done
sherpaService.dispose();
```

### Integration with Recording

The speech recognition system is integrated with the recording system through the `RecordingCubit`. When a recording is stopped, the audio is automatically transcribed and the result is made available to the app.

## Troubleshooting

### Model Download Issues

If you encounter issues with model downloads:

1. Check your internet connection
2. Ensure the app has permission to access storage
3. Try clearing the app cache and restarting

### Recognition Accuracy Issues

If you're experiencing poor recognition accuracy:

1. Ensure you're using the correct language model
2. Speak clearly and at a normal pace
3. Minimize background noise
4. Try adjusting the microphone sensitivity in the app settings

## Advanced Configuration

### Custom Model Paths

You can specify custom model paths by modifying the `SherpaService` class:

```dart
// In SherpaService.dart
static const Map<String, String> _modelUrls = {
  'en': 'https://custom-model-url/english-model.tar.bz2',
  // Other languages...
};
```

### Performance Tuning

You can adjust the performance settings by modifying the recognizer configuration:

```dart
// In SherpaService.dart, _createRecognizerConfig method
final modelConfig = OnlineModelConfig(
  // ...
  numThreads: 4,  // Increase for better performance on multi-core devices
  // ...
);
```

## References

- [sherpa_onnx package](https://pub.dev/packages/sherpa_onnx)
- [Sherpa-ONNX documentation](https://k2-fsa.github.io/sherpa/onnx/)
- [Available pre-trained models](https://k2-fsa.github.io/sherpa/onnx/pretrained_models/index.html)
