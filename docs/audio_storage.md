# Audio Storage in ProjectPilot

This document explains the hybrid approach used for audio file storage in ProjectPilot.

## Overview

ProjectPilot uses a hybrid approach for storing audio recordings:

1. **File System for Audio Files**: Actual audio recordings are stored in the app's documents directory using `path_provider`
2. **Hive for Metadata**: Hive database stores metadata and absolute file paths

This approach provides several advantages:

- **Persistence**: Audio files are stored in a persistent location that survives app restarts
- **Efficiency**: Binary data is stored as files rather than in the database
- **Reliability**: File system operations are more reliable for large binary data

## Implementation Details

### AudioFileManager

The `AudioFileManager` class (`lib/core/services/voice/audio_file_manager.dart`) is responsible for:

- Generating consistent file paths for audio recordings
- Moving audio files from temporary to permanent storage
- Managing audio file operations (deletion, size calculation, etc.)
- Listing all saved recordings

### Storage Location

Audio files are stored in:
```
{ApplicationDocumentsDirectory}/voice_pilot_audio/
```

File naming convention:
```
recording_{timestamp}_{unique_id}.m4a
```

### Workflow

1. When recording, the `AudioService` initially saves the recording to a temporary location
2. When saving to the collection or batch, the `AudioFileManager` moves the file to permanent storage
3. The permanent file path is stored in the Hive database as part of the Task or BatchRecording metadata
4. When playing back recordings, the app uses the absolute file path from the metadata

## Usage in Components

### CollectionCubit

The `CollectionCubit` uses the `AudioFileManager` to:
- Move audio files to permanent storage
- Get file size for metadata
- Store permanent file paths in Task metadata

### BatchCubit

The `BatchCubit` uses the `AudioFileManager` to:
- Move audio files to permanent storage
- Create batch recordings with permanent file paths

### RecordingCubit

The `RecordingCubit` passes audio file paths and durations to:
- The `CollectionCubit` for direct saving
- The `BatchCubit` for batch processing

## Error Handling

The system includes error handling for:
- Files that don't exist or can't be found
- Failed file operations
- Invalid paths

## Future Improvements

Potential future improvements:
- Automatic cleanup of unused audio files
- Compression options for long recordings
- Cloud backup integration
- Sharing features for audio recordings 