# Intelligent Content Model

## Overview

The Intelligent Content Model in ProjectPilot allows users to create, reference, and update entries through voice commands. This feature enables a more natural and contextual way of organizing information by allowing users to refer to previous entries and update them without creating duplicates.

## Key Features

### 1. Unique Identifiers

- Each entry (task, note, or idea) has a unique UUID
- Entries are stored both locally (Hive) and in the cloud (Supabase)
- This allows for reliable referencing across sessions and devices

### 2. Voice References

Users can reference previous entries in their voice commands:

- "Update the hotel booking idea with new pricing information"
- "Add more details to the marketing task from yesterday"
- "Expand on my note about the client meeting"

### 3. Contextual Understanding

The system uses a combination of techniques to understand references:

- Pattern matching for reference phrases in multiple languages
- Semantic similarity to find the most likely entry being referenced
- Confidence scoring to determine when to ask for clarification

### 4. Thread-Based Updates

- Updates are stored as threads linked to the original entry
- The timeline view shows the chronological history of an entry
- Each update preserves its source (voice, manual, GPT)

### 5. Multi-Platform Integration

- Updates can be synchronized with connected platforms
- For example, adding a comment to a ClickUp task or appending to a Notion page

## Technical Implementation

### Data Model

#### Entries Table

| Column       | Type      | Description                    |
|--------------|-----------|--------------------------------|
| id           | UUID      | Primary ID                     |
| type         | TEXT      | "task", "idea", "note"         |
| content      | TEXT      | Main content                   |
| created_at   | TIMESTAMP | Creation timestamp             |
| platform     | TEXT      | Target platform (optional)     |
| project_name | TEXT      | Target project (optional)      |
| user_id      | UUID      | User reference                 |
| metadata     | JSONB     | Additional data                |

#### Entry Updates Table

| Column      | Type      | Description                    |
|-------------|-----------|--------------------------------|
| id          | UUID      | Update ID                      |
| parent_id   | UUID      | Reference to entries.id        |
| content     | TEXT      | Update content                 |
| added_at    | TIMESTAMP | Update timestamp               |
| source_type | TEXT      | "voice", "manual", "gpt"       |
| user_id     | UUID      | User reference                 |
| metadata    | JSONB     | Additional data                |

### Components

1. **EntryReferenceDetector**: Analyzes voice input to detect references to existing entries
2. **EntryCubit**: Manages state for entries and updates
3. **EntryRepository**: Handles data operations and synchronization
4. **TimelineView**: Displays the history of an entry with all its updates

## User Flow

1. User records a voice command
2. System transcribes the audio
3. EntryReferenceDetector analyzes the text for references
4. If a reference is detected with high confidence:
   - The system finds the matching entry
   - The new content is added as an update
5. If multiple matches or low confidence:
   - User is shown a selection UI to choose the correct entry
   - Or can choose to create a new entry instead
6. The timeline view shows the original entry with all updates

## Multilingual Support

The reference detection system supports multiple languages:

- English
- German
- Russian
- Turkish
- Arabic

Each language has its own set of reference phrases and patterns to detect when a user is referring to an existing entry.

## Future Enhancements

- Enhanced semantic search using embeddings
- More sophisticated confidence scoring
- Improved disambiguation UI
- Better integration with third-party platforms for bidirectional updates

## Usage Examples

### Voice Command Examples

- "Add more details to the hotel booking idea"
- "Update the marketing task with the new deadline"
- "Expand on my note about the client meeting"

### API Usage

```dart
// Check if a voice command references an existing entry
final referenceResult = _entryReferenceDetector.detectEntryReference(
  transcribedText,
  languageCode,
);

if (referenceResult.hasReference && referenceResult.confidence > 0.4) {
  // Process the reference
  await _entryCubit.processVoiceCommand(transcribedText, languageCode);
}
```

## Integration with Voice Flow

The intelligent content model is integrated into the main voice recording flow, allowing for a seamless experience when creating or updating entries.
