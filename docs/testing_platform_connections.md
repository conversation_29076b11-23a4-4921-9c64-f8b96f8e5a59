# Testing Platform Connections on Real Devices

This guide provides instructions for testing the platform connections (<PERSON><PERSON><PERSON><PERSON>, Notion, Monday) on real devices to ensure they work correctly.

## Prerequisites

Before testing, make sure you have:

1. A development build of the ProjectPilot app installed on your device
2. Test accounts for each platform you want to test
3. OAuth credentials configured in Supabase or environment variables
4. Internet connectivity on your test device

## Test Plan

For each platform, follow these steps:

### 1. Connection Test

1. Open the ProjectPilot app
2. Navigate to Settings > Platform Connections
3. Tap "Connect" for the platform you want to test
4. Verify that the OAuth flow starts and you're redirected to the platform's login page
5. Log in with your test account
6. Authorize the ProjectPilot app
7. Verify that you're redirected back to the ProjectPilot app
8. Verify that the platform shows as connected in the Platform Connections screen
9. Verify that the connection details (user name, workspace) are displayed correctly

### 2. Task Creation Test

1. Go to the home screen
2. Tap the record button and record a voice command like "Create a task to review the marketing plan in [Platform Name]"
3. Stop recording
4. Verify that the transcription is accurate
5. Verify that the platform is correctly detected from the voice command
6. Verify that the task is created on the selected platform
7. Check the platform's website or app to confirm the task was created with the correct content

### 3. Error Handling Test

#### Connection Errors

1. Temporarily modify the OAuth credentials to be invalid
2. Try to connect to the platform
3. Verify that an appropriate error message is displayed
4. Restore the correct OAuth credentials

#### Network Errors

1. Enable airplane mode on your device
2. Try to connect to a platform
3. Verify that an appropriate error message is displayed
4. Disable airplane mode

#### Authentication Errors

1. Connect to a platform
2. Revoke the access token from the platform's developer console
3. Try to create a task on that platform
4. Verify that an appropriate error message is displayed and the user is prompted to reconnect

### 4. Disconnection Test

1. Connect to a platform
2. Go to Settings > Platform Connections
3. Tap "Disconnect" for the connected platform
4. Confirm the disconnection
5. Verify that the platform shows as disconnected in the Platform Connections screen
6. Try to create a task on the disconnected platform
7. Verify that you're prompted to connect to the platform first

## Platform-Specific Test Cases

### ClickUp

#### Connection Test
- Verify that the user can select a workspace after connecting
- Verify that the user can see their lists and folders

#### Task Creation Test
- Create a task with a due date: "Create a task to review the marketing plan in ClickUp due tomorrow"
- Create a task with a priority: "Create a high priority task to call the client in ClickUp"
- Create a task with an assignee: "Create a task for John to update the website in ClickUp"

### Notion

#### Connection Test
- Verify that the user can see their databases after connecting
- Verify that the user can select a database for task creation

#### Task Creation Test
- Create a page in a database: "Create a note about the meeting minutes in Notion"
- Create a task with properties: "Create a task with high priority to review the design in Notion"
- Create a task with a date: "Create a task due next Monday to submit the report in Notion"



### Monday.com

#### Connection Test
- Verify that the user can see their boards after connecting
- Verify that the user can select a board for item creation

#### Task Creation Test
- Create an item on a board: "Create a task to update the roadmap in Monday"
- Create an item with a status: "Create a task in progress to design the logo in Monday"
- Create an item with a date: "Create a task due next week to prepare the presentation in Monday"

## Troubleshooting

### OAuth Redirect Issues

If the OAuth redirect fails:

1. Check that the redirect URI is correctly configured in the platform's developer console
2. Verify that the URL scheme is properly set up in the app's configuration
3. Check the logs for any error messages
4. Try clearing the app's cache and data

### Task Creation Issues

If tasks aren't being created correctly:

1. Check the platform adapter implementation for any issues
2. Verify that the required metadata (e.g., board_id, database_id) is provided
3. Look for error messages in the app logs
4. Test the platform's API directly to ensure it's working

### Connection Management Issues

If connections aren't being managed correctly:

1. Check the secure storage for any leftover tokens
2. Verify that the platform connector is being saved correctly in the repository
3. Check for any issues with the platform connections cubit
4. Try reinstalling the app and reconnecting

## Reporting Issues

When reporting issues with platform connections, include:

1. The platform name and version
2. The steps to reproduce the issue
3. Any error messages displayed
4. The expected behavior
5. The actual behavior
6. Screenshots or screen recordings if possible

## Conclusion

Testing platform connections on real devices is essential to ensure a smooth user experience. By following this test plan, you can identify and fix issues before they affect users.
