# Platform Connections in ProjectPilot

ProjectPilot integrates with various productivity platforms to allow users to create tasks, notes, and ideas directly from voice commands. This document explains how to set up and use platform connections.

## Supported Platforms

ProjectPilot currently supports the following platforms:

- **ClickUp**: Task and project management
- **Notion**: Notes, databases, and wikis

- **Monday.com**: Project management and team collaboration

## Architecture Overview

The platform connection system consists of several components:

1. **OAuth Handler**: Manages the authentication flow with external platforms
2. **Platform Adapters**: Convert ProjectPilot tasks to platform-specific formats
3. **Platform Connections Cubit**: Manages the state of platform connections
4. **Platform Connections Repository**: Stores and retrieves platform connection data
5. **UI Components**: Allow users to connect and manage platform integrations

## Setup Instructions

### 1. Register OAuth Applications

For each platform you want to integrate with, you need to register an OAuth application:

#### ClickUp

1. Go to [ClickUp Developer Portal](https://clickup.com/api)
2. Create a new OAuth application
3. Set the redirect URI to `projectpilot://auth/clickup`
4. Note the Client ID and Client Secret

#### Notion

1. Go to [Notion Developers](https://www.notion.so/my-integrations)
2. Create a new integration
3. Set the redirect URI to `projectpilot://auth/notion`
4. Note the OAuth Client ID and Client Secret



#### Monday.com

1. Go to [Monday.com Developers](https://monday.com/developers/apps)
2. Create a new app
3. Set the redirect URL to `projectpilot://auth/monday`
4. Note the Client ID and Client Secret

### 2. Configure OAuth Credentials

There are two ways to configure OAuth credentials:

#### Option 1: Environment Variables

Add the following to your `.env` file:

```
CLICKUP_CLIENT_ID=your_clickup_client_id
CLICKUP_CLIENT_SECRET=your_clickup_client_secret

NOTION_CLIENT_ID=your_notion_client_id
NOTION_CLIENT_SECRET=your_notion_client_secret



MONDAY_CLIENT_ID=your_monday_client_id
MONDAY_CLIENT_SECRET=your_monday_client_secret
```

#### Option 2: Supabase Configuration

Store your OAuth credentials in Supabase:

1. Create a table called `oauth_config` with the following schema:
   - `id`: UUID (primary key)
   - `platform_id`: String (e.g., 'clickup', 'notion')
   - `client_id`: String
   - `client_secret`: String
   - `created_at`: Timestamp

2. Insert records for each platform with their respective credentials

### 3. Configure URL Schemes

To handle OAuth callbacks, add the following URL schemes to your app:

#### iOS (Info.plist)

```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLName</key>
    <string>com.yourdomain.projectpilot</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>projectpilot</string>
    </array>
  </dict>
</array>
```

#### Android (AndroidManifest.xml)

```xml
<activity
  android:name=".MainActivity"
  android:launchMode="singleTask">
  <intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="projectpilot" />
  </intent-filter>
</activity>
```

## Usage

### Connecting to a Platform

1. Navigate to the Settings screen
2. Tap on "Platform Connections"
3. Select the platform you want to connect to
4. Follow the OAuth authentication flow
5. Once authenticated, the platform will appear as connected

### Creating Tasks on Connected Platforms

Once a platform is connected, you can create tasks on it using voice commands:

1. Record a voice command (e.g., "Create a task to review the marketing plan")
2. ProjectPilot will transcribe and analyze the command
3. If the platform is detected in the command (e.g., "in ClickUp"), it will be used automatically
4. Otherwise, you'll be prompted to select a platform
5. The task will be created on the selected platform

### Platform-Specific Features

#### ClickUp

- Tasks are created in the specified list or default list
- Task status is mapped from ProjectPilot categories
- Due dates are set to 7 days from creation by default

#### Notion

- Pages are created in the specified database or default database
- Task categories are mapped to Notion select properties
- Content is formatted as Notion blocks



#### Monday.com

- Items are created on the specified board or default board
- Status columns are mapped from ProjectPilot categories
- Text columns contain the task content

## Troubleshooting

### Authentication Issues

If you encounter authentication issues:

1. Verify that your OAuth credentials are correct
2. Check that the redirect URIs match exactly
3. Ensure your app has the necessary permissions
4. Check the platform's developer console for error logs

### Task Creation Issues

If tasks aren't being created correctly:

1. Check the platform adapter implementation
2. Verify that the required metadata (e.g., board_id, database_id) is provided
3. Look for error messages in the app logs
4. Test the platform's API directly to ensure it's working

### Connection Management

If you need to reset or manage connections:

1. Go to Settings > Platform Connections
2. Use the disconnect button to remove a connection
3. You can reconnect at any time
4. Check the secure storage for any leftover tokens

## Advanced Configuration

### Default Workspaces

You can configure default workspaces for each platform:

1. After connecting a platform, select a default workspace
2. This workspace will be used when no specific workspace is mentioned
3. You can change the default workspace at any time

### Custom Mappings

You can customize how ProjectPilot categories map to platform-specific statuses:

1. Edit the platform adapter implementation
2. Modify the mapping functions (e.g., `_mapCategoryToStatus`)
3. Rebuild the app to apply the changes

## Security Considerations

- OAuth tokens are stored securely using Flutter Secure Storage
- Client secrets are never stored on the device
- Token exchange happens on the server side
- Access can be revoked at any time from the Settings screen
