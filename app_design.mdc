---
description:
globs:
alwaysApply: false
---
# ProjectPilot App Design System

## Brand Colors

- **Primary Gradient**: Deep purple (#6C63FF) to electric blue (#2ACAEA)
- **Accent**: Neon turquoise / teal glow (#1FE2C1)
- **Dark Background**: Deep navy (#0F111A)
- **Light Background**: Clean white (#FAFAFA)
- **Neutral Text**: Light grey (#A3A3A3) for secondary, Dark (#1C1C1C) for headlines

## Color Scheme Implementation

```dart
// Dark Theme (Primary)
final darkColorScheme = ColorScheme.dark(
  primary: const Color(0xFF6C63FF),
  primaryContainer: const Color(0xFF4F46E5),
  secondary: const Color(0xFF2ACAEA),
  secondaryContainer: const Color(0xFF0BB8D4),
  tertiary: const Color(0xFF1FE2C1),
  tertiaryContainer: const Color(0xFF00C4B0),
  background: const Color(0xFF0F111A),
  surface: const Color(0xFF1A1C25),
  surfaceVariant: const Color(0xFF232535),
  error: const Color(0xFFFF5252),
  onPrimary: Colors.white,
  onSecondary: Colors.white,
  onTertiary: Colors.black,
  onBackground: Colors.white,
  onSurface: Colors.white,
  onSurfaceVariant: Colors.white.withOpacity(0.8),
  onError: Colors.white,
  brightness: Brightness.dark,
);

// Light Theme (Alternative)
final lightColorScheme = ColorScheme.light(
  primary: const Color(0xFF6C63FF),
  primaryContainer: const Color(0xFFDEDCFF),
  secondary: const Color(0xFF2ACAEA),
  secondaryContainer: const Color(0xFFCDF4FF),
  tertiary: const Color(0xFF1FE2C1),
  tertiaryContainer: const Color(0xFFCEF7EC),
  background: const Color(0xFFFAFAFA),
  surface: Colors.white,
  surfaceVariant: const Color(0xFFF5F5FA),
  error: const Color(0xFFD32F2F),
  onPrimary: Colors.white,
  onSecondary: Colors.white,
  onTertiary: Colors.black,
  onBackground: const Color(0xFF1C1C1C),
  onSurface: const Color(0xFF1C1C1C),
  onSurfaceVariant: const Color(0xFF424242),
  onError: Colors.white,
  brightness: Brightness.light,
);
```

## Typography

```dart
final textTheme = TextTheme(
  displayLarge: TextStyle(
    fontSize: 57,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.25,
  ),
  displayMedium: TextStyle(
    fontSize: 45,
    fontWeight: FontWeight.bold,
  ),
  displaySmall: TextStyle(
    fontSize: 36,
    fontWeight: FontWeight.bold,
  ),
  headlineLarge: TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
  ),
  headlineMedium: TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.w600,
  ),
  headlineSmall: TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
  ),
  titleLarge: TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.15,
  ),
  titleMedium: TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.15,
  ),
  titleSmall: TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.1,
  ),
  bodyLarge: TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.5,
  ),
  bodyMedium: TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.25,
  ),
  bodySmall: TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.4,
  ),
  labelLarge: TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
  ),
  labelMedium: TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  ),
  labelSmall: TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  ),
);
```

## Complete ThemeData

```dart
ThemeData darkTheme = ThemeData(
  useMaterial3: true,
  colorScheme: darkColorScheme,
  textTheme: textTheme,
  fontFamily: 'Inter',
  scaffoldBackgroundColor: darkColorScheme.background,
  appBarTheme: AppBarTheme(
    backgroundColor: darkColorScheme.background,
    foregroundColor: darkColorScheme.onBackground,
    elevation: 0,
    centerTitle: false,
    titleTextStyle: textTheme.titleLarge?.copyWith(
      color: darkColorScheme.onBackground,
      fontWeight: FontWeight.w600,
    ),
  ),
  cardTheme: CardTheme(
    color: darkColorScheme.surfaceVariant,
    elevation: 0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
    margin: EdgeInsets.zero,
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: darkColorScheme.primary,
      foregroundColor: darkColorScheme.onPrimary,
      elevation: 0,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      textStyle: textTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    ),
  ),
  outlinedButtonTheme: OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: darkColorScheme.primary,
      side: BorderSide(color: darkColorScheme.primary, width: 1.5),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      textStyle: textTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: darkColorScheme.primary,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      textStyle: textTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    ),
  ),
  floatingActionButtonTheme: FloatingActionButtonThemeData(
    backgroundColor: darkColorScheme.tertiary,
    foregroundColor: darkColorScheme.onTertiary,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: darkColorScheme.surfaceVariant.withOpacity(0.5),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide.none,
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide.none,
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide(color: darkColorScheme.primary, width: 2),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    hintStyle: textTheme.bodyLarge?.copyWith(
      color: darkColorScheme.onBackground.withOpacity(0.5),
    ),
  ),
  chipTheme: ChipThemeData(
    backgroundColor: darkColorScheme.surfaceVariant,
    selectedColor: darkColorScheme.primary.withOpacity(0.2),
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    labelStyle: textTheme.bodySmall?.copyWith(
      color: darkColorScheme.onSurfaceVariant,
      fontWeight: FontWeight.w500,
    ),
    secondaryLabelStyle: textTheme.bodySmall?.copyWith(
      color: darkColorScheme.primary,
      fontWeight: FontWeight.w500,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20),
    ),
  ),
  tabBarTheme: TabBarTheme(
    indicatorColor: darkColorScheme.primary,
    labelColor: darkColorScheme.primary,
    unselectedLabelColor: darkColorScheme.onBackground.withOpacity(0.7),
    labelStyle: textTheme.labelLarge?.copyWith(
      fontWeight: FontWeight.w600,
    ),
    unselectedLabelStyle: textTheme.labelLarge?.copyWith(
      fontWeight: FontWeight.w500,
    ),
  ),
  dividerTheme: DividerThemeData(
    color: darkColorScheme.onBackground.withOpacity(0.1),
    space: 1,
    thickness: 1,
  ),
  bottomNavigationBarTheme: BottomNavigationBarThemeData(
    backgroundColor: darkColorScheme.surface,
    selectedItemColor: darkColorScheme.primary,
    unselectedItemColor: darkColorScheme.onBackground.withOpacity(0.6),
    type: BottomNavigationBarType.fixed,
    elevation: 0,
  ),
  bottomSheetTheme: BottomSheetThemeData(
    backgroundColor: darkColorScheme.surface,
    modalBackgroundColor: darkColorScheme.surface,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
  ),
  dialogTheme: DialogTheme(
    backgroundColor: darkColorScheme.surface,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20),
    ),
  ),
);

ThemeData lightTheme = ThemeData(
  useMaterial3: true,
  colorScheme: lightColorScheme,
  textTheme: textTheme,
  fontFamily: 'Inter',
  scaffoldBackgroundColor: lightColorScheme.background,
  appBarTheme: AppBarTheme(
    backgroundColor: lightColorScheme.background,
    foregroundColor: lightColorScheme.onBackground,
    elevation: 0,
    centerTitle: false,
    titleTextStyle: textTheme.titleLarge?.copyWith(
      color: lightColorScheme.onBackground,
      fontWeight: FontWeight.w600,
    ),
  ),
  cardTheme: CardTheme(
    color: lightColorScheme.surface,
    elevation: 0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
    margin: EdgeInsets.zero,
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: lightColorScheme.primary,
      foregroundColor: lightColorScheme.onPrimary,
      elevation: 0,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      textStyle: textTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    ),
  ),
  outlinedButtonTheme: OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: lightColorScheme.primary,
      side: BorderSide(color: lightColorScheme.primary, width: 1.5),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      textStyle: textTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: lightColorScheme.primary,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      textStyle: textTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    ),
  ),
  floatingActionButtonTheme: FloatingActionButtonThemeData(
    backgroundColor: lightColorScheme.tertiary,
    foregroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: lightColorScheme.surfaceVariant.withOpacity(0.5),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide.none,
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide.none,
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide(color: lightColorScheme.primary, width: 2),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    hintStyle: textTheme.bodyLarge?.copyWith(
      color: lightColorScheme.onBackground.withOpacity(0.5),
    ),
  ),
  chipTheme: ChipThemeData(
    backgroundColor: lightColorScheme.surfaceVariant,
    selectedColor: lightColorScheme.primary.withOpacity(0.2),
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    labelStyle: textTheme.bodySmall?.copyWith(
      color: lightColorScheme.onSurfaceVariant,
      fontWeight: FontWeight.w500,
    ),
    secondaryLabelStyle: textTheme.bodySmall?.copyWith(
      color: lightColorScheme.primary,
      fontWeight: FontWeight.w500,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20),
    ),
  ),
  tabBarTheme: TabBarTheme(
    indicatorColor: lightColorScheme.primary,
    labelColor: lightColorScheme.primary,
    unselectedLabelColor: lightColorScheme.onBackground.withOpacity(0.7),
    labelStyle: textTheme.labelLarge?.copyWith(
      fontWeight: FontWeight.w600,
    ),
    unselectedLabelStyle: textTheme.labelLarge?.copyWith(
      fontWeight: FontWeight.w500,
    ),
  ),
  dividerTheme: DividerThemeData(
    color: lightColorScheme.onBackground.withOpacity(0.1),
    space: 1,
    thickness: 1,
  ),
  bottomNavigationBarTheme: BottomNavigationBarThemeData(
    backgroundColor: lightColorScheme.surface,
    selectedItemColor: lightColorScheme.primary,
    unselectedItemColor: lightColorScheme.onBackground.withOpacity(0.6),
    type: BottomNavigationBarType.fixed,
    elevation: 0,
  ),
  bottomSheetTheme: BottomSheetThemeData(
    backgroundColor: lightColorScheme.surface,
    modalBackgroundColor: lightColorScheme.surface,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
  ),
  dialogTheme: DialogTheme(
    backgroundColor: lightColorScheme.surface,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20),
    ),
  ),
);
```

## Special UI Components & Patterns

### Gradients

```dart
// Primary brand gradient (Purple to Blue)
final primaryGradient = LinearGradient(
  colors: [
    const Color(0xFF6C63FF),
    const Color(0xFF2ACAEA),
  ],
  begin: Alignment.topLeft,
  end: Alignment.bottomRight,
);

// Accent gradient (Blue to Teal)
final accentGradient = LinearGradient(
  colors: [
    const Color(0xFF2ACAEA),
    const Color(0xFF1FE2C1),
  ],
  begin: Alignment.topLeft,
  end: Alignment.bottomRight,
);

// Dark background gradient
final darkBackgroundGradient = LinearGradient(
  colors: [
    const Color(0xFF0F111A),
    const Color(0xFF1A1C25),
  ],
  begin: Alignment.topCenter,
  end: Alignment.bottomCenter,
);
```

### Glow Effects

```dart
// Glow effect for primary action buttons and important UI elements
BoxDecoration primaryGlow = BoxDecoration(
  boxShadow: [
    BoxShadow(
      color: const Color(0xFF6C63FF).withOpacity(0.4),
      blurRadius: 20,
      spreadRadius: -5,
    ),
  ],
);

// Glow effect for microphone button
BoxDecoration microphoneGlow = BoxDecoration(
  boxShadow: [
    BoxShadow(
      color: const Color(0xFF2ACAEA).withOpacity(0.6),
      blurRadius: 30,
      spreadRadius: 0,
    ),
  ],
);
```

### Card Styles

```dart
// Feature card with gradient
BoxDecoration featureCardDecoration = BoxDecoration(
  gradient: LinearGradient(
    colors: [
      const Color(0xFF292D3E),
      const Color(0xFF1A1C25),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  ),
  borderRadius: BorderRadius.circular(20),
  boxShadow: [
    BoxShadow(
      color: Colors.black.withOpacity(0.2),
      blurRadius: 10,
      offset: const Offset(0, 4),
    ),
  ],
);

// Task card style
BoxDecoration taskCardDecoration = BoxDecoration(
  color: const Color(0xFF1A1C25),
  borderRadius: BorderRadius.circular(16),
  border: Border.all(
    color: Colors.white.withOpacity(0.05),
    width: 1,
  ),
);
```

### Button Styles

```dart
// Primary action button (Get Started)
ButtonStyle primaryActionButton = ElevatedButton.styleFrom(
  backgroundColor: const Color(0xFF6C63FF),
  foregroundColor: Colors.white,
  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(30),
  ),
  elevation: 0,
  textStyle: const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
  ),
).copyWith(
  overlayColor: MaterialStateProperty.resolveWith<Color?>(
    (Set<MaterialState> states) {
      if (states.contains(MaterialState.pressed)) {
        return Colors.white.withOpacity(0.1);
      }
      return null;
    },
  ),
);

// Microphone button
ButtonStyle microphoneButton = ElevatedButton.styleFrom(
  shape: const CircleBorder(),
  padding: const EdgeInsets.all(24),
  backgroundColor: const Color(0xFF2ACAEA),
  foregroundColor: Colors.white,
).copyWith(
  overlayColor: MaterialStateProperty.resolveWith<Color?>(
    (Set<MaterialState> states) {
      if (states.contains(MaterialState.pressed)) {
        return Colors.white.withOpacity(0.1);
      }
      return null;
    },
  ),
);
```

### Screen Templates

#### 1. BaseScaffold

All screens should use BaseScaffold or BaseScaffold.rounded for consistent layout:

```dart
BaseScaffold.rounded(
  title: 'Screen Title',
  backgroundColor: theme.colorScheme.surface,
  roundedColor: const Color(0xFF6C44EB),
  roundedHeight: 180,
  showBackButton: true,
  showQuickActions: false,
  body: screenContent,
);
```

#### 2. Landing/Intro Screens

For intro and marketing screens with large headings and gradient backgrounds:

```dart
Scaffold(
  body: Container(
    decoration: BoxDecoration(
      gradient: darkBackgroundGradient,
    ),
    child: SafeArea(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Text(
              'Screen Title',
              style: theme.textTheme.displayMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Content
          // ...
        ],
      ),
    ),
  ),
);
```

#### 3. Feature Cards

For features and pricing cards:

```dart
Container(
  decoration: featureCardDecoration,
  padding: const EdgeInsets.all(24),
  child: Column(
    children: [
      Icon(
        Icons.mic,
        size: 48,
        color: const Color(0xFF6C63FF),
      ),
      const SizedBox(height: 16),
      Text(
        'Feature Title',
        style: theme.textTheme.titleLarge?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      const SizedBox(height: 8),
      Text(
        'Feature description text goes here.',
        style: theme.textTheme.bodyMedium?.copyWith(
          color: Colors.white.withOpacity(0.7),
        ),
        textAlign: TextAlign.center,
      ),
    ],
  ),
);
```

## Implementation Guide

1. Apply the dark theme as the primary theme since the image shows a dark interface
2. Use BaseScaffold.rounded for main content screens
3. For landing/marketing pages, use the provided landing screen template
4. Apply gradients for important containers and backgrounds
5. Use glow effects strategically for key interactive elements
6. Ensure all text has sufficient contrast against backgrounds
7. Maintain consistent spacing and padding throughout the app
8. Support both LTR and RTL layouts for all components
9. When using icons, prefer outlined variants for better visibility on dark backgrounds
10. Apply animations with restraint, focusing on microinteractions and feedback
