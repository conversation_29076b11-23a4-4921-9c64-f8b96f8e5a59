import 'package:projectpilot/core/constants/app_constants.dart';

/// Mock implementation of AppConstants for testing
class MockAppConstants {
  /// Replace the real AppConstants with mock values for testing
  static void setup() {
    // Use reflection to replace the getters with mock values
    AppConstants.stripePublishableKeyGetter = () => 'pk_test_placeholder';
    AppConstants.supabaseUrlGetter = () => 'https://default-project.supabase.co';
    AppConstants.supabaseAnonKeyGetter = () => 'default-anon-key';
    AppConstants.openaiApiKeyGetter = () => 'default-openai-key';
    AppConstants.enableOfflineTranscriptionGetter = () => true;
    AppConstants.enableOnlineTranscriptionGetter = () => true;
    AppConstants.enableAnalyticsGetter = () => false;
    AppConstants.defaultLanguageGetter = () => 'en';
  }
}
