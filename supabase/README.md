# Supabase OAuth Integration für VoicePilot

Diese Komponente bietet eine sichere Backend-Lösung für OAuth-Authentifizierung mit verschiedenen Plattformen (ClickUp, Notion usw.) ohne Client-Secrets im Frontend zu exponieren.

## Komponenten

1. **SQL-Migration**: `migrations/20240501000000_create_oauth_configs.sql`
   - Erstellt eine Tabelle für OAuth-Konfigurationen
   - Definiert Zugriffsregeln (RLS)
   - Stellt Funktionen für den sicheren Zugriff bereit

2. **Edge-Funktion**: `functions/oauth-proxy/index.ts`
   - Dient als Proxy für OAuth-Operationen
   - Ruft Client-IDs aus der Datenbank ab
   - Führt Token-Austausch serverseitig durch

3. **Flutter Service**: `lib/data/remote/oauth_service.dart`
   - Kommuniziert mit der Edge-Funktion
   - Bietet eine sichere API für OAuth-Operationen

## Einrichtung

### 1. Supabase-Datenbank einrichten

```bash
# Migration ausführen
supabase db push
```

### 2. OAuth-Konfigurationen in Supabase speichern

Füge die OAuth-Konfigurationen für die unterstützten Plattformen direkt in der Supabase-Tabelle hinzu:

```sql
INSERT INTO oauth_configs (
  platform_id, 
  client_id, 
  client_secret, 
  redirect_uri, 
  auth_endpoint, 
  token_endpoint, 
  scopes, 
  is_public
) VALUES 
('clickup', 'YOUR_CLICKUP_CLIENT_ID', 'YOUR_CLICKUP_CLIENT_SECRET', 'voicepilot://auth/clickup', 'https://app.clickup.com/api', 'https://api.clickup.com/api/v2/oauth/token', 'tasks:write', true),

-- Füge weitere Plattformen nach Bedarf hinzu
;
```

### 3. Edge-Funktion bereitstellen

```bash
# Edge-Funktion bereitstellen
supabase functions deploy oauth-proxy

# Funktion lokal testen
supabase functions serve oauth-proxy
```

### 4. App-Konfiguration

1. Stelle sicher, dass die Supabase-URL und der API-Key in deiner App konfiguriert sind
2. Registriere die URL-Schemata in der App (`voicepilot://auth/...`)
3. Konfiguriere die Redirect-URLs in den Entwickler-Dashboards der externen Plattformen

## Sicherheitshinweise

- Die Client-Secrets werden niemals an den Client gesendet
- Die OAuth-Token-Austausch-Logik läuft vollständig auf dem Server
- Die Berechtigungssteuerung wird über Row-Level Security (RLS) und Funktionsberechtigungen umgesetzt
- Nur autorisierte Administratoren können OAuth-Konfigurationen verwalten

## Nutzung in der App

Der `OAuthHandler` verwendet den `OAuthService`, der mit der Edge-Funktion kommuniziert, um:

1. Client-IDs sicher aus der Datenbank abzurufen
2. Authorization Codes gegen Tokens auszutauschen, ohne das Client-Secret zu exponieren

Der Ablauf funktioniert wie folgt:

1. App fordert Client-ID vom Backend an
2. App startet OAuth-Flow mit dem Browser
3. Nach erfolgreichem Login erhält die App einen Authorization Code
4. Der Code wird an die Edge-Funktion gesendet, die ihn serverseitig gegen Tokens austauscht
5. Tokens werden an die App zurückgegeben und sicher gespeichert

Diese Architektur bietet maximale Sicherheit bei gleichzeitiger vollständiger Funktionalität für OAuth-basierte Integrationen. 