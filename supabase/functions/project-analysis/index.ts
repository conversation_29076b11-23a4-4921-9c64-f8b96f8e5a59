import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ProjectData {
  project: {
    id: string;
    name: string;
    description: string;
    status: string;
    progress: number;
    deadline?: string;
  };
  tasks: Array<{
    id: string;
    name: string;
    description: string;
    status: string;
    priority: string;
    assignee: string;
    deadline?: string;
    progress: number;
    blockers: string[];
  }>;
  teamMembers: Array<{
    id: string;
    name: string;
    role: string;
    isActive: boolean;
  }>;
  budget?: {
    total: number;
    spent: number;
    remaining: number;
  };
}

interface AnalysisRequest {
  projectData: ProjectData;
  analysisType: string;
  includeRecommendations: boolean;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { projectData, analysisType, includeRecommendations }: AnalysisRequest = await req.json()

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Perform AI analysis
    const analysisResult = await performProjectAnalysis(projectData, analysisType, includeRecommendations)

    // Store analysis result in database
    const { error: insertError } = await supabase
      .from('project_analyses')
      .insert({
        project_id: projectData.project.id,
        progress_percentage: analysisResult.progressPercentage,
        blockers: analysisResult.analysis.blockers,
        recommendations: analysisResult.analysis.recommendations,
        critical_paths: analysisResult.analysis.criticalPath,
        summary: analysisResult.analysis.summary,
        key_insights: analysisResult.analysis.keyInsights,
        risk_score: analysisResult.riskScore,
        confidence_level: analysisResult.confidence,
        metadata: { analysisType, version: '1.0' }
      })

    if (insertError) {
      console.error('Error storing analysis:', insertError)
    }

    return new Response(
      JSON.stringify(analysisResult),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in project-analysis function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error.message 
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function performProjectAnalysis(
  projectData: ProjectData, 
  analysisType: string, 
  includeRecommendations: boolean
) {
  // Calculate progress percentage
  const progressPercentage = calculateProgressPercentage(projectData)
  
  // Identify blockers
  const blockers = identifyBlockers(projectData)
  
  // Generate recommendations
  const recommendations = includeRecommendations ? generateRecommendations(projectData, blockers) : []
  
  // Identify critical paths
  const criticalPath = identifyCriticalPaths(projectData)
  
  // Generate summary and insights
  const summary = generateProjectSummary(projectData, blockers, recommendations)
  const keyInsights = generateKeyInsights(projectData, blockers)
  
  // Calculate risk score
  const riskScore = calculateRiskScore(projectData, blockers)
  
  // Calculate confidence level
  const confidence = calculateConfidenceLevel(projectData)

  return {
    progressPercentage,
    riskScore,
    confidence,
    analysis: {
      summary,
      blockers,
      recommendations,
      criticalPath,
      keyInsights
    }
  }
}

function calculateProgressPercentage(projectData: ProjectData): number {
  const { tasks } = projectData
  
  if (tasks.length === 0) {
    return projectData.project.progress * 100
  }
  
  const totalProgress = tasks.reduce((sum, task) => sum + task.progress, 0)
  return Math.round((totalProgress / tasks.length) * 100 * 100) / 100
}

function identifyBlockers(projectData: ProjectData): Array<any> {
  const blockers: Array<any> = []
  
  projectData.tasks.forEach(task => {
    // Check for explicit blockers
    if (task.blockers && task.blockers.length > 0) {
      task.blockers.forEach(blocker => {
        blockers.push({
          id: `${task.id}-${blocker}`,
          taskId: task.id,
          taskName: task.name,
          description: blocker,
          severity: determineSeverity(task, blocker),
          suggestedActions: generateBlockerActions(task, blocker)
        })
      })
    }
    
    // Check for overdue tasks
    if (task.deadline && new Date(task.deadline) < new Date() && task.status !== 'done') {
      blockers.push({
        id: `${task.id}-overdue`,
        taskId: task.id,
        taskName: task.name,
        description: `Task is overdue by ${Math.ceil((new Date().getTime() - new Date(task.deadline).getTime()) / (1000 * 60 * 60 * 24))} days`,
        severity: 'major',
        suggestedActions: [
          'Reassess task priority and deadline',
          'Allocate additional resources',
          'Break down task into smaller components'
        ]
      })
    }
    
    // Check for tasks with no assignee
    if (!task.assignee && task.status !== 'done') {
      blockers.push({
        id: `${task.id}-unassigned`,
        taskId: task.id,
        taskName: task.name,
        description: 'Task has no assignee',
        severity: 'minor',
        suggestedActions: [
          'Assign task to available team member',
          'Review task requirements and complexity'
        ]
      })
    }
  })
  
  return blockers
}

function generateRecommendations(projectData: ProjectData, blockers: Array<any>): Array<any> {
  const recommendations: Array<any> = []
  
  // Resource allocation recommendations
  const unassignedTasks = projectData.tasks.filter(t => !t.assignee && t.status !== 'done')
  if (unassignedTasks.length > 0) {
    recommendations.push({
      id: 'resource-allocation',
      title: 'Assign Unassigned Tasks',
      description: `${unassignedTasks.length} tasks need assignees. Consider workload distribution among team members.`,
      priority: 'high',
      category: 'resource-management',
      effortHours: unassignedTasks.length * 0.5
    })
  }
  
  // Critical blocker recommendations
  const criticalBlockers = blockers.filter(b => b.severity === 'critical' || b.severity === 'major')
  if (criticalBlockers.length > 0) {
    recommendations.push({
      id: 'resolve-blockers',
      title: 'Address Critical Blockers',
      description: `${criticalBlockers.length} critical blockers are impacting project progress. Immediate attention required.`,
      priority: 'urgent',
      category: 'risk-management',
      effortHours: criticalBlockers.length * 2
    })
  }
  
  // Timeline recommendations
  const overdueTasks = projectData.tasks.filter(t => 
    t.deadline && new Date(t.deadline) < new Date() && t.status !== 'done'
  )
  if (overdueTasks.length > 0) {
    recommendations.push({
      id: 'timeline-adjustment',
      title: 'Review Project Timeline',
      description: `${overdueTasks.length} tasks are overdue. Consider adjusting deadlines or adding resources.`,
      priority: 'high',
      category: 'timeline-management',
      effortHours: 4
    })
  }
  
  // Budget recommendations
  if (projectData.budget && projectData.budget.remaining < projectData.budget.total * 0.1) {
    recommendations.push({
      id: 'budget-review',
      title: 'Budget Review Required',
      description: 'Project is approaching budget limit. Review remaining tasks and costs.',
      priority: 'urgent',
      category: 'budget-management',
      effortHours: 2
    })
  }
  
  return recommendations
}

function identifyCriticalPaths(projectData: ProjectData): Array<any> {
  const criticalPaths: Array<any> = []
  
  // Simple critical path identification based on dependencies and high priority tasks
  const highPriorityTasks = projectData.tasks.filter(t => t.priority === 'urgent' || t.priority === 'high')
  
  if (highPriorityTasks.length > 0) {
    criticalPaths.push({
      id: 'high-priority-path',
      taskIds: highPriorityTasks.map(t => t.id),
      totalDuration: highPriorityTasks.length * 3, // Estimated 3 days per task
      riskLevel: 'high',
      description: `Critical path through ${highPriorityTasks.length} high-priority tasks`
    })
  }
  
  return criticalPaths
}

function generateProjectSummary(projectData: ProjectData, blockers: Array<any>, recommendations: Array<any>): string {
  const project = projectData.project
  const tasksCount = projectData.tasks.length
  const completedTasks = projectData.tasks.filter(t => t.status === 'done').length
  const progressPercent = Math.round((completedTasks / tasksCount) * 100)
  
  let summary = `Project "${project.name}" is ${progressPercent}% complete with ${completedTasks} of ${tasksCount} tasks finished. `
  
  if (blockers.length > 0) {
    summary += `There are ${blockers.length} blockers identified that need attention. `
  }
  
  if (recommendations.length > 0) {
    const urgentRecs = recommendations.filter(r => r.priority === 'urgent' || r.priority === 'critical')
    if (urgentRecs.length > 0) {
      summary += `${urgentRecs.length} urgent recommendations require immediate action. `
    }
  }
  
  if (project.deadline) {
    const daysToDeadline = Math.ceil((new Date(project.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    if (daysToDeadline > 0) {
      summary += `Project deadline is in ${daysToDeadline} days.`
    } else {
      summary += `Project is ${Math.abs(daysToDeadline)} days past deadline.`
    }
  }
  
  return summary
}

function generateKeyInsights(projectData: ProjectData, blockers: Array<any>): string[] {
  const insights: string[] = []
  
  // Team workload insights
  const assignedTasks = projectData.tasks.filter(t => t.assignee && t.status !== 'done')
  const workloadMap = new Map<string, number>()
  assignedTasks.forEach(task => {
    const count = workloadMap.get(task.assignee) || 0
    workloadMap.set(task.assignee, count + 1)
  })
  
  if (workloadMap.size > 0) {
    const maxWorkload = Math.max(...workloadMap.values())
    const overloadedMembers = Array.from(workloadMap.entries()).filter(([_, count]) => count >= maxWorkload)
    
    if (overloadedMembers.length > 0) {
      insights.push(`Team member workload imbalance detected - ${overloadedMembers[0][0]} has the highest task load`)
    }
  }
  
  // Progress insights
  const stuckTasks = projectData.tasks.filter(t => t.progress === 0 && t.status !== 'done')
  if (stuckTasks.length > projectData.tasks.length * 0.3) {
    insights.push(`${stuckTasks.length} tasks haven't been started - consider reviewing task priorities`)
  }
  
  // Timeline insights
  const nearDeadlineTasks = projectData.tasks.filter(t => {
    if (!t.deadline) return false
    const daysToDeadline = (new Date(t.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
    return daysToDeadline <= 3 && daysToDeadline > 0 && t.status !== 'done'
  })
  
  if (nearDeadlineTasks.length > 0) {
    insights.push(`${nearDeadlineTasks.length} tasks are due within 3 days`)
  }
  
  return insights
}

function calculateRiskScore(projectData: ProjectData, blockers: Array<any>): number {
  let riskScore = 0
  
  // Blocker risk
  const criticalBlockers = blockers.filter(b => b.severity === 'critical').length
  const majorBlockers = blockers.filter(b => b.severity === 'major').length
  riskScore += (criticalBlockers * 0.3) + (majorBlockers * 0.2)
  
  // Timeline risk
  const overdueTasks = projectData.tasks.filter(t => 
    t.deadline && new Date(t.deadline) < new Date() && t.status !== 'done'
  ).length
  riskScore += (overdueTasks / projectData.tasks.length) * 0.4
  
  // Budget risk
  if (projectData.budget) {
    const budgetUtilization = projectData.budget.spent / projectData.budget.total
    if (budgetUtilization > 0.9) {
      riskScore += 0.3
    } else if (budgetUtilization > 0.8) {
      riskScore += 0.2
    }
  }
  
  return Math.min(riskScore, 1.0)
}

function calculateConfidenceLevel(projectData: ProjectData): number {
  let confidence = 0.5 // Base confidence
  
  // Data completeness
  const tasksWithAssignee = projectData.tasks.filter(t => t.assignee).length
  const assigneeCompleteness = tasksWithAssignee / projectData.tasks.length
  confidence += assigneeCompleteness * 0.2
  
  const tasksWithDeadline = projectData.tasks.filter(t => t.deadline).length
  const deadlineCompleteness = tasksWithDeadline / projectData.tasks.length
  confidence += deadlineCompleteness * 0.2
  
  // Team size factor
  if (projectData.teamMembers.length >= 3) {
    confidence += 0.1
  }
  
  return Math.min(confidence, 1.0)
}

function determineSeverity(task: any, blocker: string): string {
  if (task.priority === 'urgent') return 'critical'
  if (task.priority === 'high') return 'major'
  if (blocker.toLowerCase().includes('critical') || blocker.toLowerCase().includes('urgent')) return 'critical'
  if (blocker.toLowerCase().includes('major') || blocker.toLowerCase().includes('important')) return 'major'
  return 'minor'
}

function generateBlockerActions(task: any, blocker: string): string[] {
  const actions: string[] = []
  
  if (blocker.toLowerCase().includes('resource')) {
    actions.push('Allocate additional resources')
    actions.push('Review resource availability')
  }
  
  if (blocker.toLowerCase().includes('dependency')) {
    actions.push('Review task dependencies')
    actions.push('Consider parallel execution where possible')
  }
  
  if (blocker.toLowerCase().includes('approval')) {
    actions.push('Follow up on pending approvals')
    actions.push('Escalate to appropriate stakeholders')
  }
  
  if (actions.length === 0) {
    actions.push('Review blocker details with team')
    actions.push('Create action plan to resolve')
  }
  
  return actions
} 