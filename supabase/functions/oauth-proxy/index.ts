import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Parse request body
    const { action, platformId, code, redirectUri, accessToken } = await req.json()

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Perform different actions based on the request
    switch (action) {
      case 'getClientId':
        return await handleGetClientId(supabase, platformId, corsHeaders)

      case 'exchangeToken':
        return await handleExchangeToken(supabase, platformId, code, redirectUri, corsHeaders)

      case 'fetchPlatformInfo':
        return await handleFetchPlatformInfo(supabase, platformId, accessToken, corsHeaders)

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
    }
  } catch (error) {
    console.error('Error processing request:', error)

    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})

/**
 * Handler for getting a client ID for a specific platform
 */
async function handleGetClientId(supabase, platformId, corsHeaders) {
  // Query the client ID from the database
  const { data, error } = await supabase
    .rpc('get_oauth_client_id', { p_platform_id: platformId })

  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }

  return new Response(JSON.stringify({ clientId: data }), {
    status: 200,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  })
}

/**
 * Handler for exchanging an authorization code for an access token
 */
async function handleExchangeToken(supabase, platformId, code, redirectUri, corsHeaders) {
  try {
    // Get the full OAuth config for the platform
    const { data: configData, error: configError } = await supabase
      .from('oauth_configs')  // Direct query with service role bypasses RLS
      .select('*')
      .eq('platform_id', platformId)
      .single()

    if (configError || !configData) {
      return new Response(JSON.stringify({ error: 'Platform configuration not found' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Use the provided redirect URI or the default one from config
    const finalRedirectUri = redirectUri || configData.redirect_uri

    // Exchange the authorization code for an access token
    const tokenResponse = await fetch(configData.token_endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code,
        client_id: configData.client_id,
        client_secret: configData.client_secret, // This is secure as it's server-side
        redirect_uri: finalRedirectUri,
      }),
    })

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text()
      console.error('Token exchange failed:', errorData)

      return new Response(
        JSON.stringify({ error: 'Failed to exchange token', details: errorData }),
        {
          status: tokenResponse.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Parse token response
    const tokenData = await tokenResponse.json()

    // Return the token response to the client
    // Never expose client_secret to the client
    return new Response(JSON.stringify({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      expires_in: tokenData.expires_in,
      token_type: tokenData.token_type,
      scope: tokenData.scope,
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Error exchanging token:', error)

    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
}

/**
 * Handler for fetching platform information using an access token
 */
async function handleFetchPlatformInfo(supabase, platformId, accessToken, corsHeaders) {
  try {
    if (!platformId || !accessToken) {
      return new Response(JSON.stringify({ error: 'Missing required parameters' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Get the platform configuration
    const { data: configData, error: configError } = await supabase
      .from('oauth_configs')
      .select('*')
      .eq('platform_id', platformId)
      .single()

    if (configError || !configData) {
      return new Response(JSON.stringify({ error: 'Platform configuration not found' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Fetch user information based on the platform
    let userInfo = null
    let workspaceInfo = null
    let displayName = ''

    // Platform-specific API calls
    switch (platformId) {
      case 'clickup':
        userInfo = await fetchClickUpUserInfo(accessToken)
        workspaceInfo = await fetchClickUpWorkspaceInfo(accessToken)
        displayName = userInfo?.user?.username || userInfo?.user?.email || ''
        break

      case 'notion':
        userInfo = await fetchNotionUserInfo(accessToken)
        workspaceInfo = await fetchNotionWorkspaceInfo(accessToken)
        displayName = userInfo?.name || userInfo?.bot?.name || ''
        break



      case 'monday':
        userInfo = await fetchMondayUserInfo(accessToken)
        workspaceInfo = await fetchMondayWorkspaceInfo(accessToken)
        displayName = userInfo?.data?.me?.name || ''
        break

      default:
        // Generic approach for unsupported platforms
        try {
          userInfo = { message: 'Platform-specific user info not implemented' }
          workspaceInfo = { message: 'Platform-specific workspace info not implemented' }
          displayName = platformId
        } catch (e) {
          console.error(`Error fetching info for ${platformId}:`, e)
        }
    }

    return new Response(JSON.stringify({
      user: userInfo,
      workspace: workspaceInfo,
      name: displayName,
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Error fetching platform info:', error)

    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
}

// Platform-specific API calls

async function fetchClickUpUserInfo(accessToken) {
  const response = await fetch('https://api.clickup.com/api/v2/user', {
    headers: { 'Authorization': accessToken }
  })

  if (!response.ok) return null
  return await response.json()
}

async function fetchClickUpWorkspaceInfo(accessToken) {
  const response = await fetch('https://api.clickup.com/api/v2/team', {
    headers: { 'Authorization': accessToken }
  })

  if (!response.ok) return null
  return await response.json()
}

async function fetchNotionUserInfo(accessToken) {
  const response = await fetch('https://api.notion.com/v1/users/me', {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Notion-Version': '2022-06-28'
    }
  })

  if (!response.ok) return null
  return await response.json()
}

async function fetchNotionWorkspaceInfo(accessToken) {
  const response = await fetch('https://api.notion.com/v1/search', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Notion-Version': '2022-06-28',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      filter: { value: 'database', property: 'object' },
      page_size: 10
    })
  })

  if (!response.ok) return null
  return await response.json()
}



async function fetchMondayUserInfo(accessToken) {
  const query = `query { me { id name email } }`

  const response = await fetch('https://api.monday.com/v2', {
    method: 'POST',
    headers: {
      'Authorization': accessToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query })
  })

  if (!response.ok) return null
  return await response.json()
}

async function fetchMondayWorkspaceInfo(accessToken) {
  const query = `query { boards { id name } }`

  const response = await fetch('https://api.monday.com/v2', {
    method: 'POST',
    headers: {
      'Authorization': accessToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query })
  })

  if (!response.ok) return null
  return await response.json()
}