import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { Configuration, OpenAIApi } from 'https://esm.sh/openai@3.2.1'

// Kategorien für die Klassifizierung
type ContentCategory = 'TASK' | 'IDEA' | 'NOTE'

interface TranscriptionResponse {
    success: boolean
    data?: {
        transcription: string
        optimizedContent: string
        category: ContentCategory
        confidence: number
        detectedPlatform?: string
        detectedProject?: string
        platformConfidence?: number
        tokensUsed?: {
            whisper: number
            gpt: number
        }
    }
    error?: string
}

interface RequestData {
    audioPath?: string
    transcription?: string
    userId: string
    apiKey?: string
    useWhisper: boolean
    useGpt: boolean
}

serve(async (req: Request) => {
    try {
        // CORS Headers für lokale Entwicklung
        if (req.method === 'OPTIONS') {
            return new Response('ok', {
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
                }
            })
        }

        // Request-Body auslesen
        const requestData = await req.json() as RequestData

        // Validate request
        if (!requestData.userId) {
            return new Response(
                JSON.stringify({ success: false, error: 'User ID is required' }),
                { headers: { 'Content-Type': 'application/json' } }
            )
        }

        if (!requestData.audioPath && !requestData.transcription) {
            return new Response(
                JSON.stringify({ success: false, error: 'Either audio path or transcription is required' }),
                { headers: { 'Content-Type': 'application/json' } }
            )
        }

        // Initialize token usage tracking
        let tokensUsed = {
            whisper: 0,
            gpt: 0
        }

        // Supabase Client mit Service-Role erstellen
        const supabaseAdmin = createClient(
            Deno.env.get('SUPABASE_URL') ?? '',
            Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
        )

        // Configure OpenAI client with the appropriate API key
        const apiKey = requestData.apiKey || Deno.env.get('OPENAI_API_KEY') || ''
        const configuration = new Configuration({ apiKey })
        const openai = new OpenAIApi(configuration)

        // Begin transcription & optimization process
        let transcription = requestData.transcription || ''

        // 1. Transcribe audio if needed
        if (!transcription && requestData.audioPath && requestData.useWhisper) {
            // 1.a Check if user has enough tokens or API key
            if (!requestData.apiKey) {
                const { data: tokenData, error: tokenError } = await supabaseAdmin
                    .from('user_tokens')
                    .select('whisper_minutes')
                    .eq('user_id', requestData.userId)
                    .single()

                if (tokenError || !tokenData || tokenData.whisper_minutes <= 0) {
                    return new Response(
                        JSON.stringify({ 
                            success: false, 
                            error: 'Insufficient tokens for Whisper transcription. Please provide an API key or add more tokens.',
                            data: { tokensUsed } 
                        }),
                        { headers: { 'Content-Type': 'application/json' } }
                    )
                }
            }

            // Download audio file
            const { data: audioData, error: downloadError } = await supabaseAdmin
                .storage
                .from('voice_recordings')
                .download(requestData.audioPath)

            if (downloadError || !audioData) {
                return new Response(
                    JSON.stringify({ success: false, error: `Error downloading audio: ${downloadError?.message}` }),
                    { headers: { 'Content-Type': 'application/json' } }
                )
            }

            // Prepare audio file for Whisper API
            const formData = new FormData()
            formData.append('file', new Blob([audioData]), 'audio.mp3')
            formData.append('model', 'whisper-1')
            formData.append('language', 'de') // German language

            // Call Whisper API
            const transcriptionResponse = await openai.createTranscription(
                // @ts-ignore - Type incompatibility with Deno
                formData.get('file') as any,
                'whisper-1',
                undefined,
                'json',
                1,
                'de'
            )

            transcription = transcriptionResponse.data.text

            // Calculate whisper minutes used (assuming 1 minute for simplicity)
            // In production, we'd calculate based on audio length
            tokensUsed.whisper = 1

            // Deduct whisper minutes if not using own API key
            if (!requestData.apiKey) {
                await supabaseAdmin
                    .from('user_tokens')
                    .update({ 
                        whisper_minutes: supabaseAdmin.rpc('decrement', { x: tokensUsed.whisper }),
                        updated_at: new Date().toISOString()
                    })
                    .eq('user_id', requestData.userId)
            }
        }

        // If we still don't have a transcription, return error
        if (!transcription) {
            return new Response(
                JSON.stringify({ success: false, error: 'Failed to obtain transcription' }),
                { headers: { 'Content-Type': 'application/json' } }
            )
        }

        // 2. Optimize and classify with GPT
        let optimizationResult = {
            optimizedContent: transcription,
            category: 'NOTE' as ContentCategory,
            confidence: 0.5,
            detectedPlatform: null,
            detectedProject: null,
            platformConfidence: 0
        }

        if (requestData.useGpt) {
            // Check if user has enough tokens or API key
            if (!requestData.apiKey) {
                const { data: tokenData, error: tokenError } = await supabaseAdmin
                    .from('user_tokens')
                    .select('gpt_tokens')
                    .eq('user_id', requestData.userId)
                    .single()

                if (tokenError || !tokenData || tokenData.gpt_tokens <= 0) {
                    // Return basic transcription without optimization
                    return new Response(
                        JSON.stringify({ 
                            success: true, 
                            data: {
                                transcription,
                                optimizedContent: transcription,
                                category: 'NOTE' as ContentCategory,
                                confidence: 0.5,
                                tokensUsed
                            }
                        }),
                        { headers: { 'Content-Type': 'application/json' } }
                    )
                }
            }

            // Prepare prompt for optimization and classification
            const gptPrompt = `
Du bist ein KI-Assistent, der Audiotranskriptionen optimiert und kategorisiert.

Hier ist ein transkribierter Text: "${transcription}"

Bitte:
1. Korrigiere grammatikalische Fehler und verbessere die Formulierung.
2. Kategorisiere den Inhalt als:
   - TASK: Wenn es sich um eine zu erledigende Aufgabe handelt
   - IDEA: Wenn es sich um eine kreative Idee handelt
   - NOTE: Wenn es sich um eine allgemeine Notiz handelt
3. Analysiere, ob eine spezifische Plattform genannt wird (z.B. "in ClickUp", "auf Notion", "in Jira", usw.)
4. Prüfe, ob ein Projekt oder eine Kategorie für die Aufgabe angegeben wurde
5. Gib deine Antwort als JSON-Objekt zurück mit den folgenden Feldern:
   - optimizedContent: Der optimierte Text
   - category: Die Kategorie (TASK, IDEA oder NOTE)
   - confidence: Deine Konfidenz in die Kategorisierung (0.0 bis 1.0)
   - detectedPlatform: Die erkannte Plattform (null wenn keine erkannt)
   - detectedProject: Das erkannte Projekt (null wenn keins erkannt)
   - platformConfidence: Konfidenz in die Plattform-Erkennung (0.0 bis 1.0)
`

            // Call GPT for optimization and classification
            const gptResponse = await openai.createChatCompletion({
                model: 'gpt-3.5-turbo',
                messages: [
                    { role: 'system', content: 'Du bist ein hilfreicher KI-Assistent für Textoptimierung und Kategorisierung.' },
                    { role: 'user', content: gptPrompt }
                ],
                temperature: 0.3,
                max_tokens: 500
            })

            // Parse GPT response
            const gptResponseText = gptResponse.data.choices[0].message?.content || ''
            const jsonMatch = gptResponseText.match(/\{[\s\S]*\}/)
            const jsonString = jsonMatch ? jsonMatch[0] : ''

            // Estimate token usage (approx. 1 token per 4 chars)
            tokensUsed.gpt = Math.ceil((gptPrompt.length + gptResponseText.length) / 4)

            // Deduct GPT tokens if not using own API key
            if (!requestData.apiKey) {
                await supabaseAdmin
                    .from('user_tokens')
                    .update({ 
                        gpt_tokens: supabaseAdmin.rpc('decrement', { x: tokensUsed.gpt }),
                        updated_at: new Date().toISOString()
                    })
                    .eq('user_id', requestData.userId)
            }

            try {
                optimizationResult = JSON.parse(jsonString)
            } catch (e) {
                console.error('Failed to parse GPT response as JSON: ', e)
                // Keep default values if parsing fails
            }
        }

        // 3. Log usage for analytics
        await supabaseAdmin
            .from('usage_logs')
            .insert({
                user_id: requestData.userId,
                timestamp: new Date().toISOString(),
                service: requestData.useWhisper ? 'whisper' : 'local_transcription',
                tokens_used: tokensUsed.whisper + tokensUsed.gpt,
                transcription_length: transcription.length,
                own_api_key: requestData.apiKey ? true : false,
                detected_category: optimizationResult.category,
                detected_platform: optimizationResult.detectedPlatform
            })

        // 4. Prepare successful response
        const response: TranscriptionResponse = {
            success: true,
            data: {
                transcription,
                optimizedContent: optimizationResult.optimizedContent,
                category: optimizationResult.category,
                confidence: optimizationResult.confidence,
                detectedPlatform: optimizationResult.detectedPlatform,
                detectedProject: optimizationResult.detectedProject,
                platformConfidence: optimizationResult.platformConfidence,
                tokensUsed
            }
        }

        return new Response(
            JSON.stringify(response),
            { headers: { 'Content-Type': 'application/json' } }
        )

    } catch (error) {
        return new Response(
            JSON.stringify({ success: false, error: error.message }),
            { headers: { 'Content-Type': 'application/json' } }
        )
    }
}) 