import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface QueryRequest {
  query: string;
  projectContext: {
    project: any;
    tasks: any[];
    teamMembers: any[];
    budget?: any;
    recentActivities: any[];
  };
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { query, projectContext }: QueryRequest = await req.json()

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Process the natural language query
    const response = await processNaturalLanguageQuery(query, projectContext)

    return new Response(
      JSON.stringify({ response }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in project-query function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error.message 
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function processNaturalLanguageQuery(query: string, projectContext: any): Promise<string> {
  const lowerQuery = query.toLowerCase()
  
  // Project status queries
  if (lowerQuery.includes('status') || lowerQuery.includes('how is') || lowerQuery.includes('progress')) {
    return handleStatusQuery(projectContext)
  }
  
  // Task-related queries
  if (lowerQuery.includes('task') || lowerQuery.includes('todo') || lowerQuery.includes('work')) {
    return handleTaskQuery(query, projectContext)
  }
  
  // Team-related queries
  if (lowerQuery.includes('team') || lowerQuery.includes('member') || lowerQuery.includes('who')) {
    return handleTeamQuery(query, projectContext)
  }
  
  // Budget-related queries
  if (lowerQuery.includes('budget') || lowerQuery.includes('cost') || lowerQuery.includes('money') || lowerQuery.includes('spend')) {
    return handleBudgetQuery(projectContext)
  }
  
  // Timeline queries
  if (lowerQuery.includes('deadline') || lowerQuery.includes('when') || lowerQuery.includes('due') || lowerQuery.includes('timeline')) {
    return handleTimelineQuery(query, projectContext)
  }
  
  // Blocker queries
  if (lowerQuery.includes('block') || lowerQuery.includes('stuck') || lowerQuery.includes('problem') || lowerQuery.includes('issue')) {
    return handleBlockerQuery(projectContext)
  }
  
  // Default response with project summary
  return generateProjectSummary(projectContext)
}

function handleStatusQuery(projectContext: any): string {
  const { project, tasks } = projectContext
  const totalTasks = tasks.length
  const completedTasks = tasks.filter((t: any) => t.status === 'done').length
  const inProgressTasks = tasks.filter((t: any) => t.status === 'inProgress').length
  const blockedTasks = tasks.filter((t: any) => t.status === 'blocked').length
  
  const progressPercent = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
  
  let response = `Project "${project.name}" is currently ${progressPercent}% complete. `
  response += `Out of ${totalTasks} tasks, ${completedTasks} are completed`
  
  if (inProgressTasks > 0) {
    response += `, ${inProgressTasks} are in progress`
  }
  
  if (blockedTasks > 0) {
    response += `, and ${blockedTasks} are blocked`
  }
  
  response += '. '
  
  if (project.deadline) {
    const daysToDeadline = Math.ceil((new Date(project.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    if (daysToDeadline > 0) {
      response += `The project deadline is in ${daysToDeadline} days.`
    } else if (daysToDeadline === 0) {
      response += `The project deadline is today!`
    } else {
      response += `The project is ${Math.abs(daysToDeadline)} days past the deadline.`
    }
  }
  
  return response
}

function handleTaskQuery(query: string, projectContext: any): string {
  const { tasks } = projectContext
  const lowerQuery = query.toLowerCase()
  
  if (lowerQuery.includes('overdue') || lowerQuery.includes('late')) {
    const overdueTasks = tasks.filter((t: any) => 
      t.deadline && new Date(t.deadline) < new Date() && t.status !== 'done'
    )
    
    if (overdueTasks.length === 0) {
      return "Great news! There are no overdue tasks in this project."
    }
    
    let response = `There are ${overdueTasks.length} overdue tasks:\n`
    overdueTasks.slice(0, 5).forEach((task: any) => {
      const daysOverdue = Math.ceil((new Date().getTime() - new Date(task.deadline).getTime()) / (1000 * 60 * 60 * 24))
      response += `• "${task.name}" (${daysOverdue} days overdue)\n`
    })
    
    if (overdueTasks.length > 5) {
      response += `... and ${overdueTasks.length - 5} more.`
    }
    
    return response
  }
  
  if (lowerQuery.includes('completed') || lowerQuery.includes('done') || lowerQuery.includes('finished')) {
    const completedTasks = tasks.filter((t: any) => t.status === 'done')
    
    if (completedTasks.length === 0) {
      return "No tasks have been completed yet in this project."
    }
    
    let response = `${completedTasks.length} tasks have been completed:\n`
    completedTasks.slice(0, 5).forEach((task: any) => {
      response += `• "${task.name}"\n`
    })
    
    if (completedTasks.length > 5) {
      response += `... and ${completedTasks.length - 5} more.`
    }
    
    return response
  }
  
  if (lowerQuery.includes('next') || lowerQuery.includes('upcoming') || lowerQuery.includes('due soon')) {
    const upcomingTasks = tasks
      .filter((t: any) => t.deadline && new Date(t.deadline) > new Date() && t.status !== 'done')
      .sort((a: any, b: any) => new Date(a.deadline).getTime() - new Date(b.deadline).getTime())
      .slice(0, 5)
    
    if (upcomingTasks.length === 0) {
      return "There are no upcoming task deadlines."
    }
    
    let response = `Upcoming task deadlines:\n`
    upcomingTasks.forEach((task: any) => {
      const daysUntilDue = Math.ceil((new Date(task.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
      response += `• "${task.name}" (due in ${daysUntilDue} days)\n`
    })
    
    return response
  }
  
  // General task overview
  const totalTasks = tasks.length
  const completedTasks = tasks.filter((t: any) => t.status === 'done').length
  const todoTasks = tasks.filter((t: any) => t.status === 'todo').length
  const inProgressTasks = tasks.filter((t: any) => t.status === 'inProgress').length
  
  return `Task overview: ${totalTasks} total tasks with ${completedTasks} completed, ${inProgressTasks} in progress, and ${todoTasks} not started yet.`
}

function handleTeamQuery(query: string, projectContext: any): string {
  const { teamMembers, tasks } = projectContext
  const lowerQuery = query.toLowerCase()
  
  if (teamMembers.length === 0) {
    return "No team members are assigned to this project."
  }
  
  if (lowerQuery.includes('workload') || lowerQuery.includes('busy') || lowerQuery.includes('assigned')) {
    const workloadMap = new Map<string, number>()
    tasks.filter((t: any) => t.assignee && t.status !== 'done').forEach((task: any) => {
      const count = workloadMap.get(task.assignee) || 0
      workloadMap.set(task.assignee, count + 1)
    })
    
    if (workloadMap.size === 0) {
      return "All tasks are either completed or unassigned."
    }
    
    let response = "Current team workload:\n"
    Array.from(workloadMap.entries())
      .sort(([,a], [,b]) => b - a)
      .forEach(([member, count]) => {
        response += `• ${member}: ${count} active tasks\n`
      })
    
    return response
  }
  
  if (lowerQuery.includes('who is') || lowerQuery.includes('responsible')) {
    const taskMatch = query.match(/"([^"]+)"/);
    if (taskMatch) {
      const taskName = taskMatch[1];
      const task = tasks.find((t: any) => t.name.toLowerCase().includes(taskName.toLowerCase()));
      if (task && task.assignee) {
        return `"${task.name}" is assigned to ${task.assignee}.`;
      } else if (task) {
        return `"${task.name}" is not assigned to anyone yet.`;
      } else {
        return `I couldn't find a task matching "${taskName}".`;
      }
    }
  }
  
  // General team overview
  const activeMembers = teamMembers.filter((m: any) => m.isActive).length
  let response = `Team overview: ${teamMembers.length} team members`
  
  if (activeMembers < teamMembers.length) {
    response += ` (${activeMembers} active)`
  }
  
  response += ":\n"
  teamMembers.slice(0, 5).forEach((member: any) => {
    response += `• ${member.name} - ${member.role}${member.isActive ? '' : ' (inactive)'}\n`
  })
  
  if (teamMembers.length > 5) {
    response += `... and ${teamMembers.length - 5} more.`
  }
  
  return response
}

function handleBudgetQuery(projectContext: any): string {
  const { budget } = projectContext
  
  if (!budget) {
    return "No budget information is available for this project."
  }
  
  const utilization = (budget.spent / budget.total) * 100
  const remaining = budget.total - budget.spent
  
  let response = `Budget overview:\n`
  response += `• Total budget: $${budget.total.toLocaleString()}\n`
  response += `• Spent: $${budget.spent.toLocaleString()} (${utilization.toFixed(1)}%)\n`
  response += `• Remaining: $${remaining.toLocaleString()}\n`
  
  if (utilization > 90) {
    response += "\n⚠️ Warning: Budget utilization is very high!"
  } else if (utilization > 80) {
    response += "\n⚡ Note: Budget utilization is approaching the limit."
  } else {
    response += "\n✅ Budget utilization is within normal range."
  }
  
  return response
}

function handleTimelineQuery(query: string, projectContext: any): string {
  const { project, tasks } = projectContext
  const lowerQuery = query.toLowerCase()
  
  if (lowerQuery.includes('project deadline') || lowerQuery.includes('when is the project due')) {
    if (!project.deadline) {
      return "No deadline is set for this project."
    }
    
    const daysToDeadline = Math.ceil((new Date(project.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    const deadlineDate = new Date(project.deadline).toLocaleDateString()
    
    if (daysToDeadline > 0) {
      return `The project deadline is ${deadlineDate}, which is in ${daysToDeadline} days.`
    } else if (daysToDeadline === 0) {
      return `The project deadline is today (${deadlineDate})!`
    } else {
      return `The project deadline was ${deadlineDate}, which is ${Math.abs(daysToDeadline)} days ago.`
    }
  }
  
  // Task deadlines
  const tasksWithDeadlines = tasks.filter((t: any) => t.deadline && t.status !== 'done')
  
  if (tasksWithDeadlines.length === 0) {
    return "No active tasks have deadlines set."
  }
  
  const upcomingDeadlines = tasksWithDeadlines
    .sort((a: any, b: any) => new Date(a.deadline).getTime() - new Date(b.deadline).getTime())
    .slice(0, 5)
  
  let response = "Upcoming task deadlines:\n"
  upcomingDeadlines.forEach((task: any) => {
    const daysUntilDue = Math.ceil((new Date(task.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    const dueDate = new Date(task.deadline).toLocaleDateString()
    
    if (daysUntilDue > 0) {
      response += `• "${task.name}" - ${dueDate} (${daysUntilDue} days)\n`
    } else if (daysUntilDue === 0) {
      response += `• "${task.name}" - ${dueDate} (due today!)\n`
    } else {
      response += `• "${task.name}" - ${dueDate} (${Math.abs(daysUntilDue)} days overdue)\n`
    }
  })
  
  return response
}

function handleBlockerQuery(projectContext: any): string {
  const { tasks } = projectContext
  
  const blockedTasks = tasks.filter((t: any) => t.status === 'blocked' || (t.blockers && t.blockers.length > 0))
  
  if (blockedTasks.length === 0) {
    return "Great news! No tasks are currently blocked."
  }
  
  let response = `${blockedTasks.length} tasks are currently blocked:\n`
  
  blockedTasks.slice(0, 5).forEach((task: any) => {
    response += `• "${task.name}"`
    if (task.blockers && task.blockers.length > 0) {
      response += ` - ${task.blockers[0]}`
    }
    response += '\n'
  })
  
  if (blockedTasks.length > 5) {
    response += `... and ${blockedTasks.length - 5} more blocked tasks.`
  }
  
  return response
}

function generateProjectSummary(projectContext: any): string {
  const { project, tasks, teamMembers } = projectContext
  
  const totalTasks = tasks.length
  const completedTasks = tasks.filter((t: any) => t.status === 'done').length
  const progressPercent = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
  
  let response = `Here's a summary of project "${project.name}":\n\n`
  response += `📊 Progress: ${progressPercent}% complete (${completedTasks}/${totalTasks} tasks)\n`
  response += `👥 Team: ${teamMembers.length} members\n`
  
  if (project.deadline) {
    const daysToDeadline = Math.ceil((new Date(project.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    response += `📅 Deadline: ${daysToDeadline > 0 ? `${daysToDeadline} days remaining` : `${Math.abs(daysToDeadline)} days overdue`}\n`
  }
  
  const blockedTasks = tasks.filter((t: any) => t.status === 'blocked').length
  if (blockedTasks > 0) {
    response += `⚠️  ${blockedTasks} tasks are currently blocked\n`
  }
  
  response += `\nStatus: ${project.status}`
  
  return response
} 