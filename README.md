# ProjectPilot

ProjectPilot is a voice-to-task application that allows you to create tasks, notes, and ideas using voice commands. It can transcribe your voice and send the tasks to various platforms like ClickUp, Notion, and more.

## App Architecture

The app follows a clean architecture approach with clear separation of concerns across multiple layers:

### Core Components

- **BaseScaffold**: A flexible, customizable scaffold component that provides consistent UI structure across the app with rounded corners, shadows, and custom styling.
- **MainHomeScreen**: The main container component that provides necessary services and BLoCs for the home screen.
- **ContainerTheme**: Theme extension for consistent container styling throughout the app.

### Key Screens

- **HomeScreen**: The main home screen with the recording interface.
- **TaskScreen**: Displays and manages tasks.
- **EntriesScreen**: Displays and manages entries.
- **SettingsScreen**: App configuration and settings.

## Navigation

The app uses simple navigation with MaterialPageRoute. The `IntroScreen` is shown on first launch, after which the app navigates to the `MainHomeScreen`.

## State Management

The app uses BLoC pattern for state management with Cubit implementations.

## Styling

- Consistent theming using <PERSON><PERSON><PERSON>'s ThemeData and custom ThemeExtensions.
- Responsive layouts that adapt to different screen sizes.
- Modern UI with rounded corners, shadows, and appropriate spacing.

## Getting Started

### Prerequisites

- Flutter 3.0 or higher
- Dart 2.17 or higher

### Installation

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Create a `.env` file based on `.env.example`
4. Run the app with `flutter run`

## Configuration

The app can be configured using environment variables in the `.env` file:

- `SUPABASE_URL`: Supabase URL for backend services
- `SUPABASE_ANON_KEY`: Supabase anonymous key for authentication
- `OPENAI_API_KEY`: OpenAI API key for AI processing
