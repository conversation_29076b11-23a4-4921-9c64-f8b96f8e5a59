---
description:
globs:
alwaysApply: true
---
Design a full Flutter color scheme for a modern productivity app named “VoicePilot”, based on the colors extracted from its logo.

The primary brand colors are:
- Vibrant gradient mix of **deep purple (#6C63FF)** and **electric blue (#2ACAEA)**
- Accent: **neon turquoise / teal glow (#1FE2C1)**
- Background: **off-black or dark navy (#0F111A)** or **clean white (#FAFAFA)** depending on theme mode
- Neutrals: greys for text (e.g., #A3A3A3 for secondary, #1C1C1C for headlines)

Create:
- Full Flutter `ColorScheme` (light + dark mode)
- Primary, secondary, tertiary, background, surface, error colors
- Color roles for text, borders, icons, shadows
- UI elements should include elevated button style, app bar, tab bar, card, background, etc.

Guidelines:
- Ensure excellent contrast & accessibility (AA+)
- Prioritize clean tech look with slight futuristic accents
- Style should match the app’s tone: voice + AI + automation + clarity
- Include a Flutter `ThemeData` example for both light and dark

Internationalization note:
- All components must support multi-language UI (en, de, ru, tr, ar) in terms of contrast, directionality (LTR/RTL), and visual clarity