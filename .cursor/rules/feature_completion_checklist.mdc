---
description:
globs:
alwaysApply: true
---
# Feature Completion Checklist

This rule serves as a comprehensive checklist to ensure that a feature is completely implemented according to the VoicePilot project's clean architecture guidelines. Use this checklist when implementing a new feature or enhancing an existing one.

## Project Structure Checklist

### Screen Implementation
- [ ] Screen file created at `lib/presentation/ui/screens/[feature]/[feature]_screen.dart`
- [ ] Feature-specific widgets folder created at `lib/presentation/ui/screens/[feature]/widgets/`
- [ ] Complex screens use component views at `lib/presentation/ui/screens/[feature]/components/`
- [ ] Screen follows the established pattern (StatelessWidget wrapper + View implementation)

### State Management
- [ ] Cubit created at `lib/presentation/bloc/[feature]/[feature]_cubit.dart`
- [ ] State class created at `lib/presentation/bloc/[feature]/[feature]_state.dart`
- [ ] States follow established patterns (Initial, Loading, Loaded, Error, etc.)
- [ ] Cubit methods use proper error handling with try/catch blocks

### Domain Layer
- [ ] Entity classes defined in `lib/domain/entities/`
- [ ] Repository interfaces defined in `lib/domain/repositories/`
- [ ] Failure cases defined in `lib/domain/failure/failures.dart` (don't create new failure files)

### Data Layer
- [ ] Repository implementations in `lib/data/repositories/`
- [ ] Local data source implementations in `lib/data/local/`
- [ ] Remote data source implementations in `lib/data/remote/`
- [ ] API adapters in `lib/data/remote/platform_adapters/` (if connecting to external services)

### Dependency Injection
- [ ] Service registrations added to `lib/core/di/service_locator.dart`
- [ ] Proper Singleton/Factory patterns used based on the component type

### Routing
- [ ] Routes added to `lib/core/router/app_router.dart`
- [ ] Route parameters properly defined and documented

## Internationalization and Accessibility

### Translations
- [ ] All user-facing strings added to `lib/l10n/app_en.arb`
- [ ] No hardcoded strings in UI components
- [ ] Proper use of placeholders for dynamic content
- [ ] All strings have descriptions for context

### Accessibility
- [ ] Semantic labels added to important widgets
- [ ] Color contrast ratios meet AA standards
- [ ] Interactive elements have appropriate sizes (minimum 44x44 for touch targets)
- [ ] Support for text scaling

## Code Quality and Conventions

### Architecture Compliance
- [ ] Follows clean architecture principles (UI → Presentation → Domain ← Data)
- [ ] No layer violations (e.g., presentation layer doesn't directly depend on data layer)
- [ ] Domain entities don't depend on UI or external packages

### Widget Patterns
- [ ] Reusable widgets used from `lib/core/widgets/` where appropriate
- [ ] Feature-specific widgets properly extracted
- [ ] Widget constructors use `const` where appropriate
- [ ] Large widget trees broken down into smaller, focused components

### Error Handling
- [ ] Repository methods return `Either<Failure, T>` for error handling
- [ ] UI provides appropriate error states and messages
- [ ] Error messages use localized strings

### Styling
- [ ] Theme colors accessed via `Theme.of(context).colorScheme`
- [ ] Text styles use the theme's text theme
- [ ] Consistent spacing using predefined constants
- [ ] Responsive design considerations

## Testing

### Unit Tests
- [ ] Unit tests for repository implementations
- [ ] Unit tests for cubits and business logic
- [ ] Mocks created for dependencies
- [ ] Edge cases and error scenarios covered

### Widget Tests
- [ ] Widget tests for complex UI components
- [ ] Tests for different states (loading, error, success)

## Documentation

### Code Documentation
- [ ] Public methods and classes documented with dartdoc comments
- [ ] Complex logic explained with regular comments
- [ ] Architecture decisions documented where necessary

### Project Documentation
- [ ] README updated if the feature introduces new concepts
- [ ] Any required setup steps documented

## Performance and Optimization

- [ ] Efficient list rendering (ListView.builder, GridView.builder)
- [ ] Minimal widget rebuilds (using const constructors, selective rebuilds)
- [ ] Images and assets optimized
- [ ] Heavy computations handled appropriately (compute() for expensive operations)

## Connectivity and Offline Support

- [ ] Offline-first approach implemented
- [ ] Appropriate caching strategies
- [ ] Graceful handling of connectivity changes
- [ ] Sync mechanisms for offline operations

## Security Considerations

- [ ] Sensitive data stored securely
- [ ] API keys and secrets not hardcoded
- [ ] Input validation for user inputs
- [ ] Safe handling of external data

## Feature-Specific Checklist

Add specific requirements for your feature here:

- [ ] Requirement 1
- [ ] Requirement 2
- [ ] Requirement 3

## Final Quality Checks

- [ ] No linting warnings or errors
- [ ] Code formatted according to project standards
- [ ] No debugging code left in production code
- [ ] Feature tested on multiple device sizes
- [ ] Feature manually tested for usability

---

## How to Use This Checklist

1. Copy this checklist to your feature implementation documentation or issue
2. Check off items as you complete them
3. Have another team member review the checklist before considering the feature complete
4. Use this checklist during code reviews to ensure completeness

Remember that not all items may apply to every feature. Use your judgment to determine which items are relevant for your specific implementation.
