---
description:
globs:
alwaysApply: true
---
# Flutter & Dart Best Practices

This document outlines general best practices for Flutter and Dart development that apply to any Flutter project, not just this specific app.

## Effective Dart Rules

### Naming Conventions
1. Use terms consistently throughout your code.
2. Follow existing mnemonic conventions when naming type parameters (e.g., `E` for element, `K`/`V` for key/value, `T`/`S`/`U` for generic types).
3. Name types using `UpperCamelCase` (classes, enums, typedefs, type parameters).
4. Name extensions using `UpperCamelCase`.
5. Name packages, directories, and source files using `lowercase_with_underscores`.
6. Name import prefixes using `lowercase_with_underscores`.
7. Name other identifiers using `lowerCamelCase` (variables, parameters, named parameters).
8. Capitalize acronyms and abbreviations longer than two letters like words.
9. Avoid abbreviations unless the abbreviation is more common than the unabbreviated term.
10. Consider making code read like a sentence when designing APIs.
11. Prefer a noun phrase for non-boolean properties or variables.
12. Prefer a non-imperative verb phrase for boolean properties or variables.
13. Prefer the positive form for boolean property and variable names.

### Types and Functions
1. Use class modifiers to control if your class can be extended or used as an interface.
2. Type annotate variables without initializers.
3. Type annotate fields and top-level variables if the type isn't obvious.
4. Annotate return types on function declarations.
5. Annotate parameter types on function declarations.
6. Write type arguments on generic invocations that aren't inferred.
7. Annotate with `dynamic` instead of letting inference fail.
8. Use `Future<void>` as the return type of asynchronous members that do not produce values.
9. Use getters for operations that conceptually access properties.
10. Use setters for operations that conceptually change properties.
11. Use a function declaration to bind a function to a name.
12. Use inclusive start and exclusive end parameters to accept a range.

### Style
1. Format your code using `dart format`.
2. Use curly braces for all flow control statements.
3. Prefer `final` over `var` when variable values won't change.
4. Use `const` for compile-time constants.

### Imports & Files
1. Don't import libraries inside the `src` directory of another package.
2. Don't allow import paths to reach into or out of `lib`.
3. Prefer relative import paths within a package.
4. Don't use `/lib/` or `../` in import paths.
5. Consider writing a library-level doc comment for library files.

### Structure
1. Keep files focused on a single responsibility.
2. Limit file length to maintain readability.
3. Group related functionality together.
4. Prefer making fields and top-level variables `final`.
5. Consider making your constructor `const` if the class supports it.
6. Prefer making declarations private.

## Bloc/Cubit Best Practices

### State Management Principles
1. Separate business logic from UI.
2. Use Cubit for simple state management; use Bloc for more complex, event-driven state management.
3. Keep states immutable - never modify state directly.
4. Extend Equatable for state classes to implement value equality.
5. Handle all possible states in the UI (loading, success, error, etc.).

### Cubit Patterns
1. Define the initial state by passing it to the superclass constructor.
2. Only use the `emit` method inside a Cubit or Bloc.
3. Avoid direct bloc-to-bloc communication to prevent tight coupling.
4. Use BlocListener for side effects like navigation or showing dialogs.
5. Use BlocProvider for dependency injection of cubits.
6. Use context.read() to access a cubit in event handlers without subscribing to changes.
7. Handle error states properly in all asynchronous operations.

### State Class Design
1. Make state classes immutable and implement a copyWith method.
2. Use `const` constructors for state classes whenever possible.
3. Include all relevant properties in the `props` getter when using Equatable.
4. Consider implementing extension methods on state classes for derived properties.

## Flutter UI Guidelines

### Widget Best Practices
1. Use `StatelessWidget` whenever possible.
2. Extract reusable widgets into separate components.
3. Keep build methods simple and focused.
4. Use `const` constructors for widgets when possible to improve performance.
5. Implement proper error boundaries.
6. Set keys on widgets when their identity matters.

### Performance Best Practices
1. Avoid expensive operations in build methods.
2. Implement pagination for large lists.
3. Use proper list view optimization techniques.
4. Implement proper memory management.
5. Keep state as local as possible to minimize rebuilds.

### Layout Guidelines
1. Use proper constraints to avoid overflow errors.
2. Wrap ListView inside a Column with Expanded to avoid unbounded height errors.
3. Constrain the width of widgets like TextField using Expanded or SizedBox.
4. Never call setState or showDialog directly inside build methods.
5. Ensure each ScrollController is only attached to a single scrollable widget.

### Common Flutter Errors
1. "RenderFlex overflowed" - Wrap children in Flexible, Expanded, or set constraints.
2. "Vertical viewport was given unbounded height" - Add bounded height to ListView in Column.
3. "An InputDecorator cannot have an unbounded width" - Constrain TextField width.
4. "setState called during build" - Move setState calls outside of build.
5. "The ScrollController is attached to multiple scroll views" - Use unique controllers.
6. "RenderBox was not laid out" - Check for missing or unbounded constraints.

## Clean Architecture Principles

### Layer Separation
1. Separate your features into UI Layer, Domain Layer, and Data Layer.
2. Only allow communication between adjacent layers.
3. Keep the Domain layer independent of frameworks.
4. Use repository interfaces in the Domain layer, implementations in the Data layer.
5. Use dependency injection to provide implementations to the UI layer.

### Responsibilities by Layer
1. UI Layer: Present data and handle user interactions
2. Domain Layer: Define business logic, entities, and repository interfaces
3. Data Layer: Implement repositories, manage remote and local data sources

### Data Flow
1. Follow unidirectional data flow (UI → Domain → Data and back).
2. Data changes should always happen in the data layer, not in the UI.
3. The UI should always reflect the current (immutable) state.
4. Use repository pattern as single source of truth for data.

## Testing Best Practices
1. Write unit tests for business logic.
2. Write widget tests for UI components.
3. Use proper mocking strategies for dependencies.
4. Test both success and error paths.
5. Keep tests focused on a single functionality.
6. Use `blocTest` for testing Cubits and Blocs.
7. Test the initial state and all state transitions.
8. Mock cubits/blocs in widget tests to verify UI behavior for all states.
